import numpy as np 
import pandas as pd  #Realizzato con la versione di Pandas 2.2.3
import os
import psycopg
from sqlalchemy import create_engine, text

conn = psycopg.connect(
    dbname="db_modena",           # database creato in pgAdmin4
    user="postgres",              # Il tuo nome utente PostgreSQL
    password="AcquaLevissima1",   # La password che hai scelto per 'postgres'
    host="localhost",             # 'localhost' se è sul tuo PC
    port=5432                     # La porta predefinita è 5432
)

# Crea un cursore per eseguire le query
cur = conn.cursor()

#creo un engine per poter usare pandas
engine = create_engine('postgresql+psycopg://postgres:AcquaLevissima1@localhost:5432/db_modena')

#Creiamo il trigger che aggiorna IDHomeTeam e IDVisitorTeam in games in base a TeamID e HomeAway in teams_each_game, così potrò correggere le partite a aggiornarle correttamente guardando il calendario della legavolley.it

# Definizione della funzione
cur.execute("""
CREATE OR REPLACE FUNCTION update_idhometeam_idvisitorteam_games()
RETURNS TRIGGER AS $$
BEGIN
    -- Aggiorna IDHomeTeam nella tabella games
    UPDATE games
    SET "IDHomeTeam" = (
        SELECT "TeamID"
        FROM teams_each_game
        WHERE "GameID" = NEW."GameID" AND "HomeAway" = FALSE
    )
    WHERE "GameID" = NEW."GameID";

    -- Aggiorna IDVisitorTeam nella tabella games
    UPDATE games
    SET "IDVisitorTeam" = (
        SELECT "TeamID"
        FROM teams_each_game
        WHERE "GameID" = NEW."GameID" AND "HomeAway" = TRUE
    )
    WHERE "GameID" = NEW."GameID";

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
""")

# Creazione del trigger
cur.execute("""
DO $$
BEGIN
    -- Elimina il trigger se esiste già
    IF EXISTS (
        SELECT 1
        FROM pg_trigger
        WHERE tgname = 'trigger_update_idhometeam_idvisitorteam_games'
    ) THEN
        DROP TRIGGER trigger_update_idhometeam_idvisitorteam_games ON teams_each_game;
    END IF;

    -- Crea il trigger associato alla funzione
    CREATE TRIGGER trigger_update_idhometeam_idvisitorteam_games
    AFTER UPDATE ON teams_each_game
    FOR EACH ROW
    EXECUTE FUNCTION update_idhometeam_idvisitorteam_games();
END $$;
""")


conn.commit()
print("Trigger update_idhometeam_idvisitorteam_games creato con successo")






