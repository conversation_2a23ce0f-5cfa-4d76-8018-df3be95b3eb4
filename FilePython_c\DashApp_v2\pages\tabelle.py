"""
Pagina Tabelle dell'applicazione
"""
import dash
from dash import html, dcc, callback, Input, Output, State
import dash_bootstrap_components as dbc
import dash_ag_grid as dag
import pandas as pd
import numpy as np

# Non registrare la pagina qui - viene fatto nell'app.py principale

def layout(filtered_data=None, filters=None):
    """
    Layout della pagina Tabelle
    
    Args:
        filtered_data (dict): Dati filtrati dai filtri globali
        filters (dict): Filtri attualmente attivi
    """
    
    return html.Div([
        dbc.Container([
            dbc.Row([
                dbc.Col([
                    html.H1("Tabelle Dati", className="text-center mb-4"),
                    html.Hr()
                ])
            ]),
            
            # Informazioni sulla tabella
            dbc.Row([
                dbc.Col([
                    html.Div([
                        html.P("Questa pagina mostra sempre le tabelle principali (Rilevazioni e Partite) e, in base al filtro 'Suddivisione dati' nella sidebar, anche le tabelle appropriate:", 
                               className="text-muted mb-1"),
                        html.P("• Complessiva → Giocatori e Squadre", className="text-muted mb-1"),
                        html.P("• Per stagione → Giocatori per Stagione e Squadre per Stagione", className="text-muted mb-1"),
                        html.P("• Per mese → Giocatori per Mese e Squadre per Mese", className="text-muted mb-1"),
                        html.P("• Per partita → Giocatori per Partita e Squadre per Partita", className="text-muted mb-0"),
                        html.P("• Usa i controlli di paginazione nelle tabelle per navigare tra le righe", 
                               className="text-info mb-0"),
                    ])
                ], width=12)
            ], className="mb-4"),
            
            # Tabella Rilevazioni (sempre visibile)
            dbc.Row([
                dbc.Col([
                    html.H3("Tabella Rilevazioni", className="mb-3"),
                    html.Div(id="rilevations-table")
                ], width=12)
            ], className="mb-4"),
            
            # Tabella Partite (sempre visibile)
            dbc.Row([
                dbc.Col([
                    html.H3("Tabella Partite", className="mb-3"),
                    html.Div(id="games-table")
                ], width=12)
            ], className="mb-4"),
            
            # Tabelle dinamiche in base al filtro
            dbc.Row([
                dbc.Col([
                    html.H3("Tabelle Dinamiche", className="mb-3"),
                    html.Div(id="dynamic-tables")
                ], width=12)
            ], className="mb-4"),
            
        ], fluid=True)
    ])


@callback(
    [Output("rilevations-table", "children"),
     Output("games-table", "children"),
     Output("dynamic-tables", "children")],
    [Input("filtered-data-store", "data"),
     Input("filters-store", "data")]  # I filtri della sidebar per forzare l'aggiornamento
)
def update_tables(filtered_data, filters):
    """Aggiorna tutte le tabelle in base ai filtri della sidebar"""
    
    # Determina le tabelle da mostrare in base al filtro suddivisione dati
    suddivisione_dati = filters.get('suddivisione_dati', 1) if filters else 1
    
    # Mappa i valori di suddivisione dati alle tabelle appropriate
    table_mapping = {
        1: {  # Complessiva
            'players': 'df_players',
            'teams': 'df_teams'
        },
        2: {  # Per stagione
            'players': 'df_players_each_season',
            'teams': 'df_teams_each_season'
        },
        3: {  # Per mese
            'players': 'df_players_each_month',
            'teams': 'df_teams_each_month'
        },
        4: {  # Per partita
            'players': 'df_players_each_game',
            'teams': 'df_teams_each_game'
        }
    }
    
    tables_to_show = table_mapping.get(suddivisione_dati, table_mapping[1])
    
    # Crea le tabelle principali (sempre visibili)
    rilevations_table = create_table_component('df_rilevations', filtered_data, "Rilevazioni")
    games_table = create_table_component('df_games', filtered_data, "Partite")
    
    # Crea le tabelle dinamiche
    dynamic_tables = create_dynamic_tables(tables_to_show, filtered_data)
    
    return rilevations_table, games_table, dynamic_tables


def create_table_component(table_name, filtered_data, display_name):
    """Crea un componente tabella per una tabella specifica"""
    if not filtered_data or table_name not in filtered_data:
        return dbc.Alert(f"Nessun dato disponibile per {display_name}", color="warning")
    
    table_data = filtered_data[table_name]
    
    if isinstance(table_data, dict) and 'data' in table_data:
        df = pd.DataFrame(table_data['data'])
        total_rows = table_data.get('total_rows', len(df))
    else:
        df = pd.DataFrame(table_data)
        total_rows = len(df)
    
    if df.empty:
        return dbc.Alert(f"La tabella {display_name} è vuota", color="warning")
    
    # Crea la tabella con AG Grid
    table_content = create_paginated_table(df)
    
    # Informazioni sulla tabella con pulsanti di download
    table_info = dbc.Alert([
        #html.H5(f"Tabella: {display_name}", className="alert-heading"),
        html.P(f"Righe: {len(df):,} di {total_rows:,} totali"),
        html.Hr(),
        html.Div([
            html.Span("Scarica i dati:", className="me-2"),
            dbc.Button("CSV", id=f"download-csv-{table_name}", color="success", size="sm", className="me-2"),
            dbc.Button("Excel", id=f"download-excel-{table_name}", color="primary", size="sm", className="me-2"),
            dcc.Download(id=f"download-data-{table_name}")
        ], className="mt-2")
    ], color="info", className="mb-3")
    
    return html.Div([
        table_info,
        table_content
    ])


def create_dynamic_tables(tables_to_show, filtered_data):
    """Crea le tabelle dinamiche in base al filtro suddivisione dati"""
    if not filtered_data:
        return dbc.Alert("Nessun dato disponibile per le tabelle dinamiche", color="warning")
    
    tables_content = []
    
    # Tabella Giocatori
    if 'players' in tables_to_show:
        table_name = tables_to_show['players']
        if table_name in filtered_data:
            players_table = create_table_component(table_name, filtered_data, "Giocatori")
            tables_content.append(
                dbc.Row([
                    dbc.Col([
                        html.H4("Tabella Giocatori", className="mb-3"),
                        players_table
                    ], width=12)
                ], className="mb-4")
            )
        else:
            # Mostra errore specifico per la tabella mancante
            tables_content.append(
                dbc.Row([
                    dbc.Col([
                        html.H4("Tabella Giocatori", className="mb-3"),
                        dbc.Alert(f"Tabella '{table_name}' non trovata nei dati filtrati", color="warning")
                    ], width=12)
                ], className="mb-4")
            )
    
    # Tabella Squadre
    if 'teams' in tables_to_show:
        table_name = tables_to_show['teams']
        if table_name in filtered_data:
            teams_table = create_table_component(table_name, filtered_data, "Squadre")
            tables_content.append(
                dbc.Row([
                    dbc.Col([
                        html.H4("Tabella Squadre", className="mb-3"),
                        teams_table
                    ], width=12)
                ], className="mb-4")
            )
        else:
            # Mostra errore specifico per la tabella mancante
            tables_content.append(
                dbc.Row([
                    dbc.Col([
                        html.H4("Tabella Squadre", className="mb-3"),
                        dbc.Alert(f"Tabella '{table_name}' non trovata nei dati filtrati", color="warning")
                    ], width=12)
                ], className="mb-4")
            )
    
    if not tables_content:
        return dbc.Alert("Nessuna tabella dinamica disponibile", color="warning")
    
    return html.Div(tables_content)


def create_paginated_table(df):
    """Crea una tabella usando AG Grid con ordinamento e tutte le osservazioni"""
    
    if df.empty:
        return dbc.Alert("Nessuna colonna disponibile per la visualizzazione", color="warning")
    
    # Prepara i dati per AG Grid - usa TUTTE le osservazioni
    # Converti i valori NaN in stringhe vuote per evitare problemi
    df_clean = df.fillna('')
    
    # Crea le definizioni delle colonne per AG Grid
    column_defs = []
    for col in df_clean.columns:
        # Determina il tipo di dato per la colonna
        if df_clean[col].dtype in ['int64', 'int32', 'float64', 'float32']:
            col_type = 'numericColumn'
        else:
            col_type = 'textColumn'
        
        column_defs.append({
            'field': col,
            'headerName': col,
            'type': col_type,
            'sortable': True,
            'filter': True,
            'resizable': True,
            'width': 150,  # Larghezza di default
            'minWidth': 80,
            'maxWidth': 400
        })
    
    # Crea la tabella AG Grid
    ag_grid = dag.AgGrid(
        id='data-table',
        rowData=df_clean.to_dict('records'),
        columnDefs=column_defs,
        defaultColDef={
            'sortable': True,
            'filter': True,
            'resizable': True,
            'floatingFilter': False,
        },
        style={'height': '600px', 'width': '100%'},
        dashGridOptions={
            'pagination': True,
            'paginationPageSize': 100,  # Pagina di default più piccola per performance
            'paginationPageSizeSelector': [50, 100, 200, 500],  # Selettore di dimensione pagina
            'suppressRowClickSelection': True,
            'animateRows': True,
            'rowSelection': 'single',
            'domLayout': 'normal',
            'enableRangeSelection': True,
            'enableCharts': False
        }
    )
    
    return html.Div([
        ag_grid,
        html.P(f"Caricate {len(df_clean):,} righe totali - Usa i controlli di paginazione per navigare", 
               className="text-muted text-center mt-2")
    ])


def create_pagination_controls(df, total_rows=None):
    """Crea i controlli di paginazione per AG Grid"""
    
    if total_rows is None:
        total_rows = len(df)
    
    # AG Grid gestisce la paginazione internamente, mostriamo solo le informazioni
    return dbc.Alert([
        html.H6("Informazioni Tabella", className="alert-heading"),
        html.P(f"• Clicca sull'intestazione di una colonna per ordinare", className="mb-1"),
        html.P(f"• Usa i filtri nelle intestazioni per cercare", className="mb-1"),
        html.P(f"• Ridimensiona le colonne trascinando i bordi", className="mb-1"),
        html.P(f"• Usa il selettore di dimensione pagina nella tabella (50, 100, 200, 500 righe)", className="mb-1"),
        html.P(f"• {total_rows:,} righe totali disponibili", className="mb-0")
    ], color="info")


# Callback combinati per i download - base tables
@callback(
    Output("download-data-df_rilevations", "data"),
    [Input("download-csv-df_rilevations", "n_clicks"),
     Input("download-excel-df_rilevations", "n_clicks")],
    State("filtered-data-store", "data"),
    prevent_initial_call=True
)
def download_rilevations(csv_clicks, excel_clicks, filtered_data):
    ctx = dash.callback_context
    if not ctx.triggered or not filtered_data or 'df_rilevations' not in filtered_data:
        return None
    
    table_data = filtered_data['df_rilevations']
    if isinstance(table_data, dict) and 'data' in table_data:
        df = pd.DataFrame(table_data['data'])
    else:
        df = pd.DataFrame(table_data)
    
    if df.empty:
        return None
    
    trigger_id = ctx.triggered[0]['prop_id']
    if 'csv' in trigger_id:
        return dcc.send_data_frame(df.to_csv, "rilevations.csv", index=False)
    elif 'excel' in trigger_id:
        return dcc.send_data_frame(df.to_excel, "rilevations.xlsx", index=False)
    
    return None


@callback(
    Output("download-data-df_games", "data"),
    [Input("download-csv-df_games", "n_clicks"),
     Input("download-excel-df_games", "n_clicks")],
    State("filtered-data-store", "data"),
    prevent_initial_call=True
)
def download_games(csv_clicks, excel_clicks, filtered_data):
    ctx = dash.callback_context
    if not ctx.triggered or not filtered_data or 'df_games' not in filtered_data:
        return None
    
    table_data = filtered_data['df_games']
    if isinstance(table_data, dict) and 'data' in table_data:
        df = pd.DataFrame(table_data['data'])
    else:
        df = pd.DataFrame(table_data)
    
    if df.empty:
        return None
    
    trigger_id = ctx.triggered[0]['prop_id']
    if 'csv' in trigger_id:
        return dcc.send_data_frame(df.to_csv, "games.csv", index=False)
    elif 'excel' in trigger_id:
        return dcc.send_data_frame(df.to_excel, "games.xlsx", index=False)
    
    return None


@callback(
    Output("download-data-df_players", "data"),
    [Input("download-csv-df_players", "n_clicks"),
     Input("download-excel-df_players", "n_clicks")],
    State("filtered-data-store", "data"),
    prevent_initial_call=True
)
def download_players(csv_clicks, excel_clicks, filtered_data):
    ctx = dash.callback_context
    if not ctx.triggered or not filtered_data or 'df_players' not in filtered_data:
        return None
    
    table_data = filtered_data['df_players']
    if isinstance(table_data, dict) and 'data' in table_data:
        df = pd.DataFrame(table_data['data'])
    else:
        df = pd.DataFrame(table_data)
    
    if df.empty:
        return None
    
    trigger_id = ctx.triggered[0]['prop_id']
    if 'csv' in trigger_id:
        return dcc.send_data_frame(df.to_csv, "players.csv", index=False)
    elif 'excel' in trigger_id:
        return dcc.send_data_frame(df.to_excel, "players.xlsx", index=False)
    
    return None


@callback(
    Output("download-data-df_teams", "data"),
    [Input("download-csv-df_teams", "n_clicks"),
     Input("download-excel-df_teams", "n_clicks")],
    State("filtered-data-store", "data"),
    prevent_initial_call=True
)
def download_teams(csv_clicks, excel_clicks, filtered_data):
    ctx = dash.callback_context
    if not ctx.triggered or not filtered_data or 'df_teams' not in filtered_data:
        return None
    
    table_data = filtered_data['df_teams']
    if isinstance(table_data, dict) and 'data' in table_data:
        df = pd.DataFrame(table_data['data'])
    else:
        df = pd.DataFrame(table_data)
    
    if df.empty:
        return None
    
    trigger_id = ctx.triggered[0]['prop_id']
    if 'csv' in trigger_id:
        return dcc.send_data_frame(df.to_csv, "teams.csv", index=False)
    elif 'excel' in trigger_id:
        return dcc.send_data_frame(df.to_excel, "teams.xlsx", index=False)
    
    return None


# Callback combinati per le tabelle dinamiche
@callback(
    Output("download-data-df_players_each_season", "data"),
    [Input("download-csv-df_players_each_season", "n_clicks"),
     Input("download-excel-df_players_each_season", "n_clicks")],
    State("filtered-data-store", "data"),
    prevent_initial_call=True
)
def download_players_each_season(csv_clicks, excel_clicks, filtered_data):
    ctx = dash.callback_context
    if not ctx.triggered or not filtered_data or 'df_players_each_season' not in filtered_data:
        return None
    
    table_data = filtered_data['df_players_each_season']
    if isinstance(table_data, dict) and 'data' in table_data:
        df = pd.DataFrame(table_data['data'])
    else:
        df = pd.DataFrame(table_data)
    
    if df.empty:
        return None
    
    trigger_id = ctx.triggered[0]['prop_id']
    if 'csv' in trigger_id:
        return dcc.send_data_frame(df.to_csv, "players_each_season.csv", index=False)
    elif 'excel' in trigger_id:
        return dcc.send_data_frame(df.to_excel, "players_each_season.xlsx", index=False)
    
    return None


@callback(
    Output("download-data-df_teams_each_season", "data"),
    [Input("download-csv-df_teams_each_season", "n_clicks"),
     Input("download-excel-df_teams_each_season", "n_clicks")],
    State("filtered-data-store", "data"),
    prevent_initial_call=True
)
def download_teams_each_season(csv_clicks, excel_clicks, filtered_data):
    ctx = dash.callback_context
    if not ctx.triggered or not filtered_data or 'df_teams_each_season' not in filtered_data:
        return None
    
    table_data = filtered_data['df_teams_each_season']
    if isinstance(table_data, dict) and 'data' in table_data:
        df = pd.DataFrame(table_data['data'])
    else:
        df = pd.DataFrame(table_data)
    
    if df.empty:
        return None
    
    trigger_id = ctx.triggered[0]['prop_id']
    if 'csv' in trigger_id:
        return dcc.send_data_frame(df.to_csv, "teams_each_season.csv", index=False)
    elif 'excel' in trigger_id:
        return dcc.send_data_frame(df.to_excel, "teams_each_season.xlsx", index=False)
    
    return None


@callback(
    Output("download-data-df_players_each_month", "data"),
    [Input("download-csv-df_players_each_month", "n_clicks"),
     Input("download-excel-df_players_each_month", "n_clicks")],
    State("filtered-data-store", "data"),
    prevent_initial_call=True
)
def download_players_each_month(csv_clicks, excel_clicks, filtered_data):
    ctx = dash.callback_context
    if not ctx.triggered or not filtered_data or 'df_players_each_month' not in filtered_data:
        return None
    
    table_data = filtered_data['df_players_each_month']
    if isinstance(table_data, dict) and 'data' in table_data:
        df = pd.DataFrame(table_data['data'])
    else:
        df = pd.DataFrame(table_data)
    
    if df.empty:
        return None
    
    trigger_id = ctx.triggered[0]['prop_id']
    if 'csv' in trigger_id:
        return dcc.send_data_frame(df.to_csv, "players_each_month.csv", index=False)
    elif 'excel' in trigger_id:
        return dcc.send_data_frame(df.to_excel, "players_each_month.xlsx", index=False)
    
    return None


@callback(
    Output("download-data-df_teams_each_month", "data"),
    [Input("download-csv-df_teams_each_month", "n_clicks"),
     Input("download-excel-df_teams_each_month", "n_clicks")],
    State("filtered-data-store", "data"),
    prevent_initial_call=True
)
def download_teams_each_month(csv_clicks, excel_clicks, filtered_data):
    ctx = dash.callback_context
    if not ctx.triggered or not filtered_data or 'df_teams_each_month' not in filtered_data:
        return None
    
    table_data = filtered_data['df_teams_each_month']
    if isinstance(table_data, dict) and 'data' in table_data:
        df = pd.DataFrame(table_data['data'])
    else:
        df = pd.DataFrame(table_data)
    
    if df.empty:
        return None
    
    trigger_id = ctx.triggered[0]['prop_id']
    if 'csv' in trigger_id:
        return dcc.send_data_frame(df.to_csv, "teams_each_month.csv", index=False)
    elif 'excel' in trigger_id:
        return dcc.send_data_frame(df.to_excel, "teams_each_month.xlsx", index=False)
    
    return None


@callback(
    Output("download-data-df_players_each_game", "data"),
    [Input("download-csv-df_players_each_game", "n_clicks"),
     Input("download-excel-df_players_each_game", "n_clicks")],
    State("filtered-data-store", "data"),
    prevent_initial_call=True
)
def download_players_each_game(csv_clicks, excel_clicks, filtered_data):
    ctx = dash.callback_context
    if not ctx.triggered or not filtered_data or 'df_players_each_game' not in filtered_data:
        return None
    
    table_data = filtered_data['df_players_each_game']
    if isinstance(table_data, dict) and 'data' in table_data:
        df = pd.DataFrame(table_data['data'])
    else:
        df = pd.DataFrame(table_data)
    
    if df.empty:
        return None
    
    trigger_id = ctx.triggered[0]['prop_id']
    if 'csv' in trigger_id:
        return dcc.send_data_frame(df.to_csv, "players_each_game.csv", index=False)
    elif 'excel' in trigger_id:
        return dcc.send_data_frame(df.to_excel, "players_each_game.xlsx", index=False)
    
    return None


@callback(
    Output("download-data-df_teams_each_game", "data"),
    [Input("download-csv-df_teams_each_game", "n_clicks"),
     Input("download-excel-df_teams_each_game", "n_clicks")],
    State("filtered-data-store", "data"),
    prevent_initial_call=True
)
def download_teams_each_game(csv_clicks, excel_clicks, filtered_data):
    ctx = dash.callback_context
    if not ctx.triggered or not filtered_data or 'df_teams_each_game' not in filtered_data:
        return None
    
    table_data = filtered_data['df_teams_each_game']
    if isinstance(table_data, dict) and 'data' in table_data:
        df = pd.DataFrame(table_data['data'])
    else:
        df = pd.DataFrame(table_data)
    
    if df.empty:
        return None
    
    trigger_id = ctx.triggered[0]['prop_id']
    if 'csv' in trigger_id:
        return dcc.send_data_frame(df.to_csv, "teams_each_game.csv", index=False)
    elif 'excel' in trigger_id:
        return dcc.send_data_frame(df.to_excel, "teams_each_game.xlsx", index=False)
    
    return None
