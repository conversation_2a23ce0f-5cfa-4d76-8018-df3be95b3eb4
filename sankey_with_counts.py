# CODICE AGGIORNATO PER MOSTRARE I CONTEGGI NEI NODI E NEI LINK
# Sostituisci la parte del callback principale con questo codice

# Callback principale per creare il diagramma Sankey
@app.callback(
    Output('sankey-graph', 'figure'),
    Output('debug-info', 'children'),
    Input('source-fondamentale', 'value'),
    Input('source-caratteristica', 'value'),
    Input('target-fondamentale', 'value'),
    Input('target-caratteristica', 'value')
)
def update_sankey_graph(source_fond, source_char, target_fond, target_char):
    debug_messages = []
    
    # Controlli di validità
    if not all([source_fond, source_char, target_fond, target_char]):
        debug_messages.append("⚠️ Seleziona tutti i parametri")
        return go.Figure(), html.Div([html.P(msg) for msg in debug_messages])
    
    debug_messages.append(f"🔍 Analisi: {source_fond}({source_char}) → {target_fond}({target_char})")
    
    # Verifica che df esista
    if 'df' not in globals() or df.empty:
        debug_messages.append("❌ Dati non disponibili")
        return go.Figure(), html.Div([html.P(msg) for msg in debug_messages])
    
    debug_messages.append(f"✅ Dataset: {len(df)} righe totali")
    
    # Verifica se esiste una regola
    if target_fond not in regole_abs or source_fond not in regole_abs[target_fond]:
        debug_messages.append(f"❌ Nessuna regola definita da {source_fond} a {target_fond}")
        regole_disponibili = []
        for target, sources in regole_abs.items():
            for source in sources.keys():
                regole_disponibili.append(f"{source} → {target}")
        debug_messages.append(f"Regole disponibili: {', '.join(regole_disponibili)}")
        return go.Figure(), html.Div([html.P(msg) for msg in debug_messages])
    
    # Filtra i dataframe
    df1 = df[df['Foundamental'] == source_fond].copy()
    df2 = df[df['Foundamental'] == target_fond].copy()
    
    debug_messages.append(f"📊 {source_fond}: {len(df1)} righe, {target_fond}: {len(df2)} righe")
    
    if len(df1) == 0 or len(df2) == 0:
        debug_messages.append("❌ Uno dei fondamentali non ha dati")
        return go.Figure(), html.Div([html.P(msg) for msg in debug_messages])
    
    # Verifica che le caratteristiche esistano
    if source_char not in df1.columns:
        debug_messages.append(f"❌ Caratteristica '{source_char}' non trovata in {source_fond}")
        debug_messages.append(f"Colonne disponibili: {', '.join(df1.columns)}")
        return go.Figure(), html.Div([html.P(msg) for msg in debug_messages])
    
    if target_char not in df2.columns:
        debug_messages.append(f"❌ Caratteristica '{target_char}' non trovata in {target_fond}")
        debug_messages.append(f"Colonne disponibili: {', '.join(df2.columns)}")
        return go.Figure(), html.Div([html.P(msg) for msg in debug_messages])
    
    # Crea i collegamenti
    join_pairs = []
    matches_found = 0
    
    # Prendi un campione per velocità (primi 2000 record)
    df1_sample = df1.head(2000)
    debug_messages.append(f"🔍 Analizzando {len(df1_sample)} record di {source_fond}")
    
    for idx, row in df1_sample.iterrows():
        prev_abs = row['AbsNumeroPossesso']
        game_id = row['GameID']
        team_id = row['whichTeamID']
        
        try:
            expected_abs = regole_abs[target_fond][source_fond](prev_abs)
        except Exception as e:
            continue
        
        # Trova i match
        matches = df2[
            (df2['GameID'] == game_id) &
            (df2['AbsNumeroPossesso'] == expected_abs) &
            (df2['whichTeamID'] == team_id)
        ]
        
        if not matches.empty:
            matches_found += len(matches)
            match = matches.iloc[0]
            val1 = row.get(source_char)
            val2 = match.get(target_char)
            
            if pd.notna(val1) and pd.notna(val2) and str(val1) != '' and str(val2) != '':
                join_pairs.append((str(val1), str(val2)))
    
    debug_messages.append(f"🔗 Trovati {matches_found} match, creati {len(join_pairs)} collegamenti")
    
    if not join_pairs:
        debug_messages.append("❌ Nessun collegamento valido trovato")
        return go.Figure(), html.Div([html.P(msg) for msg in debug_messages])
    
    # Crea il dataframe dei collegamenti
    df_pairs = pd.DataFrame(join_pairs, columns=['source', 'target'])
    df_links = df_pairs.groupby(['source', 'target']).size().reset_index(name='value')
    
    debug_messages.append(f"📊 Collegamenti unici: {len(df_links)}")
    debug_messages.append(f"📊 Valori source: {', '.join(df_links['source'].unique()[:10])}")
    debug_messages.append(f"📊 Valori target: {', '.join(df_links['target'].unique()[:10])}")
    
    # Calcola i conteggi per ogni nodo
    source_counts = df_links.groupby('source')['value'].sum()
    target_counts = df_links.groupby('target')['value'].sum()
    
    # Crea le etichette con i conteggi
    source_labels = [f"{source_fond}: {label}\n({source_counts[label]} obs)" for label in df_links['source'].unique()]
    target_labels = [f"{target_fond}: {label}\n({target_counts[label]} obs)" for label in df_links['target'].unique()]
    all_labels = source_labels + target_labels
    
    # Crea hover text dettagliato per i nodi
    source_hovertexts = [f"{source_fond}: {label}<br>Totale osservazioni: {source_counts[label]}<br>Percentuale: {source_counts[label]/source_counts.sum()*100:.1f}%" 
                        for label in df_links['source'].unique()]
    target_hovertexts = [f"{target_fond}: {label}<br>Totale osservazioni: {target_counts[label]}<br>Percentuale: {target_counts[label]/target_counts.sum()*100:.1f}%" 
                        for label in df_links['target'].unique()]
    all_hovertexts = source_hovertexts + target_hovertexts
    
    # Mappa i valori agli indici
    source_map = {val: idx for idx, val in enumerate(df_links['source'].unique())}
    target_map = {val: idx + len(source_labels) for idx, val in enumerate(df_links['target'].unique())}
    
    # Crea hover text per i link con percentuali
    total_links = df_links['value'].sum()
    link_hovertexts = [f"{row['source']} → {row['target']}<br>Osservazioni: {row['value']}<br>Percentuale: {row['value']/total_links*100:.1f}%" 
                      for _, row in df_links.iterrows()]
    
    # Aggiungi statistiche al debug
    debug_messages.append(f"📈 Totale osservazioni nei collegamenti: {total_links}")
    debug_messages.append(f"📈 Media osservazioni per collegamento: {total_links/len(df_links):.1f}")
    debug_messages.append(f"📈 Collegamento più frequente: {df_links.loc[df_links['value'].idxmax(), 'source']} → {df_links.loc[df_links['value'].idxmax(), 'target']} ({df_links['value'].max()} obs)")
    
    # Crea il diagramma Sankey con hover personalizzato
    fig = go.Figure(data=[go.Sankey(
        node=dict(
            label=all_labels,
            pad=15,
            thickness=20,
            line=dict(color="black", width=0.5),
            color=["lightblue"] * len(source_labels) + ["lightcoral"] * len(target_labels),
            hovertemplate='%{customdata}<extra></extra>',
            customdata=all_hovertexts
        ),
        link=dict(
            source=[source_map[s] for s in df_links['source']],
            target=[target_map[t] for t in df_links['target']],
            value=df_links['value'].tolist(),
            hovertemplate='%{customdata}<extra></extra>',
            customdata=link_hovertexts
        )
    )])
    
    fig.update_layout(
        title_text=f"{source_fond} ({source_char}) → {target_fond} ({target_char})<br>Totale osservazioni: {total_links}",
        font_size=12,
        height=650
    )
    
    debug_messages.append("✅ Diagramma creato con successo!")
    
    return fig, html.Div([html.P(msg) for msg in debug_messages])
