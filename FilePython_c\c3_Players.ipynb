{"cells": [{"cell_type": "markdown", "metadata": {"id": "FV-xpyOjhqWO"}, "source": ["In questo notebook ottengo un dataframe con tutte le squadre e i loro rispettivi ID"]}, {"cell_type": "code", "execution_count": 155, "metadata": {}, "outputs": [], "source": ["import numpy as np \n", "import pandas as pd  #Realizzato con la versione di Pandas 2.2.3\n", "import polars as pl\n", "import os\n", "import psycopg\n", "from sqlalchemy import create_engine, text"]}, {"cell_type": "code", "execution_count": 156, "metadata": {}, "outputs": [], "source": ["conn = psycopg.connect(\n", "    dbname=\"db_modena\",           # database creato in pgAdmin4\n", "    user=\"postgres\",              # Il tuo nome utente PostgreSQL\n", "    password=\"AcquaLevissima1\",   # La password che hai scelto per 'postgres'\n", "    host=\"localhost\",             # 'localhost' se è sul tuo PC\n", "    port=5432                     # La porta predefinita è 5432\n", ")\n", "\n", "# Crea un cursore per eseguire le query\n", "cur = conn.cursor()\n", "\n", "#creo un engine per poter usare pandas\n", "engine = create_engine('postgresql+psycopg://postgres:AcquaLevissima1@localhost:5432/db_modena')"]}, {"cell_type": "code", "execution_count": 157, "metadata": {}, "outputs": [], "source": ["pd.set_option('display.max_columns', None)  # Mostra tutte le colonne"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In players_each_game, controlla che non ci siano giocatori con stesso GameID e stesso originalPlayerID, che significa che il giocatore è inserito più volte nel game."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Crea VIEW_players_updated, e poi creaci una tabella sopra, aggiungendo<PERSON> le colonne che estrarrai dal VolleyBox"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON><PERSON> la VIEW Players"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Questa VIEW contiene tutte le righe di players_each_game in base a PlayerID, Nome e Cognome"]}, {"cell_type": "code", "execution_count": 158, "metadata": {}, "outputs": [], "source": ["cur.execute(\"\"\"\n", "CREATE OR REPLACE VIEW players AS\n", "SELECT DISTINCT\n", "  \"PlayerID\",\n", "  \"Nome\",\n", "  \"Cognome\"\n", "FROM\n", "  players_each_game \n", "\"\"\")\n", "conn.commit()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Faccio trim e lower delle colonne Nome e Cognome in players_each_game (al massimo metto l'iniziale maiusco)"]}, {"cell_type": "code", "execution_count": 159, "metadata": {}, "outputs": [], "source": ["cur.execute(\"\"\"\n", "UPDATE players_each_game\n", "SET \n", "  \"Nome\" = UPPER(LEFT(TRIM(\"Nome\"), 1)) || LOWER(SUBSTRING(TRIM(\"Nome\") FROM 2)),\n", "  \"Cognome\" = UPPER(LEFT(TRIM(\"Cognome\"), 1)) || LOWER(SUBSTRING(TRIM(\"Cognome\") FROM 2))\n", "\"\"\")\n", "\n", "conn.commit()"]}, {"cell_type": "code", "execution_count": 160, "metadata": {}, "outputs": [], "source": ["# Dizionario con le sostituzioni\n", "#La parola da sostituire la devi copiare e incollare mettendo prima \"Impostazione codifica -> UTF-8\"\n", "\n", "nomi_da_sostituire = {\n", "  \"Andra©\": \"<PERSON><PERSON><PERSON>\",\n", "  \"Andra© ryuma oto\": \"Andr<PERSON> ryuma oto\",\n", "  \"Bertuð\": \"<PERSON><PERSON>\",\n", "  \"Fla¡vio antonio santos\": \"Flavio antonio santos\",\n", "  \"Flavio cesar\": \"Flavio\",\n", "  \"Ulaþ\": '<PERSON><PERSON>',\n", "  \"Primoï¿½\": 'Primoz',\n", "  \"Primo\": \"Primoz\",\n", "  \"£omacz\": '<PERSON><PERSON><PERSON>',\n", "  \"Bart³omiej\": '<PERSON><PERSON><PERSON><PERSON>',\n", "  \"Jêdrzej\": '<PERSON><PERSON><PERSON>',\n", "  \"Mitiæ\": 'Mi<PERSON>',\n", "  \"Gruszczyñski\": \"Grus<PERSON><PERSON>ynski\",\n", "  \"Musia³\": 'Musial',\n", "  \"Nicolï¿½\": '<PERSON><PERSON><PERSON>',\n", "  \"Niklas aleski\": \"<PERSON>las\",\n", "  \"<PERSON><PERSON>\": '<PERSON><PERSON><PERSON><PERSON>',\n", "  \"<PERSON><PERSON>vic\": '<PERSON><PERSON><PERSON><PERSON>',\n", "  \"Mattï¿½o\": '<PERSON><PERSON><PERSON>',\n", "  \"Thï¿½o\": 'Théo',\n", "  \"Ramï¿½n ferragut\": \"Ramón ferragut\",\n", "  \"<PERSON><PERSON>ï¿½lï¿½my\": \"Barthélémy\",\n", "  \"Jordan matthew\": \"Jordan\",\n", "  \"Jordan ralph\": \"Jordan\",\n", "  \"György\": \"<PERSON>yo<PERSON>\",\n", "  \"Gyï¿½rgy\": \"Gyorgy\",\n", "  \"Giovannimaria\": \"Giovanni maria\",\n", "  \"K³os\": \"<PERSON><PERSON>\",\n", "  \"Bertuð\": \"<PERSON><PERSON>\",\n", "  \"Jedrez\": \"<PERSON><PERSON><PERSON>\",\n", "  \"Jêdrzej\": \"<PERSON><PERSON><PERSON>\",\n", "  \"jedrez\": \"<PERSON><PERSON><PERSON>\",\n", "  \"Josè miguel\": \"Jose miguel\",\n", "  \"<PERSON><PERSON><PERSON><PERSON><PERSON>\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n", "  \"SaO\": \"Saso\",\n", "  \"Sao\": \"Saso\",\n", "  \"SaÂ�o\": \"Saso\",\n", "  \"Mas³Owski\": \"<PERSON><PERSON><PERSON>\",\n", "  \"Mas³owski\": \"<PERSON><PERSON><PERSON>\",\n", "  \"Mas<PERSON><PERSON>owski\": \"<PERSON><PERSON><PERSON>\",\n", "  \"Lawrence Vidal\": \"<PERSON>\",\n", "  \"Micha³\": \"<PERSON><PERSON>\",\n", "  \"Kï¿½K\": \"Kok\",\n", "  \"KÃ¯Â¿Â½K\": \"Kok\",\n", "  \"KÃ¯Â¿Â½k\": \"Kok\",\n", "  \"KĂ¯Â¿Â½k\": \"Kok\",\n", "  \"Kï¿½K\": \"Kok\",\n", "  \"Kï¿½k\": \"Kok\",\n", "  \"Pawe³\": \"<PERSON>we<PERSON>\",\n", "  \"Pawe³\": \"<PERSON>we<PERSON>\",\n", "  \"Mousse'\": \"<PERSON><PERSON><PERSON>\",\n", "  \"Uro\": \"Uros\",\n", "  \"Väinö\": \"<PERSON>ain<PERSON>\",\n", "  \"iga\": \"Ziga\"\n", "}\n", "\n", "\n", "cognomi_da_sostituire = {\n", "  \"Domaga³a\": \"Domagala\",\n", "  \"Ulaþ\": '<PERSON><PERSON>',\n", "  \"Ï¿½en\": 'Sen',\n", "  \"I¿½en\": \"<PERSON>\",\n", "  \"en\": 'Sen',\n", "  \"ÂŠen\": \"Sen\",\n", "  \"ÂŠen\": \"Sen\",\n", "  \"Ã¯Â¿Â½en\": \"Sen\",\n", "  \"Ă¯Â¿Â½en\": \"Sen\",\n", "  \"£omacz\": '<PERSON><PERSON><PERSON>',\n", "  \"Bart³omiej\": '<PERSON><PERSON><PERSON><PERSON>',\n", "  \"Mitiæ\": 'Mi<PERSON>',\n", "  \"G¹sior\": \"Gasior\",\n", "  \"Gruszczyñski\": \"Grus<PERSON><PERSON>ynski\",\n", "  \"Musia³\": 'Musial',\n", "  \"<PERSON><PERSON>\": '<PERSON><PERSON><PERSON><PERSON>',\n", "  \"<PERSON><PERSON>vic\": '<PERSON><PERSON><PERSON><PERSON>',\n", "  \"<PERSON><PERSON>¿½hrs\": 'Rohrs',\n", "  \"Röhrs\": '<PERSON>oh<PERSON>',\n", "  \"Moic\": '<PERSON><PERSON><PERSON>',\n", "  \"<PERSON><PERSON>\": '<PERSON><PERSON><PERSON>',\n", "  \"<PERSON><PERSON><PERSON>\": 'Visic',\n", "  \"Viic\": 'Visic',\n", "  \"Romanï¿½\": \"Romanò\",\n", "  \"Romano'\": \"Romanò\",\n", "  \"Ramï¿½n ferragut\": \"Ramón ferragut\",\n", "  \"<PERSON><PERSON>ï¿½lï¿½my\": \"Barthélémy\",\n", "  \"Jordan matthew\": \"Jordan\",\n", "  \"Solï¿½\": \"Solé\",\n", "  \"György\": \"<PERSON>yo<PERSON>\",\n", "  \"Gyï¿½rgy\": \"Gyorgy\",\n", "  \"Podraï¿½canin\": \"Podrascanin\",\n", "  \"PodraÂšcanin\": \"Podrascanin\",\n", "  \"PodraÃ¯Â¿Â½canin\": \"Podrascanin\",\n", "  \"Podracanin\": \"Podrascanin\",\n", "  \"Hossein khanzadeh firouzjah\": \"<PERSON><PERSON><PERSON> khanzadeh\",\n", "  \"Hossein khanzadeh fi\": \"<PERSON><PERSON><PERSON> khanzadeh\",\n", "  \"K³os\": \"<PERSON><PERSON>\",\n", "  \"Ndes\": \"Ondes\",\n", "  \"öndeþ\": \"Ondes\",\n", "  \"öndes\": \"Ondes\",\n", "  \"Öndeþ\": \"Ondes\",\n", "  \"Öndes\": \"Ondes\",\n", "  \"ÖndeÞ\": \"Ondes\",\n", "  \"Ã–ndes\": \"Ondes\",\n", "  \"Ã¶ndeþ\": \"Ondes\",\n", "  \"Ă–NDES\": \"Ondes\",\n", "  \"Ă–ndeĂ�\": \"Ondes\",\n", "  \"ÃÂ¿Â½Ket\": \"Sket\",\n", "  \"ÃÂ¿Â½ket\": \"Sket\",\n", "  \"Ă¯Â¿Â½ket\": \"Sket\",\n", "  \"Ã¯Â¿Â½ket\": \"Sket\",\n", "  \"Ï¿½Ket\": \"Sket\",\n", "  \"Ï¿½ket\": \"Sket\",\n", "  \"ï¿½ket \": \"Sket\",\n", "  \"Ã¶ket\": \"Sket\",\n", "  \"ket\": \"Sket\",\n", "  \"�ket\": \"Sket\",\n", "  \"Kamierczak\": \"Ka<PERSON><PERSON><PERSON><PERSON>\",\n", "  \"<PERSON><PERSON><PERSON><PERSON><PERSON>\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n", "  \"Talekar\": \"Stalekar\",\n", "  \"Â�talekar\": \"<PERSON>ale<PERSON>\",\n", "  \"Mas³Owski\": \"<PERSON><PERSON><PERSON>\",\n", "  \"Mas³owski\": \"<PERSON><PERSON><PERSON>\",\n", "  \"Mas<PERSON><PERSON>owski\": \"<PERSON><PERSON><PERSON>\",\n", "  \"Boï¿½njak\": \"Bosnjak\",\n", "  \"BoNjak\": \"Bosnjak\",\n", "  \"BoÂŠNjak\": \"Bosnjak\",\n", "  \"Bonjak\": \"Bosnjak\",\n", "  \"BoNjak\": \"Bosnjak\",\n", "  \"BONJAK\": \"Bosnjak\",\n", "  \"BoÃ¯Â¿Â½njak\": \"Bosnjak\",  #questo e quello sotto sono diversi, cambia una A. Quindi fai la stessa cosa con altri se vedi che non si cambiano\n", "  \"BoĂ¯Â¿Â½Njak\": \"Bosnjak\",\n", "  \"BoÃ¯Â¿Â½njak\": \"Bosnjak\",\n", "  \"BoÃ¯Â¿Â½njak\": \"Bosnjak\",\n", "  \"BoÃ¯Â¿Â½njak\": \"Bosnjak\",\n", "  \"BoÃ¯Â¿Â½njak\": \"Bosnjak\",\n", "  \"G¹Sior\": \"Gasior\",\n", "  \"GÂ¹sior\": \"Gasior\",\n", "  \"ÂŠtalekar\": \"<PERSON><PERSON><PERSON>\",\n", "  \"Âštalekar\": \"Stale<PERSON>\",\n", "  \"Â�talekar\": \"<PERSON>ale<PERSON>\",\n", "  \"talekar\": \"<PERSON>ale<PERSON>\",\n", "  \"Ï¿½estan\": \"Sestan\",\n", "  \"ï¿½estan\": \"Sestan\",\n", "  \"Ă¯Â¿Â½estan\": \"Sestan\",\n", "  \"Ã¯Â¿Â½estan\": \"Sestan\",\n", "  \"en\": \"Sen\",\n", "  \"ÂŠEn\": \"Sen\",\n", "  \"Âšen\": \"Sen\",\n", "  \"Ă¯Â¿Â½en\": \"Sen\",\n", "  \"tern\": \"Stern\",\n", "  \"Lawrence vidal\": \"<PERSON>\",\n", "  \"Kï¿½k\": \"Kok\",\n", "  \"KÃ¯Â¿Â½k\": \"Kok\",\n", "  \"KĂ¯Â¿Â½k\": \"Kok\",\n", "  \"Kï¿½k\": \"Kok\",\n", "  \"Kujundic\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n", "  \"Mousse'\": \"<PERSON><PERSON><PERSON>\",\n", "  \"Jordan ralph\": \"Jordan\",\n", "  \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\"\n", "}\n"]}, {"cell_type": "code", "execution_count": 161, "metadata": {}, "outputs": [], "source": ["# Crea e esegui le query di aggiornamento usando REPLACE()\n", "'''\n", "for old_name, new_name in nomi_da_sostituire.items():\n", "    query = f\"\"\"\n", "    UPDATE players_each_game \n", "    SET {\"Nome\"} = REPLACE({\"Nome\"}, \"{old_name}\", \"{new_name}\");  --REPLACE sostituisce solo una parte della stringa, ovvero se una stringa contiene old_name, sostituisce solo old_name con new_name\n", "    \"\"\"\n", "    cur.execute(query)\n", "'''\n", "\n", "for old_name, new_name in nomi_da_sostituire.items():\n", "    query = \"\"\"\n", "    UPDATE players_each_game \n", "    SET \"Nome\" = %s \n", "    WHERE \"Nome\" = %s;\n", "    \"\"\"\n", "    cur.execute(query, (new_name, old_name))  #%s sono segnaposto per parametri, a cui assegno un valore qui. Con (new_name, old_name) sto dicendo che il primo segnaposto assume valore new_name, e il secondo diventa old_name\n", "\n", "for old_surname, new_surname in cognomi_da_sostituire.items():\n", "    query = \"\"\"\n", "    UPDATE players_each_game \n", "    SET \"Cognome\" = %s \n", "    WHERE \"Cognome\" = %s;\n", "    \"\"\"\n", "    cur.execute(query, (new_surname, old_surname))\n", "\n", "\n", "# Commit delle modifiche e chiusura della connessione\n", "conn.commit()"]}, {"cell_type": "code", "execution_count": 162, "metadata": {}, "outputs": [], "source": ["#Sostituisco tutti i caratteri strani con caratteri più comuni\n", "\n", "sostituzioni = {\n", "    'ä': 'a', 'Ä': 'A','ã': 'a', 'Ã': 'A',\n", "    'ë': 'e', 'Ë': 'E',\n", "    'ï': 'i', 'Ï': 'I', 'ı': 'i', 'İ': 'I',\n", "    'ö': 'o', 'Ö': 'O','õ': 'o', 'Õ': 'O',\n", "    'ü': 'u', 'Ü': 'U',\n", "    'ł': 'l', 'Ł': 'L', '£': 'L',\n", "    'ž': 'z', 'Ž': 'Z',\n", "    'đ': 'd', 'Đ': 'D',\n", "    'č': 'c', 'Č': 'C', 'ç': 'c', 'Ç': 'C', 'ć': 'c', 'Ć': 'C',\n", "    'ž': 'z', 'Ž': 'Z',\n", "    'ñ': 'n', 'Ñ': 'N',\n", "    'ğ': 'g', 'Ğ': 'G',\n", "    'ş': 's', 'Ş': 'S', 'š': 's', 'Š': 'S',\n", "}\n", "#Non puoi sostituire ³ con l perchè non è sempre così\n", "\n", "\n", "for char_da_sostituire, char_sostituto in sostituzioni.items():\n", "    query = f\"\"\"\n", "    UPDATE players_each_game\n", "    SET \"Nome\" = REPLACE(\"Nome\", '{char_da_sostituire}', '{char_sostituto}')\n", "    WHERE \"Nome\" LIKE '%{char_da_sostituire}%';\n", "    \"\"\"\n", "    cur.execute(query)\n", "    \n", "    query = f\"\"\"\n", "    UPDATE players_each_game\n", "    SET \"Cognome\" = REPLACE(\"Cognome\", '{char_da_sostituire}', '{char_sostituto}')\n", "    WHERE \"Cognome\" LIKE '%{char_da_sostituire}%';\n", "    \"\"\"\n", "    cur.execute(query)\n", "conn.commit()\n"]}, {"cell_type": "code", "execution_count": 163, "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\ncur.execute(\"\"\"\\nUPDATE players_each_game\\nSET \"Nome\" = REPLACE(\"Nome\", \\'\\x9a\\', \\'s\\')\\nWHERE \"Nome\" LIKE \\'%\\x9a%\\';\\n\"\"\")    \\n'"]}, "execution_count": 163, "metadata": {}, "output_type": "execute_result"}], "source": ["#Sostituisco il carattere \\x9a, che è la š codificata male, con la lettera s\n", "'''\n", "cur.execute(\"\"\"\n", "UPDATE players_each_game\n", "SET \"Nome\" = REPLACE(\"Nome\", '\\x9a', 's')\n", "WHERE \"Nome\" LIKE '%\\x9a%';\n", "\"\"\")    \n", "'''\n", "#Non funziona"]}, {"cell_type": "code", "execution_count": 164, "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\ncur.execute(\"\"\"\\nSELECT \\n  \"Nome\",\\n  encode(\"Nome\"::bytea, \\'escape\\') AS \"Nome_escape\",\\n  encode(\"Nome\"::bytea, \\'hex\\') AS \"Nome_hex\"\\nFROM players_each_game\\nWHERE \"Nome\" LIKE \\'_iga%\\';\\n\"\"\")\\nresults = cur.fetchall()\\nprint(results)\\n\\ncur.execute(\"\"\"\\nSELECT \\n  \"Nome\",\\n  encode(\"Nome\"::bytea, \\'escape\\') AS \"Nome_escape\",\\n  encode(\"Nome\"::bytea, \\'hex\\') AS \"Nome_hex\"\\nFROM players_each_game\\nWHERE \"Nome\" LIKE \\'Uro%\\' AND \"originalPlayerID\" = \\'KOV-URO-93\\';\\n\"\"\")\\nresults = cur.fetchall()\\nprint(results)\\n\\ncur.execute(\"\"\"\\nSELECT \\n  \"Nome\",\\n  encode(\"Nome\"::bytea, \\'escape\\') AS \"Nome_escape\",\\n  encode(\"Nome\"::bytea, \\'hex\\') AS \"Nome_hex\"\\nFROM players_each_game\\nWHERE \"Nome\" LIKE \\'Pawe%\\' AND \"Cognome\" = \\'Rusin\\';\\n\"\"\")\\nresults = cur.fetchall()\\nprint(results)\\n\\ncur.execute(\"\"\"\\nSELECT \\n  \"Nome\",\\n  encode(\"Nome\"::bytea, \\'escape\\') AS \"Nome_escape\",\\n  encode(\"Nome\"::bytea, \\'hex\\') AS \"Nome_hex\"\\nFROM players_each_game\\nWHERE \"Nome\" LIKE \\'Pawe%\\' AND \"Cognome\" = \\'Pietraszko\\';\\n\"\"\")\\nresults = cur.fetchall()\\nprint(results)\\n\\ncur.execute(\"\"\"\\nSELECT \\n  \"Nome\",\\n  encode(\"Nome\"::bytea, \\'escape\\') AS \"Nome_escape\",\\n  encode(\"Nome\"::bytea, \\'hex\\') AS \"Nome_hex\"\\nFROM players_each_game\\nWHERE \"Nome\" LIKE \\'Pawe%\\' AND \"Cognome\" = \\'Woicki\\';\\n\"\"\")\\nresults = cur.fetchall()\\nprint(results)\\n'"]}, "execution_count": 164, "metadata": {}, "output_type": "execute_result"}], "source": ["#Troviamo il codice dei caratteri codificati male\n", "'''\n", "cur.execute(\"\"\"\n", "SELECT \n", "  \"Nome\",\n", "  encode(\"Nome\"::bytea, 'escape') AS \"Nome_escape\",\n", "  encode(\"Nome\"::bytea, 'hex') AS \"Nome_hex\"\n", "FROM players_each_game\n", "WHERE \"Nome\" LIKE '_iga%';\n", "\"\"\")\n", "results = cur.fetchall()\n", "print(results)\n", "\n", "cur.execute(\"\"\"\n", "SELECT \n", "  \"Nome\",\n", "  encode(\"Nome\"::bytea, 'escape') AS \"Nome_escape\",\n", "  encode(\"Nome\"::bytea, 'hex') AS \"Nome_hex\"\n", "FROM players_each_game\n", "WHERE \"Nome\" LIKE 'Uro%' AND \"originalPlayerID\" = 'KOV-URO-93';\n", "\"\"\")\n", "results = cur.fetchall()\n", "print(results)\n", "\n", "cur.execute(\"\"\"\n", "SELECT \n", "  \"Nome\",\n", "  encode(\"Nome\"::bytea, 'escape') AS \"Nome_escape\",\n", "  encode(\"Nome\"::bytea, 'hex') AS \"Nome_hex\"\n", "FROM players_each_game\n", "WHERE \"Nome\" LIKE 'Pawe%' AND \"Cognome\" = 'Rusin';\n", "\"\"\")\n", "results = cur.fetchall()\n", "print(results)\n", "\n", "cur.execute(\"\"\"\n", "SELECT \n", "  \"Nome\",\n", "  encode(\"Nome\"::bytea, 'escape') AS \"Nome_escape\",\n", "  encode(\"Nome\"::bytea, 'hex') AS \"Nome_hex\"\n", "FROM players_each_game\n", "WHERE \"Nome\" LIKE 'Pawe%' AND \"Cognome\" = 'Pietraszko';\n", "\"\"\")\n", "results = cur.fetchall()\n", "print(results)\n", "\n", "cur.execute(\"\"\"\n", "SELECT \n", "  \"Nome\",\n", "  encode(\"Nome\"::bytea, 'escape') AS \"Nome_escape\",\n", "  encode(\"Nome\"::bytea, 'hex') AS \"Nome_hex\"\n", "FROM players_each_game\n", "WHERE \"Nome\" LIKE 'Pawe%' AND \"Cognome\" = 'Woicki';\n", "\"\"\")\n", "results = cur.fetchall()\n", "print(results)\n", "'''"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Fai l'unificazione automatica degli ID. Se qualcosa va male, reimposti tutti i PlayerID = ID_auto e rifai da capo l'unificazione."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Temporaneamente, per fare l'unificazione, aggiungi le colonne HomeAway, Year e Annata a players_each_game"]}, {"cell_type": "code", "execution_count": 165, "metadata": {}, "outputs": [], "source": ["conn.rollback()"]}, {"cell_type": "code", "execution_count": 166, "metadata": {}, "outputs": [{"data": {"text/plain": ["<psycopg.Cursor [TUPLES_OK] [INTRANS] (host=localhost user=postgres database=db_modena) at 0x20e2ea77110>"]}, "execution_count": 166, "metadata": {}, "output_type": "execute_result"}], "source": ["#Libero lock/transazioni\n", "cur.execute(\"\"\"\n", "SELECT pg_terminate_backend(pid)\n", "FROM pg_stat_activity\n", "WHERE query ILIKE '%players_each_game%'\n", "  AND state = 'idle in transaction (aborted)';\n", "\"\"\")"]}, {"cell_type": "code", "execution_count": 167, "metadata": {}, "outputs": [], "source": ["#Aggiungo temporaneamente le colonne HomeAway, Year, <PERSON><PERSON>, TeamID, RuoloCalc\n", "conn.rollback()\n", "\n", "cur.execute(\"\"\"\n", "ALTER TABLE players_each_game\n", "DROP COLUMN IF EXISTS \"HomeAway\",\n", "DROP COLUMN IF EXISTS \"TeamID\",\n", "DROP COLUMN IF EXISTS \"Year\",\n", "DROP COLUMN IF EXISTS \"Annata\",\n", "DROP COLUMN IF EXISTS \"RuoloCalc\";\n", "\"\"\")\n", "conn.commit()\n", "\n", "cur.execute(\"\"\"\n", "ALTER TABLE players_each_game\n", "ADD COLUMN IF NOT EXISTS \"HomeAway\" bool,\n", "ADD COLUMN IF NOT EXISTS \"TeamID\" integer,\n", "ADD COLUMN IF NOT EXISTS \"Year\" smallint,\n", "ADD COLUMN IF NOT EXISTS \"Annata\" smallint,\n", "ADD COLUMN IF NOT EXISTS \"RuoloCalc\" smallint;\n", "\"\"\")\n", "conn.commit()\n", "\n", "\n", "cur.execute(\"\"\"\n", "UPDATE players_each_game\n", "SET \n", "    \"HomeAway\" = t.\"HomeAway\",\n", "    \"TeamID\" = t.\"TeamID\"\n", "FROM teams_each_game t\n", "WHERE players_each_game.\"GameID\" = t.\"GameID\" and players_each_game.\"TeamID_auto\" = t.\"TeamID_auto\";\n", "\"\"\")\n", "\n", "\n", "cur.execute(\"\"\"\n", "UPDATE players_each_game\n", "SET \n", "    \"Year\" = EXTRACT(YEAR FROM g.\"DateSQL\"),\n", "    \"Annata\" = g.\"<PERSON><PERSON>\"\n", "FROM games g\n", "WHERE players_each_game.\"GameID\" = g.\"GameID\";\n", "\"\"\")\n", "\n", "\n", "cur.execute(\"\"\"\n", "UPDATE players_each_game\n", "SET \n", "    \"RuoloCalc\" = pegv.\"RuoloCalc\"\n", "FROM players_each_game_view pegv\n", "WHERE players_each_game.\"GameID\" = pegv.\"GameID\" AND players_each_game.\"PlayerID_auto\" = pegv.\"PlayerID_auto\";\n", "\"\"\")\n", "conn.commit()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 168, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Giocatori con caratteri non ASCII (escluse vocali accentate comuni) trovati:\n"]}], "source": ["conn.rollback()\n", "\n", "#Se<PERSON>ziono tutte le righe di players_each_game che in \"Nome\" o in \"Cognome\" contengono un carattere non ASCII\n", "cur.execute(\"\"\"\n", "SELECT DISTINCT\n", "    \"PlayerID\",\n", "    \"Nome\",\n", "    \"Cognome\",\n", "    \"TeamID\",\n", "    \"NumeroMaglia\",\n", "    \"Annata\"\n", "FROM players_each_game_view\n", "WHERE (\"Nome\" ~ '[^[:ascii:]]'\n", "  AND \"Nome\" !~ '[àáèéìíòóùúÀÁÈÉÌÍÒÓÙÚ]')  -- esclude le vocali con accenti comuni (maiuscole e minuscole)\n", "  OR (\"Cognome\" ~ '[^[:ascii:]]'\n", "  AND \"Cognome\" !~ '[àáèéìíòóùúÀÁÈÉÌÍÒÓÙÚ]')  -- esclude le vocali con accenti comuni (maiuscole e minuscole)\n", "ORDER BY \"Nome\", \"Cognome\";\n", "\"\"\")\n", "\n", "results = cur.fetchall()\n", "print(\"\\nGiocatori con caratteri non ASCII (escluse vocali accentate comuni) trovati:\")\n", "for r in results:\n", "    print(f\"\\nPlayerID: {r[0]}\")\n", "    print(f\"Nome: {r[1]}\")\n", "    print(f\"Cognome: {r[2]}\")\n", "    print(f\"TeamID: {r[3]}\")\n", "    print(f\"NumeroMaglia: {r[4]}\")\n", "    print(f\"Annata: {r[5]}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Per aggiustare automaticamente alcuni nomi e cognomi:\n", "\n", "Raggruppa per originalPlayerID, Nome, TeamID, NumeroMaglia, Anna<PERSON>\n", "- E stampa i gruppi che contengono due cognomi diversi. Oppure in modo automatico se uno dei due è compreso nell'altro, allora imposta il più frequente. Oppure imposta direttamente il più frequente.\n", "\n", "Reggruppa per originalPlayerID, Cognome, TeamID, NumeroMaglia, Annata\n", "- Stessa cosa"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Ora unisco i giocatori che hanno stesso:\n", "\n", "Nome, Cognome\n", "1) <PERSON><PERSON>, <PERSON><PERSON><PERSON>, (Year+-1)\n", "2) Team<PERSON>, R<PERSON>loCalc, (Year+-1)\n", "3) TeamID, NumeroMaglia, (Year+-1)\n", "4) <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, (Year+-1)\n", "5) originalPlayer<PERSON>, RuoloCalc, (Year+-1)\n", "6) originalPlayerID, NumeroMaglia, (Year+-1)\n", "7) originalPlayerID, TeamID, (Year+-1)\n", "\n", "Metto Year+-1 siccome a volte il giocatore va a giocare all'estero per qualche anno, e quindi non appare nei dati, quindi in questo modo lo catturo anche se non ha fatto tutte le stagioni di fila.\n", "\n", "Poi guarda se quelli con stesso originalPlayerID o stesso altro hanno PlayerID diversi"]}, {"cell_type": "code", "execution_count": 169, "metadata": {}, "outputs": [], "source": ["#<PERSON><PERSON><PERSON> questi update finchè non ci sono più righe modificate alla fine di un ciclo, o dopo 10 cicli\n", "\n", "#Nelle SELECT devo usare delle tabelle, perchè con le VIEW ci mette troppo. Quindi devo avere i dati che mi servono in players_each_game\n", "def unifica_PlayerID_players_each_game():\n", "    righe_modificate = 1\n", "    num_cicli = 0\n", "\n", "    while righe_modificate > 0 and num_cicli < 10:\n", "        righe_modificate = 0\n", "        \n", "        #Aggiorno la vista così i RuoloCalc si aggiornano, e magari faccio nuovi aggiornamenti\n", "        #cur.execute(\"REFRESH MATERIALIZED VIEW players_each_game_view;\")\n", "        conn.commit()\n", "        \n", "\n", "        # 1)\n", "        # Step 1: Recup<PERSON> la mappa OldID -> NewID\n", "        cur.execute(\"\"\"\n", "            WITH gruppi AS (\n", "            SELECT \n", "                LEAST(p1.\"PlayerID\", p2.\"PlayerID\") AS \"PlayerID_min\",\n", "                GREATEST(p1.\"PlayerID\", p2.\"PlayerID\") AS \"PlayerID_max\"\n", "            FROM players_each_game p1\n", "            JOIN players_each_game p2\n", "                ON p1.\"Nome\" = p2.\"Nome\"\n", "            AND p1.\"Cognome\" = p2.\"Cognome\"\n", "            AND p1.\"TeamID\" = p2.\"TeamID\"\n", "            AND p1.\"Ruolo\" = p2.\"Ruolo\"\n", "            AND ABS(p1.\"Year\" - p2.\"Year\") <= 3\n", "            AND p1.\"PlayerID\" <> p2.\"PlayerID\"\n", "            )\n", "            SELECT DISTINCT \"PlayerID_max\", \"PlayerID_min\"\n", "            FROM gruppi\n", "            WHERE \"PlayerID_max\" <> \"PlayerID_min\";\n", "        \"\"\")\n", "        to_update = cur.fetchall()  #to_update è una lista di tuple di due elementi. Il primo elemento è l'ID vecchio, il secondo è l'ID nuovo\n", "        # Step 2: <PERSON>ggio<PERSON> ogni riga\n", "        for old_id, new_id in to_update:     \n", "            cur.execute(\"\"\"\n", "                UPDATE players_each_game\n", "                SET \"PlayerID\" = %s\n", "                WHERE \"PlayerID\" = %s;\n", "            \"\"\", (new_id, old_id))\n", "        righe_modificate += cur.rowcount\n", "        conn.commit()\n", "        \n", "        \n", "        # 2)\n", "        cur.execute(\"\"\"\n", "            WITH gruppi AS (\n", "            SELECT \n", "                LEAST(p1.\"PlayerID\", p2.\"PlayerID\") AS \"PlayerID_min\",\n", "                GREATEST(p1.\"PlayerID\", p2.\"PlayerID\") AS \"PlayerID_max\"\n", "            FROM players_each_game p1\n", "            JOIN players_each_game p2\n", "                ON p1.\"Nome\" = p2.\"Nome\"\n", "            AND p1.\"Cognome\" = p2.\"Cognome\"\n", "            AND p1.\"TeamID\" = p2.\"TeamID\"\n", "            AND p1.\"RuoloCalc\" = p2.\"RuoloCalc\"\n", "            AND ABS(p1.\"Year\" - p2.\"Year\") <= 3\n", "            AND p1.\"PlayerID\" <> p2.\"PlayerID\"\n", "            )\n", "            SELECT DISTINCT \"PlayerID_max\", \"PlayerID_min\"\n", "            FROM gruppi\n", "            WHERE \"PlayerID_max\" <> \"PlayerID_min\";\n", "        \"\"\")\n", "        to_update = cur.fetchall()  #to_update è una lista di tuple di due elementi. Il primo elemento è l'ID vecchio, il secondo è l'ID nuovo\n", "        \n", "        for old_id, new_id in to_update:     \n", "            cur.execute(\"\"\"\n", "                UPDATE players_each_game\n", "                SET \"PlayerID\" = %s\n", "                WHERE \"PlayerID\" = %s;\n", "            \"\"\", (new_id, old_id))\n", "        righe_modificate += cur.rowcount\n", "        conn.commit()\n", "        \n", "\n", "        # 3)\n", "        cur.execute(\"\"\"\n", "            WITH gruppi AS (\n", "            SELECT \n", "                LEAST(p1.\"PlayerID\", p2.\"PlayerID\") AS \"PlayerID_min\",\n", "                GREATEST(p1.\"PlayerID\", p2.\"PlayerID\") AS \"PlayerID_max\"\n", "            FROM players_each_game p1\n", "            JOIN players_each_game p2\n", "                ON p1.\"Nome\" = p2.\"Nome\"\n", "            AND p1.\"Cognome\" = p2.\"Cognome\"\n", "            AND p1.\"TeamID\" = p2.\"TeamID\"\n", "            AND p1.\"NumeroMaglia\" = p2.\"NumeroMaglia\"\n", "            AND ABS(p1.\"Year\" - p2.\"Year\") <= 3\n", "            AND p1.\"PlayerID\" <> p2.\"PlayerID\"\n", "            )\n", "            SELECT DISTINCT \"PlayerID_max\", \"PlayerID_min\"\n", "            FROM gruppi\n", "            WHERE \"PlayerID_max\" <> \"PlayerID_min\";\n", "        \"\"\")\n", "        to_update = cur.fetchall()  #to_update è una lista di tuple di due elementi. Il primo elemento è l'ID vecchio, il secondo è l'ID nuovo\n", "        \n", "        for old_id, new_id in to_update:     \n", "            cur.execute(\"\"\"\n", "                UPDATE players_each_game\n", "                SET \"PlayerID\" = %s\n", "                WHERE \"PlayerID\" = %s;\n", "            \"\"\", (new_id, old_id))\n", "        righe_modificate += cur.rowcount\n", "        conn.commit()\n", "        \n", "        \n", "        # 4)\n", "        cur.execute(\"\"\"\n", "            WITH gruppi AS (\n", "            SELECT \n", "                LEAST(p1.\"PlayerID\", p2.\"PlayerID\") AS \"PlayerID_min\",\n", "                GREATEST(p1.\"PlayerID\", p2.\"PlayerID\") AS \"PlayerID_max\"\n", "            FROM players_each_game p1\n", "            JOIN players_each_game p2\n", "                ON p1.\"Nome\" = p2.\"Nome\"\n", "            AND p1.\"Cognome\" = p2.\"Cognome\"\n", "            AND p1.\"originalPlayerID\" = p2.\"originalPlayerID\"\n", "            AND p1.\"Ruolo\" = p2.\"Ruolo\"\n", "            AND ABS(p1.\"Year\" - p2.\"Year\") <= 3\n", "            AND p1.\"PlayerID\" <> p2.\"PlayerID\"\n", "            )\n", "            SELECT DISTINCT \"PlayerID_max\", \"PlayerID_min\"\n", "            FROM gruppi\n", "            WHERE \"PlayerID_max\" <> \"PlayerID_min\";\n", "        \"\"\")\n", "        to_update = cur.fetchall()  #to_update è una lista di tuple di due elementi. Il primo elemento è l'ID vecchio, il secondo è l'ID nuovo\n", "        # Step 2: <PERSON>ggio<PERSON> ogni riga\n", "        for old_id, new_id in to_update:     \n", "            cur.execute(\"\"\"\n", "                UPDATE players_each_game\n", "                SET \"PlayerID\" = %s\n", "                WHERE \"PlayerID\" = %s;\n", "            \"\"\", (new_id, old_id))\n", "        righe_modificate += cur.rowcount\n", "        conn.commit()\n", "\n", "\n", "        # 5)\n", "        cur.execute(\"\"\"\n", "            WITH gruppi AS (\n", "            SELECT \n", "                LEAST(p1.\"PlayerID\", p2.\"PlayerID\") AS \"PlayerID_min\",\n", "                GREATEST(p1.\"PlayerID\", p2.\"PlayerID\") AS \"PlayerID_max\"\n", "            FROM players_each_game p1\n", "            JOIN players_each_game p2\n", "                ON p1.\"Nome\" = p2.\"Nome\"\n", "            AND p1.\"Cognome\" = p2.\"Cognome\"\n", "            AND p1.\"originalPlayerID\" = p2.\"originalPlayerID\"\n", "            AND p1.\"RuoloCalc\" = p2.\"RuoloCalc\"\n", "            AND ABS(p1.\"Year\" - p2.\"Year\") <= 3\n", "            AND p1.\"PlayerID\" <> p2.\"PlayerID\"\n", "            )\n", "            SELECT DISTINCT \"PlayerID_max\", \"PlayerID_min\"\n", "            FROM gruppi\n", "            WHERE \"PlayerID_max\" <> \"PlayerID_min\";\n", "        \"\"\")\n", "        to_update = cur.fetchall()  #to_update è una lista di tuple di due elementi. Il primo elemento è l'ID vecchio, il secondo è l'ID nuovo\n", "        # Step 2: <PERSON>ggio<PERSON> ogni riga\n", "        for old_id, new_id in to_update:     \n", "            cur.execute(\"\"\"\n", "                UPDATE players_each_game\n", "                SET \"PlayerID\" = %s\n", "                WHERE \"PlayerID\" = %s;\n", "            \"\"\", (new_id, old_id))\n", "        righe_modificate += cur.rowcount\n", "        conn.commit()\n", "\n", "\n", "        # 6)\n", "        cur.execute(\"\"\"\n", "            WITH gruppi AS (\n", "            SELECT \n", "                LEAST(p1.\"PlayerID\", p2.\"PlayerID\") AS \"PlayerID_min\",\n", "                GREATEST(p1.\"PlayerID\", p2.\"PlayerID\") AS \"PlayerID_max\"\n", "            FROM players_each_game p1\n", "            JOIN players_each_game p2\n", "                ON p1.\"Nome\" = p2.\"Nome\"\n", "            AND p1.\"Cognome\" = p2.\"Cognome\"\n", "            AND p1.\"originalPlayerID\" = p2.\"originalPlayerID\"\n", "            AND p1.\"NumeroMaglia\" = p2.\"NumeroMaglia\"\n", "            AND ABS(p1.\"Year\" - p2.\"Year\") <= 3\n", "            AND p1.\"PlayerID\" <> p2.\"PlayerID\"\n", "            )\n", "            SELECT DISTINCT \"PlayerID_max\", \"PlayerID_min\"\n", "            FROM gruppi\n", "            WHERE \"PlayerID_max\" <> \"PlayerID_min\";\n", "        \"\"\")\n", "        to_update = cur.fetchall()  #to_update è una lista di tuple di due elementi. Il primo elemento è l'ID vecchio, il secondo è l'ID nuovo\n", "        # Step 2: <PERSON>ggio<PERSON> ogni riga\n", "        for old_id, new_id in to_update:     \n", "            cur.execute(\"\"\"\n", "                UPDATE players_each_game\n", "                SET \"PlayerID\" = %s\n", "                WHERE \"PlayerID\" = %s;\n", "            \"\"\", (new_id, old_id))\n", "        righe_modificate += cur.rowcount\n", "        conn.commit()\n", "        \n", "        \n", "        # 7)\n", "        cur.execute(\"\"\"\n", "            WITH gruppi AS (\n", "            SELECT \n", "                LEAST(p1.\"PlayerID\", p2.\"PlayerID\") AS \"PlayerID_min\",\n", "                GREATEST(p1.\"PlayerID\", p2.\"PlayerID\") AS \"PlayerID_max\"\n", "            FROM players_each_game p1\n", "            JOIN players_each_game p2\n", "                ON p1.\"Nome\" = p2.\"Nome\"\n", "            AND p1.\"Cognome\" = p2.\"Cognome\"\n", "            AND p1.\"originalPlayerID\" = p2.\"originalPlayerID\"\n", "            AND p1.\"TeamID\" = p2.\"TeamID\"\n", "            AND ABS(p1.\"Year\" - p2.\"Year\") <= 3\n", "            AND p1.\"PlayerID\" <> p2.\"PlayerID\"\n", "            )\n", "            SELECT DISTINCT \"PlayerID_max\", \"PlayerID_min\"\n", "            FROM gruppi\n", "            WHERE \"PlayerID_max\" <> \"PlayerID_min\";\n", "        \"\"\")\n", "        to_update = cur.fetchall()  #to_update è una lista di tuple di due elementi. Il primo elemento è l'ID vecchio, il secondo è l'ID nuovo\n", "        # Step 2: <PERSON>ggio<PERSON> ogni riga\n", "        for old_id, new_id in to_update:     \n", "            cur.execute(\"\"\"\n", "                UPDATE players_each_game\n", "                SET \"PlayerID\" = %s\n", "                WHERE \"PlayerID\" = %s;\n", "            \"\"\", (new_id, old_id))\n", "        righe_modificate += cur.rowcount\n", "        conn.commit()\n", "        \n", "\n", "        print(f\"Ciclo {num_cicli}: {righe_modificate} righe modificate\")\n", "        num_cicli +=1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Riscrivo la funzione in modo più efficente"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def unifica_PlayerID_players_each_game():\n", "    righe_modificate = 1\n", "    num_cicli = 0\n", "    \n", "    while righe_modificate > 0 and num_cicli < 10:  #Eseguo la funzione fino a 10 volte o finchè non ci sono più modifiche nei giocatori\n", "        righe_modificate = 0\n", "\n", "        #I gruppi di giocatori devono avere lo stesso Nome AND Cognome AND Year+-1 AND una di queste condizioni\n", "        condizioni = [  \n", "            ('\"TeamID\"', '\"Ruolo\"'),\n", "            ('\"TeamID\"', '\"RuoloCalc\"'),\n", "            ('\"TeamID\"', '\"NumeroMaglia\"'),\n", "            ('\"originalPlayerID\"', '\"Ruolo\"'),\n", "            ('\"originalPlayerID\"', '\"RuoloCalc\"'),\n", "            ('\"originalPlayerID\"', '\"NumeroMaglia\"'),\n", "            ('\"originalPlayerID\"', '\"TeamID\"'),\n", "        ]\n", "\n", "        # 3. <PERSON><PERSON> tempo<PERSON> con le mappature\n", "        cur.execute(\"DROP TABLE IF EXISTS tmp_playerid_map;\")\n", "        cur.execute(\"\"\"\n", "        CREATE TEMP TABLE tmp_playerid_map (\n", "            \"OldID\" INTEGER,\n", "            \"NewID\" INTEGER,\n", "            PRIMARY KEY (\"OldID\")\n", "        );\n", "        \"\"\")\n", "        conn.commit()\n", "\n", "        # 4. Costruzione delle mappature\n", "        for col1, col2 in condizioni:\n", "            query = f\"\"\"\n", "            WITH gruppi AS (\n", "                SELECT DISTINCT\n", "                    LEAST(p1.\"PlayerID\", p2.\"PlayerID\") AS \"PlayerID_min\",\n", "                    GREATEST(p1.\"PlayerID\", p2.\"PlayerID\") AS \"PlayerID_max\"\n", "                FROM players_each_game p1\n", "                JOIN players_each_game p2\n", "                    ON p1.\"Nome\" = p2.\"Nome\"\n", "                    AND p1.\"Cognome\" = p2.\"Cognome\"\n", "                    AND p1.{col1} = p2.{col1}\n", "                    AND p1.{col2} = p2.{col2}\n", "                    AND ABS(p1.\"Year\" - p2.\"Year\") <= 2\n", "                    AND p1.\"PlayerID\" <> p2.\"PlayerID\"\n", "                    AND p1.{col1} IS NOT NULL AND p2.{col1} IS NOT NULL\n", "                    AND p1.{col2} IS NOT NULL AND p2.{col2} IS NOT NULL\n", "            )\n", "            INSERT INTO tmp_playerid_map (\"OldID\", \"NewID\")\n", "            SELECT \"PlayerID_max\", \"PlayerID_min\" FROM gruppi\n", "            ON CONFLICT DO NOTHING;\n", "            \"\"\"\n", "            cur.execute(query)\n", "            conn.commit()\n", "\n", "        # 5. <PERSON><PERSON><PERSON> mappature ridondanti (ciclo di compressione transitiva)\n", "        cur.execute(\"\"\"\n", "        DROP TABLE IF EXISTS tmp_playerid_final;    \n", "        \n", "        WITH RECURSIVE chains AS (\n", "            SELECT \"OldID\", \"NewID\" FROM tmp_playerid_map\n", "            UNION\n", "            SELECT c.\"OldID\", m.\"NewID\"\n", "            FROM chains c\n", "            JOIN tmp_playerid_map m ON c.\"NewID\" = m.\"OldID\"\n", "        )\n", "        SELECT \"OldID\", MIN(\"NewID\") AS \"NewID\"\n", "        INTO TEMP TABLE tmp_playerid_final\n", "        FROM chains\n", "        GROUP BY \"OldID\";\n", "        \"\"\")\n", "        conn.commit()\n", "\n", "        # 6. Update finale dei PlayerID\n", "        cur.execute(\"\"\"\n", "        UPDATE players_each_game p\n", "        SET \"PlayerID\" = m.\"NewID\"\n", "        FROM tmp_playerid_final m\n", "        WHERE p.\"PlayerID\" = m.\"OldID\"\n", "        AND p.\"PlayerID\" <> m.\"NewID\";\n", "        \"\"\")\n", "        print(f\"Aggiornati {cur.rowcount} PlayerID\")\n", "        \n", "        righe_modificate += cur.rowcount\n", "        conn.commit()\n"]}, {"cell_type": "code", "execution_count": 144, "metadata": {}, "outputs": [], "source": ["cur.execute(\"\"\"\n", "CREATE INDEX IF NOT EXISTS idx_players_nome_cognome ON players_each_game (\"Nome\", \"Cognome\");\n", "CREATE INDEX IF NOT EXISTS idx_players_year ON players_each_game (\"Year\");\n", "CREATE INDEX IF NOT EXISTS idx_players_original ON players_each_game (\"originalPlayerID\");\n", "CREATE INDEX IF NOT EXISTS idx_players_numero ON players_each_game (\"NumeroMaglia\");\n", "\"\"\")\n", "conn.commit()"]}, {"cell_type": "code", "execution_count": 201, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Aggiornati 100 PlayerID\n", "Aggiornati 0 PlayerID\n"]}], "source": ["conn.rollback()\n", "\n", "unifica_PlayerID_players_each_game()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\nrighe_modificate = 1\\nnum_cicli = 0\\n\\nwhile righe_modificate > 0 and num_cicli < 20:\\n  righe_modificate = 0\\n\\n  # 1) \\n  cur.execute(\"\"\"\\n  WITH gruppi_min AS (\\n    SELECT \\n      MIN(\"PlayerID\") AS \"PlayerID_minimo\",\\n      \"Nome\",\\n      \"Cognome\",\\n      \"TeamID\",\\n      \"Ruolo\",\\n      \"Annata\"\\n    FROM players_each_game\\n    GROUP BY \"Nome\", \"Cognome\", \"TeamID\", \"Ruolo\", \"Anna<PERSON>\"\\n    HAVING COUNT(*) > 1\\n  )\\n\\n  UPDATE players_each_game AS p\\n  SET \"PlayerID\" = g.\"PlayerID_minimo\"\\n  FROM gruppi_min AS g\\n  WHERE \\n    p.\"Nome\" = g.\"Nome\" AND\\n    p.\"Cognome\" = g.\"Cognome\" AND\\n    p.\"TeamID\" = g.\"TeamID\" AND\\n    p.\"Ruolo\" = g.\"Ruolo\" AND\\n    p.\"Annata\" = g.\"Annata\" AND\\n    p.\"PlayerID\" != g.\"PlayerID_minimo\";\\n  \"\"\")\\n  righe_modificate += cur.rowcount\\n  conn.commit()\\n\\n  # 2) \\n  cur.execute(\"\"\"\\n  WITH gruppi_min AS (\\n    SELECT \\n      MIN(\"PlayerID\") AS \"PlayerID_minimo\",\\n      \"Nome\",\\n      \"Cognome\",\\n      \"TeamID\",\\n      \"Ruolo\",\\n      \"Year\"\\n    FROM players_each_game\\n    GROUP BY \"Nome\", \"Cognome\", \"TeamID\", \"Ruolo\", \"Year\"\\n    HAVING COUNT(*) > 1\\n  )\\n\\n  UPDATE players_each_game AS p\\n  SET \"PlayerID\" = g.\"PlayerID_minimo\"\\n  FROM gruppi_min AS g\\n  WHERE \\n    p.\"Nome\" = g.\"Nome\" AND\\n    p.\"Cognome\" = g.\"Cognome\" AND\\n    p.\"TeamID\" = g.\"TeamID\" AND\\n    p.\"Ruolo\" = g.\"Ruolo\" AND\\n    p.\"Year\" = g.\"Year\" AND\\n    p.\"PlayerID\" != g.\"PlayerID_minimo\";\\n  \"\"\")\\n  righe_modificate += cur.rowcount\\n  conn.commit()\\n\\n  # 3) \\n  cur.execute(\"\"\"\\n  WITH gruppi_min AS (\\n    SELECT \\n      MIN(\"PlayerID\") AS \"PlayerID_minimo\",\\n      \"Nome\",\\n      \"Cognome\",\\n      \"TeamID\",\\n      \"RuoloCalc\",\\n      \"Annata\"\\n    FROM players_each_game\\n    GROUP BY \"Nome\", \"Cognome\", \"TeamID\", \"RuoloCalc\", \"Annata\"\\n    HAVING COUNT(*) > 1\\n  )\\n\\n  UPDATE players_each_game AS p\\n  SET \"PlayerID\" = g.\"PlayerID_minimo\"\\n  FROM gruppi_min AS g\\n  WHERE \\n    p.\"Nome\" = g.\"Nome\" AND\\n    p.\"Cognome\" = g.\"Cognome\" AND\\n    p.\"TeamID\" = g.\"TeamID\" AND\\n    p.\"RuoloCalc\" = g.\"RuoloCalc\" AND\\n    p.\"Annata\" = g.\"Annata\" AND\\n    p.\"PlayerID\" != g.\"PlayerID_minimo\";\\n  \"\"\")\\n  righe_modificate += cur.rowcount\\n  conn.commit()\\n\\n  # 4) \\n  cur.execute(\"\"\"\\n  WITH gruppi_min AS (\\n    SELECT \\n      MIN(\"PlayerID\") AS \"PlayerID_minimo\",\\n      \"Nome\",\\n      \"Cognome\",\\n      \"TeamID\",\\n      \"RuoloCalc\",\\n      \"Year\"\\n    FROM players_each_game\\n    GROUP BY \"Nome\", \"Cognome\", \"TeamID\", \"RuoloCalc\", \"Year\"\\n    HAVING COUNT(*) > 1\\n  )\\n\\n  UPDATE players_each_game AS p\\n  SET \"PlayerID\" = g.\"PlayerID_minimo\"\\n  FROM gruppi_min AS g\\n  WHERE \\n    p.\"Nome\" = g.\"Nome\" AND\\n    p.\"Cognome\" = g.\"Cognome\" AND\\n    p.\"TeamID\" = g.\"TeamID\" AND\\n    p.\"RuoloCalc\" = g.\"RuoloCalc\" AND\\n    p.\"Year\" = g.\"Year\" AND\\n    p.\"PlayerID\" != g.\"PlayerID_minimo\";\\n  \"\"\")\\n  righe_modificate += cur.rowcount\\n  conn.commit()\\n\\n  # 5) \\n  cur.execute(\"\"\"\\n  WITH gruppi_min AS (\\n    SELECT \\n      MIN(\"PlayerID\") AS \"PlayerID_minimo\",\\n      \"Nome\",\\n      \"Cognome\",\\n      \"TeamID\",\\n      \"NumeroMaglia\",\\n      \"Annata\"\\n    FROM players_each_game\\n    GROUP BY \"Nome\", \"Cognome\", \"TeamID\", \"NumeroMaglia\", \"Annata\"\\n    HAVING COUNT(*) > 1\\n  )\\n\\n  UPDATE players_each_game AS p\\n  SET \"PlayerID\" = g.\"PlayerID_minimo\"\\n  FROM gruppi_min AS g\\n  WHERE \\n    p.\"Nome\" = g.\"Nome\" AND\\n    p.\"Cognome\" = g.\"Cognome\" AND\\n    p.\"TeamID\" = g.\"TeamID\" AND\\n    p.\"NumeroMaglia\" = g.\"NumeroMaglia\" AND\\n    p.\"Annata\" = g.\"Annata\" AND\\n    p.\"PlayerID\" != g.\"PlayerID_minimo\";\\n  \"\"\")\\n  righe_modificate += cur.rowcount\\n  conn.commit()\\n\\n  # 6) \\n  cur.execute(\"\"\"\\n  WITH gruppi_min AS (\\n    SELECT \\n      MIN(\"PlayerID\") AS \"PlayerID_minimo\",\\n      \"Nome\",\\n      \"Cognome\",\\n      \"TeamID\",\\n      \"NumeroMaglia\",\\n      \"Year\"\\n    FROM players_each_game\\n    GROUP BY \"Nome\", \"Cognome\", \"TeamID\", \"NumeroMaglia\", \"Year\"\\n    HAVING COUNT(*) > 1\\n  )\\n\\n  UPDATE players_each_game AS p\\n  SET \"PlayerID\" = g.\"PlayerID_minimo\"\\n  FROM gruppi_min AS g\\n  WHERE \\n    p.\"Nome\" = g.\"Nome\" AND\\n    p.\"Cognome\" = g.\"Cognome\" AND\\n    p.\"TeamID\" = g.\"TeamID\" AND\\n    p.\"NumeroMaglia\" = g.\"NumeroMaglia\" AND\\n    p.\"Year\" = g.\"Year\" AND\\n    p.\"PlayerID\" != g.\"PlayerID_minimo\";\\n  \"\"\")\\n  righe_modificate += cur.rowcount\\n  conn.commit()\\n\\n  # 7) \\n  cur.execute(\"\"\"\\n  WITH gruppi_min AS (\\n    SELECT \\n      MIN(\"PlayerID\") AS \"PlayerID_minimo\",\\n      \"Nome\",\\n      \"Cognome\",\\n      \"originalPlayerID\",\\n      \"Ruolo\",\\n      \"Annata\"\\n    FROM players_each_game\\n    GROUP BY \"Nome\", \"Cognome\", \"originalPlayerID\", \"Ruolo\", \"Annata\"\\n    HAVING COUNT(*) > 1\\n  )\\n\\n  UPDATE players_each_game AS p\\n  SET \"PlayerID\" = g.\"PlayerID_minimo\"\\n  FROM gruppi_min AS g\\n  WHERE \\n    p.\"Nome\" = g.\"Nome\" AND\\n    p.\"Cognome\" = g.\"Cognome\" AND\\n    p.\"originalPlayerID\" = g.\"originalPlayerID\" AND\\n    p.\"Ruolo\" = g.\"Ruolo\" AND\\n    p.\"Annata\" = g.\"Annata\" AND\\n    p.\"PlayerID\" != g.\"PlayerID_minimo\";\\n  \"\"\")\\n  righe_modificate += cur.rowcount\\n  conn.commit()\\n\\n  # 8) \\n  cur.execute(\"\"\"\\n  WITH gruppi_min AS (\\n    SELECT \\n      MIN(\"PlayerID\") AS \"PlayerID_minimo\",\\n      \"Nome\",\\n      \"Cognome\",\\n      \"originalPlayerID\",\\n      \"Ruolo\",\\n      \"Year\"\\n    FROM players_each_game\\n    GROUP BY \"Nome\", \"Cognome\", \"originalPlayerID\", \"Ruolo\", \"Year\"\\n    HAVING COUNT(*) > 1\\n  )\\n\\n  UPDATE players_each_game AS p\\n  SET \"PlayerID\" = g.\"PlayerID_minimo\"\\n  FROM gruppi_min AS g\\n  WHERE \\n    p.\"Nome\" = g.\"Nome\" AND\\n    p.\"Cognome\" = g.\"Cognome\" AND\\n    p.\"originalPlayerID\" = g.\"originalPlayerID\" AND\\n    p.\"Ruolo\" = g.\"Ruolo\" AND\\n    p.\"Year\" = g.\"Year\" AND\\n    p.\"PlayerID\" != g.\"PlayerID_minimo\";\\n  \"\"\")\\n  righe_modificate += cur.rowcount\\n  conn.commit()\\n\\n  # 9) \\n  cur.execute(\"\"\"\\n  WITH gruppi_min AS (\\n    SELECT \\n      MIN(\"PlayerID\") AS \"PlayerID_minimo\",\\n      \"Nome\",\\n      \"Cognome\",\\n      \"originalPlayerID\",\\n      \"NumeroMaglia\",\\n      \"Annata\"\\n    FROM players_each_game\\n    GROUP BY \"Nome\", \"Cognome\", \"originalPlayerID\", \"NumeroMaglia\", \"Annata\"\\n    HAVING COUNT(*) > 1\\n  )\\n\\n  UPDATE players_each_game AS p\\n  SET \"PlayerID\" = g.\"PlayerID_minimo\"\\n  FROM gruppi_min AS g\\n  WHERE \\n    p.\"Nome\" = g.\"Nome\" AND\\n    p.\"Cognome\" = g.\"Cognome\" AND\\n    p.\"originalPlayerID\" = g.\"originalPlayerID\" AND\\n    p.\"NumeroMaglia\" = g.\"NumeroMaglia\" AND\\n    p.\"Annata\" = g.\"Annata\" AND\\n    p.\"PlayerID\" != g.\"PlayerID_minimo\";\\n  \"\"\")\\n  righe_modificate += cur.rowcount\\n  conn.commit()\\n\\n  # 10) \\n  cur.execute(\"\"\"\\n  WITH gruppi_min AS (\\n    SELECT \\n      MIN(\"PlayerID\") AS \"PlayerID_minimo\",\\n      \"Nome\",\\n      \"Cognome\",\\n      \"originalPlayerID\",\\n      \"NumeroMaglia\",\\n      \"Year\"\\n    FROM players_each_game\\n    GROUP BY \"Nome\", \"Cognome\", \"originalPlayerID\", \"NumeroMaglia\", \"Year\"\\n    HAVING COUNT(*) > 1\\n  )\\n\\n  UPDATE players_each_game AS p\\n  SET \"PlayerID\" = g.\"PlayerID_minimo\"\\n  FROM gruppi_min AS g\\n  WHERE \\n    p.\"Nome\" = g.\"Nome\" AND\\n    p.\"Cognome\" = g.\"Cognome\" AND\\n    p.\"originalPlayerID\" = g.\"originalPlayerID\" AND\\n    p.\"NumeroMaglia\" = g.\"NumeroMaglia\" AND\\n    p.\"Year\" = g.\"Year\" AND\\n    p.\"PlayerID\" != g.\"PlayerID_minimo\";\\n  \"\"\")\\n  righe_modificate += cur.rowcount\\n  conn.commit()\\n\\n  print(f\"Ciclo {num_cicli}: {righe_modificate} righe modificate\")\\n\\n  num_cicli +=1\\n\\n'"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["#<PERSON><PERSON><PERSON> questi update finchè non ci sono più righe modificate alla fine di un ciclo, o dopo 10 cicli\n", "'''\n", "righe_modificate = 1\n", "num_cicli = 0\n", "\n", "while righe_modificate > 0 and num_cicli < 20:\n", "  righe_modificate = 0\n", "\n", "  # 1) \n", "  cur.execute(\"\"\"\n", "  WITH gruppi_min AS (\n", "    SELECT \n", "      MIN(\"PlayerID\") AS \"PlayerID_minimo\",\n", "      \"Nome\",\n", "      \"Cognome\",\n", "      \"TeamID\",\n", "      \"R<PERSON><PERSON>\",\n", "      \"Annata\"\n", "    FROM players_each_game\n", "    GROUP BY \"Nome\", \"Cognome\", \"TeamID\", \"R<PERSON><PERSON>\", \"Annata\"\n", "    HAVING COUNT(*) > 1\n", "  )\n", "\n", "  UPDATE players_each_game AS p\n", "  SET \"PlayerID\" = g.\"PlayerID_minimo\"\n", "  FROM gruppi_min AS g\n", "  WHERE \n", "    p.\"Nome\" = g.\"Nome\" AND\n", "    p.\"Cognome\" = g.\"Cognome\" AND\n", "    p.\"TeamID\" = g.\"TeamID\" AND\n", "    p.\"Ruolo\" = g.\"Ruolo\" AND\n", "    p.\"<PERSON><PERSON>\" = g.\"<PERSON><PERSON>\" AND\n", "    p.\"PlayerID\" != g.\"PlayerID_minimo\";\n", "  \"\"\")\n", "  righe_modificate += cur.rowcount\n", "  conn.commit()\n", "\n", "  # 2) \n", "  cur.execute(\"\"\"\n", "  WITH gruppi_min AS (\n", "    SELECT \n", "      MIN(\"PlayerID\") AS \"PlayerID_minimo\",\n", "      \"Nome\",\n", "      \"Cognome\",\n", "      \"TeamID\",\n", "      \"R<PERSON><PERSON>\",\n", "      \"Year\"\n", "    FROM players_each_game\n", "    GROUP BY \"Nome\", \"Cognome\", \"TeamID\", \"R<PERSON><PERSON>\", \"Year\"\n", "    HAVING COUNT(*) > 1\n", "  )\n", "\n", "  UPDATE players_each_game AS p\n", "  SET \"PlayerID\" = g.\"PlayerID_minimo\"\n", "  FROM gruppi_min AS g\n", "  WHERE \n", "    p.\"Nome\" = g.\"Nome\" AND\n", "    p.\"Cognome\" = g.\"Cognome\" AND\n", "    p.\"TeamID\" = g.\"TeamID\" AND\n", "    p.\"Ruolo\" = g.\"Ruolo\" AND\n", "    p.\"Year\" = g.\"Year\" AND\n", "    p.\"PlayerID\" != g.\"PlayerID_minimo\";\n", "  \"\"\")\n", "  righe_modificate += cur.rowcount\n", "  conn.commit()\n", "\n", "  # 3) \n", "  cur.execute(\"\"\"\n", "  WITH gruppi_min AS (\n", "    SELECT \n", "      MIN(\"PlayerID\") AS \"PlayerID_minimo\",\n", "      \"Nome\",\n", "      \"Cognome\",\n", "      \"TeamID\",\n", "      \"RuoloCalc\",\n", "      \"Annata\"\n", "    FROM players_each_game\n", "    GROUP BY \"Nome\", \"Cognome\", \"TeamID\", \"RuoloCalc\", \"Annata\"\n", "    HAVING COUNT(*) > 1\n", "  )\n", "\n", "  UPDATE players_each_game AS p\n", "  SET \"PlayerID\" = g.\"PlayerID_minimo\"\n", "  FROM gruppi_min AS g\n", "  WHERE \n", "    p.\"Nome\" = g.\"Nome\" AND\n", "    p.\"Cognome\" = g.\"Cognome\" AND\n", "    p.\"TeamID\" = g.\"TeamID\" AND\n", "    p.\"RuoloCalc\" = g.\"RuoloCalc\" AND\n", "    p.\"<PERSON><PERSON>\" = g.\"<PERSON><PERSON>\" AND\n", "    p.\"PlayerID\" != g.\"PlayerID_minimo\";\n", "  \"\"\")\n", "  righe_modificate += cur.rowcount\n", "  conn.commit()\n", "\n", "  # 4) \n", "  cur.execute(\"\"\"\n", "  WITH gruppi_min AS (\n", "    SELECT \n", "      MIN(\"PlayerID\") AS \"PlayerID_minimo\",\n", "      \"Nome\",\n", "      \"Cognome\",\n", "      \"TeamID\",\n", "      \"RuoloCalc\",\n", "      \"Year\"\n", "    FROM players_each_game\n", "    GROUP BY \"Nome\", \"Cognome\", \"TeamID\", \"RuoloCalc\", \"Year\"\n", "    HAVING COUNT(*) > 1\n", "  )\n", "\n", "  UPDATE players_each_game AS p\n", "  SET \"PlayerID\" = g.\"PlayerID_minimo\"\n", "  FROM gruppi_min AS g\n", "  WHERE \n", "    p.\"Nome\" = g.\"Nome\" AND\n", "    p.\"Cognome\" = g.\"Cognome\" AND\n", "    p.\"TeamID\" = g.\"TeamID\" AND\n", "    p.\"RuoloCalc\" = g.\"RuoloCalc\" AND\n", "    p.\"Year\" = g.\"Year\" AND\n", "    p.\"PlayerID\" != g.\"PlayerID_minimo\";\n", "  \"\"\")\n", "  righe_modificate += cur.rowcount\n", "  conn.commit()\n", "\n", "  # 5) \n", "  cur.execute(\"\"\"\n", "  WITH gruppi_min AS (\n", "    SELECT \n", "      MIN(\"PlayerID\") AS \"PlayerID_minimo\",\n", "      \"Nome\",\n", "      \"Cognome\",\n", "      \"TeamID\",\n", "      \"NumeroMaglia\",\n", "      \"Annata\"\n", "    FROM players_each_game\n", "    GROUP BY \"Nome\", \"Cognome\", \"TeamID\", \"NumeroMaglia\", \"Annata\"\n", "    HAVING COUNT(*) > 1\n", "  )\n", "\n", "  UPDATE players_each_game AS p\n", "  SET \"PlayerID\" = g.\"PlayerID_minimo\"\n", "  FROM gruppi_min AS g\n", "  WHERE \n", "    p.\"Nome\" = g.\"Nome\" AND\n", "    p.\"Cognome\" = g.\"Cognome\" AND\n", "    p.\"TeamID\" = g.\"TeamID\" AND\n", "    p.\"NumeroMaglia\" = g.\"NumeroMaglia\" AND\n", "    p.\"<PERSON><PERSON>\" = g.\"<PERSON><PERSON>\" AND\n", "    p.\"PlayerID\" != g.\"PlayerID_minimo\";\n", "  \"\"\")\n", "  righe_modificate += cur.rowcount\n", "  conn.commit()\n", "\n", "  # 6) \n", "  cur.execute(\"\"\"\n", "  WITH gruppi_min AS (\n", "    SELECT \n", "      MIN(\"PlayerID\") AS \"PlayerID_minimo\",\n", "      \"Nome\",\n", "      \"Cognome\",\n", "      \"TeamID\",\n", "      \"NumeroMaglia\",\n", "      \"Year\"\n", "    FROM players_each_game\n", "    GROUP BY \"Nome\", \"Cognome\", \"TeamID\", \"NumeroMaglia\", \"Year\"\n", "    HAVING COUNT(*) > 1\n", "  )\n", "\n", "  UPDATE players_each_game AS p\n", "  SET \"PlayerID\" = g.\"PlayerID_minimo\"\n", "  FROM gruppi_min AS g\n", "  WHERE \n", "    p.\"Nome\" = g.\"Nome\" AND\n", "    p.\"Cognome\" = g.\"Cognome\" AND\n", "    p.\"TeamID\" = g.\"TeamID\" AND\n", "    p.\"NumeroMaglia\" = g.\"NumeroMaglia\" AND\n", "    p.\"Year\" = g.\"Year\" AND\n", "    p.\"PlayerID\" != g.\"PlayerID_minimo\";\n", "  \"\"\")\n", "  righe_modificate += cur.rowcount\n", "  conn.commit()\n", "\n", "  # 7) \n", "  cur.execute(\"\"\"\n", "  WITH gruppi_min AS (\n", "    SELECT \n", "      MIN(\"PlayerID\") AS \"PlayerID_minimo\",\n", "      \"Nome\",\n", "      \"Cognome\",\n", "      \"originalPlayerID\",\n", "      \"R<PERSON><PERSON>\",\n", "      \"Annata\"\n", "    FROM players_each_game\n", "    GROUP BY \"Nome\", \"Cognome\", \"originalPlayerID\", \"<PERSON><PERSON><PERSON>\", \"Annata\"\n", "    HAVING COUNT(*) > 1\n", "  )\n", "\n", "  UPDATE players_each_game AS p\n", "  SET \"PlayerID\" = g.\"PlayerID_minimo\"\n", "  FROM gruppi_min AS g\n", "  WHERE \n", "    p.\"Nome\" = g.\"Nome\" AND\n", "    p.\"Cognome\" = g.\"Cognome\" AND\n", "    p.\"originalPlayerID\" = g.\"originalPlayerID\" AND\n", "    p.\"Ruolo\" = g.\"Ruolo\" AND\n", "    p.\"<PERSON><PERSON>\" = g.\"<PERSON><PERSON>\" AND\n", "    p.\"PlayerID\" != g.\"PlayerID_minimo\";\n", "  \"\"\")\n", "  righe_modificate += cur.rowcount\n", "  conn.commit()\n", "\n", "  # 8) \n", "  cur.execute(\"\"\"\n", "  WITH gruppi_min AS (\n", "    SELECT \n", "      MIN(\"PlayerID\") AS \"PlayerID_minimo\",\n", "      \"Nome\",\n", "      \"Cognome\",\n", "      \"originalPlayerID\",\n", "      \"R<PERSON><PERSON>\",\n", "      \"Year\"\n", "    FROM players_each_game\n", "    GROUP BY \"Nome\", \"Cognome\", \"originalPlayerID\", \"Ruolo\", \"Year\"\n", "    HAVING COUNT(*) > 1\n", "  )\n", "\n", "  UPDATE players_each_game AS p\n", "  SET \"PlayerID\" = g.\"PlayerID_minimo\"\n", "  FROM gruppi_min AS g\n", "  WHERE \n", "    p.\"Nome\" = g.\"Nome\" AND\n", "    p.\"Cognome\" = g.\"Cognome\" AND\n", "    p.\"originalPlayerID\" = g.\"originalPlayerID\" AND\n", "    p.\"Ruolo\" = g.\"Ruolo\" AND\n", "    p.\"Year\" = g.\"Year\" AND\n", "    p.\"PlayerID\" != g.\"PlayerID_minimo\";\n", "  \"\"\")\n", "  righe_modificate += cur.rowcount\n", "  conn.commit()\n", "\n", "  # 9) \n", "  cur.execute(\"\"\"\n", "  WITH gruppi_min AS (\n", "    SELECT \n", "      MIN(\"PlayerID\") AS \"PlayerID_minimo\",\n", "      \"Nome\",\n", "      \"Cognome\",\n", "      \"originalPlayerID\",\n", "      \"NumeroMaglia\",\n", "      \"Annata\"\n", "    FROM players_each_game\n", "    GROUP BY \"Nome\", \"Cognome\", \"originalPlayerID\", \"NumeroMaglia\", \"Annata\"\n", "    HAVING COUNT(*) > 1\n", "  )\n", "\n", "  UPDATE players_each_game AS p\n", "  SET \"PlayerID\" = g.\"PlayerID_minimo\"\n", "  FROM gruppi_min AS g\n", "  WHERE \n", "    p.\"Nome\" = g.\"Nome\" AND\n", "    p.\"Cognome\" = g.\"Cognome\" AND\n", "    p.\"originalPlayerID\" = g.\"originalPlayerID\" AND\n", "    p.\"NumeroMaglia\" = g.\"NumeroMaglia\" AND\n", "    p.\"<PERSON><PERSON>\" = g.\"<PERSON><PERSON>\" AND\n", "    p.\"PlayerID\" != g.\"PlayerID_minimo\";\n", "  \"\"\")\n", "  righe_modificate += cur.rowcount\n", "  conn.commit()\n", "\n", "  # 10) \n", "  cur.execute(\"\"\"\n", "  WITH gruppi_min AS (\n", "    SELECT \n", "      MIN(\"PlayerID\") AS \"PlayerID_minimo\",\n", "      \"Nome\",\n", "      \"Cognome\",\n", "      \"originalPlayerID\",\n", "      \"NumeroMaglia\",\n", "      \"Year\"\n", "    FROM players_each_game\n", "    GROUP BY \"Nome\", \"Cognome\", \"originalPlayerID\", \"NumeroMaglia\", \"Year\"\n", "    HAVING COUNT(*) > 1\n", "  )\n", "\n", "  UPDATE players_each_game AS p\n", "  SET \"PlayerID\" = g.\"PlayerID_minimo\"\n", "  FROM gruppi_min AS g\n", "  WHERE \n", "    p.\"Nome\" = g.\"Nome\" AND\n", "    p.\"Cognome\" = g.\"Cognome\" AND\n", "    p.\"originalPlayerID\" = g.\"originalPlayerID\" AND\n", "    p.\"NumeroMaglia\" = g.\"NumeroMaglia\" AND\n", "    p.\"Year\" = g.\"Year\" AND\n", "    p.\"PlayerID\" != g.\"PlayerID_minimo\";\n", "  \"\"\")\n", "  righe_modificate += cur.rowcount\n", "  conn.commit()\n", "  \n", "  print(f\"Ciclo {num_cicli}: {righe_modificate} righe modificate\")\n", "\n", "  num_cicli +=1\n", "  \n", "'''\n"]}, {"cell_type": "code", "execution_count": 187, "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\ncur.execute(\"\"\"\\n    WITH gruppi AS (\\n      SELECT \\n        LEAST(p1.\"PlayerID\", p2.\"PlayerID\") AS \"PlayerID_min\",\\n        GREATEST(p1.\"PlayerID\", p2.\"PlayerID\") AS \"PlayerID_max\"\\n      FROM players_each_game p1\\n      JOIN players_each_game p2\\n        ON p1.\"Nome\" = p2.\"Nome\"\\n       AND p1.\"Cognome\" = p2.\"Cognome\"\\n       AND p1.\"Ruolo\" = p2.\"Ruolo\"\\n       AND p1.\"originalPlayerID\" = p2.\"originalPlayerID\"\\n       AND ABS(p1.\"Year\" - p2.\"Year\") <= 2\\n       AND p1.\"PlayerID\" <> p2.\"PlayerID\"\\n    )\\n    SELECT DISTINCT \"PlayerID_max\", \"PlayerID_min\"\\n    FROM gruppi\\n    WHERE \"PlayerID_max\" <> \"PlayerID_min\";\\n\"\"\")\\nto_update = cur.fetchall()\\nprint(to_update)\\n'"]}, "execution_count": 187, "metadata": {}, "output_type": "execute_result"}], "source": ["#Siccome a volte lo stesso giocatore magari fa un anno all'estero e quindi non risulta nei dati, li raggruppo per un intervallo di Year più grande\n", "#Unifico gli ID dei giocatori che hanno s<PERSON><PERSON>, Cognome, originalPlayerID, <PERSON><PERSON><PERSON>, e un Year che differisce di al massimo 2 anni\n", "'''\n", "cur.execute(\"\"\"\n", "    WITH gruppi AS (\n", "      SELECT \n", "        LEAST(p1.\"PlayerID\", p2.\"PlayerID\") AS \"PlayerID_min\",\n", "        GREATEST(p1.\"PlayerID\", p2.\"PlayerID\") AS \"PlayerID_max\"\n", "      FROM players_each_game p1\n", "      JOIN players_each_game p2\n", "        ON p1.\"Nome\" = p2.\"Nome\"\n", "       AND p1.\"Cognome\" = p2.\"Cognome\"\n", "       AND p1.\"Ruolo\" = p2.\"Ruolo\"\n", "       AND p1.\"originalPlayerID\" = p2.\"originalPlayerID\"\n", "       AND ABS(p1.\"Year\" - p2.\"Year\") <= 2\n", "       AND p1.\"PlayerID\" <> p2.\"PlayerID\"\n", "    )\n", "    SELECT DISTINCT \"PlayerID_max\", \"PlayerID_min\"\n", "    FROM gruppi\n", "    WHERE \"PlayerID_max\" <> \"PlayerID_min\";\n", "\"\"\")\n", "to_update = cur.fetchall()\n", "print(to_update)\n", "'''"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["conn.rollback()"]}, {"cell_type": "code", "execution_count": 188, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[('152415',), ('152415',), ('210402',), ('210402',)]\n"]}], "source": ["# Mostro quelli con stesso originalPlayerID ma PlayerID diverso\n", "cur.execute(\"\"\"\n", "SELECT DISTINCT ON (\"PlayerID\")\n", "    \"originalPlayerID\"\n", "FROM players_each_game\n", "WHERE \"originalPlayerID\" IN (\n", "    SELECT \"originalPlayerID\"\n", "    FROM players_each_game\n", "    GROUP BY \"originalPlayerID\"\n", "    HAVING COUNT(DISTINCT \"PlayerID\") >= 2\n", ")\n", "AND \"originalPlayerID\" NOT IN ('5179', '00000')  --Questi PlayerID li ho già controllati, non sono problematici\n", "ORDER BY \"PlayerID\";\n", "\"\"\")\n", "\n", "results = cur.fetchall()\n", "print(results)\n", "#Mi stampa tutti gli originalPlayerID che hanno almeno due PlayerID diversi"]}, {"cell_type": "code", "execution_count": 189, "metadata": {}, "outputs": [], "source": ["#Correggo alcuni originalGameID che sono chiaramente sbagliati e che mi scasinano la cella sopra\n", "cur.execute(\"\"\"\n", "UPDATE players_each_game\n", "SET \"originalPlayerID\" = '152408'\n", "WHERE \"Nome\"='<PERSON><PERSON><PERSON><PERSON>' AND \"Cognome\"='Galassi' AND \"TeamID\"=5 AND \"Annata\"=2024\n", "\n", "\"\"\")\n", "conn.commit()\n"]}, {"cell_type": "code", "execution_count": 190, "metadata": {}, "outputs": [], "source": ["#Correggo i giocatori che hanno nome e cognome invertiti\n", "cur.execute(\"\"\"\n", "UPDATE players_each_game\n", "SET \"Nome\" = 'Dusan',\n", "    \"Cognome\" = '<PERSON><PERSON><PERSON><PERSON><PERSON>'\n", "WHERE \"Nome\"='<PERSON><PERSON><PERSON><PERSON><PERSON>' AND \"Cognome\"='Dusan' AND \"TeamID\"=15 AND \"originalPlayerID\"='210402'\n", "\"\"\")\n", "conn.commit()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Se un gruppo di righe ha lo stesso\n", "- TeamID\n", "- <PERSON><PERSON> \n", "- NumeroMaglia\n", "- Nome\n", "- (Ruolo OR RuoloCalc)\n", "\n", "Allora metti il cognome più frequente tra esse\n", "\n", "<PERSON><PERSON>a cosa per il nome"]}, {"cell_type": "code", "execution_count": 192, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[]\n", "[]\n", "[]\n", "[]\n"]}], "source": ["#Se un gruppo ha stesso TeamID, Annata, NumeroMaglia, Nome, (Ruolo o RuoloCalc) allora metto il cognome più frequente (a parte se RuoloCalc è 0, in questo caso non li raggruppo. Se RUoloCalc è 0, allora veramente non so quale sia il suo ruolo nella partita, quindi non è sicuro raggruppare due giocatori con RuoloCalc 0)\n", "cur.execute(\"\"\"\n", "WITH gruppi AS (\n", "  SELECT \n", "    \"TeamID\", \n", "    \"Anna<PERSON>\", \n", "    \"NumeroMaglia\", \n", "    \"Nome\",\n", "    COALESCE(\"Ruolo\", \"RuoloCalc\") AS RuoloEffettivo,   --Metto prima Ruolo di RuoloCalc così prende prima Ruolo\n", "    \"Cognome\",\n", "    COUNT(*) AS freq\n", "  FROM players_each_game\n", "  WHERE \"RuoloCalc\" <> 0\n", "  GROUP BY \"TeamID\", \"Annata\", \"NumeroMaglia\", \"Nome\", COALESCE(\"Ruolo\", \"RuoloCalc\"), \"Cognome\"\n", "),\n", "cognome_piu_frequente AS (\n", "  SELECT DISTINCT ON (\"TeamID\", \"Annata\", \"NumeroMaglia\", \"Nome\", RuoloEffettivo)\n", "    \"TeamID\", \n", "    \"Anna<PERSON>\", \n", "    \"NumeroMaglia\", \n", "    \"Nome\", \n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    \"Cognome\" AS cognome_dominante\n", "  FROM gruppi\n", "  ORDER BY \"TeamID\", \"Annata\", \"NumeroMaglia\", \"Nome\", RuoloEffettivo, freq DESC\n", ")\n", "UPDATE players_each_game AS p\n", "SET \"Cognome\" = c.cognome_dominante\n", "FROM cognome_piu_frequente c\n", "WHERE p.\"TeamID\" = c.\"TeamID\"\n", "  AND p.\"<PERSON><PERSON>\" = c.\"<PERSON><PERSON>\"\n", "  AND p.\"NumeroMaglia\" = c.\"NumeroMaglia\"\n", "  AND p.\"Nome\" = c.\"Nome\"\n", "  AND COALESCE(p.\"<PERSON><PERSON><PERSON>\", p.\"RuoloCal<PERSON>\") = c.<PERSON>\n", "  AND p.\"RuoloCalc\" <> 0\n", "  AND p.\"Cognome\" IS DISTINCT FROM c.cognome_dominante\n", "\n", "RETURNING *;\n", "\"\"\")\n", "results = cur.fetchall()\n", "print(results)\n", "conn.commit()\n", "\n", "\n", "cur.execute(\"\"\"\n", "WITH gruppi AS (\n", "  SELECT \n", "    \"TeamID\", \n", "    \"Anna<PERSON>\", \n", "    \"NumeroMaglia\", \n", "    \"Nome\",\n", "    COALESCE(\"RuoloCalc\", \"Ruolo\") AS RuoloEffettivo,   --Metto prima RuoloCalc di Ruolo così prende prima RuoloCalc\n", "    \"Cognome\",\n", "    COUNT(*) AS freq\n", "  FROM players_each_game\n", "  WHERE \"RuoloCalc\" <> 0\n", "  GROUP BY \"TeamID\", \"Annata\", \"NumeroMaglia\", \"Nome\", COALESCE(\"RuoloCalc\", \"Ruolo\"), \"Cognome\"\n", "),\n", "cognome_piu_frequente AS (\n", "  SELECT DISTINCT ON (\"TeamID\", \"Annata\", \"NumeroMaglia\", \"Nome\", RuoloEffettivo)\n", "    \"TeamID\", \n", "    \"Anna<PERSON>\", \n", "    \"NumeroMaglia\", \n", "    \"Nome\", \n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    \"Cognome\" AS cognome_dominante\n", "  FROM gruppi\n", "  ORDER BY \"TeamID\", \"Annata\", \"NumeroMaglia\", \"Nome\", RuoloEffettivo, freq DESC\n", ")\n", "UPDATE players_each_game AS p\n", "SET \"Cognome\" = c.cognome_dominante\n", "FROM cognome_piu_frequente c\n", "WHERE p.\"TeamID\" = c.\"TeamID\"\n", "  AND p.\"<PERSON><PERSON>\" = c.\"<PERSON><PERSON>\"\n", "  AND p.\"NumeroMaglia\" = c.\"NumeroMaglia\"\n", "  AND p.\"Nome\" = c.\"Nome\"\n", "  AND COALESCE(\"RuoloCal<PERSON>\", \"Ruolo\") = c.<PERSON><PERSON>\n", "  AND p.\"RuoloCalc\" <> 0\n", "  AND p.\"Cognome\" IS DISTINCT FROM c.cognome_dominante\n", "\n", "RETURNING *;\n", "\"\"\")\n", "results = cur.fetchall()\n", "print(results)\n", "conn.commit()\n", "\n", "\n", "\n", "#Se un gruppo ha stesso TeamID, Annata, NumeroMaglia, Cognome, (Ruolo o RuoloCalc) allora metto il nome più frequente (a parte se RuoloCalc è 0, in questo caso non li raggruppo)\n", "cur.execute(\"\"\"\n", "WITH gruppi AS (\n", "  SELECT \n", "    \"TeamID\", \n", "    \"Anna<PERSON>\", \n", "    \"NumeroMaglia\", \n", "    \"Cognome\",\n", "    COALESCE(\"Ruolo\", \"RuoloCalc\") AS RuoloEffettivo,   --Metto prima Ruolo di RuoloCalc così prende prima Ruolo\n", "    \"Nome\",\n", "    COUNT(*) AS freq\n", "  FROM players_each_game\n", "  WHERE \"RuoloCalc\" <> 0\n", "  GROUP BY \"TeamID\", \"Annata\", \"NumeroMaglia\", \"Cognome\", COALESCE(\"Ruolo\", \"RuoloCalc\"), \"Nome\"\n", "),\n", "nome_piu_frequente AS (\n", "  SELECT DISTINCT ON (\"TeamID\", \"Annata\", \"NumeroMaglia\", \"Cognome\", RuoloEffettivo)\n", "    \"TeamID\", \n", "    \"Anna<PERSON>\", \n", "    \"NumeroMaglia\", \n", "    \"Cognome\", \n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    \"Nome\" AS nome_dominante\n", "  FROM gruppi\n", "  ORDER BY \"TeamID\", \"Annata\", \"NumeroMaglia\", \"Cognome\", RuoloEffettivo, freq DESC\n", ")\n", "UPDATE players_each_game AS p\n", "SET \"Nome\" = n.nome_dominante\n", "FROM nome_piu_frequente n\n", "WHERE p.\"TeamID\" = n.\"TeamID\"\n", "  AND p.\"<PERSON><PERSON>\" = n.\"<PERSON><PERSON>\"\n", "  AND p.\"NumeroMaglia\" = n.\"NumeroMaglia\"\n", "  AND p.\"Cognome\" = n.\"Cognome\"\n", "  AND COALESCE(p.\"<PERSON><PERSON><PERSON>\", p.\"RuoloCal<PERSON>\") = n.<PERSON><PERSON><PERSON>\n", "  AND p.\"RuoloCalc\" <> 0\n", "  AND p.\"Nome\" IS DISTINCT FROM n.nome_dominante\n", "\n", "RETURNING *;\n", "\"\"\")\n", "results = cur.fetchall()\n", "print(results)\n", "conn.commit()\n", "\n", "\n", "cur.execute(\"\"\"\n", "WITH gruppi AS (\n", "  SELECT \n", "    \"TeamID\", \n", "    \"Anna<PERSON>\", \n", "    \"NumeroMaglia\", \n", "    \"Cognome\",\n", "    COALESCE(\"RuoloCalc\", \"Ruolo\") AS RuoloEffettivo,   --Metto prima RuoloCalc di Ruolo così prende prima RuoloCalc\n", "    \"Nome\",\n", "    COUNT(*) AS freq\n", "  FROM players_each_game\n", "  WHERE \"RuoloCalc\" <> 0\n", "  GROUP BY \"TeamID\", \"Annata\", \"NumeroMaglia\", \"Cognome\", COALESCE(\"RuoloCalc\", \"Ruolo\"), \"Nome\"\n", "),\n", "nome_piu_frequente AS (\n", "  SELECT DISTINCT ON (\"TeamID\", \"Annata\", \"NumeroMaglia\", \"Cognome\", RuoloEffettivo)\n", "    \"TeamID\", \n", "    \"Anna<PERSON>\", \n", "    \"NumeroMaglia\", \n", "    \"Cognome\", \n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "    \"Nome\" AS nome_dominante\n", "  FROM gruppi\n", "  ORDER BY \"TeamID\", \"Annata\", \"NumeroMaglia\", \"Cognome\", RuoloEffettivo, freq DESC\n", ")\n", "UPDATE players_each_game AS p\n", "SET \"Nome\" = n.nome_dominante\n", "FROM nome_piu_frequente n\n", "WHERE p.\"TeamID\" = n.\"TeamID\"\n", "  AND p.\"<PERSON><PERSON>\" = n.\"<PERSON><PERSON>\"\n", "  AND p.\"NumeroMaglia\" = n.\"NumeroMaglia\"\n", "  AND p.\"Cognome\" = n.\"Cognome\"\n", "  AND COALESCE(\"RuoloCal<PERSON>\", \"Ruolo\") = n.<PERSON><PERSON><PERSON>\n", "  AND p.\"RuoloCalc\" <> 0\n", "  AND p.\"Nome\" IS DISTINCT FROM n.nome_dominante\n", "\n", "RETURNING *;\n", "\"\"\")\n", "results = cur.fetchall()\n", "print(results)\n", "conn.commit()\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Raggruppa le righe per No<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> (se Ruolo diverso da 0)\n", "\n", "Se le righe con Annata X hanno tutte lo stesso TeamID, e le righe con Annata X+1 hanno tutte un solo (ALTRO) TeamID, e i due gruppi hanno entrambi almeno 5 righe, allora significa che è lo stesso giocatore che ha cambiato squadra, e quindi unisco i PlayerID delle righe con Annata X e X+1.\n", "\n", "(stessa cosa per RuoloCalc, se diverso da 0)\n", "\n", "(è un problema solo se in cambionato un giocatore smette di giocare nel campionato, e l'annata dopo un giocatore nuovo con lo stesso nome e cognome e ruolo, inizia a giocare in un altro team)\n"]}, {"cell_type": "code", "execution_count": 193, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["28 righe aggiornate.\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 4062, 2022, 6408, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 4062, 2022, 6408, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 4062, 2022, 6408, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 4062, 2022, 6408, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 4062, 2022, 6408, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 4062, 2022, 6408, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 4062, 2022, 6408, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 4062, 2022, 6408, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 4062, 2022, 6408, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 4062, 2022, 6408, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 4062, 2022, 6408, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 4062, 2022, 6408, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 4062, 2022, 6408, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 4062, 2022, 6408, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 4062, 2022, 6408, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 4062, 2022, 6408, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 4062, 2022, 6408, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 4062, 2022, 6408, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 4062, 2022, 6408, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 4062, 2022, 6408, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 4062, 2022, 6408, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 4062, 2022, 6408, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 1475, 2022, 6366, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 1475, 2022, 6366, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 1475, 2022, 6366, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 1475, 2022, 6366, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 1475, 2022, 6366, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 1475, 2022, 6366, 2023)\n"]}], "source": ["#Ora eseguo l'UPDATE in automatico (usando Ruolo)\n", "cur.execute(\"\"\"\n", "WITH grouped_players AS (\n", "  SELECT\n", "    \"Nome\", \"Cognome\", \"<PERSON><PERSON><PERSON>\", \"Anna<PERSON>\", \"TeamID\", \"PlayerID\",\n", "    COUNT(*) AS num_righe\n", "  FROM players_each_game\n", "  WHERE \"Ruolo\" <> 0\n", "  GROUP BY \"Nome\", \"Cognome\", \"R<PERSON><PERSON>\", \"Anna<PERSON>\", \"TeamID\", \"PlayerID\"\n", "),\n", "accoppiamenti_valide AS (\n", "  SELECT\n", "    g1.\"Nome\", g1.\"Cognome\", g1.\"<PERSON><PERSON><PERSON>\",\n", "    g1.\"<PERSON><PERSON>\" AS annata_precedente,\n", "    g2.\"Annata\" AS annata_successiva,\n", "    g1.\"TeamID\" AS team_old, g2.\"TeamID\" AS team_new,\n", "    g1.\"PlayerID\" AS player_old, g2.\"PlayerID\" AS player_new\n", "  FROM grouped_players g1\n", "  JOIN grouped_players g2\n", "    ON g1.\"Nome\" = g2.\"Nome\"\n", "   AND g1.\"Cognome\" = g2.\"Cognome\"\n", "   AND g1.\"Ruolo\" = g2.\"Ruolo\"\n", "   AND g2.\"<PERSON><PERSON>\" = g1.\"Anna<PERSON>\" + 1\n", "  WHERE g1.\"TeamID\" <> g2.\"TeamID\"\n", "    AND g1.\"PlayerID\" <> g2.\"PlayerID\"\n", "    AND g1.num_righe >= 4  --quante righe deve avere il gruppo con annata X\n", "    AND g2.num_righe >= 4  --quante righe deve avere il gruppo con annata X+1\n", "),\n", "riga_modificabili AS (\n", "  SELECT p.*\n", "  FROM players_each_game p\n", "  JOIN accoppiamenti_valide a\n", "    ON p.\"Nome\" = a.\"Nome\"\n", "   AND p.\"Cognome\" = a.\"Cognome\"\n", "   AND p.\"Ruolo\" = a.\"Ruolo\"\n", "   AND p.\"<PERSON><PERSON>\" = a.annata_successiva\n", "   AND p.\"PlayerID\" = a.player_new\n", ")\n", "UPDATE players_each_game p\n", "SET \"PlayerID\" = a.player_old\n", "FROM accoppiamenti_valide a\n", "WHERE p.\"Nome\" = a.\"Nome\"\n", "  AND p.\"Cognome\" = a.\"Cognome\"\n", "  AND p.\"Ruolo\" = a.\"Ruolo\"\n", "  AND p.\"<PERSON><PERSON>\" = a.annata_successiva\n", "  AND p.\"PlayerID\" = a.player_new\n", "RETURNING\n", "    p.\"Nome\", \n", "    p.\"Cognome\", \n", "    a.player_old,\n", "    a.annata_precedente,\n", "    a.player_new,\n", "    a.annata_successiva;\n", "\"\"\")\n", "\n", "results = cur.fetchall()\n", "print(f\"{len(results)} righe aggiornate.\")\n", "for row in results:\n", "    print(row)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Quando fai un cur.execute() senza fare conn.commit(), puoi ancora annullare la transazione con conn.rollback(), e comunque se non fai conn.commit() dopo un tot di tempo viene fatto un rollback in automatico."]}, {"cell_type": "code", "execution_count": 194, "metadata": {}, "outputs": [], "source": ["#Se l'output della cella sopra va bene, allora faccio commit, altrimenti faccio rollback\n", "conn.commit()"]}, {"cell_type": "code", "execution_count": 195, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["16 righe aggiornate.\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 1475, 2022, 6366, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 1475, 2022, 6366, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 1475, 2022, 6366, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 1475, 2022, 6366, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 1475, 2022, 6366, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 1475, 2022, 6366, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 1475, 2022, 6366, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 1475, 2022, 6366, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 1475, 2022, 6366, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 1475, 2022, 6366, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 1475, 2022, 6366, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 1475, 2022, 6366, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 1475, 2022, 6366, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 1475, 2022, 6366, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 1475, 2022, 6366, 2023)\n", "('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 1475, 2022, 6366, 2023)\n"]}], "source": ["#Ora eseguo l'UPDATE in automatico (usando RuoloCalc)\n", "cur.execute(\"\"\"\n", "WITH grouped_players AS (\n", "  SELECT\n", "    \"Nome\", \"Cognome\", \"RuoloCalc\", \"Annata\", \"TeamID\", \"PlayerID\",\n", "    COUNT(*) AS num_righe\n", "  FROM players_each_game\n", "  WHERE \"RuoloCalc\" <> 0\n", "  GROUP BY \"Nome\", \"Cognome\", \"RuoloCalc\", \"Annata\", \"TeamID\", \"PlayerID\"\n", "),\n", "accoppiamenti_valide AS (\n", "  SELECT\n", "    g1.\"Nome\", g1.\"Cognome\", g1.\"RuoloCalc\",\n", "    g1.\"<PERSON><PERSON>\" AS annata_precedente,\n", "    g2.\"Annata\" AS annata_successiva,\n", "    g1.\"TeamID\" AS team_old, g2.\"TeamID\" AS team_new,\n", "    g1.\"PlayerID\" AS player_old, g2.\"PlayerID\" AS player_new\n", "  FROM grouped_players g1\n", "  JOIN grouped_players g2\n", "    ON g1.\"Nome\" = g2.\"Nome\"\n", "   AND g1.\"Cognome\" = g2.\"Cognome\"\n", "   AND g1.\"RuoloCalc\" = g2.\"RuoloCalc\"\n", "   AND g2.\"<PERSON><PERSON>\" = g1.\"Anna<PERSON>\" + 1\n", "  WHERE g1.\"TeamID\" <> g2.\"TeamID\"\n", "    AND g1.\"PlayerID\" <> g2.\"PlayerID\"\n", "    AND g1.num_righe >= 4  --quante righe deve avere il gruppo con annata X   (Se metto 1 ne corregge di più)\n", "    AND g2.num_righe >= 4  --quante righe deve avere il gruppo con annata X+1 (Se metto 1 ne corregge di più)\n", "),\n", "riga_modificabili AS (\n", "  SELECT p.*\n", "  FROM players_each_game p\n", "  JOIN accoppiamenti_valide a\n", "    ON p.\"Nome\" = a.\"Nome\"\n", "   AND p.\"Cognome\" = a.\"Cognome\"\n", "   AND p.\"RuoloCalc\" = a.\"RuoloCalc\"\n", "   AND p.\"<PERSON><PERSON>\" = a.annata_successiva\n", "   AND p.\"PlayerID\" = a.player_new\n", ")\n", "UPDATE players_each_game p\n", "SET \"PlayerID\" = a.player_old\n", "FROM accoppiamenti_valide a\n", "WHERE p.\"Nome\" = a.\"Nome\"\n", "  AND p.\"Cognome\" = a.\"Cognome\"\n", "  AND p.\"RuoloCalc\" = a.\"RuoloCalc\"\n", "  AND p.\"<PERSON><PERSON>\" = a.annata_successiva\n", "  AND p.\"PlayerID\" = a.player_new\n", "RETURNING\n", "    p.\"Nome\", \n", "    p.\"Cognome\", \n", "    a.player_old,\n", "    a.annata_precedente,\n", "    a.player_new,\n", "    a.annata_successiva;\n", "\"\"\")\n", "\n", "results = cur.fetchall()\n", "print(f\"{len(results)} righe aggiornate.\")\n", "for row in results:\n", "    print(row)"]}, {"cell_type": "code", "execution_count": 196, "metadata": {}, "outputs": [], "source": ["#Se l'output della cella sopra va bene, allora faccio commit, altrimenti faccio rollback\n", "conn.commit()"]}, {"cell_type": "code", "execution_count": 202, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[('<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 6277, 6, 2024, 29, 4), ('<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 6329, 1, 2023, 14, 4), ('<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 17, 1, 2022, 14, 5), ('<PERSON>', '<PERSON><PERSON><PERSON>', 9799, 4000, 2023, 6, 5), ('<PERSON><PERSON>', '<PERSON><PERSON>', 6339, 2, 2023, 7, 3), ('<PERSON><PERSON><PERSON> ad<PERSON>', '<PERSON><PERSON>', 11177, 14, 2024, 3, 2), ('<PERSON>', '<PERSON><PERSON>', 192, 2, 2022, 9, 3), ('<PERSON>', '<PERSON><PERSON><PERSON>', 2348, 7003, 2022, 4, 2), ('<PERSON>', '<PERSON><PERSON><PERSON>', 2356, 7003, 2022, 16, 4), ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 383, 4, 2022, 16, 2), ('<PERSON>', '<PERSON><PERSON>', 16, 1, 2022, 10, 3), ('<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 366, 6, 2022, 7, 2), ('<PERSON>', '<PERSON><PERSON><PERSON>', 4888, 4002, 2022, 11, 2), ('<PERSON><PERSON>', '<PERSON><PERSON>', 9809, 4000, 2023, 99, 4), ('<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 11235, 5, 2024, 9, 2), ('<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 7862, 1002, 2023, 4, 4), ('<PERSON>', '<PERSON><PERSON>', 61, 1, 2022, 11, 4)]\n"]}], "source": ["#Mostro i giocatori con stesso Nome e Cognome, ma PlayerID diverso\n", "cur.execute(\"\"\"\n", "SELECT DISTINCT ON (\"Nome\", \"Cognome\")\n", "    \"Nome\",\n", "    \"Cognome\",\n", "    \"PlayerID\",\n", "    \"TeamID\",\n", "    \"Anna<PERSON>\",\n", "    \"NumeroMaglia\",\n", "    \"RuoloCalc\"\n", "FROM players_each_game\n", "WHERE (\"Nome\", \"Cognome\") IN (\n", "    SELECT \"Nome\", \"Cognome\"\n", "    FROM players_each_game\n", "    GROUP BY \"Nome\", \"Cognome\"\n", "    HAVING COUNT(DISTINCT \"PlayerID\") > 1\n", ")\n", "\n", "\"\"\")\n", "results = cur.fetchall()\n", "print(results)\n", "\n", "#Probabilmente questi li riesco a unificare tutti se quando unifico gli ID di giocatori tra Annate consecutive con due team distinti, invece che mettere numerosità minima 4, metto 1, li corregge tutti."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Controllo a mano che questi giocatori siano lo stesso (controllando che quel giocatore abbia effettivamente giocato in quelle squadre, e controllo il ruolo, es https://volleybox.net/it/aleksanda<PERSON>-<PERSON><PERSON><PERSON>-p6180/clubs), modificando df_fixed\n", "\n", "Gli ID di 6 lettere sono quelli della FIVB, quindi preferisci quelli (https://www.fivb.com/players/players-database/)"]}, {"cell_type": "code", "execution_count": 203, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[('<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 3882, 1, 14, 2022), ('<PERSON>', '<PERSON><PERSON>', 10654, 1, 22, 2023), ('<PERSON>', '<PERSON><PERSON><PERSON>', 25, 1, 28, 2022), ('<PERSON><PERSON><PERSON> raul', '<PERSON>', 177, 3, 11, 2024), ('<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 6346, 4, 4, 2024), ('<PERSON>', '<PERSON><PERSON><PERSON>', 1137, 6, 19, 2022), ('<PERSON>', '<PERSON><PERSON><PERSON>', 6268, 6, 19, 2023), ('<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 9441, 7, 3, 2023), ('<PERSON><PERSON> alin', '<PERSON>', 350, 7, 14, 2022), ('<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 11173, 10, 21, 2024), ('<PERSON>', '<PERSON><PERSON>', 5, 11, 6, 2022), ('<PERSON> g<PERSON>', '<PERSON><PERSON>', 11734, 11, 14, 2024), ('<PERSON><PERSON>', '<PERSON><PERSON>', 156, 11, 15, 2022), ('<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 79, 11, 18, 2022), ('<PERSON><PERSON>', '<PERSON>', 10306, 11, 19, 2023), ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 80, 11, 20, 2022), ('<PERSON>', '<PERSON><PERSON>', 7354, 12, 6, 2023), ('<PERSON>', '<PERSON><PERSON><PERSON>', 11133, 12, 12, 2024), ('<PERSON><PERSON>', '<PERSON><PERSON>', 51, 14, 3, 2023), ('<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 11177, 14, 3, 2024), ('<PERSON>', '<PERSON><PERSON><PERSON>', 391, 14, 4, 2022), ('<PERSON>', '<PERSON><PERSON><PERSON>', 12016, 14, 4, 2024), ('<PERSON>', '<PERSON><PERSON><PERSON>ic', 7686, 14, 8, 2023), ('Gianluca', 'Rossi', 2211, 14, 10, 2022), ('Gioele adeola', 'Taiwo', 49, 14, 12, 2022), ('Gabriele', 'Mariani', 51, 14, 14, 2022), ('Alessandro', 'Menazza', 4274, 15, 4, 2022), ('Alessandro', 'Menazza', 4274, 15, 8, 2022), ('Andrea', 'Zanotti', 268, 15, 8, 2023), ('Marco', 'Valbusa', 11948, 15, 8, 2024), ('Janusz', 'Marcin', 5115, 9000, 5, 2022), ('Twan', 'Wiltenburg', 4896, 9000, 7, 2022), ('Huber', 'Norbert', 5137, 9000, 99, 2022)]\n"]}], "source": ["#Mostro i giocatori con stesso TeamID, NumeroMaglia, Annata, ma PlayerID diverso (nella VIEW cerca quel TeamID and NumeroMaglia e Annata)\n", "cur.execute(\"\"\"\n", "SELECT DISTINCT ON (\"TeamID\", \"NumeroMaglia\", \"Annata\")\n", "    \"Nome\",\n", "    \"Cognome\",\n", "    \"PlayerID\",\n", "    \"TeamID\",\n", "    \"NumeroMaglia\",\n", "    \"Annata\"\n", "FROM players_each_game\n", "WHERE (\"TeamID\", \"NumeroMaglia\", \"Annata\") IN (\n", "    SELECT \"TeamID\", \"NumeroMaglia\", \"Annata\"\n", "    FROM players_each_game\n", "    GROUP BY \"TeamID\", \"NumeroMaglia\", \"Annata\"\n", "    HAVING COUNT(DISTINCT \"PlayerID\") > 1\n", "    \n", ")\n", "ORDER BY \"TeamID\", \"NumeroMaglia\", \"Annata\"\n", "\"\"\")\n", "results = cur.fetchall()\n", "print(results)"]}, {"cell_type": "code", "execution_count": 204, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Aggiornati 0 PlayerID\n"]}], "source": ["#Rilancio la funzione\n", "unifica_PlayerID_players_each_game()"]}, {"cell_type": "code", "execution_count": 205, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[('<PERSON><PERSON><PERSON> raul', '<PERSON>', 177, 3, 11, 2024), ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 11666, 14, 3, 2024), ('<PERSON><PERSON><PERSON> ad<PERSON>', '<PERSON><PERSON>', 11177, 14, 3, 2024), ('<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 48, 14, 10, 2022), ('<PERSON><PERSON><PERSON>', 'Wiltenburg', 5117, 9000, 7, 2022), ('<PERSON><PERSON>', 'Wiltenburg', 4896, 9000, 7, 2022)]\n"]}], "source": ["#Mostro i giocatori con stesso TeamID, NumeroMaglia, <PERSON><PERSON>, ma PlayerID diverso\n", "cur.execute(\"\"\"\n", "SELECT DISTINCT a.\"Nome\", a.\"Cognome\", a.\"PlayerID\", a.\"TeamID\", a.\"NumeroMaglia\", a.\"Annata\"\n", "FROM players_each_game a\n", "JOIN players_each_game b\n", "  ON a.\"TeamID\" = b.\"TeamID\"\n", "  AND a.\"NumeroMaglia\" = b.\"NumeroMaglia\"\n", "  AND a.\"<PERSON><PERSON>\" = b.\"<PERSON><PERSON>\"\n", "  AND a.\"PlayerID\" <> b.\"PlayerID\"\n", "  AND (\n", "    -- Normalizziamo Nome\n", "    lower(translate(a.\"Nome\", 'ŠšŽžÀÁÂÃÄÅàáâãäåÈÉÊËèéêëÌÍÎÏìíîïÒÓÔÕÖØòóôõöøÙÚÛÜùúûüÇçÑñ', \n", "                             'SsZzAAAAAAaaaaaaEEEEeeeeIIIIiiiiOOOOOOooooooUUUUuuuuCcNn')) LIKE\n", "      lower(translate(b.\"Nome\", 'ŠšŽžÀÁÂÃÄÅàáâãäåÈÉÊËèéêëÌÍÎÏìíîïÒÓÔÕÖØòóôõöøÙÚÛÜùúûüÇçÑñ',\n", "                               'SsZzAAAAAAaaaaaaEEEEeeeeIIIIiiiiOOOOOOooooooUUUUuuuuCcNn')) || '%' \n", "    OR\n", "    lower(translate(a.\"Nome\", 'ŠšŽžÀÁÂÃÄÅàáâãäåÈÉÊËèéêëÌÍÎÏìíîïÒÓÔÕÖØòóôõöøÙÚÛÜùúûüÇçÑñ', \n", "                             'SsZzAAAAAAaaaaaaEEEEeeeeIIIIiiiiOOOOOOooooooUUUUuuuuCcNn')) LIKE\n", "      '%' || lower(translate(b.\"Nome\", 'ŠšŽžÀÁÂÃÄÅàáâãäåÈÉÊËèéêëÌÍÎÏìíîïÒÓÔÕÖØòóôõöøÙÚÛÜùúûüÇçÑñ',\n", "                               'SsZzAAAAAAaaaaaaEEEEeeeeIIIIiiiiOOOOOOooooooUUUUuuuuCcNn'))\n", "\n", "    OR\n", "\n", "    -- Normalizziamo Cognome\n", "    lower(translate(a.\"Cognome\", 'ŠšŽžÀÁÂÃÄÅàáâãäåÈÉÊËèéêëÌÍÎÏìíîïÒÓÔÕÖØòóôõöøÙÚÛÜùúûüÇçÑñ',\n", "                                'SsZzAAAAAAaaaaaaEEEEeeeeIIIIiiiiOOOOOOooooooUUUUuuuuCcNn')) LIKE\n", "      lower(translate(b.\"Cognome\", 'ŠšŽžÀÁÂÃÄÅàáâãäåÈÉÊËèéêëÌÍÎÏìíîïÒÓÔÕÖØòóôõöøÙÚÛÜùúûüÇçÑñ',\n", "                                  'SsZzAAAAAAaaaaaaEEEEeeeeIIIIiiiiOOOOOOooooooUUUUuuuuCcNn')) || '%' \n", "    OR\n", "    lower(translate(a.\"Cognome\", 'ŠšŽžÀÁÂÃÄÅàáâãäåÈÉÊËèéêëÌÍÎÏìíîïÒÓÔÕÖØòóôõöøÙÚÛÜùúûüÇçÑñ',\n", "                                'SsZzAAAAAAaaaaaaEEEEeeeeIIIIiiiiOOOOOOooooooUUUUuuuuCcNn')) LIKE\n", "      '%' || lower(translate(b.\"Cognome\", 'ŠšŽžÀÁÂÃÄÅàáâãäåÈÉÊËèéêëÌÍÎÏìíîïÒÓÔÕÖØòóôõöøÙÚÛÜùúûüÇçÑñ',\n", "                                  'SsZzAAAAAAaaaaaaEEEEeeeeIIIIiiiiOOOOOOooooooUUUUuuuuCcNn'))\n", "  )\n", "ORDER BY a.\"TeamID\", a.\"NumeroMaglia\", a.\"Annata\";\n", "\n", "\n", "\"\"\")\n", "results = cur.fetchall()\n", "print(results)\n", "\n", "#In questi posso correggere il nome o il cognome nel dizionario iniziale, oppure correggere solo uno dei due. Se non sono stati corretti perchè la numerosità è bassissima, li lascio co<PERSON>, probabilmente si correggeranno da soli quendo quel giocatore aumenterà i suoi record."]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON> le colonne di players_each_game"]}, {"cell_type": "code", "execution_count": 207, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[(1,), (5,), (4,), (2,), (6,), (0,), (3,)]\n"]}], "source": ["#Stampo i valori unici di Ruolo\n", "cur.execute(\"\"\"\n", "SELECT DISTINCT\n", "    \"RuoloCalc\"\n", "FROM players_each_game\n", "\"\"\")\n", "results = cur.fetchall()\n", "print(results)"]}, {"cell_type": "code", "execution_count": 216, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hovertemplate": "R<PERSON>lo calcolato=%{x}<br>Numero di Occorrenze=%{y}<extra></extra>", "legendgroup": "", "marker": {"color": "#636efa", "pattern": {"shape": ""}}, "name": "", "orientation": "v", "showlegend": false, "textposition": "auto", "texttemplate": "%{y}", "type": "bar", "x": {"bdata": "AAECAwQFBg==", "dtype": "i1"}, "xaxis": "x", "y": {"bdata": "XgFdB6gPPgcQEEgIAgA=", "dtype": "i2"}, "yaxis": "y"}], "layout": {"bargap": 0.15, "barmode": "relative", "hoverlabel": {"align": "left", "bgcolor": "white", "font": {"size": 12}}, "legend": {"tracegroupgap": 0}, "showlegend": false, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "Numero di partite disputate per RuoloCalc"}, "xaxis": {"anchor": "y", "domain": [0, 1], "title": {"text": "<PERSON><PERSON><PERSON> calcolato"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "title": {"text": "Frequenza"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["import plotly.express as px  # Aggiungo l'import di plotly.express\n", "\n", "df_players_each_game = pd.read_sql_query(\"SELECT * FROM players_each_game\", engine)\n", "\n", "# Raggruppa i dati per numero di maglia e crea la lista dei giocatori\n", "ruolo_details = df_players_each_game.groupby('RuoloCalc').agg({\n", "    'Nome': list,\n", "    'Cognome': list,\n", "}).reset_index()\n", "\n", "# <PERSON><PERSON> le frequenze\n", "ruolo_details['Frequenza'] = ruolo_details['Nome'].apply(len)\n", "\n", "# Crea il grafico a barre\n", "fig = px.bar(ruolo_details, \n", "             x='RuoloCalc', \n", "             y='Frequenza',\n", "             title='Numero di partite disputate per RuoloCalc',\n", "             labels={'RuoloCalc': 'Ruolo calcolato', \n", "                    'Frequenza': 'Numero di Occorrenze'},\n", "             text_auto=True)\n", "\n", "fig.update_layout(\n", "    xaxis_title=\"Ruolo calcolato\",\n", "    yaxis_title=\"Frequenza\",\n", "    bargap=0.15,  #distanza tra le barre\n", "    showlegend=False,\n", "    hoverlabel=dict(\n", "        bgcolor=\"white\",\n", "        font_size=12,\n", "        align=\"left\"\n", "    )\n", ")\n", "\n", "# Mostra il grafico\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Visualizzo i valori unici di NumeroMaglia"]}, {"cell_type": "code", "execution_count": 217, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"customdata": [["<PERSON><PERSON>"], ["<PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON>isi<PERSON><br><PERSON><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><PERSON><br><PERSON> moss<PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON> Visic<br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON> moss<PERSON><br>Woo<PERSON><br><PERSON><br>Petar Visic<br><PERSON><PERSON><br>Petar Visic<br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON> mossa <PERSON>de<br><PERSON> mossa <PERSON><br>Tsi<PERSON><PERSON><PERSON><br>Petar Visic<br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><PERSON><br><PERSON> moss<PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON>l<br>Petar Visic<br>Davide Gardini<br>Petar Visic<br><PERSON> Cortesia<br>Tsimafei Zhukouski<br>Luka Basic<br>Luka Basic<br>Luka Basic<br>Aidan Zingel<br>Saverio De santis<br>Andrea Santangelo<br>Matey Kaziyski<br>Bruno mossa Rezende<br>Davide Gardini<br>Woojin Lee<br>Andrea Santangelo<br>Bruno mossa Rezende<br>Bruno mossa Rezende<br>Woojin Lee<br>Tommaso Stefani<br>Petar Visic<br>Barthélémy Chinenyeze<br>Bruno mossa Rezende<br>Aidan Zingel<br>Davide Gardini<br>Aidan Zingel<br>Bruno mossa Rezende<br>Bruno mossa Rezende<br>Andrea Santangelo<br>Lorenzo Cortesia<br>Bruno mossa Rezende<br>Matey Kaziyski<br>Barthélémy Chinenyeze<br>Manuel Zlatanov<br>Woojin Lee<br>Luka Basic<br>Davide Gardini<br>Aidan Zingel<br>Saverio De santis<br>Woojin Lee<br>Lorenzo Cortesia<br>Bruno mossa Rezende<br>Bruno mossa Rezende<br>Lorenzo Cortesia<br>Andrea Santangelo<br>Barthélémy Chinenyeze<br>Luka Basic<br>Luka Basic<br>Gabriel Garcia<br>Bruno mossa Rezende<br>Lorenzo Cortesia<br>Petar Visic<br>Davide Gardini<br>Gabriel Garcia<br>Matey Kaziyski<br>Davide Gardini<br>Barthélémy Chinenyeze<br>Aidan Zingel<br>Matey Kaziyski<br>Luka Basic<br>Lorenzo Cortesia<br>Luka Basic<br>Matey Kaziyski<br>Petar Visic<br>Davide Gardini<br>Gabriel Garcia<br>Tsimafei Zhukouski<br>Bruno mossa Rezende<br>Barthélémy Chinenyeze<br>Petar Visic<br>Alessandro Piccinelli<br>Gabriel Garcia<br>Urpo Sivula<br>Tsimafei Zhukouski<br>Andrea Santangelo<br>Petar Visic<br>Davide Gardini<br>Aidan Zingel<br>Aidan Zingel<br>Davide Gardini<br>Matey Kaziyski<br>Bruno mossa Rezende<br>Ulas Kiyak<br>Alessandro Piccinelli<br>Matey Kaziyski<br>Gabriel Garcia<br>Aidan Zingel<br>Aidan Zingel<br>James Weir<br>Andrea Santangelo<br>Manuel Zlatanov<br>Luka Basic<br>Joseph Worsley<br>Bruno mossa Rezende<br>Matey Kaziyski<br>Aidan Zingel<br>Aidan Zingel<br>Petar Visic<br>Saverio De santis<br>Barthélémy Chinenyeze<br>Lorenzo Cortesia<br>Luka Basic<br>Barthélémy Chinenyeze<br>Aidan Zingel<br>Gabriel Garcia<br>Lorenzo Cortesia<br>Luka Basic<br>Barthélémy Chinenyeze<br>Lorenzo Cortesia<br>Lorenzo Cortesia<br>Bruno mossa Rezende<br>Gabriel Garcia<br>Adam Kowalski<br>Saverio De santis<br>Petar Visic<br>Matey Kaziyski<br>Tommaso Stefani<br>Bruno mossa Rezende<br>Aidan Zingel<br>Davide Gardini<br>Saverio De santis<br>Luka Basic<br>Davide Gardini<br>Davide Gardini<br>Bruno mossa Rezende<br>Ulas Kiyak<br>Aidan Zingel<br>Barthélémy Chinenyeze<br>Davide Gardini<br>Tommaso Stefani<br>Primoz Mejal<br>Gabriel Garcia<br>Ulas Kiyak<br>Davide Gardini<br>Giulio Pinali<br>Bruno mossa Rezende<br>Petar Visic<br>Giulio Pinali<br>Aidan Zingel<br>Alessandro Piccinelli<br>Andrea Santangelo<br>Aidan Zingel<br>Aidan Zingel<br>Bruno mossa Rezende<br>Aidan Zingel<br>Dennis Deroey<br>Davide Gardini<br>Aidan Zingel<br>Tommaso Stefani<br>Lorenzo Cortesia<br>Andrea Santangelo<br>Joseph Worsley<br>Luka Basic<br>Lorenzo Cortesia<br>Woojin Lee<br>Luka Basic<br>Davide Gardini<br>Bruno mossa Rezende<br>Saverio De santis<br>Barthélémy Chinenyeze<br>Dennis Deroey<br>Barthélémy Chinenyeze<br>Lorenzo Cortesia<br>Matey Kaziyski<br>Bruno mossa Rezende<br>Barthélémy Chinenyeze<br>Lorenzo Cortesia<br>Bruno mossa Rezende<br>Matey Kaziyski<br>Tsimafei Zhukouski<br>Woojin Lee<br>Petar Visic<br>Lorenzo Cortesia<br>Bruno mossa Rezende<br>Matey Kaziyski<br>Petar Visic<br>Aidan Zingel<br>Saverio De santis<br>Luka Basic<br>Bruno mossa Rezende<br>Gabriel Garcia<br>Tommaso Stefani<br>Andrea Santangelo<br>Barthélémy Chinenyeze<br>Bruno mossa Rezende<br>Tommaso Stefani<br>Tommaso Stefani<br>Gabriel Garcia<br>Barthélémy Chinenyeze<br>Tommaso Stefani<br>Joseph Worsley<br>Bruno mossa Rezende<br>Luka Basic<br>Petar Visic<br>Woojin Lee<br>Davide Gardini<br>Davide Gardini<br>Petar Visic<br>Gabriel Garcia<br>Matey Kaziyski<br>Luka Basic<br>Matey Kaziyski<br>Woojin Lee<br>Aidan Zingel<br>Alessandro Piccinelli<br>Matey Kaziyski<br>Bruno mossa Rezende<br>Joseph Worsley<br>Tsimafei Zhukouski<br>Tommaso Stefani<br>Matey Kaziyski<br>Barthélémy Chinenyeze<br>Matey Kaziyski<br>Dennis Deroey<br>Aidan Zingel<br>Barthélémy Chinenyeze<br>Matey Kaziyski<br>Saverio De santis<br>Giulio Pinali<br>Matey Kaziyski<br>Aidan Zingel<br>Joseph Worsley<br>Lorenzo Cortesia<br>Aidan Zingel<br>Bruno mossa Rezende<br>Petar Visic<br>Tsimafei Zhukouski<br>Alessandro Piccinelli<br>Matey Kaziyski<br>Tsimafei Zhukouski<br>Bruno mossa Rezende<br>Alessandro Piccinelli<br>Barthélémy Chinenyeze<br>Davide Gardini<br>Barthélémy Chinenyeze<br>Matey Kaziyski<br>Petar Visic<br>Barthélémy Chinenyeze<br>Aidan Zingel<br>Davide Gardini<br>Bruno mossa Rezende<br>Matey Kaziyski<br>Barthélémy Chinenyeze<br>Luka Basic<br>Woojin Lee<br>Woojin Lee<br>Bruno mossa Rezende<br>Luka Basic<br>Luka Basic<br>Davide Gardini<br>Tsimafei Zhukouski<br>Barthélémy Chinenyeze<br>Giulio Pinali<br>Luka Basic<br>Matey Kaziyski<br>Barthélémy Chinenyeze<br>Davide Gardini<br>Barthélémy Chinenyeze<br>Gabriel Garcia<br>Petar Visic<br>Giulio Pinali<br>Tommaso Stefani<br>Joseph Worsley<br>Petar Visic<br>Lorenzo Cortesia<br>Luka Basic<br>Tommaso Stefani<br>Petar Visic<br>Matey Kaziyski<br>Bruno mossa Rezende<br>Tsimafei Zhukouski<br>Giulio Pinali<br>Bruno mossa Rezende<br>Luka Basic<br>Aidan Zingel<br>Saverio De santis<br>Alessandro Piccinelli<br>Luka Basic<br>Matey Kaziyski<br>Matey Kaziyski<br>Alessandro Piccinelli<br>Tommaso Stefani<br>Alessandro Piccinelli<br>Davide Gardini<br>Luka Basic<br>Aidan Zingel<br>Matey Kaziyski<br>Tommaso Stefani<br>Woojin Lee<br>Davide Gardini<br>Bruno mossa Rezende<br>Andrea Santangelo<br>Lorenzo Cortesia<br>Gabriel Garcia<br>Petar Visic<br>Andrea Santangelo<br>Tommaso Stefani<br>Matey Kaziyski<br>Luka Basic<br>Tommaso Stefani<br>Matey Kaziyski<br>Tommaso Stefani<br>Bruno mossa Rezende<br>Barthélémy Chinenyeze<br>Joseph Worsley<br>Alessandro Piccinelli<br>Petar Visic<br>Aidan Zingel<br>Matey Kaziyski<br>Tommaso Stefani<br>Petar Visic<br>Luka Basic<br>Aidan Zingel<br>Tsimafei Zhukouski<br>Bruno mossa Rezende<br>Barthélémy Chinenyeze<br>Luka Basic<br>Woojin Lee<br>Bruno mossa Rezende<br>Dennis Deroey<br>Bruno mossa Rezende<br>Bruno mossa Rezende<br>Tsimafei Zhukouski<br>Matey Kaziyski<br>Bruno mossa Rezende<br>Bruno mossa Rezende<br>Andrea Santangelo<br>Aidan Zingel<br>Tommaso Stefani<br>Aidan Zingel<br>Adam Kowalski<br>Lorenzo Cortesia<br>Bruno mossa Rezende<br>Aidan Zingel<br>Davide Gardini<br>Alessandro Piccinelli<br>Matey Kaziyski<br>Davide Gardini<br>Barthélémy Chinenyeze<br>Tsimafei Zhukouski<br>Matey Kaziyski<br>Matey Kaziyski<br>Lorenzo Cortesia<br>Matey Kaziyski<br>Aidan Zingel<br>Bruno mossa Rezende<br>Aidan Zingel<br>Bruno mossa Rezende<br>Bruno mossa Rezende<br>Lorenzo Cortesia<br>Saverio De santis<br>Davide Gardini<br>Tsimafei Zhukouski<br>Gabriel Garcia<br>Luka Basic<br>Saverio De santis<br>Tsimafei Zhukouski<br>Luka Basic<br>Woojin Lee<br>Dennis Deroey<br>Petar Visic<br>Bruno mossa Rezende<br>Barthélémy Chinenyeze<br>Davide Gardini<br>Bruno mossa Rezende<br>Barthélémy Chinenyeze<br>Luka Basic<br>Bruno mossa Rezende<br>Aidan Zingel<br>Primoz Mejal<br>Davide Gardini<br>Matey Kaziyski<br>Aidan Zingel<br>Davide Gardini<br>Tsimafei Zhukouski<br>Bruno mossa Rezende<br>Aidan Zingel<br>Tommaso Stefani<br>Bruno mossa Rezende<br>Aidan Zingel<br>Matey Kaziyski<br>Matey Kaziyski<br>Davide Gardini<br>Alessandro Piccinelli<br>Alessandro Piccinelli<br>Petar Visic<br>Alessandro Piccinelli<br>Barthélémy Chinenyeze<br>Matey Kaziyski<br>Aidan Zingel<br>Petar Visic<br>Petar Visic<br>Luka Basic<br>Bruno mossa Rezende<br>Matey Kaziyski<br>Gabriel Garcia<br>Bruno mossa Rezende<br>Gabriel Garcia<br>Luka Basic<br>Bruno mossa Rezende<br>Matey Kaziyski<br>Barthélémy Chinenyeze<br>Matey Kaziyski<br>Aidan Zingel<br>Tommaso Stefani<br>Bruno mossa Rezende<br>Petar Visic<br>Woojin Lee<br>Tommaso Stefani<br>Davide Gardini<br>Matey Kaziyski<br>Luka Basic<br>Bruno mossa Rezende<br>Tommaso Stefani<br>Saverio De santis<br>Barthélémy Chinenyeze<br>Gabriel Garcia<br>Bruno mossa Rezende<br>Davide Gardini<br>Bruno mossa Rezende<br>Tsimafei Zhukouski<br>Davide Gardini<br>Alessandro Piccinelli<br>Matey Kaziyski<br>Davide Gardini<br>Giulio Pinali<br>Tommaso Stefani<br>Tsimafei Zhukouski<br>Matey Kaziyski<br>Matey Kaziyski<br>Davide Gardini<br>Matey Kaziyski<br>Matey Kaziyski<br>Aidan Zingel<br>Lorenzo Cortesia<br>Tommaso Stefani<br>Tommaso Stefani<br>Barthélémy Chinenyeze<br>Barthélémy Chinenyeze<br>Aidan Zingel<br>Petar Visic<br>Barthélémy Chinenyeze<br>Barthélémy Chinenyeze<br>Aidan Zingel<br>Andrea Santangelo<br>Luka Basic<br>Bruno mossa Rezende<br>Gabriel Garcia<br>Ulas Kiyak<br>Saverio De santis<br>Barthélémy Chinenyeze<br>Barthélémy Chinenyeze<br>Matey Kaziyski<br>Davide Gardini<br>Tommaso Stefani<br>Bruno mossa Rezende<br>Matey Kaziyski<br>Aidan Zingel<br>Andrea Santangelo<br>Bruno mossa Rezende<br>Tommaso Stefani<br>Bruno mossa Rezende<br>Aidan Zingel<br>Barthélémy Chinenyeze<br>Luka Basic<br>Matey Kaziyski<br>Luka Basic<br>Matey Kaziyski<br>Giulio Pinali<br>Petar Visic<br>Petar Visic<br>Matey Kaziyski<br>Davide Gardini<br>Davide Gardini<br>Matey Kaziyski<br>Davide Gardini<br>Matey Kaziyski<br>Saverio De santis<br>Lorenzo Cortesia<br>Lorenzo Cortesia<br>Giulio Pinali<br>Petar Visic<br>Aidan Zingel<br>Matey Kaziyski<br>Petar Visic<br>Aidan Zingel<br>Aidan Zingel<br>Matey Kaziyski<br>Barthélémy Chinenyeze<br>Davide Gardini<br>Petar Visic<br>Matey Kaziyski<br>Luka Basic<br>Petar Visic<br>Petar Visic<br>Matey Kaziyski<br>Aidan Zingel<br>Matey Kaziyski<br>Woojin Lee<br>Giulio Pinali<br>Davide Gardini<br>Matey Kaziyski<br>Davide Gardini<br>Aidan Zingel<br>Davide Gardini<br>Bruno mossa Rezende<br>Barthélémy Chinenyeze<br>Luka Basic<br>Tommaso Stefani<br>Alessandro Piccinelli<br>Aidan Zingel<br>Petar Visic<br>Barthélémy Chinenyeze<br>Barthélémy Chinenyeze<br>Matey Kaziyski<br>Barthélémy Chinenyeze<br>Luka Basic<br>Tommaso Stefani<br>Barthélémy Chinenyeze<br>Aleksandar Nedeljkovic<br>Barthélémy Chinenyeze<br>Tommaso Stefani<br>Tommaso Stefani<br>Aidan Zingel<br>Nicola Salsi<br>Nicola Salsi<br>Nicola Salsi<br>Nicola Salsi<br>Nicola Salsi<br>Nicola Salsi<br>Nicola Salsi<br>Nicola Salsi<br>Nicola Salsi<br>Nicola Salsi<br>Nicola Salsi<br>Nicola Salsi<br>Matey Kaziyski<br>Matey Kaziyski<br>Matey Kaziyski<br>Matey Kaziyski<br>Matey Kaziyski<br>Matey Kaziyski<br>Matey Kaziyski<br>Matey Kaziyski<br>Matey Kaziyski<br>Matey Kaziyski<br>Davide Gardini<br>Davide Gardini<br>Davide Gardini<br>Davide Gardini<br>Davide Gardini<br>Davide Gardini<br>Davide Gardini<br>Davide Gardini<br>Davide Gardini<br>Petar Visic<br>Petar Visic<br>Petar Visic<br>Petar Visic<br>Petar Visic<br>Petar Visic<br>Petar Visic<br>Petar Visic<br>Bruno mossa Rezende<br>Bruno mossa Rezende<br>Bruno mossa Rezende<br>Bruno mossa Rezende<br>Bruno mossa Rezende<br>Bruno mossa Rezende<br>Bruno mossa Rezende<br>Bruno mossa Rezende<br>Bruno mossa Rezende<br>Bruno mossa Rezende<br>Luka Basic<br>Luka Basic<br>Luka Basic<br>Luka Basic<br>Luka Basic<br>Luka Basic<br>Lorenzo Cortesia<br>Lorenzo Cortesia<br>Lorenzo Cortesia<br>Lorenzo Cortesia<br>Lorenzo Cortesia<br>Lorenzo Cortesia<br>Tommaso Stefani<br>Tommaso Stefani<br>Tommaso Stefani<br>Tommaso Stefani<br>Tommaso Stefani<br>Tommaso Stefani<br>Gabriel Garcia<br>Gabriel Garcia<br>Gabriel Garcia<br>Gabriel Garcia<br>Gabriel Garcia<br>Gabriel Garcia<br>Gabriel Garcia<br>Gabriel Garcia<br>Aidan Zingel<br>Aidan Zingel<br>Aidan Zingel<br>Aidan Zingel<br>Aidan Zingel<br>Aidan Zingel<br>Giulio Pinali<br>Giulio Pinali<br>Giulio Pinali<br>Urpo Sivula<br>Raphael Oliveira<br>Primoz Mejal<br>Bruno mossa Rezende<br>Luka Basic<br>Giulio Pinali<br>Aleksandar Nedeljkovic<br>Joseph Worsley<br>Lorenzo Cortesia<br>Bruno mossa Rezende<br>Ulas Kiyak<br>Luka Basic<br>Lorenzo Cortesia<br>Azizcan Ataoglan<br>Matey Kaziyski<br>Giulio Pinali<br>Bruno mossa Rezende<br>Ulas Kiyak<br>Jesper Schut<br>Gabriel Garcia<br>Urpo Sivula<br>Raphael Oliveira<br>Luka Basic<br>Urpo Sivula<br>Petar Visic<br>Petar Visic<br>Ulas Kiyak<br>Martin Atanasov<br>Adam Kowalski<br>Djordje Ilic<br>Augusto renato Colito<br>Matias Sanchez<br>James Weir<br>Damian Schulz<br>Dennis Deroey<br>Luka Basic<br>Henri Treial<br>Gabriel Garcia<br>Saverio De santis<br>Luka Basic<br>Bruno mossa Rezende<br>Luka Basic<br>Bruno mossa Rezende<br>Saverio De santis<br>Matey Kaziyski<br>Luka Basic<br>Saverio De santis<br>Saverio De santis<br>Luka Basic<br>Aidan Zingel<br>Luka Basic<br>Matey Kaziyski<br>Gouessant Brendan<br>Davide Gardini<br>Bruno mossa Rezende<br>Aidan Zingel<br>Matey Kaziyski<br>Aidan Zingel<br>Bruno mossa Rezende<br>Nicola Salsi<br>Gabriel Garcia<br>Tsimafei Zhukouski<br>Woojin Lee<br>Andrea Santangelo<br>Matey Kaziyski<br>Matey Kaziyski<br>Bruno mossa Rezende<br>Woojin Lee<br>Matey Kaziyski<br>Saverio De santis<br>Aidan Zingel<br>Woojin Lee<br>Matey Kaziyski<br>Davide Gardini<br>Halil Dolasik<br>Manuel Zlatanov<br>Aidan Zingel<br>Alessandro Piccinelli<br>Barthélémy Chinenyeze<br>Primoz Mejal<br>Saverio De santis<br>Barthélémy Chinenyeze<br>Aidan Zingel<br>Barthélémy Chinenyeze<br>Petar Visic<br>Matey Kaziyski<br>Petar Visic<br>Tommaso Stefani<br>Denis Karyagin"], ["<PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON> lazaro <PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON> lazar<PERSON>nan<PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br>O<PERSON><PERSON><PERSON> lazar<PERSON> hernan<PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br>U<PERSON>va<PERSON><br>Osniel lazaro Mergarejo hernandez<br><PERSON><PERSON><br><PERSON><PERSON>infante<br><PERSON> Co<PERSON><br><PERSON>g <PERSON>ov<br>Osniel lazaro Mergarejo hernandez<br>John gordon Perrin<br>Jakob solgaard Thelle<br>Osniel lazaro Mergarejo hernandez<br>W<PERSON>ru Tanig<PERSON><br><PERSON>g <PERSON>ov<br><PERSON>lò Hoffer<br>Davide Luzzi<br>Nicolò Hoffer<br>Gabriele Nelli<br>Lorenzo Cortesia<br>Osniel lazaro Mergarejo hernandez<br>Nicolò Hoffer<br>Gabriele Nelli<br>Fabio Ricci<br>Gabriele Nelli<br>Alessandro Bristot<br>Luka Marttila<br>Nicolò Hoffer<br>Oleg Antonov<br>Nicolò Hoffer<br>Gabriele Nelli<br>Luka Marttila<br>Lorenzo Cortesia<br>Luka Marttila<br>Nicolò Hoffer<br>Lorenzo Cortesia<br>Francesco Pierri<br>Nicolò Hoffer<br>Davide Candellaro<br>Gabriele Nelli<br>Gabriele Nelli<br>Jakob solgaard Thelle<br>Osniel lazaro Mergarejo hernandez<br>Gabriele Nelli<br>Lorenzo Cortesia<br>Gabriele Nelli<br>Gabriele Nelli<br>Uros Kovacevic<br>Wataru Taniguchi<br>Gabriele Nelli<br>Wataru Taniguchi<br>Fabio Ricci<br>Davide Candellaro<br>Lorenzo Cortesia<br>Osniel lazaro Mergarejo hernandez<br>John gordon Perrin<br>Alessandro Bristot<br>Osniel lazaro Mergarejo hernandez<br>Davide Candellaro<br>Nicolò Hoffer<br>Fabio Ricci<br>Luka Marttila<br>John gordon Perrin<br>Mattia Boninfante<br>Alessandro Bristot<br>Nicolò Hoffer<br>Gabriele Nelli<br>Fabio Ricci<br>Davide Candellaro<br>Oleg Antonov<br>Francesco Pierri<br>Osniel lazaro Mergarejo hernandez<br>Oleg Antonov<br>Davide Candellaro<br>Lorenzo Cortesia<br>Eetu Pennanen<br>Davide Candellaro<br>Nicolò Hoffer<br>Veljko Masulovic<br>Nicolò Hoffer<br>Lorenzo Cortesia<br>Osniel lazaro Mergarejo hernandez<br>Nicolò Hoffer<br>Alessandro Bristot<br>Mattia Boninfante<br>Osniel lazaro Mergarejo hernandez<br>Gabriele Nelli<br>Nicolò Hoffer<br>Jakob solgaard Thelle<br>Gabriele Nelli<br>Luka Marttila<br>Osniel lazaro Mergarejo hernandez<br>Davide Luzzi<br>Jakob solgaard Thelle<br>Uros Kovacevic<br>Davide Luzzi<br>Lorenzo Cortesia<br>Veljko Masulovic<br>Pearson Eshenko<br>Uros Kovacevic<br>Jakob solgaard Thelle<br>Lorenzo Cortesia<br>Francesco Pierri<br>Osniel lazaro Mergarejo hernandez<br>Jakob solgaard Thelle<br>Fabio Ricci<br>Oleg Antonov<br>Davide Candellaro<br>Gabriele Nelli<br>Nicolò Hoffer<br>Lorenzo Cortesia<br>Veljko Masulovic<br>Francesco Pierri<br>Osniel lazaro Mergarejo hernandez<br>Gabriele Nelli<br>Fabio Ricci<br>Gabriele Nelli<br>Francesco Pierri<br>Mattia Boninfante<br>Gabriele Nelli<br>Lorenzo Cortesia<br>Fabio Ricci<br>Osniel lazaro Mergarejo hernandez<br>Lorenzo Cortesia<br>Davide Candellaro<br>Alessandro Bristot<br>Diego Frascio<br>Alessandro Bristot<br>Gabriele Nelli<br>Davide Candellaro<br>Oleg Antonov<br>Fabio Ricci<br>John gordon Perrin<br>Nicolò Hoffer<br>Davide Candellaro<br>Davide Luzzi<br>John gordon Perrin<br>Fabio Ricci<br>Oleg Antonov<br>Veljko Masulovic<br>Jakob solgaard Thelle<br>Osniel lazaro Mergarejo hernandez<br>John gordon Perrin<br>Luka Marttila<br>Lorenzo Cortesia<br>Lorenzo Cortesia<br>Nicolò Hoffer<br>Lorenzo Cortesia<br>Jakob solgaard Thelle<br>Jakob solgaard Thelle<br>Jakob solgaard Thelle<br>Lorenzo Cortesia<br>Osniel lazaro Mergarejo hernandez<br>Oleg Antonov<br>Nicolò Hoffer<br>Uros Kovacevic<br>Alessandro Bristot<br>Jakob solgaard Thelle<br>Oleg Antonov<br>Lorenzo Cortesia<br>Jakob solgaard Thelle<br>Jakob solgaard Thelle<br>Osniel lazaro Mergarejo hernandez<br>Diego Frascio<br>Mattia Boninfante<br>Gabriele Nelli<br>Davide Luzzi<br>Francesco Pierri<br>Jakob solgaard Thelle<br>Oleg Antonov<br>Osniel lazaro Mergarejo hernandez<br>Nicolò Hoffer<br>Oleg Antonov<br>Francesco Pierri<br>Osniel lazaro Mergarejo hernandez<br>Diego Frascio<br>Wataru Taniguchi<br>Nicolò Hoffer<br>Luka Marttila<br>Oleg Antonov<br>Wataru Taniguchi<br>Francesco Pierri<br>Gabriele Nelli<br>Oleg Antonov<br>Oleg Antonov<br>Oleg Antonov<br>Mattia Boninfante<br>Fabio Ricci<br>Osniel lazaro Mergarejo hernandez<br>Luka Marttila<br>Lorenzo Cortesia<br>Osniel lazaro Mergarejo hernandez<br>Alessandro Bristot<br>Davide Luzzi<br>Gabriele Nelli<br>Davide Candellaro<br>Fabio Ricci<br>Lorenzo Cortesia<br>Oleg Antonov<br>Davide Candellaro<br>Wataru Taniguchi<br>Davide Candellaro<br>Gabriele Nelli<br>Lorenzo Cortesia<br>Davide Candellaro<br>Uros Kovacevic<br>Jakob solgaard Thelle<br>Nicolò Hoffer<br>Francesco Pierri<br>Oleg Antonov<br>Alessandro Bristot<br>Pearson Eshenko<br>Gabriele Nelli<br>Davide Luzzi<br>Davide Candellaro<br>Luka Marttila<br>Davide Luzzi<br>Lorenzo Cortesia<br>Diego Frascio<br>Gabriele Nelli<br>Gabriele Nelli<br>Jakob solgaard Thelle<br>Davide Luzzi<br>Nicolò Hoffer<br>Nicolò Hoffer<br>Osniel lazaro Mergarejo hernandez<br>Nicolò Hoffer<br>Nicolò Hoffer<br>Luka Marttila<br>Luka Marttila<br>Nicolò Hoffer<br>Veljko Masulovic<br>Nicolò Hoffer<br>Lorenzo Cortesia<br>Oleg Antonov<br>Luka Marttila<br>Osniel lazaro Mergarejo hernandez<br>Gabriele Nelli<br>Osniel lazaro Mergarejo hernandez<br>Uros Kovacevic<br>Bernardo Silva<br>Davide Luzzi<br>Luka Marttila<br>Lorenzo Cortesia<br>Gabriele Nelli<br>Lorenzo Cortesia<br>Mattia Boninfante<br>Gabriele Nelli<br>Nicolò Hoffer<br>Veljko Masulovic<br>Davide Candellaro<br>Lorenzo Cortesia<br>Davide Candellaro<br>Davide Candellaro<br>Nicolò Hoffer<br>Mattia Boninfante<br>Mattia Boninfante<br>Davide Candellaro<br>Lorenzo Cortesia<br>Veljko Masulovic<br>Gabriele Nelli<br>Lorenzo Cortesia<br>Gabriele Nelli<br>Nicolò Hoffer<br>Nicolò Hoffer<br>Lorenzo Cortesia<br>Jakob solgaard Thelle<br>Davide Candellaro<br>Mattia Boninfante<br>Wataru Taniguchi<br>Lorenzo Cortesia<br>Veljko Masulovic<br>John gordon Perrin<br>Davide Candellaro<br>Jakob solgaard Thelle<br>Luka Marttila<br>Davide Candellaro<br>Lorenzo Cortesia<br>Davide Candellaro<br>Francesco Pierri<br>Oleg Antonov<br>Oleg Antonov<br>Pearson Eshenko<br>Veljko Masulovic<br>Alessandro Bristot<br>Ter Maat wouter<br>Osniel lazaro Mergarejo hernandez<br>Nicolò Hoffer<br>Uros Kovacevic<br>Satoshi Tsuiki<br>Pearson Eshenko<br>Davide Luzzi<br>Oleg Antonov<br>Gabriele Nelli<br>John gordon Perrin<br>Oleg Antonov<br>Davide Candellaro<br>Jakob solgaard Thelle<br>Davide Candellaro<br>Alessandro Bristot<br>Mattia Boninfante<br>Nicolò Hoffer<br>Osniel lazaro Mergarejo hernandez<br>Davide Candellaro<br>Gabriele Nelli<br>Davide Luzzi<br>Davide Luzzi<br>Luka Marttila<br>Lorenzo Cortesia<br>Nicolò Hoffer<br>Gabriele Nelli<br>Gabriele Nelli<br>Jakob solgaard Thelle<br>Jakob solgaard Thelle<br>Nicolò Hoffer<br>Jakob solgaard Thelle<br>Uros Kovacevic<br>Davide Candellaro<br>Nicolò Hoffer<br>Luka Marttila<br>Osniel lazaro Mergarejo hernandez<br>Francesco Pierri<br>Jakob solgaard Thelle<br>Nicolò Hoffer<br>Mattia Boninfante<br>John gordon Perrin<br>Nicolò Hoffer<br>Gabriele Nelli<br>Osniel lazaro Mergarejo hernandez<br>Davide Luzzi<br>John gordon Perrin<br>Lorenzo Cortesia<br>Mattia Boninfante<br>Davide Luzzi<br>Gabriele Nelli<br>Diego Frascio<br>Veljko Masulovic<br>Francesco Pierri<br>Fabio Ricci<br>Francesco Pierri<br>Wataru Taniguchi<br>Jakob solgaard Thelle<br>Nicolò Hoffer<br>Osniel lazaro Mergarejo hernandez<br>Nicolò Hoffer<br>Davide Luzzi<br>Gabriele Nelli<br>Uros Kovacevic<br>Davide Luzzi<br>Mattia Boninfante<br>Lorenzo Cortesia<br>Jakob solgaard Thelle<br>Francesco Pierri<br>Oleg Antonov<br>Jakob solgaard Thelle<br>Satoshi Tsuiki<br>Fabio Ricci<br>Nicolò Hoffer<br>Ter Maat wouter<br>Osniel lazaro Mergarejo hernandez<br>Osniel lazaro Mergarejo hernandez<br>Mattia Boninfante<br>Fabio Ricci<br>Gabriele Nelli<br>Lorenzo Cortesia<br>Wataru Taniguchi<br>Osniel lazaro Mergarejo hernandez<br>Osniel lazaro Mergarejo hernandez<br>Lorenzo Cortesia<br>Luka Marttila<br>Veljko Masulovic<br>Luka Marttila<br>Nicolò Hoffer<br>Davide Candellaro<br>Nicolò Hoffer<br>Nicolò Hoffer<br>Davide Candellaro<br>Davide Luzzi<br>Nicolò Hoffer<br>Francesco Pierri<br>Osniel lazaro Mergarejo hernandez<br>Gabriele Nelli<br>Nicolò Hoffer<br>Osniel lazaro Mergarejo hernandez<br>Mattia Boninfante<br>Uros Kovacevic<br>Jakob solgaard Thelle<br>Davide Candellaro<br>Diego Frascio<br>Lorenzo Cortesia<br>Gabriele Nelli<br>Nicolò Hoffer<br>Gabriele Nelli<br>Davide Candellaro<br>Alessandro Bristot<br>Davide Candellaro<br>Luka Marttila<br>Veljko Masulovic<br>Nicolò Hoffer<br>Mattia Boninfante<br>Davide Candellaro<br>Luka Marttila<br>Gabriele Nelli<br>Osniel lazaro Mergarejo hernandez<br>Nicolò Hoffer<br>Osniel lazaro Mergarejo hernandez<br>Diego Frascio<br>Davide Candellaro<br>Davide Candellaro<br>Luka Marttila<br>Gabriele Nelli<br>Wataru Taniguchi<br>Nicolò Hoffer<br>Gabriele Nelli<br>Gabriele Nelli<br>Mattia Boninfante<br>Osniel lazaro Mergarejo hernandez<br>Uros Kovacevic<br>Lorenzo Cortesia<br>Nicolò Hoffer<br>Mattia Boninfante<br>Gabriele Nelli<br>Veljko Masulovic<br>Osniel lazaro Mergarejo hernandez<br>Fabio Ricci<br>Lorenzo Cortesia<br>Oleg Antonov<br>Veljko Masulovic<br>Davide Luzzi<br>Mattia Boninfante<br>Wataru Taniguchi<br>Luka Marttila<br>Osniel lazaro Mergarejo hernandez<br>Mattia Boninfante<br>Oleg Antonov<br>Davide Candellaro<br>Uros Kovacevic<br>Oleg Antonov<br>Eetu Pennanen<br>Lorenzo Cortesia<br>Mattia Boninfante<br>Osniel lazaro Mergarejo hernandez<br>Nicolò Hoffer<br>Francesco Pierri<br>Davide Candellaro<br>Anshel Ver eecke<br>Gabriele Nelli<br>Wataru Taniguchi<br>Davide Luzzi<br>Nicolò Hoffer<br>Mattia Boninfante<br>Edvins Skruders<br>Alessandro Bristot<br>Alessandro Bristot<br>Diego Frascio<br>Lorenzo Cortesia<br>Fabio Ricci<br>Osniel lazaro Mergarejo hernandez<br>Davide Candellaro<br>Davide Candellaro<br>Nicolò Hoffer<br>Matteo Staforini<br>Matteo Staforini<br>Matteo Staforini<br>Matteo Staforini<br>Matteo Staforini<br>Matteo Staforini<br>Matteo Staforini<br>Matteo Staforini<br>Matteo Staforini<br>Matteo Staforini<br>Matteo Staforini<br>Matteo Staforini<br>Matteo Staforini<br>Matteo Staforini<br>Matteo Staforini<br>Matteo Staforini<br>Matteo Staforini<br>Matteo Staforini<br>Jakob solgaard Thelle<br>Khalifa Khalid<br>Gabriele Nelli<br>Gabriele Nelli<br>Gabriele Nelli<br>Gabriele Nelli<br>Gabriele Nelli<br>Gabriele Nelli<br>Gabriele Nelli<br>Gabriele Nelli<br>Gabriele Nelli<br>Osniel lazaro Mergarejo hernandez<br>Osniel lazaro Mergarejo hernandez<br>Osniel lazaro Mergarejo hernandez<br>Osniel lazaro Mergarejo hernandez<br>Osniel lazaro Mergarejo hernandez<br>Osniel lazaro Mergarejo hernandez<br>Osniel lazaro Mergarejo hernandez<br>Nicolò Hoffer<br>Nicolò Hoffer<br>Nicolò Hoffer<br>Nicolò Hoffer<br>Nicolò Hoffer<br>Nicolò Hoffer<br>John gordon Perrin<br>John gordon Perrin<br>John gordon Perrin<br>John gordon Perrin<br>John gordon Perrin<br>Oleg Antonov<br>Oleg Antonov<br>Oleg Antonov<br>Oleg Antonov<br>Oleg Antonov<br>Oleg Antonov<br>Luka Marttila<br>Luka Marttila<br>Luka Marttila<br>Luka Marttila<br>Luka Marttila<br>Luka Marttila<br>Fabio Ricci<br>Fabio Ricci<br>Eetu Pennanen<br>Eetu Pennanen<br>Anshel Ver eecke<br>Emre berat Firat<br>Caner Peksen<br>John gordon Perrin<br>Pearson Eshenko<br>John gordon Perrin<br>Osniel lazaro Mergarejo hernandez<br>Nicolò Hoffer<br>John gordon Perrin<br>Bozidar Vucicevic<br>Gabriele Nelli<br>Gabriele Nelli<br>Osniel lazaro Mergarejo hernandez<br>Fabio Ricci<br>John gordon Perrin<br>Eetu Pennanen<br>Fabio Ricci<br>Luka Marttila<br>Esko Vuorinen<br>Luka Marttila<br>Ter Maat wouter<br>Satoshi Tsuiki<br>Albert Hurt<br>Oleg Antonov<br>Dominik Depowski<br>Jakub Szymanski<br>Michal Ostrowski<br>Lukasz Kaczmarek<br>Lukasz Kaczmarek<br>Gabriele Nelli<br>Ferre Reggers<br>Osniel lazaro Mergarejo hernandez<br>Jakob solgaard Thelle<br>Mattia Boninfante<br>Davide Candellaro<br>Wataru Taniguchi<br>Gabriele Nelli<br>Lorenzo Cortesia<br>Gabriele Nelli<br>Mattia Boninfante<br>Francesco Pierri<br>Nicolò Hoffer<br>Mattia Boninfante<br>Davide Candellaro<br>Osniel lazaro Mergarejo hernandez<br>Francesco Pierri<br>Wataru Taniguchi<br>Francesco Pierri<br>Edvins Skruders<br>Lorenzo Cortesia<br>Francesco Pierri<br>Gabriele Nelli<br>Osniel lazaro Mergarejo hernandez<br>Lorenzo Cortesia<br>Gabriele Nelli<br>Mattia Boninfante<br>Uros Kovacevic<br>Nicolò Hoffer<br>Veljko Masulovic<br>Matteo Staforini<br>Osniel lazaro Mergarejo hernandez<br>Osniel lazaro Mergarejo hernandez<br>Uros Kovacevic<br>Mattia Boninfante<br>Osniel lazaro Mergarejo hernandez<br>Lorenzo Cortesia<br>Davide Candellaro<br>Osniel lazaro Mergarejo hernandez<br>Nicolò Hoffer<br>Bernardo Silva<br>Lorenzo Cortesia<br>Nicolò Hoffer<br>Wataru Taniguchi<br>Jakob solgaard Thelle<br>Nicolò Hoffer<br>Jakob solgaard Thelle<br>Lorenzo Cortesia<br>Davide Candellaro<br>Nicolò Hoffer<br>Jakob solgaard Thelle<br>Oleg Antonov<br>Veljko Masulovic<br>Diego Frascio"], ["<PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON>out <PERSON>he<PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br>Wout D'heer<br><PERSON><br><PERSON> ma<PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON> mayo <PERSON><br>G<PERSON><PERSON><br>Shay mayo <PERSON><br><PERSON><br>G<PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON> ma<PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br>Shay may<PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON>maso <PERSON><PERSON><br>Wout D'heer<br>Jordan Ewert<br><PERSON> Recine<br><PERSON> Cubito<br><PERSON> Fanizza<br><PERSON> Recine<br><PERSON> maria Gargiulo<br>Maarten Van garderen<br><PERSON> maria Gargiulo<br>Tim Held<br>Giulio Magalini<br><PERSON> Pope<br><PERSON> Recine<br>Wout D'heer<br><PERSON> Recine<br>Pasquale Sottile<br>Maarten Van garderen<br>Tim Held<br>Alessandro Fanizza<br>Nicola Pesaresi<br>Alessandro Fanizza<br>Gioele Taiwo<br>Domenico Cavaccini<br>Jacopo Larizza<br>Giovanni maria Gargiulo<br>Francesco Recine<br>Maarten Van garderen<br>Giulio Magalini<br>Klistan Lawrence<br>Domenico Cavaccini<br>Francesco Recine<br>Francesco Zoppellari<br>Francesco Recine<br>Tommaso Stefani<br>Giovanni maria Gargiulo<br>Wout D'heer<br>Giovanni maria Gargiulo<br>Matteo Pirazzoli<br>Michele Starace<br>Marco Cubito<br>Klistan Lawrence<br>Maarten Van garderen<br>Tim Held<br>Giovanni maria Gargiulo<br>Francesco Zoppellari<br>Francesco Recine<br>Klistan Lawrence<br>Michal Kedzierski<br>Domenico Cavaccini<br>Matteo Pirazzoli<br>Wout D'heer<br>Domenico Cavaccini<br>Jacopo Tosti<br>Francesco Recine<br>Giovanni maria Gargiulo<br>Matteo Pirazzoli<br>Francesco Recine<br>Maarten Van garderen<br>Giovanni maria Gargiulo<br>Lorenzo Pope<br>Francesco Recine<br>Marco Cubito<br>Gianluca Cremoni<br>Tommaso Stefani<br>Klistan Lawrence<br>Giovanni maria Gargiulo<br>Andrea Canella<br>Andrea Canella<br>Matteo Pirazzoli<br>Andrea Canella<br>Klistan Lawrence<br>Gioele adeola Taiwo<br>Wout D'heer<br>Shay mayo Liberman<br>Wout D'heer<br>Giovanni maria Gargiulo<br>Giulio Magalini<br>Tim Held<br>Wout D'heer<br>Francesco Recine<br>Shay mayo Liberman<br>Javier facundo Martinez<br>Tommaso Stefani<br>Giulio Magalini<br>Nicola Pesaresi<br>Jacopo Larizza<br>Marco Cubito<br>Giulio Magalini<br>Nicola Pesaresi<br>Nicola Pesaresi<br>Nicola Pesaresi<br>Wout D'heer<br>Wout D'heer<br>Francesco Recine<br>Pasquale Sottile<br>Tim Held<br>Francesco Recine<br>Andrea Canella<br>Francesco Zoppellari<br>Giovanni maria Gargiulo<br>Domenico Cavaccini<br>Gioele adeola Taiwo<br>Marco Cubito<br>Tim Held<br>Giovanni maria Gargiulo<br>Gianluca Cremoni<br>Jacopo Larizza<br>Tommaso Stefani<br>Michele Starace<br>Giovanni maria Gargiulo<br>Domenico Cavaccini<br>Alessandro Fanizza<br>Pasquale Sottile<br>Jacopo Tosti<br>Francesco Recine<br>Wout D'heer<br>Giovanni maria Gargiulo<br>Klistan Lawrence<br>Michele Starace<br>Michal Kedzierski<br>Matteo Pirazzoli<br>Javier facundo Martinez<br>Andrea Canella<br>Shay mayo Liberman<br>Javier facundo Martinez<br>Giovanni maria Gargiulo<br>Matteo Pirazzoli<br>Shay mayo Liberman<br>Michal Kedzierski<br>Matteo Pirazzoli<br>Francesco Recine<br>Francesco Recine<br>Javier facundo Martinez<br>Tim Held<br>Marco Cubito<br>Klistan Lawrence<br>Giulio Magalini<br>Jacopo Larizza<br>Javier facundo Martinez<br>Francesco Recine<br>Wout D'heer<br>Wout D'heer<br>Giulio Magalini<br>Jacopo Larizza<br>Giulio Magalini<br>Michal Kedzierski<br>Matteo Pirazzoli<br>Wout D'heer<br>Francesco Zoppellari<br>Klistan Lawrence<br>Jacopo Larizza<br>Marco Cubito<br>Giovanni maria Gargiulo<br>Jakub Popiwczak<br>Giovanni maria Gargiulo<br>Wout D'heer<br>Francesco Recine<br>Andrea Canella<br>Francesco Recine<br>Jacopo Larizza<br>Wout D'heer<br>Jacopo Larizza<br>Francesco Recine<br>Francesco Recine<br>Shay mayo Liberman<br>Andrea Canella<br>Jacopo Larizza<br>Matteo Pirazzoli<br>Alessandro Fanizza<br>Nicola Pesaresi<br>Giovanni maria Gargiulo<br>Tommaso Stefani<br>Domenico Cavaccini<br>Klistan Lawrence<br>Marco Cubito<br>Francesco Recine<br>Giovanni maria Gargiulo<br>Francesco Recine<br>Wout D'heer<br>Matteo Pirazzoli<br>Nicola Pesaresi<br>Lorenzo Pope<br>Pasquale Sottile<br>Pasquale Sottile<br>Tim Held<br>Jacopo Larizza<br>Javier facundo Martinez<br>Tommaso Stefani<br>Matteo Pirazzoli<br>Marco Cubito<br>Gioele adeola Taiwo<br>Tim Held<br>Javier facundo Martinez<br>Giulio Magalini<br>Klistan Lawrence<br>Klistan Lawrence<br>Giovanni maria Gargiulo<br>Alessandro Fanizza<br>Francesco Recine<br>Marco Cubito<br>Pasquale Sottile<br>Francesco Recine<br>Francesco Zoppellari<br>Francesco Recine<br>Giovanni maria Gargiulo<br>Pasquale Sottile<br>Giovanni maria Gargiulo<br>Matteo Pirazzoli<br>Wout D'heer<br>Klistan Lawrence<br>Giulio Magalini<br>Andrea Canella<br>Matteo Pirazzoli<br>Giovanni maria Gargiulo<br>Francesco Recine<br>Wiktor Musial<br>Shay mayo Liberman<br>Jacopo Larizza<br>Francesco Recine<br>Wout D'heer<br>Francesco Zoppellari<br>Wout D'heer<br>Domenico Cavaccini<br>Giovanni maria Gargiulo<br>Alessandro Fanizza<br>Klistan Lawrence<br>Giulio Magalini<br>Tim Held<br>Michal Kedzierski<br>Tim Held<br>Nicola Pesaresi<br>Giovanni maria Gargiulo<br>Shay mayo Liberman<br>Andrea Canella<br>Francesco Zoppellari<br>Shay mayo Liberman<br>Wout D'heer<br>Giulio Magalini<br>Alessandro Fanizza<br>Francesco Zoppellari<br>Wout D'heer<br>Francesco Zoppellari<br>Francesco Recine<br>Javier facundo Martinez<br>Tim Held<br>Francesco Recine<br>Wout D'heer<br>Gioele adeola Taiwo<br>Wout D'heer<br>Giovanni maria Gargiulo<br>Giovanni maria Gargiulo<br>Klistan Lawrence<br>Tim Held<br>Pasquale Sottile<br>Giulio Magalini<br>Maarten Van garderen<br>Giulio Magalini<br>Wout D'heer<br>Klistan Lawrence<br>Giovanni maria Gargiulo<br>Francesco Recine<br>Wout D'heer<br>Maarten Van garderen<br>Matteo Pirazzoli<br>Wout D'heer<br>Alessandro Fanizza<br>Tommaso Stefani<br>Tommaso Stefani<br>Matteo Pirazzoli<br>Marco Cubito<br>Shay mayo Liberman<br>Matteo Pirazzoli<br>Lorenzo Pope<br>Domenico Cavaccini<br>Francesco Zoppellari<br>Alessandro Fanizza<br>Giovanni maria Gargiulo<br>Matteo Pirazzoli<br>Matteo Pirazzoli<br>Francesco Zoppellari<br>Giulio Magalini<br>Matteo Pirazzoli<br>Maarten Van garderen<br>Shay mayo Liberman<br>Giovanni maria Gargiulo<br>Domenico Cavaccini<br>Francesco Recine<br>Nicola Pesaresi<br>Wout D'heer<br>Andrea Canella<br>Giovanni maria Gargiulo<br>Francesco Recine<br>Nicola Pesaresi<br>Wout D'heer<br>Wout D'heer<br>Francesco Recine<br>Andrea Canella<br>Andrea Canella<br>Andrea Canella<br>Andrea Canella<br>Andrea Canella<br>Tim Held<br>Pasquale Sottile<br>Alessandro Fanizza<br>Tim Held<br>Francesco Zoppellari<br>Maarten Van garderen<br>Pasquale Sottile<br>Lorenzo Pope<br>Francesco Recine<br>Giovanni maria Gargiulo<br>Andrea Canella<br>Nicola Pesaresi<br>Daniel Pfeffer<br>Gianluca Cremoni<br>Wout D'heer<br>Wout D'heer<br>Francesco Recine<br>Francesco Recine<br>Wout D'heer<br>Wiktor Musial<br>Jacopo Tosti<br>Shay mayo Liberman<br>Domenico Cavaccini<br>Wout D'heer<br>Pasquale Sottile<br>Tim Held<br>Francesco Recine<br>Wout D'heer<br>Klistan Lawrence<br>Michal Kedzierski<br>Alessandro Fanizza<br>Francesco Recine<br>Giulio Magalini<br>Wout D'heer<br>Giovanni maria Gargiulo<br>Jacopo Larizza<br>Wout D'heer<br>Pasquale Sottile<br>Francesco Zoppellari<br>Michele Starace<br>Giulio Magalini<br>Giovanni maria Gargiulo<br>Alessandro Fanizza<br>Pasquale Sottile<br>Wout D'heer<br>Wout D'heer<br>Wiktor Musial<br>Nicola Pesaresi<br>Francesco Recine<br>Tim Held<br>Tommaso Stefani<br>Francesco Zoppellari<br>Francesco Recine<br>Nicola Pesaresi<br>Wout D'heer<br>Giulio Magalini<br>Giulio Magalini<br>Francesco Recine<br>Giovanni maria Gargiulo<br>Giovanni maria Gargiulo<br>Andrea Canella<br>Shay mayo Liberman<br>Marco Cubito<br>Wiktor Musial<br>Francesco Recine<br>Klistan Lawrence<br>Gianluca Cremoni<br>Giovanni maria Gargiulo<br>Lorenzo Pope<br>Klistan Lawrence<br>Tim Held<br>Jacopo Larizza<br>Giulio Magalini<br>Gioele adeola Taiwo<br>Wiktor Musial<br>Francesco Recine<br>Giovanni maria Gargiulo<br>Pasquale Sottile<br>Francesco Zoppellari<br>Giovanni maria Gargiulo<br>Lorenzo Pope<br>Shay mayo Liberman<br>Wout D'heer<br>Pasquale Sottile<br>Andrea Canella<br>Francesco Recine<br>Tim Held<br>Francesco Recine<br>Domenico Cavaccini<br>Klistan Lawrence<br>Giovanni maria Gargiulo<br>Alessandro Fanizza<br>Giovanni maria Gargiulo<br>Domenico Cavaccini<br>Lorenzo Pope<br>Tim Held<br>Jacopo Larizza<br>Francesco Zoppellari<br>Wout D'heer<br>Andrea Canella<br>Francesco Recine<br>Javier facundo Martinez<br>Matteo Pirazzoli<br>Wout D'heer<br>Alessandro Fanizza<br>Pasquale Sottile<br>Wout D'heer<br>Shay mayo Liberman<br>Marco Cubito<br>Marco Cubito<br>Robert Taht<br>Lorenzo Pope<br>Francesco Recine<br>Giovanni maria Gargiulo<br>Francesco Recine<br>Wout D'heer<br>Wout D'heer<br>Domenico Cavaccini<br>Giulio Magalini<br>Francesco Recine<br>Francesco Zoppellari<br>Giovanni maria Gargiulo<br>Francesco Recine<br>Wout D'heer<br>Maarten Van garderen<br>Maarten Van garderen<br>Andrea Canella<br>Klistan Lawrence<br>Giovanni maria Gargiulo<br>Andrea Canella<br>Francesco Recine<br>Pasquale Sottile<br>Wout D'heer<br>Giovanni maria Gargiulo<br>Nicola Pesaresi<br>Wout D'heer<br>Giovanni maria Gargiulo<br>Giulio Magalini<br>Klistan Lawrence<br>Maarten Van garderen<br>Michele Starace<br>Tommaso Stefani<br>Nicola Pesaresi<br>Gioele adeola Taiwo<br>Jacopo Larizza<br>Andrea Canella<br>Tim Held<br>Klistan Lawrence<br>Andrea Canella<br>Francesco Recine<br>Alessandro Fanizza<br>Nicola Pesaresi<br>Francesco Recine<br>Domenico Cavaccini<br>Giovanni maria Gargiulo<br>Giovanni maria Gargiulo<br>Gabriele Mariani<br>Javier facundo Martinez<br>Maarten Van garderen<br>Michele Starace<br>Nicola Pesaresi<br>Wiktor Musial<br>Tim Held<br>Wout D'heer<br>Giovanni maria Gargiulo<br>Maarten Van garderen<br>Marco Cubito<br>Maarten Van garderen<br>Shay mayo Liberman<br>Francesco Zoppellari<br>Tim Held<br>Tim Held<br>Tim Held<br>Tim Held<br>Tim Held<br>Tim Held<br>Tim Held<br>Tim Held<br>Tim Held<br>Tim Held<br>Tim Held<br>Tim Held<br>Tim Held<br>Tim Held<br>Tim Held<br>Tim Held<br>Tim Held<br>Tim Held<br>Jacopo Massari<br>Jacopo Massari<br>Jacopo Massari<br>Jacopo Massari<br>Jacopo Massari<br>Jacopo Massari<br>Jacopo Massari<br>Jacopo Massari<br>Jacopo Massari<br>Jacopo Massari<br>Jacopo Massari<br>Jacopo Massari<br>Jacopo Massari<br>Jacopo Massari<br>Jacopo Massari<br>Jacopo Massari<br>Jacopo Massari<br>Jacopo Massari<br>Jacopo Massari<br>Wout D'heer<br>Wout D'heer<br>Wout D'heer<br>Wout D'heer<br>Wout D'heer<br>Wout D'heer<br>Wout D'heer<br>Wout D'heer<br>Lorenzo Pope<br>Lorenzo Pope<br>Lorenzo Pope<br>Lorenzo Pope<br>Lorenzo Pope<br>Lorenzo Pope<br>Lorenzo Pope<br>Lorenzo Pope<br>Lorenzo Pope<br>Lorenzo Pope<br>Lorenzo Pope<br>Lorenzo Pope<br>Andrea Canella<br>Andrea Canella<br>Andrea Canella<br>Andrea Canella<br>Matteo Pirazzoli<br>Matteo Pirazzoli<br>Matteo Pirazzoli<br>Matteo Pirazzoli<br>Matteo Pirazzoli<br>Matteo Pirazzoli<br>Matteo Pirazzoli<br>Matteo Pirazzoli<br>Francesco Recine<br>Francesco Recine<br>Francesco Recine<br>Francesco Recine<br>Francesco Recine<br>Francesco Recine<br>Klistan Lawrence<br>Klistan Lawrence<br>Klistan Lawrence<br>Klistan Lawrence<br>Klistan Lawrence<br>Klistan Lawrence<br>Klistan Lawrence<br>Giulio Magalini<br>Giulio Magalini<br>Giulio Magalini<br>Giulio Magalini<br>Giulio Magalini<br>Giulio Magalini<br>Giovanni maria Gargiulo<br>Giovanni maria Gargiulo<br>Giovanni maria Gargiulo<br>Giovanni maria Gargiulo<br>Giovanni maria Gargiulo<br>Giovanni maria Gargiulo<br>Pasquale Sottile<br>Pasquale Sottile<br>Pasquale Sottile<br>Pasquale Sottile<br>Pasquale Sottile<br>Pasquale Sottile<br>Pasquale Sottile<br>Pasquale Sottile<br>Maarten Van garderen<br>Maarten Van garderen<br>Maarten Van garderen<br>Andrè Reis lopes<br>Andrè Reis lopes<br>Javier facundo Martinez<br>Javier facundo Martinez<br>Giulio Magalini<br>Francesco Recine<br>Giulio Magalini<br>Sercan yuksel Bidak<br>Wout D'heer<br>Maarten Van garderen<br>Lorenzo Pope<br>Wout D'heer<br>Markus Held<br>Lorenzo Pope<br>Pasquale Sottile<br>Francesco Recine<br>Maarten Van garderen<br>Matteo Pirazzoli<br>Andrea Canella<br>Maarten Van garderen<br>Wout D'heer<br>Matteo Pirazzoli<br>Lorenzo Pope<br>Antti Ronkainen<br>James walker Shaw<br>Jori alexander Mantha<br>Wiktor Musial<br>Daniel Pfeffer<br>Maciej Sas<br>Wiktor Mielczarek<br>Wiktor Nowak<br>Wout D'heer<br>Nazar Hetman<br>Francesco Recine<br>Pasquale Sottile<br>Francesco Recine<br>Tim Held<br>Domenico Cavaccini<br>Wout D'heer<br>Wout D'heer<br>Giovanni maria Gargiulo<br>Jacopo Tosti<br>Tim Held<br>Domenico Cavaccini<br>Robert Taht<br>Andrea Giani<br>Wout D'heer<br>Julien Faganas<br>Gianluca Cremoni<br>Tim Held<br>Domenico Cavaccini<br>Jacopo Tosti<br>Michele Starace<br>Domenico Cavaccini<br>Jakub Popiwczak<br>Domenico Cavaccini<br>Faezi Samaan<br>Michele Starace<br>Tommaso Stefani<br>Tim Held<br>Tommaso Stefani<br>Jacopo Massari<br>Francesco Recine<br>Shay mayo Liberman<br>Alessandro Fanizza<br>Marco Cubito<br>Gioele adeola Taiwo<br>Tim Held<br>Michal Kedzierski<br>Gioele Taiwo<br>Giovanni maria Gargiulo<br>Marco Cubito<br>Jacopo Larizza<br>Giovanni maria Gargiulo<br>Giovanni maria Gargiulo<br>Tim Held<br>Jacopo Massari<br>Gioele adeola Taiwo<br>Jacopo Tosti<br>Gioele adeola Taiwo<br>Francesco Recine<br>Jerome Cross<br>Jerome Cross<br>Wout D'heer<br>Tommaso Stefani<br>Germano Latella<br>Giovanni maria Gargiulo<br>Erenhan Can<br>Giovanni maria Gargiulo<br>Francesco Recine<br>Gabriele Mariani<br>Jordan Ewert<br>Tim Held<br>Francesco Recine<br>Matteo Pirazzoli<br>Wout D'heer<br>Lorenzo Pope"], ["<PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON>ik<br>Riccardo <PERSON>ngia<br><PERSON> Dzavoronok<br><PERSON> Dzavoronok<br>Fabio Ricci<br>Fabio Ricci<br>Donovan Dzavoronok<br>Aimone Alletti<br><PERSON> Zonta<br>Jan Kozamernik<br>Jan <PERSON>zamernik<br>Jan Kozamernik<br>Benjamin Diez<br>Aimone Alletti<br>Riccardo Cengia<br>Eric Loeppky<br>Benjamin Diez<br>Benjamin Diez<br>Aimone Alletti<br>Eric Loeppky<br>Fabrizio Gironi<br>Aimone Alletti<br>Aimone Alletti<br>Donovan Dzavoronok<br>Enrico Zappoli<br>Nemanja Petric<br>Eric Loeppky<br>Donovan Dzavoronok<br>Fabio Ricci<br>Riccardo Vecchi<br>Aimone Alletti<br>Aimone Alletti<br>Nicola Zonta<br>Donovan Dzavoronok<br>Alessandro Finauri<br>Matheus Motzo<br>Donovan Dzavoronok<br>Eric Loeppky<br>Nicolas Marechal<br>Silvester Meijs<br>Matheus Motzo<br>Jan Kozamernik<br>Nemanja Petric<br>Nicolas Marechal<br>Stijn D'hulst<br>Jan Kozamernik<br>Fabio Ricci<br>Aimone Alletti<br>Donovan Dzavoronok<br>Gabriele Mariani<br>Alessandro Finauri<br>Jan Kozamernik<br>Riccardo Vecchi<br>Nicolas Marechal<br>Donovan Dzavoronok<br>Nicola Zonta<br>Riccardo Vecchi<br>Matheus Motzo<br>Nemanja Petric<br>Riccardo Vecchi<br>Nicola Zonta<br>Riccardo Cengia<br>Jan Kozamernik<br>Silvester Meijs<br>Eric Loeppky<br>Alessandro Finauri<br>Aimone Alletti<br>Benjamin Diez<br>Donovan Dzavoronok<br>Enrico Zappoli<br>Nicolas Marechal<br>Alessandro Finauri<br>Adrian Markiewicz<br>Eric Loeppky<br>Donovan Dzavoronok<br>Donovan Dzavoronok<br>Silvester Meijs<br>Aimone Alletti<br>Tim Peter<br>Fabio Ricci<br>Nicola Zonta<br>Matheus Motzo<br>Matheus Motzo<br>Riccardo Vecchi<br>Fabrizio Gironi<br>Nicolas Marechal<br>Matheus Motzo<br>Sebastian Solé<br>Eric Loeppky<br>Nicola Zonta<br>Jan Kozamernik<br>Eric Loeppky<br>Matheus Motzo<br>Alessandro Finauri<br>Benjamin Diez<br>Aimone Alletti<br>Nicola Zonta<br>Donovan Dzavoronok<br>Matheus Motzo<br>Nicola Zonta<br>Nicola Zonta<br>Donovan Dzavoronok<br>Fabrizio Gironi<br>Matheus Motzo<br>Jordan Ewert<br>Nicola Zonta<br>Donovan Dzavoronok<br>Fabrizio Gironi<br>Enrico Zappoli<br>Donovan Dzavoronok<br>Alessandro Finauri<br>Benjamin Diez<br>Nicola Zonta<br>Aimone Alletti<br>Nicola Zonta<br>Alessandro Finauri<br>Aimone Alletti<br>Donovan Dzavoronok<br>Nicolas Marechal<br>Alessandro Pisoni<br>Eric Loeppky<br>Riccardo Vecchi<br>Aimone Alletti<br>Jan Kozamernik<br>Nicola Zonta<br>Aimone Alletti<br>Aimone Alletti<br>Matheus Motzo<br>Nicolas Marechal<br>Nemanja Petric<br>Fabrizio Gironi<br>Donovan Dzavoronok<br>Nicola Zonta<br>Aimone Alletti<br>Aimone Alletti<br>Riccardo Vecchi<br>Donovan Dzavoronok<br>Nicola Zonta<br>Silvester Meijs<br>Donovan Dzavoronok<br>Jan Kozamernik<br>Matheus Motzo<br>Nicola Zonta<br>Alessandro Finauri<br>Alessandro Finauri<br>Nemanja Petric<br>Patrik Lamanec<br>Fabrizio Gironi<br>Donovan Dzavoronok<br>Fabrizio Gironi<br>Donovan Dzavoronok<br>Donovan Dzavoronok<br>Fabrizio Gironi<br>Benjamin Diez<br>Fabrizio Gironi<br>Enrico Zappoli<br>Dawid Gunia<br>Alessandro Finauri<br>Donovan Dzavoronok<br>Alessandro Finauri<br>Aimone Alletti<br>Donovan Dzavoronok<br>Eric Loeppky<br>Riccardo Cengia<br>Eric Loeppky<br>Fabrizio Gironi<br>Benjamin Diez<br>Aimone Alletti<br>Aimone Alletti<br>Karli Allik<br>Alessandro Finauri<br>Alessandro Finauri<br>Matheus Motzo<br>Javier facundo Martinez<br>Fabio Ricci<br>Aimone Alletti<br>Nicolas Marechal<br>Aimone Alletti<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Eric Loeppky<br>Fabrizio Gironi<br>Enrico Zappoli<br>Matheus Motzo<br>Jan Kozamernik<br>Jan Kozamernik<br>Matheus Motzo<br>Fabrizio Gironi<br>Jan Kozamernik<br>Fabrizio Gironi<br>Donovan Dzavoronok<br>Nicholas Hoag<br>Aimone Alletti<br>Nicolas Marechal<br>Donovan Dzavoronok<br>Alessandro Finauri<br>Eric Loeppky<br>Donovan Dzavoronok<br>Eric Loeppky<br>Fabio Ricci<br>Jan Kozamernik<br>Nicola Zonta<br>Fabrizio Gironi<br>Jan Kozamernik<br>Nicola Zonta<br>Donovan Dzavoronok<br>Donovan Dzavoronok<br>Alessandro Finauri<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Aimone Alletti<br>Donovan Dzavoronok<br>Matheus Motzo<br>Silvester Meijs<br>Matheus Motzo<br>Benjamin Diez<br>Nemanja Petric<br>Fabio Ricci<br>Eric Loeppky<br>Nicola Zonta<br>Aimone Alletti<br>Nicolas Marechal<br>Donovan Dzavoronok<br>Nicola Zonta<br>Javier facundo Martinez<br>Timo Tammemaa<br>Alessandro Finauri<br>Fabio Ricci<br>Eric Loeppky<br>Benjamin Diez<br>Alessandro Finauri<br>Aimone Alletti<br>Aimone Alletti<br>Nicola Zonta<br>Fabrizio Gironi<br>Nicolas Marechal<br>Aimone Alletti<br>Jan Kozamernik<br>Nemanja Petric<br>Donovan Dzavoronok<br>Silvester Meijs<br>Jan Kozamernik<br>Stijn D'hulst<br>Eric Loeppky<br>Nicolas Marechal<br>Alessandro Finauri<br>Riccardo Vecchi<br>Donovan Dzavoronok<br>Nicolas Marechal<br>Davide Candellaro<br>Nicola Zonta<br>Dawid Gunia<br>Javier facundo Martinez<br>Fabrizio Gironi<br>Silvester Meijs<br>Fabrizio Gironi<br>Donovan Dzavoronok<br>Javier facundo Martinez<br>Fabrizio Gironi<br>Nicola Zonta<br>Fabrizio Gironi<br>Jordan Ewert<br>Eric Loeppky<br>Donovan Dzavoronok<br>Eric Loeppky<br>Fabio Ricci<br>Stijn D'hulst<br>Riccardo Vecchi<br>Nicolas Marechal<br>Enrico Zappoli<br>Donovan Dzavoronok<br>Nicola Zonta<br>Alessandro Finauri<br>Matheus Motzo<br>Fabrizio Gironi<br>Aimone Alletti<br>Riccardo Vecchi<br>Matheus Motzo<br>Alessandro Finauri<br>Riccardo Cengia<br>Aimone Alletti<br>Donovan Dzavoronok<br>Alessandro Finauri<br>Jordan Ewert<br>Eric Loeppky<br>Alessandro Finauri<br>Benjamin Diez<br>Karli Allik<br>Alessandro Pisoni<br>Aimone Alletti<br>Benjamin Diez<br>Jan Kozamernik<br>Eric Loeppky<br>Aimone Alletti<br>Riccardo Cengia<br>Eric Loeppky<br>Eric Loeppky<br>Fabrizio Gironi<br>Eric Loeppky<br>Eric Loeppky<br>Fabrizio Gironi<br>Aimone Alletti<br>Stijn D'hulst<br>Jan Kozamernik<br>Alessandro Finauri<br>Eric Loeppky<br>Donovan Dzavoronok<br>Alessandro Finauri<br>Donovan Dzavoronok<br>Donovan Dzavoronok<br>Matheus Motzo<br>Alessandro Finauri<br>Jordan Ewert<br>Nicolas Marechal<br>Nicolas Marechal<br>Dawid Gunia<br>Benjamin Diez<br>Matheus Motzo<br>Fabrizio Gironi<br>Nicola Zonta<br>Aimone Alletti<br>Alessandro Finauri<br>Nicolas Marechal<br>Donovan Dzavoronok<br>Eric Loeppky<br>Nicholas Hoag<br>Nemanja Petric<br>Fabrizio Gironi<br>Enrico Zappoli<br>Eric Loeppky<br>Dawid Gunia<br>Alessandro Finauri<br>Nicolas Marechal<br>Eric Loeppky<br>Nicola Zonta<br>Nicolas Marechal<br>Fabrizio Gironi<br>Fabio Ricci<br>Aimone Alletti<br>Eric Loeppky<br>Fabrizio Gironi<br>Matheus Motzo<br>Matheus Motzo<br>Nicola Zonta<br>Fabrizio Gironi<br>Alessandro Menazza<br>Alessandro Menazza<br>Nicolas Marechal<br>Fabio Ricci<br>Nicholas Hoag<br>Donovan Dzavoronok<br>Aimone Alletti<br>Fabrizio Gironi<br>Eric Loeppky<br>Alessandro Finauri<br>Nicola Zonta<br>Nicolas Marechal<br>Nemanja Petric<br>Donovan Dzavoronok<br>Jan Kozamernik<br>Donovan Dzavoronok<br>Nemanja Petric<br>Riccardo Cengia<br>Eric Loeppky<br>Nemanja Petric<br>Alessandro Finauri<br>Eric Loeppky<br>Matheus Motzo<br>Jan Kozamernik<br>Nemanja Petric<br>Silvester Meijs<br>Jan Kozamernik<br>Donovan Dzavoronok<br>Alessandro Finauri<br>Aimone Alletti<br>Nicola Zonta<br>Donovan Dzavoronok<br>Fabrizio Gironi<br>Eric Loeppky<br>Riccardo Vecchi<br>Riccardo Cengia<br>Nemanja Petric<br>Fabrizio Gironi<br>Donovan Dzavoronok<br>Jan Kozamernik<br>Riccardo Vecchi<br>Nicolas Marechal<br>Jan Kozamernik<br>Nicolas Marechal<br>Aimone Alletti<br>Fabrizio Gironi<br>Riccardo Cengia<br>Fabio Ricci<br>Nicolas Marechal<br>Benjamin Diez<br>Riccardo Cengia<br>Eric Loeppky<br>Nicola Zonta<br>Nicolas Marechal<br>Jan Kozamernik<br>Fabrizio Gironi<br>Jordan Ewert<br>Donovan Dzavoronok<br>Riccardo Cengia<br>Enrico Zappoli<br>Nemanja Petric<br>Nicola Zonta<br>Jan Kozamernik<br>Alessandro Finauri<br>Fabrizio Gironi<br>Alessandro Finauri<br>Aimone Alletti<br>Fabrizio Gironi<br>Alessandro Finauri<br>Aimone Alletti<br>Alessandro Pisoni<br>Enrico Zappoli<br>Aimone Alletti<br>Eric Loeppky<br>Jan Kozamernik<br>Benjamin Diez<br>Donovan Dzavoronok<br>Riccardo Vecchi<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Javier facundo Martinez<br>Aimone Alletti<br>Nicola Zonta<br>Alessandro Finauri<br>Jan Kozamernik<br>Eric Loeppky<br>Nicola Zonta<br>Stijn D'hulst<br>Fabrizio Gironi<br>Donovan Dzavoronok<br>Davide Candellaro<br>Jan Kozamernik<br>Jordan Ewert<br>Eric Loeppky<br>Silvester Meijs<br>Riccardo Cengia<br>Eric Loeppky<br>Donovan Dzavoronok<br>Fabrizio Gironi<br>Silvester Meijs<br>Jan Kozamernik<br>Fabrizio Gironi<br>Benjamin Diez<br>Aimone Alletti<br>Alessandro Finauri<br>Riccardo Vecchi<br>Fabrizio Gironi<br>Nicolas Marechal<br>Nicolas Marechal<br>Eric Loeppky<br>Nemanja Petric<br>Eric Loeppky<br>Alessandro Finauri<br>Fabrizio Gironi<br>Dawid Gunia<br>Enrico Zappoli<br>Riccardo Cengia<br>Riccardo Vecchi<br>Nicola Zonta<br>Riccardo Vecchi<br>Fabrizio Gironi<br>Donovan Dzavoronok<br>Jan Kozamernik<br>Eric Loeppky<br>Fabio Ricci<br>Silvester Meijs<br>Nicolas Marechal<br>Eric Loeppky<br>Matheus Motzo<br>Fabrizio Gironi<br>Alessandro Pisoni<br>Donovan Dzavoronok<br>Matheus Motzo<br>Alessandro Finauri<br>Riccardo Cengia<br>Fabio Ricci<br>Alessandro Pisoni<br>Aimone Alletti<br>Eric Loeppky<br>Enrico Zappoli<br>Nicola Zonta<br>Donovan Dzavoronok<br>Donovan Dzavoronok<br>Donovan Dzavoronok<br>Nicolas Marechal<br>Alessandro Finauri<br>Silvester Meijs<br>Aimone Alletti<br>Enrico Zappoli<br>Jan Kozamernik<br>Fabrizio Gironi<br>Jan Kozamernik<br>Fabio Ricci<br>Nemanja Petric<br>Nicolas Marechal<br>Jan Kozamernik<br>Enrico Zappoli<br>Riccardo Cengia<br>Jan Kozamernik<br>Fabio Ricci<br>Nicola Zonta<br>Aimone Alletti<br>Nicolas Marechal<br>Aimone Alletti<br>Aimone Alletti<br>Jordan Ewert<br>Matheus Motzo<br>Dawid Gunia<br>Benjamin Diez<br>Donovan Dzavoronok<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Aimone Alletti<br>Enrico Zappoli<br>Nicola Zonta<br>Nicola Zonta<br>Jan Kozamernik<br>Silvester Meijs<br>Silvester Meijs<br>Silvester Meijs<br>Silvester Meijs<br>Alessandro Menazza<br>Donovan Dzavoronok<br>Donovan Dzavoronok<br>Donovan Dzavoronok<br>Donovan Dzavoronok<br>Donovan Dzavoronok<br>Donovan Dzavoronok<br>Donovan Dzavoronok<br>Donovan Dzavoronok<br>Donovan Dzavoronok<br>Riccardo Cengia<br>Riccardo Cengia<br>Riccardo Cengia<br>Riccardo Cengia<br>Riccardo Cengia<br>Riccardo Cengia<br>Riccardo Cengia<br>Riccardo Cengia<br>Riccardo Cengia<br>Diego Frascio<br>Diego Frascio<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Aimone Alletti<br>Aimone Alletti<br>Aimone Alletti<br>Aimone Alletti<br>Aimone Alletti<br>Aimone Alletti<br>Alessandro Pisoni<br>Alessandro Pisoni<br>Alessandro Pisoni<br>Alessandro Pisoni<br>Nemanja Petric<br>Nemanja Petric<br>Nemanja Petric<br>Nicolas Marechal<br>Nicolas Marechal<br>Nicolas Marechal<br>Nicolas Marechal<br>Karli Allik<br>Karli Allik<br>Peter Wohlfahrtstatter<br>Ahmet samet Baltaci<br>Tim Peter<br>Jordan Ewert<br>Nicholas Hoag<br>Fabrizio Gironi<br>Abdulsamet Yalcin<br>Donovan Dzavoronok<br>Nemanja Petric<br>Nicholas Hoag<br>Nicolas Marechal<br>Donovan Dzavoronok<br>Fabrizio Gironi<br>Nicolas Marechal<br>Karli Allik<br>Peter Wohlfahrtstatter<br>Nemanja Petric<br>Riccardo Cengia<br>Nicholas Hoag<br>Nicolas Marechal<br>Arshdeep Dosanjh<br>Rok Satler<br>Stijn Van tilburg<br>Francisco javier Iribarne fernandez<br>Dawid Gunia<br>Patrik Lamanec<br>Alessandro Menazza<br>Bartosz Marianski<br>Timo Tammemaa<br>Stijn D'hulst<br>Przemyslaw Stepien<br>Nicolas Marechal<br>Przemyslaw Stepien<br>Robin Baghdady<br>Gustavo Cavalcanti<br>Alessandro Finauri<br>Jan Kozamernik<br>Matheus Motzo<br>Nicola Zonta<br>Enrico Zappoli<br>Donovan Dzavoronok<br>Jan Kozamernik<br>Donovan Dzavoronok<br>Enrico Zappoli<br>Timo Tammemaa<br>Jan Kozamernik<br>Donovan Dzavoronok<br>Enrico Zappoli<br>Nicola Zonta<br>Alessandro Finauri<br>Alessandro Finauri<br>Enrico Zappoli<br>Adrian Markiewicz<br>Donovan Dzavoronok<br>Enrico Zappoli<br>Eric Loeppky<br>Nicola Zonta<br>Donovan Dzavoronok<br>Silvester Meijs<br>Fabrizio Gironi<br>Benjamin Diez<br>Riccardo Vecchi<br>Silvester Meijs<br>Nicola Zonta<br>Aimone Alletti<br>Nicola Zonta<br>Fabio Ricci<br>Aimone Alletti<br>Riccardo Vecchi<br>Silvester Meijs<br>Luca Vagnetti<br>Nicola Zonta<br>Alessandro Finauri<br>Gabriele Pertoldi<br>Fabrizio Gironi<br>Nicola Zonta<br>Alessandro Pisoni<br>Nicolas Marechal<br>Alessandro Menazza<br>Gabriele Pertoldi<br>Lorenzo Ciampi<br>Hossein Amir<br>Matheus Motzo<br>Nicolas Marechal<br>Aimone Alletti<br>Nicolas Marechal<br>Alessandro Menazza<br>Matheus Motzo<br>Nicolas Marechal<br>Donovan Dzavoronok<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Diego Frascio<br>Fabio Ricci<br>Alessandro Menazza"], ["<PERSON><br><PERSON><PERSON><PERSON> raul <PERSON> arc<PERSON><br><PERSON><br><PERSON><br>Santiago Orduna<br>Santiago Orduna<br><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br>Santiago Orduna<br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br>Santiago Orduna<br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><br>R<PERSON><PERSON> raul <PERSON> arc<PERSON><br>Roamy raul <PERSON> arce<br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br>Roamy raul <PERSON> arc<PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br>R<PERSON>y raul <PERSON> arc<PERSON><br><PERSON><br><PERSON><PERSON><PERSON> raul <PERSON> arc<PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON> raul <PERSON> arce<br><PERSON><PERSON><br><PERSON><PERSON> <PERSON><br>Maksim <PERSON>pozhkov<br><PERSON> Coolman<br><PERSON> Ciancio<PERSON><br><PERSON> Falaschi<br>Brodie Ho<PERSON><br><PERSON> Orduna<br><PERSON> Michieletto<br><PERSON> <PERSON>chieletto<br><PERSON><PERSON>llini<br><PERSON> Falaschi<br>Roamy raul <PERSON> arce<br><PERSON>o <PERSON><br><PERSON> <PERSON>chieletto<br>Nicola Cianciotta<br>Santiago Orduna<br>Davide Luzzi<br>Riccardo Gollini<br>Federico Bonacchi<br>Pardo Mati<br>Federico Bonacchi<br>Riccardo Gollini<br>Brodie Hofer<br>Brodie Hofer<br>Brodie Hofer<br>Federico Bonacchi<br>Roamy raul Alonso arce<br>Santiago Orduna<br>Marco Falaschi<br>Roamy raul Alonso arce<br>Marco Falaschi<br>Osmany Juantorena<br>Bartlomiej Janus<br>Brodie Hofer<br>Osmany Juantorena<br>Santiago Orduna<br>Alessandro Michieletto<br>Davide Luzzi<br>Federico Bonacchi<br>Davide Luzzi<br>Pardo Mati<br>Santiago Orduna<br>Alessandro Michieletto<br>Damiano Catania<br>Riccardo Gollini<br>Alessandro Michieletto<br>Davide Luzzi<br>Pieter Coolman<br>Lorenzo Magliano<br>Brodie Hofer<br>Marco Falaschi<br>Maksim Sapozhkov<br>Damiano Catania<br>Alessandro Michieletto<br>Santiago Orduna<br>Marco Falaschi<br>Alessandro Michieletto<br>Marco Falaschi<br>Maksim Sapozhkov<br>Roamy raul Alonso arce<br>Riccardo Gollini<br>Alessandro Michieletto<br>Santiago Orduna<br>Marco Falaschi<br>Alessandro Michieletto<br>Maksim Sapozhkov<br>Damiano Catania<br>Federico Bonacchi<br>Santiago Orduna<br>Pardo Mati<br>Damiano Catania<br>Marco Falaschi<br>Alen Sket<br>Osmany Juantorena<br>Riccardo Gollini<br>Marco Falaschi<br>Pardo Mati<br>Osmany Juantorena<br>Marco Falaschi<br>Nicola Cianciotta<br>Marco Falaschi<br>Federico Bonacchi<br>Roamy raul Alonso arce<br>Osmany Juantorena<br>Damiano Catania<br>Alessandro Michieletto<br>Riccardo Gollini<br>Riccardo Gollini<br>Lorenzo Magliano<br>Alessandro Michieletto<br>Riccardo Gollini<br>Damiano Catania<br>Alessandro Michieletto<br>Federico Bonacchi<br>Roamy raul Alonso arce<br>Damiano Catania<br>Davide Luzzi<br>Osmany Juantorena<br>Damiano Catania<br>Lorenzo Magliano<br>Osmany Juantorena<br>Alessandro Michieletto<br>Roamy raul Alonso arce<br>Damiano Catania<br>Marco Falaschi<br>Lorenzo Magliano<br>Roamy raul Alonso arce<br>Maksim Sapozhkov<br>Riccardo Gollini<br>Pieter Coolman<br>Santiago Orduna<br>Roamy raul Alonso arce<br>Roamy raul Alonso arce<br>Marco Falaschi<br>Diego Frascio<br>Davide Luzzi<br>Damiano Catania<br>Brodie Hofer<br>Alessandro Michieletto<br>Santiago Orduna<br>Osmany Juantorena<br>Maksim Sapozhkov<br>Roamy raul Alonso arce<br>Alessandro Michieletto<br>Damiano Catania<br>Damiano Catania<br>Marco Falaschi<br>Pardo Mati<br>Marco Falaschi<br>Federico Bonacchi<br>Brodie Hofer<br>Alessandro Michieletto<br>Maksim Sapozhkov<br>Santiago Orduna<br>Santiago Orduna<br>Brodie Hofer<br>Damiano Catania<br>Nicola Cianciotta<br>Alessandro Michieletto<br>Marco Falaschi<br>Roamy raul Alonso arce<br>Davide Luzzi<br>Marco Falaschi<br>Roamy raul Alonso arce<br>Alessandro Michieletto<br>Davide Luzzi<br>Bartlomiej Janus<br>Osmany Juantorena<br>Damiano Catania<br>Alessandro Michieletto<br>Roamy raul Alonso arce<br>Nicola Cianciotta<br>Damiano Catania<br>Alessandro Michieletto<br>Maksim Sapozhkov<br>Riccardo Gollini<br>Damiano Catania<br>Roamy raul Alonso arce<br>Marco Falaschi<br>Roamy raul Alonso arce<br>Roamy raul Alonso arce<br>Marco Falaschi<br>Lorenzo Magliano<br>Marco Falaschi<br>Marco Falaschi<br>Federico Bonacchi<br>Damiano Catania<br>Roamy raul Alonso arce<br>Nicola Cianciotta<br>Roamy raul Alonso arce<br>Nicola Cianciotta<br>Nicola Cianciotta<br>Roamy raul Alonso arce<br>Damiano Catania<br>Alessandro Michieletto<br>Federico Bonacchi<br>Alessandro Michieletto<br>Tino Vuori<br>Damiano Catania<br>Riccardo Gollini<br>Roamy raul Alonso arce<br>Damiano Catania<br>Federico Bonacchi<br>Santiago Orduna<br>Marco Falaschi<br>Roamy raul Alonso arce<br>Riccardo Gollini<br>Alessandro Michieletto<br>Damiano Catania<br>Roamy raul Alonso arce<br>Santiago Orduna<br>Santiago Orduna<br>Osmany Juantorena<br>Marco Falaschi<br>Brodie Hofer<br>Damiano Catania<br>Marco Falaschi<br>Santiago Orduna<br>Roamy raul Alonso arce<br>Marco Falaschi<br>Federico Bonacchi<br>Osmany Juantorena<br>Pardo Mati<br>Brodie Hofer<br>Auke Van de kamp<br>Osmany Juantorena<br>Damiano Catania<br>Santiago Orduna<br>Roamy raul Alonso arce<br>Davide Luzzi<br>Damiano Catania<br>Marco Falaschi<br>Marco Falaschi<br>Damiano Catania<br>Tino Vuori<br>Damiano Catania<br>Alessandro Michieletto<br>Pieter Coolman<br>Marco Falaschi<br>Santiago Orduna<br>Osmany Juantorena<br>Kobe Brems<br>Roamy raul Alonso arce<br>Roamy raul Alonso arce<br>Damiano Catania<br>Alen Sket<br>Federico Bonacchi<br>Roamy raul Alonso arce<br>Alessandro Michieletto<br>Maksim Sapozhkov<br>Roamy raul Alonso arce<br>Riccardo Gollini<br>Davide Luzzi<br>Alessandro Michieletto<br>Diego Frascio<br>Damiano Catania<br>Roamy raul Alonso arce<br>Roamy raul Alonso arce<br>Roamy raul Alonso arce<br>Roamy raul Alonso arce<br>Alessandro Michieletto<br>Alessandro Michieletto<br>Alessandro Michieletto<br>Pardo Mati<br>Roamy raul Alonso arce<br>Riccardo Gollini<br>Alessandro Michieletto<br>Riccardo Gollini<br>Nicola Cianciotta<br>Roamy raul Alonso arce<br>Alessandro Michieletto<br>Santiago Orduna<br>Pardo Mati<br>Roamy raul Alonso arce<br>Marco Falaschi<br>Federico Bonacchi<br>Osmany Juantorena<br>Roamy raul Alonso arce<br>Marco Falaschi<br>Osmany Juantorena<br>Marco Falaschi<br>Roamy raul Alonso arce<br>Alessandro Michieletto<br>Marco Falaschi<br>Diego Frascio<br>Federico Bonacchi<br>Federico Bonacchi<br>Roamy raul Alonso arce<br>Marco Falaschi<br>Alen Sket<br>Pardo Mati<br>Santiago Orduna<br>Alessandro Michieletto<br>Auke Van de kamp<br>Riccardo Gollini<br>Damiano Catania<br>Alessandro Michieletto<br>Marco Falaschi<br>Marco Falaschi<br>Marco Falaschi<br>Marco Falaschi<br>Alessandro Michieletto<br>Alessandro Michieletto<br>Marco Falaschi<br>Damiano Catania<br>Roamy raul Alonso arce<br>Alessandro Michieletto<br>Santiago Orduna<br>Bartlomiej Janus<br>Marco Falaschi<br>Federico Bonacchi<br>Alessandro Michieletto<br>Damiano Catania<br>Osmany Juantorena<br>Roamy raul Alonso arce<br>Alessandro Michieletto<br>Riccardo Gollini<br>Damiano Catania<br>Lorenzo Magliano<br>Adam Zajíèek<br>Bartlomiej Janus<br>Osmany Juantorena<br>Alessandro Michieletto<br>Brodie Hofer<br>Nicola Cianciotta<br>Osmany Juantorena<br>Damiano Catania<br>Santiago Orduna<br>Santiago Orduna<br>Maksim Sapozhkov<br>Marco Falaschi<br>Marco Falaschi<br>Damiano Catania<br>Santiago Orduna<br>Damiano Catania<br>Lorenzo Magliano<br>Roamy raul Alonso arce<br>Federico Bonacchi<br>Maksim Sapozhkov<br>Alessandro Michieletto<br>Damiano Catania<br>Nicola Cianciotta<br>Maksim Sapozhkov<br>Marco Falaschi<br>Pardo Mati<br>Riccardo Gollini<br>Federico Bonacchi<br>Pieter Coolman<br>Damiano Catania<br>Damiano Catania<br>Brodie Hofer<br>Osmany Juantorena<br>Roamy raul Alonso arce<br>Lorenzo Magliano<br>Maksim Sapozhkov<br>Damiano Catania<br>Auke Van de kamp<br>Brodie Hofer<br>Brodie Hofer<br>Maksim Sapozhkov<br>Alessandro Michieletto<br>Santiago Orduna<br>Damiano Catania<br>Damiano Catania<br>Pardo Mati<br>Marco Falaschi<br>Alessandro Michieletto<br>Alessandro Michieletto<br>Alessandro Michieletto<br>Riccardo Gollini<br>Brodie Hofer<br>Nehemiah Mote<br>Marco Falaschi<br>Osmany Juantorena<br>Federico Bonacchi<br>Alessandro Michieletto<br>Nicola Cianciotta<br>Nicola Cianciotta<br>Santiago Orduna<br>Alessandro Michieletto<br>Damiano Catania<br>Roamy raul Alonso arce<br>Santiago Orduna<br>Osmany Juantorena<br>Maksim Sapozhkov<br>Marco Falaschi<br>Roamy raul Alonso arce<br>Marco Falaschi<br>Santiago Orduna<br>Alessandro Michieletto<br>Marco Falaschi<br>Federico Bonacchi<br>Osmany Juantorena<br>Alessandro Michieletto<br>Pardo Mati<br>Maksim Sapozhkov<br>Santiago Orduna<br>Davide Luzzi<br>Pardo Mati<br>Riccardo Gollini<br>Damiano Catania<br>Pardo Mati<br>Marco Falaschi<br>Damiano Catania<br>Marco Falaschi<br>Davide Luzzi<br>Brodie Hofer<br>Alessandro Michieletto<br>Roamy raul Alonso arce<br>Marco Falaschi<br>Santiago Orduna<br>Pardo Mati<br>Riccardo Gollini<br>Lorenzo Magliano<br>Santiago Orduna<br>Santiago Orduna<br>Davide Luzzi<br>Osmany Juantorena<br>Roamy raul Alonso arce<br>Denislav Bardarov<br>Nicola Cianciotta<br>Roamy raul Alonso arce<br>Alessandro Michieletto<br>Osmany Juantorena<br>Lorenzo Magliano<br>Riccardo Gollini<br>Brodie Hofer<br>Davide Luzzi<br>Marco Falaschi<br>Maksim Sapozhkov<br>Lorenzo Magliano<br>Riccardo Gollini<br>Bartlomiej Janus<br>Osmany Juantorena<br>Marco Falaschi<br>Maksim Sapozhkov<br>Alen Sket<br>Osmany Juantorena<br>Riccardo Gollini<br>Alessandro Michieletto<br>Brodie Hofer<br>Marco Falaschi<br>Roamy raul Alonso arce<br>Marco Falaschi<br>Alessandro Michieletto<br>Maksim Sapozhkov<br>Roamy raul Alonso arce<br>Davide Luzzi<br>Lorenzo Magliano<br>Osmany Juantorena<br>Riccardo Gollini<br>Federico Bonacchi<br>Riccardo Gollini<br>Osmany Juantorena<br>Riccardo Gollini<br>Alessandro Michieletto<br>Auke Van de kamp<br>Lorenzo Magliano<br>Roamy raul Alonso arce<br>Alessandro Michieletto<br>Riccardo Gollini<br>Alessandro Michieletto<br>Pardo Mati<br>Alessandro Michieletto<br>Federico Bonacchi<br>Damiano Catania<br>Alessandro Michieletto<br>Osmany Juantorena<br>Damiano Catania<br>Alessandro Michieletto<br>Davide Luzzi<br>Osmany Juantorena<br>Alessandro Michieletto<br>Roamy raul Alonso arce<br>Pardo Mati<br>Damiano Catania<br>Osmany Juantorena<br>Riccardo Gollini<br>Alessandro Michieletto<br>Osmany Juantorena<br>Riccardo Gollini<br>Alessandro Michieletto<br>Alessandro Michieletto<br>Osmany Juantorena<br>Damiano Catania<br>Auke Van de kamp<br>Federico Bonacchi<br>Damiano Catania<br>Alessandro Michieletto<br>Santiago Orduna<br>Damiano Catania<br>Alessandro Michieletto<br>Alen Sket<br>Bartlomiej Janus<br>Roamy raul Alonso arce<br>Roamy raul Alonso arce<br>Auke Van de kamp<br>Damiano Catania<br>Diego Frascio<br>Maksim Sapozhkov<br>Marco Falaschi<br>Roamy raul Alonso arce<br>Osmany Juantorena<br>Riccardo Gollini<br>Riccardo Gollini<br>Riccardo Gollini<br>Riccardo Gollini<br>Riccardo Gollini<br>Riccardo Gollini<br>Riccardo Gollini<br>Riccardo Gollini<br>Riccardo Gollini<br>Riccardo Gollini<br>Riccardo Gollini<br>Riccardo Gollini<br>Lorenzo Magliano<br>Lorenzo Magliano<br>Lorenzo Magliano<br>Lorenzo Magliano<br>Lorenzo Magliano<br>Lorenzo Magliano<br>Lorenzo Magliano<br>Lorenzo Magliano<br>Federico Bonacchi<br>Federico Bonacchi<br>Federico Bonacchi<br>Federico Bonacchi<br>Federico Bonacchi<br>Federico Bonacchi<br>Federico Bonacchi<br>Alessandro Michieletto<br>Alessandro Michieletto<br>Alessandro Michieletto<br>Alessandro Michieletto<br>Alessandro Michieletto<br>Alessandro Michieletto<br>Alessandro Michieletto<br>Maksim Sapozhkov<br>Maksim Sapozhkov<br>Maksim Sapozhkov<br>Maksim Sapozhkov<br>Maksim Sapozhkov<br>Maksim Sapozhkov<br>Marco Falaschi<br>Marco Falaschi<br>Marco Falaschi<br>Marco Falaschi<br>Marco Falaschi<br>Marco Falaschi<br>Damiano Catania<br>Damiano Catania<br>Damiano Catania<br>Damiano Catania<br>Damiano Catania<br>Damiano Catania<br>Tino Vuori<br>Tino Vuori<br>Kobe Brems<br>Caner Ergul<br>Damiano Catania<br>Alen Sket<br>Mateusz Biernat<br>Auke Van de kamp<br>Maksim Sapozhkov<br>Federico Bonacchi<br>Alessandro Michieletto<br>Maksim Sapozhkov<br>Mert Sonmezgul<br>Alessandro Michieletto<br>Riccardo Gollini<br>Kyle Mc cauley<br>Maksim Sapozhkov<br>Riccardo Gollini<br>Tino Vuori<br>Federico Bonacchi<br>Lorenzo Magliano<br>Riccardo Gollini<br>Jonas Sagstetter<br>Arkadiusz Zakieta<br>Bartlomiej Janus<br>Adam Zajíèek<br>Wojciech Ferens<br>Pieter Coolman<br>Marcin Janusz<br>Riccardo Gollini<br>Alessandro Michieletto<br>Bartlomiej Janus<br>Janusz Marcin<br>Adam Bartos<br>Osmany Juantorena<br>Santiago Orduna<br>Davide Luzzi<br>Alessandro Michieletto<br>Alessandro Michieletto<br>Marco Falaschi<br>Osmany Juantorena<br>Nehemiah Mote<br>Alessandro Michieletto<br>Leandro Nascimento dos santos<br>Osmany Juantorena<br>Santiago Orduna<br>Hauke Wagner<br>Davide Luzzi<br>Damiano Catania<br>Santiago Orduna<br>Diego Frascio<br>Damiano Catania<br>Osmany Juantorena<br>Osmany Juantorena<br>Roamy raul Alonso arce<br>Marco Falaschi<br>Pardo Mati<br>Roamy raul Alonso arce<br>Nicola Cianciotta<br>Brodie Hofer<br>Damiano Catania<br>Nicola Cianciotta<br>Davide Luzzi<br>Damiano Catania<br>Pardo Mati<br>Pardo Mati<br>Denislav Bardarov<br>Damiano Catania<br>Osmany Juantorena<br>Roamy raul Alonso arce<br>Damiano Catania<br>Marco Falaschi<br>Lorenzo Magliano<br>Riccardo Gollini<br>Roamy raul Alonso arce<br>Roamy raul Alonso arce<br>Roamy raul Alonso arce<br>Riccardo Gollini<br>Roamy raul Alonso arce<br>Riccardo Gollini<br>Davide Luzzi<br>Abbas Yaqoob<br>Roamy raul Alonso arce<br>Diego Frascio<br>Riccardo Gollini<br>Lorenzo Magliano"], ["<PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON>oma<PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON> thomas <PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON>am<PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON> Rohrs<br><PERSON><PERSON><br><PERSON> Rizzo<br>Denys <PERSON>berda<br>Gage thomas Worsley<br><PERSON> Brizard<br><PERSON> Rizzo<br><PERSON> Vitelli<br><PERSON> Vite<PERSON><br><PERSON> Giani<br><PERSON> D'amico<br>J<PERSON><PERSON>ari<br><PERSON> Sanguinetti<br>Denys Kaliberda<br><PERSON> Comparoni<br><PERSON> D'amico<br>Karol Klos<br>Riccardo Sbertoli<br>Simone Giannelli<br>Marco Rizzo<br>Marco Rizzo<br>Erik Rohrs<br>Antoine Brizard<br>Danny Demyanenko<br>Antoine Brizard<br>Marco Rizzo<br>Giovanni Sanguinetti<br>Marco Vitelli<br>Giovanni Sanguinetti<br>Riccardo Sbertoli<br>Lorenzo Giani<br>Antoine Brizard<br>Francesco D'amico<br>Giovanni Sanguinetti<br>Lorenzo Giani<br>Francesco Comparoni<br>Giovanni Sanguinetti<br>Federico Bonami<br>Francesco D'amico<br>Francesco Bisotto<br>Marco Vitelli<br>Marco Vitelli<br>Denys Kaliberda<br>Francesco Bisotto<br>Marco Rizzo<br>Marco Vitelli<br>Antoine Brizard<br>Marco Rizzo<br>Simone Giannelli<br>Marco Vitelli<br>Simone Giannelli<br>Simone Giannelli<br>Giovanni Sanguinetti<br>Francesco D'amico<br>Simone Giannelli<br>Riccardo Sbertoli<br>Francesco Comparoni<br>Simone Giannelli<br>Francesco Comparoni<br>Marco Vitelli<br>Marco Vitelli<br>Simone Giannelli<br>Giovanni Sanguinetti<br>Marco Vitelli<br>Antoine Brizard<br>Riccardo Sbertoli<br>Francesco D'amico<br>Johannes Tille<br>Simone Giannelli<br>Francesco Bisotto<br>Simone Giannelli<br>Simone Giannelli<br>Riccardo Sbertoli<br>Simone Giannelli<br>Francesco Bisotto<br>Marco Rizzo<br>Simone Giannelli<br>Giovanni Sanguinetti<br>Jacopo Massari<br>Federico Bonami<br>Simone Giannelli<br>Riccardo Sbertoli<br>Marco Vitelli<br>Giovanni Sanguinetti<br>Antoine Brizard<br>Jacopo Massari<br>Giovanni Sanguinetti<br>Simone Giannelli<br>Simone Giannelli<br>Riccardo Sbertoli<br>Riccardo Sbertoli<br>Fernando Gil kreling<br>Giovanni Sanguinetti<br>Karol Klos<br>Riccardo Sbertoli<br>Marco Vitelli<br>Simone Giannelli<br>Marco Rizzo<br>Francesco D'amico<br>Giovanni Sanguinetti<br>Giovanni Sanguinetti<br>Antoine Brizard<br>Francesco D'amico<br>Francesco D'amico<br>Francesco Comparoni<br>Giovanni Sanguinetti<br>Francesco D'amico<br>Danny Demyanenko<br>Jacopo Massari<br>Francesco Bisotto<br>Danny Demyanenko<br>Riccardo Sbertoli<br>Giovanni Sanguinetti<br>Marco Rizzo<br>Fernando Gil kreling<br>Simone Giannelli<br>Antoine Brizard<br>Francesco Comparoni<br>Francesco D'amico<br>Marco Vitelli<br>Riccardo Sbertoli<br>Antoine Brizard<br>Francesco Comparoni<br>Riccardo Sbertoli<br>Marco Rizzo<br>Giovanni Sanguinetti<br>Giovanni Sanguinetti<br>Lorenzo Giani<br>Simone Giannelli<br>Francesco D'amico<br>Giovanni Sanguinetti<br>Francesco Bisotto<br>Danny Demyanenko<br>Francesco D'amico<br>Riccardo Sbertoli<br>Marco Rizzo<br>Erik Rohrs<br>Erik Rohrs<br>Riccardo Sbertoli<br>Simone Giannelli<br>Francesco D'amico<br>Francesco D'amico<br>Jacopo Massari<br>Francesco D'amico<br>Karol Klos<br>Giovanni Sanguinetti<br>Lorenzo Giani<br>Francesco Bisotto<br>Danny Demyanenko<br>Francesco Bisotto<br>Francesco Comparoni<br>Marco Rizzo<br>Antoine Brizard<br>Erik Rohrs<br>Francesco D'amico<br>Antoine Brizard<br>Marco Rizzo<br>Riccardo Sbertoli<br>Erik Rohrs<br>Giovanni Sanguinetti<br>Marco Rizzo<br>Simone Giannelli<br>Giovanni Sanguinetti<br>Michal Superlak<br>Fernando Gil kreling<br>Simone Giannelli<br>Simone Giannelli<br>Simone Giannelli<br>Antoine Brizard<br>Simone Giannelli<br>Riccardo Sbertoli<br>Simone Giannelli<br>Riccardo Sbertoli<br>Giovanni Sanguinetti<br>Giovanni Sanguinetti<br>Marco Vitelli<br>Riccardo Sbertoli<br>Marco Rizzo<br>Denys Kaliberda<br>Federico Bonami<br>Giovanni Sanguinetti<br>Francesco Comparoni<br>Francesco Bisotto<br>Francesco Bisotto<br>Marco Vitelli<br>Francesco D'amico<br>Antoine Brizard<br>Simone Giannelli<br>Fernando Gil kreling<br>Giovanni Sanguinetti<br>Antoine Brizard<br>Giovanni Sanguinetti<br>Simone Giannelli<br>Francesco Comparoni<br>Simone Giannelli<br>Giovanni Sanguinetti<br>Antoine Brizard<br>Riccardo Sbertoli<br>Antoine Brizard<br>Federico Bonami<br>Riccardo Sbertoli<br>Francesco D'amico<br>Marco Rizzo<br>Marco Vitelli<br>Jacopo Massari<br>Francesco D'amico<br>Luca Galiazzo<br>Ben-simon Bonin<br>Simone Giannelli<br>Antoine Brizard<br>Simone Giannelli<br>Marco Rizzo<br>Simone Giannelli<br>Giovanni Sanguinetti<br>Antoine Brizard<br>Marco Vitelli<br>Danny Demyanenko<br>Francesco Bisotto<br>Antoine Brizard<br>Francesco D'amico<br>Francesco D'amico<br>Riccardo Sbertoli<br>Riccardo Sbertoli<br>Fernando Gil kreling<br>Simone Giannelli<br>Jacopo Massari<br>Michiel Ahyi<br>Simone Giannelli<br>Federico Bonami<br>Francesco D'amico<br>Simone Giannelli<br>Federico Bonami<br>Michiel Ahyi<br>Francesco Bisotto<br>Simone Giannelli<br>Fernando Gil kreling<br>Giovanni Sanguinetti<br>Simone Giannelli<br>Simone Giannelli<br>Lorenzo Giani<br>Giovanni Sanguinetti<br>Riccardo Sbertoli<br>Francesco D'amico<br>Marco Rizzo<br>Marco Rizzo<br>Riccardo Sbertoli<br>Fernando Gil kreling<br>Antoine Brizard<br>Marco Vitelli<br>Francesco Comparoni<br>Francesco Bisotto<br>Antoine Brizard<br>Marco Vitelli<br>Antoine Brizard<br>Marco Rizzo<br>Francesco Bisotto<br>Antoine Brizard<br>Marco Rizzo<br>Giovanni Sanguinetti<br>Denys Kaliberda<br>Marco Vitelli<br>Karol Klos<br>Giovanni Sanguinetti<br>Riccardo Sbertoli<br>Giovanni Sanguinetti<br>Francesco Bisotto<br>Simone Giannelli<br>Marco Vitelli<br>Francesco Bisotto<br>Francesco D'amico<br>Riccardo Sbertoli<br>Antoine Brizard<br>Giovanni Sanguinetti<br>Francesco Comparoni<br>Simone Giannelli<br>Simone Giannelli<br>Giovanni Sanguinetti<br>Antoine Brizard<br>Marco Rizzo<br>Denys Kaliberda<br>Lorenzo Giani<br>Simone Giannelli<br>Riccardo Sbertoli<br>Antoine Brizard<br>Simone Giannelli<br>Marco Rizzo<br>Danny Demyanenko<br>Antoine Brizard<br>Riccardo Sbertoli<br>Francesco Bisotto<br>Simone Giannelli<br>Francesco Bisotto<br>Antoine Brizard<br>Francesco D'amico<br>Simone Giannelli<br>Marco Vitelli<br>Tommaso Guzzo<br>Francesco Bisotto<br>Riccardo Sbertoli<br>Francesco D'amico<br>Giovanni Sanguinetti<br>Marco Rizzo<br>Antoine Brizard<br>Francesco Bisotto<br>Erik Rohrs<br>Francesco D'amico<br>Francesco D'amico<br>Abdel-aziz Doumbia<br>Francesco Bisotto<br>Francesco Bisotto<br>Simone Giannelli<br>Riccardo Sbertoli<br>Federico Bonami<br>Antoine Brizard<br>Riccardo Sbertoli<br>Francesco D'amico<br>Francesco Bisotto<br>Karol Klos<br>Riccardo Sbertoli<br>Giovanni Sanguinetti<br>Giovanni Sanguinetti<br>Karol Klos<br>Francesco Bisotto<br>Ben-simon Bonin<br>Antoine Brizard<br>Marco Rizzo<br>Erik Rohrs<br>Marco Vitelli<br>Jacopo Massari<br>Riccardo Sbertoli<br>Lorenzo Giani<br>Antoine Brizard<br>Francesco D'amico<br>Simone Giannelli<br>Marco Vitelli<br>Francesco D'amico<br>Giovanni Sanguinetti<br>Antoine Brizard<br>Francesco Bisotto<br>Marco Rizzo<br>Federico Bonami<br>Simone Giannelli<br>Antoine Brizard<br>Danny Demyanenko<br>Enis ali Ay<br>Denys Kaliberda<br>Francesco D'amico<br>Marco Vitelli<br>Riccardo Sbertoli<br>Francesco D'amico<br>Francesco D'amico<br>Marco Rizzo<br>Francesco Comparoni<br>Simone Giannelli<br>Simone Giannelli<br>Giovanni Sanguinetti<br>Marco Vitelli<br>Giovanni Sanguinetti<br>Danny Demyanenko<br>Marco Rizzo<br>Danny Demyanenko<br>Giovanni Sanguinetti<br>Francesco D'amico<br>Antoine Brizard<br>Lorenzo Giani<br>Antoine Brizard<br>Marco Vitelli<br>Francesco Comparoni<br>Francesco D'amico<br>Danny Demyanenko<br>Antoine Brizard<br>Danny Demyanenko<br>Marco Rizzo<br>Marco Rizzo<br>Antoine Brizard<br>Francesco Comparoni<br>Francesco Bisotto<br>Francesco Bisotto<br>Antoine Brizard<br>Giovanni Sanguinetti<br>Antoine Brizard<br>Marco Rizzo<br>Marco Vitelli<br>Jacopo Massari<br>Antoine Brizard<br>Antoine Brizard<br>Benjamin Toniutti<br>Francesco Bisotto<br>Francesco D'amico<br>Tommaso Guzzo<br>Riccardo Sbertoli<br>Marco Rizzo<br>Marco Rizzo<br>Giovanni Sanguinetti<br>Antoine Brizard<br>Simone Giannelli<br>Danny Demyanenko<br>Gage thomas Worsley<br>Francesco D'amico<br>Marco Rizzo<br>Marco Vitelli<br>Francesco Comparoni<br>Giovanni Sanguinetti<br>Antoine Brizard<br>Antoine Brizard<br>Lorenzo Giani<br>Francesco D'amico<br>Fernando Gil kreling<br>Riccardo Sbertoli<br>Simone Giannelli<br>Jacopo Massari<br>Erik Rohrs<br>Johannes Tille<br>Michiel Ahyi<br>Marco Vitelli<br>Francesco Comparoni<br>Antoine Brizard<br>Antoine Brizard<br>Francesco Bisotto<br>Giovanni Sanguinetti<br>Marco Rizzo<br>Riccardo Sbertoli<br>Antoine Brizard<br>Antoine Brizard<br>Antoine Brizard<br>Jacopo Massari<br>Simone Giannelli<br>Karol Klos<br>Francesco Bisotto<br>Enis ali Ay<br>Simone Giannelli<br>Marco Vitelli<br>Giovanni Sanguinetti<br>Giovanni Sanguinetti<br>Antoine Brizard<br>Martti Juhkami<br>Riccardo Sbertoli<br>Simone Giannelli<br>Lorenzo Giani<br>Francesco D'amico<br>Enis ali Ay<br>Francesco D'amico<br>Francesco Bisotto<br>Giovanni Sanguinetti<br>Francesco Bisotto<br>Giovanni Sanguinetti<br>Antoine Brizard<br>Riccardo Sbertoli<br>Marco Rizzo<br>Giovanni Sanguinetti<br>Denys Kaliberda<br>Simone Giannelli<br>Francesco D'amico<br>Francesco D'amico<br>Tommaso Guzzo<br>Francesco Bisotto<br>Giovanni Sanguinetti<br>Riccardo Sbertoli<br>Marco Rizzo<br>Giovanni Sanguinetti<br>Francesco D'amico<br>Marco Vitelli<br>Simone Giannelli<br>Erik Rohrs<br>Erik Rohrs<br>Francesco D'amico<br>Antoine Brizard<br>Federico Bonami<br>Riccardo Sbertoli<br>Marco Vitelli<br>Marco Rizzo<br>Antoine Brizard<br>Federico Bonami<br>Gage thomas Worsley<br>Marco Vitelli<br>Francesco Comparoni<br>Marco Vitelli<br>Andrea Truocchio<br>Marco Rizzo<br>Simone Giannelli<br>Francesco Bisotto<br>Fernando Gil kreling<br>Francesco Comparoni<br>Marco Vitelli<br>Antoine Brizard<br>Lorenzo Giani<br>Riccardo Sbertoli<br>Marco Rizzo<br>Riccardo Sbertoli<br>Federico Bonami<br>Giovanni Sanguinetti<br>Giovanni Sanguinetti<br>Michiel Ahyi<br>Erik Rohrs<br>Simone Giannelli<br>Marco Rizzo<br>Giovanni Sanguinetti<br>Simone Giannelli<br>Lorenzo Giani<br>Riccardo Sbertoli<br>Marco Rizzo<br>Giovanni Sanguinetti<br>Antoine Brizard<br>Antoine Brizard<br>Giovanni Sanguinetti<br>Marco Rizzo<br>Francesco Bisotto<br>Marco Rizzo<br>Marco Rizzo<br>Marco Vitelli<br>Francesco D'amico<br>Antoine Brizard<br>Enis ali Ay<br>Riccardo Sbertoli<br>Erik Rohrs<br>Francesco D'amico<br>Francesco Comparoni<br>Antoine Brizard<br>Giovanni Sanguinetti<br>Lorenzo Giani<br>Denys Kaliberda<br>Simone Giannelli<br>Giovanni Sanguinetti<br>Federico Bonami<br>Marco Rizzo<br>Riccardo Sbertoli<br>Francesco D'amico<br>Luca Beccaro<br>Antoine Brizard<br>Francesco Bisotto<br>Antoine Brizard<br>Francesco Bisotto<br>Francesco D'amico<br>Francesco Bisotto<br>Giovanni Sanguinetti<br>Francesco Bisotto<br>Francesco D'amico<br>Simone Giannelli<br>Lorenzo Giani<br>Giovanni Sanguinetti<br>Riccardo Sbertoli<br>Giovanni Sanguinetti<br>Francesco Bisotto<br>Francesco Comparoni<br>Marco Rizzo<br>Antoine Brizard<br>Giovanni Sanguinetti<br>Jacopo Massari<br>Simone Giannelli<br>Riccardo Sbertoli<br>Antoine Brizard<br>Simone Giannelli<br>Antoine Brizard<br>Giovanni Sanguinetti<br>Marco Vitelli<br>Simone Giannelli<br>Marco Vitelli<br>Riccardo Sbertoli<br>Giovanni Sanguinetti<br>Riccardo Sbertoli<br>Riccardo Sbertoli<br>Simone Giannelli<br>Antoine Brizard<br>Riccardo Sbertoli<br>Riccardo Sbertoli<br>Francesco Bisotto<br>Francesco Bisotto<br>Andrea Truocchio<br>Francesco Bisotto<br>Riccardo Sbertoli<br>Riccardo Sbertoli<br>Riccardo Sbertoli<br>Marco Rizzo<br>Simone Giannelli<br>Francesco D'amico<br>Erik Rohrs<br>Erik Rohrs<br>Giovanni Sanguinetti<br>Marco Rizzo<br>Federico Bonami<br>Francesco D'amico<br>Francesco D'amico<br>Antoine Brizard<br>Riccardo Sbertoli<br>Marco Vitelli<br>Francesco D'amico<br>Fernando Gil kreling<br>Gabriel Garcia<br>Francesco D'amico<br>Francesco D'amico<br>Giovanni Sanguinetti<br>Francesco Comparoni<br>Riccardo Sbertoli<br>Fernando Gil kreling<br>Francesco D'amico<br>Francesco Bisotto<br>Marco Rizzo<br>Giovanni Sanguinetti<br>Francesco Comparoni<br>Denys Kaliberda<br>Francesco Bisotto<br>Simone Giannelli<br>Francesco D'amico<br>Giovanni Sanguinetti<br>Riccardo Sbertoli<br>Lorenzo Giani<br>Giovanni Sanguinetti<br>Francesco D'amico<br>Giovanni Sanguinetti<br>Denys Kaliberda<br>Denys Kaliberda<br>Francesco Comparoni<br>Danny Demyanenko<br>Simone Giannelli<br>Giovanni Sanguinetti<br>Francesco D'amico<br>Giovanni Sanguinetti<br>Denys Kaliberda<br>Antoine Brizard<br>Simone Giannelli<br>Giovanni Sanguinetti<br>Giovanni Sanguinetti<br>Danny Demyanenko<br>Francesco D'amico<br>Simone Giannelli<br>Marco Vitelli<br>Erik Rohrs<br>Marco Rizzo<br>Riccardo Sbertoli<br>Francesco Bisotto<br>Gage thomas Worsley<br>Marco Rizzo<br>Riccardo Sbertoli<br>Marco Vitelli<br>Francesco Bisotto<br>Antoine Brizard<br>Marco Vitelli<br>Antoine Brizard<br>Enis ali Ay<br>Marco Vitelli<br>Francesco Comparoni<br>Giovanni Sanguinetti<br>Francesco D'amico<br>Francesco Fusaro<br>Francesco Fusaro<br>Francesco Fusaro<br>Francesco Fusaro<br>Francesco Fusaro<br>Francesco Bernardis<br>Giovanni Sanguinetti<br>Giovanni Sanguinetti<br>Giovanni Sanguinetti<br>Giovanni Sanguinetti<br>Giovanni Sanguinetti<br>Giovanni Sanguinetti<br>Giovanni Sanguinetti<br>Giovanni Sanguinetti<br>Giovanni Sanguinetti<br>Giovanni Sanguinetti<br>Giovanni Sanguinetti<br>Giovanni Sanguinetti<br>Marco Vitelli<br>Marco Vitelli<br>Marco Vitelli<br>Marco Vitelli<br>Marco Vitelli<br>Marco Vitelli<br>Marco Vitelli<br>Riccardo Sbertoli<br>Riccardo Sbertoli<br>Riccardo Sbertoli<br>Riccardo Sbertoli<br>Riccardo Sbertoli<br>Riccardo Sbertoli<br>Antoine Brizard<br>Antoine Brizard<br>Antoine Brizard<br>Antoine Brizard<br>Antoine Brizard<br>Antoine Brizard<br>Fernando Gil kreling<br>Marco Rizzo<br>Marco Rizzo<br>Marco Rizzo<br>Marco Rizzo<br>Marco Rizzo<br>Marco Rizzo<br>Francesco D'amico<br>Francesco D'amico<br>Francesco D'amico<br>Francesco D'amico<br>Francesco D'amico<br>Francesco D'amico<br>Francesco D'amico<br>Francesco D'amico<br>Denys Kaliberda<br>Denys Kaliberda<br>Denys Kaliberda<br>Denys Kaliberda<br>Denys Kaliberda<br>Simone Giannelli<br>Simone Giannelli<br>Simone Giannelli<br>Simone Giannelli<br>Simone Giannelli<br>Federico Bonami<br>Federico Bonami<br>Federico Bonami<br>Federico Bonami<br>Ben-simon Bonin<br>Ben-simon Bonin<br>Bernardo Westermann<br>Fatih Cihan<br>Michal Superlak<br>Gage thomas Worsley<br>Marco Vitelli<br>Enis ali Ay<br>Antoine Brizard<br>Riccardo Sbertoli<br>Federico Bonami<br>Giovanni Sanguinetti<br>Simone Giannelli<br>Gage thomas Worsley<br>Luuk Hofhuis<br>Giovanni Sanguinetti<br>Ben-simon Bonin<br>Bernardo Westermann<br>Antoine Brizard<br>Simone Giannelli<br>Sebastian Pozo hernandez<br>Enis ali Ay<br>Giovanni Sanguinetti<br>Johannes Tille<br>Karol Klos<br>Martti Juhkami<br>Adam Lorenc<br>Piotr Lukasik<br>Michiel Ahyi<br>Karol Klos<br>Giovanni Sanguinetti<br>Luciano Aloisi<br>Antoine Brizard<br>Francesco Bisotto<br>Francesco Comparoni<br>Francesco Fusaro<br>Marco Rizzo<br>Riccardo Sbertoli<br>Riccardo Sbertoli<br>Francesco D'amico<br>Lorenzo Giani<br>Lorenzo Giani<br>Francesco Bisotto<br>Marco Vitelli<br>Riccardo Sbertoli<br>Jacopo Massari<br>Antoine Pothron<br>Marco Vitelli<br>Giovanni Sanguinetti<br>Simone Giannelli<br>Francesco Fusaro<br>Jacopo Massari<br>Lorenzo Giani<br>Marco Vitelli<br>Lorenzo Giani<br>Benjamin Toniutti<br>Francesco D'amico<br>Erik Beggiato<br>Jacopo Massari<br>Garrett Ethan<br>Giovanni Sanguinetti<br>Giovanni Sanguinetti<br>Francesco Comparoni<br>Marco Vitelli<br>Francesco D'amico<br>Simone Giannelli<br>Antoine Brizard<br>Giovanni Sanguinetti<br>Francesco D'amico<br>Lorenzo Giani<br>Antoine Brizard<br>Danny Demyanenko<br>Erik Rohrs<br>Riccardo Sbertoli<br>Marco Vitelli<br>Antoine Brizard<br>Marco Rizzo<br>Marco Vitelli<br>Riccardo Sbertoli<br>Giovanni Sanguinetti<br>Lorenzo Giani<br>Danny Demyanenko<br>Lorenzo Giani<br>Marco Vitelli<br>Francesco D'amico<br>Simone Giannelli<br>Antoine Brizard<br>Marco Vitelli<br>Luca Beccaro<br>Benjamin Toniutti<br>Abdel-aziz Doumbia<br>Francesco Bisotto<br>Francesco Fusaro<br>Francesco D'amico<br>Marco Rizzo<br>Francesco Bisotto<br>Francesco D'amico<br>Simone Giannelli<br>Erik Rohrs<br>Giovanni Sanguinetti<br>Antoine Brizard<br>Francesco D'amico<br>Marco Vitelli<br>Luca Beccaro"], ["<PERSON> j<PERSON>me<br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON> timothy <PERSON><br><PERSON><PERSON> ferragut<br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON> j<PERSON><br><PERSON><PERSON><PERSON>han<br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON> j<PERSON><br><PERSON><PERSON> ferragut<br><PERSON> j<PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON> timothy <PERSON><br><PERSON><PERSON> ferragut<br><PERSON><PERSON><br><PERSON> timothy <PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON> j<PERSON><br><PERSON><br><PERSON>ndo<br><PERSON> Tondo<br><PERSON> Tondo<br>Fabi<PERSON> Ba<PERSON>o<br>Dragan <PERSON><br>Fabio Ba<PERSON>o<br><PERSON> Jovo<PERSON><br>Jordi <PERSON> ferragut<br><PERSON> <PERSON>ei<br>Or<PERSON> Cavuto<br><PERSON> Jo<PERSON><br><PERSON>ka <PERSON>tila<br><PERSON>abio Ba<PERSON>o<br><PERSON> <PERSON>op<PERSON>ri<br>Fabio Ba<PERSON><PERSON><br><PERSON> timothy Maar<br><PERSON> <PERSON> jai<PERSON><br>Konstantin Abaev<br>Filippo Federici<br>Giacomo Raffaelli<br>Francesco Zoppellari<br>Raphael Vieira de oliveira<br>Alessandro Tondo<br>Dragan Stankovic<br>Fabio Balaso<br>Francesco Fusaro<br>Dragan Stankovic<br>Fabio Balaso<br>Dragan Stankovic<br>Ferre Reggers<br>Marko Sedlacek<br>Konstantin Abaev<br>Fabio Balaso<br>Dragan Stankovic<br>Raphael Vieira de oliveira<br>Giacomo Raffaelli<br>Konstantin Abaev<br>Ángel Trinidad<br>Stephen timothy Maar<br>Konstantin Abaev<br>Alessandro Tondo<br>Ángel Trinidad<br>Luka Marttila<br>Francesco Zoppellari<br>Dragan Stankovic<br>Dragan Stankovic<br>Fabio Balaso<br>Francesco Zoppellari<br>Francesco Fusaro<br>Dragan Stankovic<br>Francesco Zoppellari<br>Ángel Trinidad<br>Fabio Balaso<br>Andrea Mattei<br>Filippo Federici<br>Marko Sedlacek<br>Jesus Herrera jaime<br>Manuele Lucconi<br>Andrea Mattei<br>Dragan Stankovic<br>Francesco Zoppellari<br>Jordi Ramón ferragut<br>Stephen timothy Maar<br>Oreste Cavuto<br>Jordi Ramón ferragut<br>Marko Sedlacek<br>Raphael Vieira de oliveira<br>Fabio Balaso<br>Jordi Ramón ferragut<br>Oreste Cavuto<br>Raphael Vieira de oliveira<br>Stephen timothy Maar<br>Dragan Stankovic<br>Oreste Cavuto<br>Jesus Herrera jaime<br>Raphael Vieira de oliveira<br>Alessandro Tondo<br>Nikola Jovovic<br>Konstantin Abaev<br>Jesus Herrera jaime<br>Dragan Stankovic<br>Ferre Reggers<br>Jesus Herrera jaime<br>Marko Sedlacek<br>Nikola Jovovic<br>Wout D'heer<br>Dragan Stankovic<br>Jordi Ramón ferragut<br>Oreste Cavuto<br>Stephen timothy Maar<br>Stephen timothy Maar<br>Dragan Stankovic<br>Ferre Reggers<br>Marko Sedlacek<br>Stephen timothy Maar<br>Fabio Balaso<br>Ferre Reggers<br>Andrea Mattei<br>Fabio Balaso<br>Stephen timothy Maar<br>Jesus Herrera jaime<br>Francesco Fusaro<br>Jesus Herrera jaime<br>Ferre Reggers<br>Jesus Herrera jaime<br>Alessandro Tondo<br>Alessandro Tondo<br>Ferre Reggers<br>Fabio Balaso<br>Luka Marttila<br>Fabio Balaso<br>Francesco Zoppellari<br>Fabio Balaso<br>Jordi Ramón ferragut<br>Filippo Federici<br>Stephen timothy Maar<br>Marko Sedlacek<br>Alessandro Tondo<br>Jordi Ramón ferragut<br>Nikola Jovovic<br>Francesco Zoppellari<br>Stephen timothy Maar<br>Jesus Herrera jaime<br>Oreste Cavuto<br>Francesco Fusaro<br>Nikola Jovovic<br>Oreste Cavuto<br>Andrea Mattei<br>Dragan Stankovic<br>Fabio Balaso<br>Francesco Fusaro<br>Fabio Balaso<br>Konstantin Abaev<br>Jesus Herrera jaime<br>Fabio Balaso<br>Jesus Herrera jaime<br>Jesus Herrera jaime<br>Dragan Stankovic<br>Andrea Mattei<br>Ferre Reggers<br>Jordi Ramón ferragut<br>Ferre Reggers<br>Francesco Fusaro<br>Dragan Stankovic<br>Fabio Balaso<br>Jesus Herrera jaime<br>Oreste Cavuto<br>Marko Sedlacek<br>Konstantin Abaev<br>Manuele Lucconi<br>Ferre Reggers<br>Dragan Stankovic<br>Ferre Reggers<br>Dragan Stankovic<br>Jordi Ramón ferragut<br>Dragan Stankovic<br>Luciano Vicentin<br>Jesus Herrera jaime<br>Filippo Federici<br>Jesus Herrera jaime<br>Francesco Zoppellari<br>Giacomo Raffaelli<br>Dragan Stankovic<br>Fabio Balaso<br>Raphael Vieira de oliveira<br>Jesus Herrera jaime<br>Ángel Trinidad<br>Francesco Fusaro<br>Nikola Jovovic<br>Nikola Jovovic<br>Ferre Reggers<br>Stephen timothy Maar<br>Jordi Ramón ferragut<br>Filippo Federici<br>Francesco Zoppellari<br>Dragan Stankovic<br>Oreste Cavuto<br>Marko Sedlacek<br>Ángel Trinidad<br>Jesus Herrera jaime<br>Dragan Stankovic<br>Francesco Zoppellari<br>Francesco Fusaro<br>Stephen timothy Maar<br>Filippo Federici<br>Andrea Mattei<br>Raphael Vieira de oliveira<br>Jesus Herrera jaime<br>Nikola Jovovic<br>Jesus Herrera jaime<br>Ángel Trinidad<br>Stephen timothy Maar<br>Oreste Cavuto<br>Dragan Stankovic<br>Mikko Esko<br>Jordi Ramón ferragut<br>Alessandro Tondo<br>Dragan Stankovic<br>Fabio Balaso<br>Raphael Vieira de oliveira<br>Jordi Ramón ferragut<br>Francesco Fusaro<br>Oreste Cavuto<br>Fabio Balaso<br>Alessandro Tondo<br>Stephen timothy Maar<br>Ferre Reggers<br>Stephen timothy Maar<br>Francesco Zoppellari<br>Nikola Jovovic<br>Oreste Cavuto<br>Dragan Stankovic<br>Marko Sedlacek<br>Raphael Vieira de oliveira<br>Marko Sedlacek<br>Giacomo Raffaelli<br>Ferre Reggers<br>Fabio Balaso<br>Jesus Herrera jaime<br>Dragan Stankovic<br>Simon Plaskie<br>Fabio Balaso<br>Jordi Ramón ferragut<br>Jente De vries<br>Ferre Reggers<br>Marko Sedlacek<br>Stephen timothy Maar<br>Nikola Jovovic<br>Ferre Reggers<br>Filippo Federici<br>Alessandro Tondo<br>Jordi Ramón ferragut<br>Fabio Balaso<br>Jesus Herrera jaime<br>Jordi Ramón ferragut<br>Raphael Vieira de oliveira<br>Ferre Reggers<br>Jordi Ramón ferragut<br>Ángel Trinidad<br>Oreste Cavuto<br>Jesus Herrera jaime<br>Francesco Fusaro<br>Fabio Balaso<br>Nikola Jovovic<br>Francesco Zoppellari<br>Marko Sedlacek<br>Mirza Lagumdzija<br>Oreste Cavuto<br>Stephen timothy Maar<br>Jesus Herrera jaime<br>Stephen timothy Maar<br>Stephen timothy Maar<br>Stephen timothy Maar<br>Francesco Zoppellari<br>Dragan Stankovic<br>Marko Sedlacek<br>Dragan Stankovic<br>Jesus Herrera jaime<br>Luka Marttila<br>Francesco Fusaro<br>Fabio Balaso<br>Ferre Reggers<br>Stephen timothy Maar<br>Fabio Balaso<br>Dragan Stankovic<br>Jordi Ramón ferragut<br>Giacomo Raffaelli<br>Francesco Zoppellari<br>Jordi Ramón ferragut<br>Dragan Stankovic<br>Nikola Jovovic<br>Francesco Zoppellari<br>Ferre Reggers<br>Stephen timothy Maar<br>Jesus Herrera jaime<br>Ferre Reggers<br>Filippo Federici<br>Filippo Federici<br>Marko Sedlacek<br>Luka Marttila<br>Jesus Herrera jaime<br>Fabio Balaso<br>Fabio Balaso<br>Dragan Stankovic<br>Fabio Balaso<br>Ferre Reggers<br>Jesus Herrera jaime<br>Luka Marttila<br>Dragan Stankovic<br>Ferre Reggers<br>Jesus Herrera jaime<br>Fabio Balaso<br>Raphael Vieira de oliveira<br>Stephen timothy Maar<br>Ferre Reggers<br>Filippo Federici<br>Stephen timothy Maar<br>Fabio Balaso<br>Jesus Herrera jaime<br>Oreste Cavuto<br>Andrea Mattei<br>Oreste Cavuto<br>Jesus Herrera jaime<br>Stephen timothy Maar<br>Marko Sedlacek<br>Dragan Stankovic<br>Jesus Herrera jaime<br>Dragan Stankovic<br>Fabio Balaso<br>Filippo Federici<br>Oreste Cavuto<br>Dragan Stankovic<br>Simon Plaskie<br>Filippo Federici<br>Ferre Reggers<br>Jesus Herrera jaime<br>Marko Sedlacek<br>Oreste Cavuto<br>Oreste Cavuto<br>Jordi Ramón ferragut<br>Stephen timothy Maar<br>Dragan Stankovic<br>Jordi Ramón ferragut<br>Dragan Stankovic<br>Marko Sedlacek<br>Filippo Federici<br>Filippo Federici<br>Jesus Herrera jaime<br>Francesco Zoppellari<br>Dragan Stankovic<br>Dragan Stankovic<br>Stephen timothy Maar<br>Oreste Cavuto<br>Jordi Ramón ferragut<br>Jesus Herrera jaime<br>Fabio Balaso<br>Francesco Zoppellari<br>Dragan Stankovic<br>Marko Sedlacek<br>Ángel Trinidad<br>Dragan Stankovic<br>Oreste Cavuto<br>Fabio Balaso<br>Luka Marttila<br>Giacomo Raffaelli<br>Fabio Balaso<br>Marko Sedlacek<br>Fabio Balaso<br>Marko Sedlacek<br>Konstantin Abaev<br>Fabio Balaso<br>Raphael Vieira de oliveira<br>Raphael Vieira de oliveira<br>Ferre Reggers<br>Francesco Zoppellari<br>Francesco Zoppellari<br>Ángel Trinidad<br>Alessandro Tondo<br>Nikola Jovovic<br>Oreste Cavuto<br>Luka Marttila<br>Konstantin Abaev<br>Jesus Herrera jaime<br>Dragan Stankovic<br>Stephen timothy Maar<br>Stephen timothy Maar<br>Dragan Stankovic<br>Francesco Zoppellari<br>Stephen timothy Maar<br>Oreste Cavuto<br>Andrea Mattei<br>Giacomo Raffaelli<br>Fabio Balaso<br>Dragan Stankovic<br>Raphael Vieira de oliveira<br>Dragan Stankovic<br>Marko Sedlacek<br>Fabio Balaso<br>Francesco Zoppellari<br>Dragan Stankovic<br>Fabio Balaso<br>Mikko Esko<br>Stephen timothy Maar<br>Francesco Zoppellari<br>Ferre Reggers<br>Andrea Mattei<br>Ferre Reggers<br>Jordi Ramón ferragut<br>Luka Marttila<br>Marko Sedlacek<br>Ángel Trinidad<br>Francesco Zoppellari<br>Nikola Jovovic<br>Oreste Cavuto<br>Alessandro Tondo<br>Dragan Stankovic<br>Konstantin Abaev<br>Nikola Jovovic<br>Luka Marttila<br>Konstantin Abaev<br>Francesco Zoppellari<br>Oreste Cavuto<br>Fabio Balaso<br>Ángel Trinidad<br>Jesus Herrera jaime<br>Ferre Reggers<br>Fabio Balaso<br>Fabio Balaso<br>Andrea Mattei<br>Filippo Federici<br>Ángel Trinidad<br>Fabio Balaso<br>Alessandro Tondo<br>Dragan Stankovic<br>Jesus Herrera jaime<br>Jesus Herrera jaime<br>Konstantin Abaev<br>Oreste Cavuto<br>Oreste Cavuto<br>Jordi Ramón ferragut<br>Marko Sedlacek<br>Fabio Balaso<br>Mirza Lagumdzija<br>Fabio Balaso<br>Oreste Cavuto<br>Andrea Mattei<br>Oreste Cavuto<br>Francesco Fusaro<br>Marko Sedlacek<br>Marko Sedlacek<br>Ferre Reggers<br>Giacomo Raffaelli<br>Fabio Balaso<br>Simon Plaskie<br>Raphael Vieira de oliveira<br>Francesco Zoppellari<br>Andrea Mattei<br>Giacomo Raffaelli<br>Dragan Stankovic<br>Dragan Stankovic<br>Stephen timothy Maar<br>Stephen timothy Maar<br>Marko Sedlacek<br>Dragan Stankovic<br>Jordi Ramón ferragut<br>Dragan Stankovic<br>Ferre Reggers<br>Konstantin Abaev<br>Andrea Mattei<br>Nikola Jovovic<br>Jesus Herrera jaime<br>Ángel Trinidad<br>Giacomo Raffaelli<br>Francesco Zoppellari<br>Fabio Balaso<br>Giacomo Raffaelli<br>Ferre Reggers<br>Francesco Zoppellari<br>Jesus Herrera jaime<br>Nikola Jovovic<br>Raphael Vieira de oliveira<br>Dragan Stankovic<br>Francesco Zoppellari<br>Oreste Cavuto<br>Marko Sedlacek<br>Dragan Stankovic<br>Raphael Vieira de oliveira<br>Stephen timothy Maar<br>Manuele Lucconi<br>Francesco Zoppellari<br>Konstantin Abaev<br>Fabio Balaso<br>Jordi Ramón ferragut<br>Oreste Cavuto<br>Nikola Jovovic<br>Jesus Herrera jaime<br>Jesus Herrera jaime<br>Francesco Zoppellari<br>Fabio Balaso<br>Dragan Stankovic<br>Jordi Ramón ferragut<br>Jordi Ramón ferragut<br>Francesco Zoppellari<br>Andrea Mattei<br>Fabio Balaso<br>Fabio Balaso<br>Nikola Jovovic<br>Dragan Stankovic<br>Jesus Herrera jaime<br>Mirza Lagumdzija<br>Jesus Herrera jaime<br>Oreste Cavuto<br>Jesus Herrera jaime<br>Nikola Jovovic<br>Stephen timothy Maar<br>Ferre Reggers<br>Ferre Reggers<br>Mirza Lagumdzija<br>Jesus Herrera jaime<br>Dragan Stankovic<br>Oreste Cavuto<br>Jesus Herrera jaime<br>Fabio Balaso<br>Luka Marttila<br>Ferre Reggers<br>Ángel Trinidad<br>Ferre Reggers<br>Fabio Balaso<br>Dragan Stankovic<br>Luka Marttila<br>Dragan Stankovic<br>Ferre Reggers<br>Bulbul Bedirhan<br>Francesco Zoppellari<br>Nikola Jovovic<br>Raphael Vieira de oliveira<br>Fabio Balaso<br>Jesus Herrera jaime<br>Giacomo Raffaelli<br>Dragan Stankovic<br>Ferre Reggers<br>Stephen timothy Maar<br>Filippo Federici<br>Luka Marttila<br>Jesus Herrera jaime<br>Nikola Jovovic<br>Francesco Fusaro<br>Luka Marttila<br>Fabio Balaso<br>Jesus Herrera jaime<br>Fabio Balaso<br>Fabio Balaso<br>Nikola Jovovic<br>Francesco Zoppellari<br>Ferre Reggers<br>Jesus Herrera jaime<br>Konstantin Abaev<br>Jesus Herrera jaime<br>Jesus Herrera jaime<br>Oreste Cavuto<br>Ángel Trinidad<br>Alessandro Tondo<br>Dragan Stankovic<br>Dragan Stankovic<br>Andrea Mattei<br>Ferre Reggers<br>Dragan Stankovic<br>Dragan Stankovic<br>Oreste Cavuto<br>Andrea Mattei<br>Jordi Ramón ferragut<br>Francesco Fusaro<br>Stephen timothy Maar<br>Francesco Zoppellari<br>Dragan Stankovic<br>Jesus Herrera jaime<br>Ángel Trinidad<br>Luka Marttila<br>Raphael Vieira de oliveira<br>Jesus Herrera jaime<br>Francesco Zoppellari<br>Oreste Cavuto<br>Giacomo Raffaelli<br>Jesus Herrera jaime<br>Dragan Stankovic<br>Fabio Balaso<br>Marko Sedlacek<br>Jordi Ramón ferragut<br>Filippo Federici<br>Raphael Vieira de oliveira<br>Giacomo Raffaelli<br>Oreste Cavuto<br>Francesco Fusaro<br>Oreste Cavuto<br>Dragan Stankovic<br>Fabio Balaso<br>Fabio Balaso<br>Filippo Federici<br>Andrea Mattei<br>Oreste Cavuto<br>Francesco Fusaro<br>Francesco Zoppellari<br>Oreste Cavuto<br>Francesco Zoppellari<br>Fabio Balaso<br>Andrea Mattei<br>Ferre Reggers<br>Simon Plaskie<br>Francesco Fusaro<br>Jesus Herrera jaime<br>Stephen timothy Maar<br>Giacomo Raffaelli<br>Jordi Ramón ferragut<br>Francesco Zoppellari<br>Fabio Balaso<br>Simon Plaskie<br>Oreste Cavuto<br>Jesus Herrera jaime<br>Ferre Reggers<br>Oreste Cavuto<br>Dragan Stankovic<br>Oreste Cavuto<br>Dragan Stankovic<br>Jesus Herrera jaime<br>Stephen timothy Maar<br>Francesco Fusaro<br>Dragan Stankovic<br>Marko Sedlacek<br>Dragan Stankovic<br>Giacomo Raffaelli<br>Manuele Lucconi<br>Fabio Balaso<br>Jesus Herrera jaime<br>Fabio Balaso<br>Raphael Vieira de oliveira<br>Francesco Zoppellari<br>Dragan Stankovic<br>Luka Marttila<br>Jordi Ramón ferragut<br>Luka Marttila<br>Francesco Fusaro<br>Jesus Herrera jaime<br>Francesco Fusaro<br>Jesus Herrera jaime<br>Marko Sedlacek<br>Marko Sedlacek<br>Konstantin Abaev<br>Fabio Balaso<br>Dragan Stankovic<br>Dragan Stankovic<br>Jesus Herrera jaime<br>Giacomo Raffaelli<br>Nikola Jovovic<br>Raphael Vieira de oliveira<br>Dragan Stankovic<br>Oreste Cavuto<br>Jesus Herrera jaime<br>Francesco Zoppellari<br>Jordi Ramón ferragut<br>Dragan Stankovic<br>Jordi Ramón ferragut<br>Stephen timothy Maar<br>Jordi Ramón ferragut<br>Stephen timothy Maar<br>Stephen timothy Maar<br>Fabio Balaso<br>Francesco Zoppellari<br>Oreste Cavuto<br>Dragan Stankovic<br>Jesus Herrera jaime<br>Raphael Vieira de oliveira<br>Jesus Herrera jaime<br>Fabio Balaso<br>Alessandro Tondo<br>Jesus Herrera jaime<br>Oreste Cavuto<br>Fabio Balaso<br>Wout D'heer<br>Wout D'heer<br>Wout D'heer<br>Wout D'heer<br>Wout D'heer<br>Wout D'heer<br>Wout D'heer<br>Wout D'heer<br>Wout D'heer<br>Wout D'heer<br>Wout D'heer<br>Wout D'heer<br>Wout D'heer<br>Wout D'heer<br>Wout D'heer<br>Wout D'heer<br>Wout D'heer<br>Ghulam Amer<br>Oreste Cavuto<br>Oreste Cavuto<br>Oreste Cavuto<br>Oreste Cavuto<br>Oreste Cavuto<br>Oreste Cavuto<br>Oreste Cavuto<br>Oreste Cavuto<br>Oreste Cavuto<br>Dragan Stankovic<br>Dragan Stankovic<br>Dragan Stankovic<br>Dragan Stankovic<br>Dragan Stankovic<br>Dragan Stankovic<br>Dragan Stankovic<br>Dragan Stankovic<br>Dragan Stankovic<br>Dragan Stankovic<br>Dragan Stankovic<br>Dragan Stankovic<br>Francesco Zoppellari<br>Francesco Zoppellari<br>Francesco Zoppellari<br>Francesco Zoppellari<br>Francesco Zoppellari<br>Francesco Zoppellari<br>Francesco Zoppellari<br>Francesco Zoppellari<br>Francesco Zoppellari<br>Filippo Federici<br>Filippo Federici<br>Filippo Federici<br>Filippo Federici<br>Filippo Federici<br>Filippo Federici<br>Filippo Federici<br>Filippo Federici<br>Filippo Federici<br>Raphael Vieira de oliveira<br>Raphael Vieira de oliveira<br>Raphael Vieira de oliveira<br>Raphael Vieira de oliveira<br>Raphael Vieira de oliveira<br>Manuele Lucconi<br>Manuele Lucconi<br>Manuele Lucconi<br>Manuele Lucconi<br>Manuele Lucconi<br>Fabio Balaso<br>Fabio Balaso<br>Fabio Balaso<br>Fabio Balaso<br>Fabio Balaso<br>Fabio Balaso<br>Fabio Balaso<br>Fabio Balaso<br>Marko Sedlacek<br>Marko Sedlacek<br>Marko Sedlacek<br>Marko Sedlacek<br>Marko Sedlacek<br>Marko Sedlacek<br>Jesus Herrera jaime<br>Jesus Herrera jaime<br>Jesus Herrera jaime<br>Jesus Herrera jaime<br>Jesus Herrera jaime<br>Giacomo Raffaelli<br>Giacomo Raffaelli<br>Giacomo Raffaelli<br>Mikko Esko<br>Mikko Esko<br>Ivo Casas<br>Ivo Casas<br>Dragan Stankovic<br>Jente De vries<br>Murilo Radke<br>Berkan Eryuz<br>Oreste Cavuto<br>Francesco Fusaro<br>Luciano Vicentin<br>Mirza Lagumdzija<br>Raphael Vieira de oliveira<br>Raphael Vieira de oliveira<br>Oreste Cavuto<br>Giacomo Raffaelli<br>Jesus Herrera jaime<br>Dragan Stankovic<br>Francesco Fusaro<br>Marko Sedlacek<br>Ferdy Van dijk<br>Dragan Stankovic<br>Mikko Esko<br>Giacomo Raffaelli<br>Jesus Herrera jaime<br>Francesco Zoppellari<br>Filippo Federici<br>Aapeli Kouki<br>Mirza Lagumdzija<br>Bulbul Bedirhan<br>Filippo Federici<br>Dragan Stankovic<br>Ferdinand Tille<br>Dragan Stankovic<br>Kajetan Kubicki<br>Jakub Jarosz<br>Oreste Cavuto<br>Bartlomiej Lemanski<br>Simon Plaskie<br>Twan Wiltenburg<br>Dragan Stankovic<br>Raphael Vieira de oliveira<br>Jordi Ramón ferragut<br>Ferre Reggers<br>Alessandro Tondo<br>Ángel Trinidad<br>Nikola Jovovic<br>Dragan Stankovic<br>Nikola Jovovic<br>Oreste Cavuto<br>Ángel Trinidad<br>Oreste Cavuto<br>Ángel Trinidad<br>Oreste Cavuto<br>Alessandro Tondo<br>Oreste Cavuto<br>Luca Ramon<br>Dragan Stankovic<br>Jesus Herrera jaime<br>Alessandro Tondo<br>Timon Peckmann<br>Jordi Ramón ferragut<br>Francesco Zoppellari<br>Jordi Ramón ferragut<br>Ferre Reggers<br>Marko Sedlacek<br>Nikola Jovovic<br>Alessandro Tondo<br>Stephen timothy Maar<br>Dragan Stankovic<br>Ferre Reggers<br>Francesco Zoppellari<br>Jesus Herrera jaime<br>Nikola Jovovic<br>Dragan Stankovic<br>Stephen timothy Maar<br>Fabio Balaso<br>Konstantin Abaev<br>Wout D'heer<br>Ferre Reggers<br>Ángel Trinidad<br>Ferre Reggers<br>Oreste Cavuto<br>Jordi Ramón ferragut<br>Oreste Cavuto<br>Stephen timothy Maar<br>Dragan Stankovic<br>Wout D'heer<br>Jordi Ramón ferragut<br>Ferre Reggers<br>Jordi Ramón ferragut<br>Luka Marttila<br>Jesus Herrera jaime<br>Filippo Federici<br>Dragan Stankovic<br>Francesco Zoppellari<br>Raphael Vieira de oliveira<br>Twam Wiltenburg<br>Jesus Herrera jaime<br>Oreste Cavuto<br>Fabio Balaso<br>Ángel Trinidad<br>Fabio Balaso<br>Nikola Jovovic<br>Luka Marttila<br>Dragan Stankovic<br>Konstantin Abaev"], ["<PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON> timothy <PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br>A<PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br>A<PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br>A<PERSON><PERSON><br><PERSON><br><PERSON> l<PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON>uza<br><PERSON><PERSON>in<PERSON>e<br><PERSON> Toscani<br>Agustin Loser<br>Nemanja Masulovic<br><PERSON> <PERSON><br><PERSON> luca<PERSON> <PERSON>uza<br>Cosi<PERSON> Ba<PERSON>tra<br>Nemanja Masulovic<br><PERSON><PERSON> Boninfante<br><PERSON> Plak<br>G<PERSON><PERSON> <PERSON><PERSON>i<br><PERSON> P<PERSON><PERSON>i<br><PERSON> <PERSON><br><PERSON> timothy Maar<br><PERSON> lucarelli Souza<br><PERSON> Keemink<br>Omar Biglino<br>Fabian Plak<br>Cosimo Balestra<br>Nik Mujanovic<br>Stephen timothy Maar<br>Giulio Pinali<br>Kamil Rychlicki<br>Fabian Plak<br>Domenico Pace<br>Cosimo Balestra<br>Mattia Boninfante<br>Kamil Rychlicki<br>Giulio Pinali<br>Domenico Pace<br>Agustin Loser<br>Ricardo lucarelli Souza<br>Eric Loeppky<br>Ricardo lucarelli Souza<br>Kamil Rychlicki<br>Nik Mujanovic<br>Nemanja Masulovic<br>Eric Loeppky<br>Domenico Pace<br>Ricardo lucarelli Souza<br>Diego Frascio<br>Davide Saitta<br>Ricardo lucarelli Souza<br>Agustin Loser<br>Ricardo lucarelli Souza<br>Omar Biglino<br>Agustin Loser<br>Eric Loeppky<br>Domenico Pace<br>Domenico Pace<br>Ibrahim Yucel halil<br>Matthijs Verhanneman<br>Omar Biglino<br>Fabian Plak<br>Domenico Pace<br>Domenico Pace<br>Agustin Loser<br>Kamil Rychlicki<br>Kamil Rychlicki<br>Jedrzej Gruszczynski<br>Ricardo lucarelli Souza<br>Nemanja Masulovic<br>Marco Pellacani<br>Blair Bann<br>Marco Pellacani<br>Agustin Loser<br>Cosimo Balestra<br>Ricardo lucarelli Souza<br>Giulio Pinali<br>Burutay Subasi<br>Nik Mujanovic<br>Davide Saitta<br>Domenico Pace<br>Agustin Loser<br>Michael Zanni<br>Ricardo lucarelli Souza<br>Alessandro Toscani<br>Domenico Pace<br>Cosimo Balestra<br>Nemanja Masulovic<br>Ricardo lucarelli Souza<br>Ricardo lucarelli Souza<br>Ricardo lucarelli Souza<br>Stephen timothy Maar<br>Ricardo lucarelli Souza<br>Domenico Pace<br>Kamil Rychlicki<br>Agustin Loser<br>Fabian Plak<br>Cosimo Balestra<br>Domenico Pace<br>Cosimo Balestra<br>Mattia Boninfante<br>Theo Mohwinkel<br>Ricardo lucarelli Souza<br>Eric Loeppky<br>Agustin Loser<br>Agustin Loser<br>Alessandro Toscani<br>Domenico Pace<br>Stephen timothy Maar<br>Giulio Pinali<br>Agustin Loser<br>Davide Saitta<br>Ricardo lucarelli Souza<br>Domenico Pace<br>Marco Pellacani<br>Stephen timothy Maar<br>Marco Pellacani<br>Marco Pellacani<br>Agustin Loser<br>Agustin Loser<br>Cosimo Balestra<br>Ricardo lucarelli Souza<br>Nemanja Masulovic<br>Cosimo Balestra<br>Ricardo lucarelli Souza<br>Nik Mujanovic<br>Stephen timothy Maar<br>Michael Zanni<br>Michael Zanni<br>Nik Mujanovic<br>Davide Saitta<br>Fabian Plak<br>Andrea Zanotti<br>Nemanja Masulovic<br>Burutay Subasi<br>Marco Pellacani<br>Agustin Loser<br>Domenico Pace<br>Fabian Plak<br>Davide Saitta<br>Giulio Pinali<br>Domenico Pace<br>Nemanja Masulovic<br>Fabian Plak<br>Domenico Pace<br>Stephen timothy Maar<br>Fabian Plak<br>Michael Zanni<br>Ricardo lucarelli Souza<br>Mattia Boninfante<br>Davide Saitta<br>Omar Biglino<br>Agustin Loser<br>Agustin Loser<br>Stephen timothy Maar<br>Nemanja Masulovic<br>Ricardo lucarelli Souza<br>Stephen timothy Maar<br>Stephen timothy Maar<br>Nik Mujanovic<br>Stephen timothy Maar<br>Davide Saitta<br>Domenico Pace<br>Domenico Pace<br>Domenico Pace<br>Davide Saitta<br>Agustin Loser<br>Domenico Pace<br>Nik Mujanovic<br>Fabian Plak<br>Domenico Pace<br>Nik Mujanovic<br>Kamil Rychlicki<br>Eric Loeppky<br>Ricardo lucarelli Souza<br>Agustin Loser<br>Fabian Plak<br>Giulio Pinali<br>Davide Saitta<br>Nik Mujanovic<br>Kamil Rychlicki<br>Domenico Pace<br>Ricardo lucarelli Souza<br>Giulio Pinali<br>Fabian Plak<br>Davide Saitta<br>Stephen timothy Maar<br>Ricardo lucarelli Souza<br>Mattia Boninfante<br>Giulio Pinali<br>Ricardo lucarelli Souza<br>Axel Truhtchev<br>Nemanja Masulovic<br>Stephen timothy Maar<br>Fabian Plak<br>Nik Mujanovic<br>Domenico Pace<br>Marco Pellacani<br>Alessandro Toscani<br>Kamil Rychlicki<br>Agustin Loser<br>Davide Saitta<br>Alessandro Toscani<br>Agustin Loser<br>Mattia Boninfante<br>Domenico Pace<br>Giulio Pinali<br>Giulio Pinali<br>Burutay Subasi<br>Domenico Pace<br>Domenico Pace<br>Ricardo lucarelli Souza<br>Jedrzej Gruszczynski<br>Eric Loeppky<br>Kamil Rychlicki<br>Fabian Plak<br>Kamil Rychlicki<br>Giulio Pinali<br>Domenico Pace<br>Ibrahim Yucel halil<br>Jedrzej Gruszczynski<br>Agustin Loser<br>Fabian Plak<br>Fabian Plak<br>Cosimo Balestra<br>Nemanja Masulovic<br>Giulio Pinali<br>Cosimo Balestra<br>Domenico Pace<br>Agustin Loser<br>Omar Biglino<br>Ricardo lucarelli Souza<br>Mattia Boninfante<br>Mattia Boninfante<br>Davide Saitta<br>Domenico Pace<br>Agustin Loser<br>Giulio Pinali<br>Stephen timothy Maar<br>Agustin Loser<br>Nik Mujanovic<br>Domenico Pace<br>Gustavo Cavalcanti<br>Agustin Loser<br>Fabian Plak<br>Diego Frascio<br>Agustin Loser<br>Alessandro Toscani<br>Omar Biglino<br>Omar Biglino<br>Alessandro Toscani<br>Giulio Pinali<br>Kamil Rychlicki<br>Theo Mohwinkel<br>Giulio Pinali<br>Domenico Pace<br>Fabian Plak<br>Nik Mujanovic<br>Alessandro Toscani<br>Davide Saitta<br>Fabian Plak<br>Agustin Loser<br>Domenico Pace<br>Agustin Loser<br>Domenico Pace<br>Omar Biglino<br>Kamil Rychlicki<br>Ricardo lucarelli Souza<br>Domenico Pace<br>Alessandro Toscani<br>Mattia Boninfante<br>Ricardo lucarelli Souza<br>Kamil Rychlicki<br>Agustin Loser<br>Cosimo Balestra<br>Agustin Loser<br>Nemanja Masulovic<br>Agustin Loser<br>Kamil Rychlicki<br>Ricardo lucarelli Souza<br>Agustin Loser<br>Agustin Loser<br>Marco Pellacani<br>Domenico Pace<br>Agustin Loser<br>Stephen timothy Maar<br>Jedrzej Gruszczynski<br>Ricardo lucarelli Souza<br>Domenico Pace<br>Stephen timothy Maar<br>Fabian Plak<br>Domenico Pace<br>Gustavo Cavalcanti<br>Domenico Pace<br>Nik Mujanovic<br>Fabian Plak<br>Davide Saitta<br>Ricardo lucarelli Souza<br>Matthijs Verhanneman<br>Domenico Pace<br>Davide Saitta<br>Stephen timothy Maar<br>Kamil Rychlicki<br>Mattia Boninfante<br>Fabian Plak<br>Davide Saitta<br>Theo Mohwinkel<br>Mattia Boninfante<br>Davide Saitta<br>Fabian Plak<br>Ricardo lucarelli Souza<br>Domenico Pace<br>Davide Saitta<br>Cosimo Balestra<br>Jedrzej Gruszczynski<br>Agustin Loser<br>Fabian Plak<br>Alessandro Toscani<br>Nik Mujanovic<br>Mattia Boninfante<br>Kamil Rychlicki<br>Michael Zanni<br>Kamil Rychlicki<br>Eric Loeppky<br>Omar Biglino<br>Agustin Loser<br>Cosimo Balestra<br>Ricardo lucarelli Souza<br>Kamil Rychlicki<br>Marco Pellacani<br>Michael Zanni<br>Agustin Loser<br>Michael Zanni<br>Ricardo lucarelli Souza<br>Domenico Pace<br>Agustin Loser<br>Alessandro Toscani<br>Ricardo lucarelli Souza<br>Mattia Boninfante<br>Domenico Pace<br>Domenico Pace<br>Fabian Plak<br>Matthijs Verhanneman<br>Fabian Plak<br>Domenico Pace<br>Domenico Pace<br>Domenico Pace<br>Agustin Loser<br>Agustin Loser<br>Agustin Loser<br>Gustavo Cavalcanti<br>Davide Saitta<br>Agustin Loser<br>Agustin Loser<br>Nik Mujanovic<br>Michael Zanni<br>Davide Saitta<br>Giulio Pinali<br>Mattia Boninfante<br>Agustin Loser<br>Agustin Loser<br>Fabian Plak<br>Giulio Pinali<br>Ricardo lucarelli Souza<br>Fabian Plak<br>Domenico Pace<br>Domenico Pace<br>Nemanja Masulovic<br>Fabian Plak<br>Nik Mujanovic<br>Fabian Plak<br>Cosimo Balestra<br>Omar Biglino<br>Alessandro Toscani<br>Agustin Loser<br>Domenico Pace<br>Agustin Loser<br>Fabian Plak<br>Domenico Pace<br>Mattia Boninfante<br>Jedrzej Gruszczynski<br>Ricardo lucarelli Souza<br>Diego Frascio<br>Fabian Plak<br>Alessandro Toscani<br>Matthijs Verhanneman<br>Kamil Rychlicki<br>Nemanja Masulovic<br>Ricardo lucarelli Souza<br>Fabian Plak<br>Fabian Plak<br>Alessandro Toscani<br>Domenico Pace<br>Domenico Pace<br>Alessandro Toscani<br>Marco Pellacani<br>Kamil Rychlicki<br>Nik Mujanovic<br>Davide Saitta<br>Agustin Loser<br>Eric Loeppky<br>Agustin Loser<br>Severi Savonsalmi<br>Omar Biglino<br>Michael Zanni<br>Stephen timothy Maar<br>Nik Mujanovic<br>Ricardo lucarelli Souza<br>Stephen timothy Maar<br>Giulio Pinali<br>Domenico Pace<br>Domenico Pace<br>Alessandro Toscani<br>Fabian Plak<br>Alessandro Toscani<br>Nemanja Masulovic<br>Michael Zanni<br>Agustin Loser<br>Ionut alin Ambrose<br>Fabian Plak<br>Domenico Pace<br>Kamil Rychlicki<br>Marco Pellacani<br>Marco Pellacani<br>Chris Ogink<br>Agustin Loser<br>Agustin Loser<br>Giulio Pinali<br>Michael Zanni<br>Agustin Loser<br>Michael Zanni<br>Theo Mohwinkel<br>Agustin Loser<br>Cosimo Balestra<br>Omar Biglino<br>Burutay Subasi<br>Mattia Boninfante<br>Agustin Loser<br>Ricardo lucarelli Souza<br>Cosimo Balestra<br>Theo Mohwinkel<br>Agustin Loser<br>Diego Frascio<br>Ricardo lucarelli Souza<br>Nemanja Masulovic<br>Domenico Pace<br>Ricardo lucarelli Souza<br>Kamil Rychlicki<br>Agustin Loser<br>Matthijs Verhanneman<br>Alessandro Toscani<br>Davide Saitta<br>Ricardo lucarelli Souza<br>Stephen timothy Maar<br>Kamil Rychlicki<br>Michael Zanni<br>Michael Zanni<br>Marco Pellacani<br>Eric Loeppky<br>Davide Saitta<br>Ricardo lucarelli Souza<br>Ricardo lucarelli Souza<br>Ricardo lucarelli Souza<br>Ricardo lucarelli Souza<br>Ricardo lucarelli Souza<br>Ricardo lucarelli Souza<br>Domenico Pace<br>Agustin Loser<br>Domenico Pace<br>Ricardo lucarelli Souza<br>Fabian Plak<br>Ricardo lucarelli Souza<br>Ricardo lucarelli Souza<br>Kamil Rychlicki<br>Davide Saitta<br>Nemanja Masulovic<br>Domenico Pace<br>Ionut alin Ambrose<br>Ricardo lucarelli Souza<br>Marco Pellacani<br>Alessandro Toscani<br>Giulio Pinali<br>Theo Mohwinkel<br>Agustin Loser<br>Giulio Pinali<br>Fabian Plak<br>Mattia Boninfante<br>Domenico Pace<br>Agustin Loser<br>Nemanja Masulovic<br>Nemanja Masulovic<br>Nemanja Masulovic<br>Nemanja Masulovic<br>Nemanja Masulovic<br>Nemanja Masulovic<br>Nemanja Masulovic<br>Agustin Loser<br>Domenico Pace<br>Domenico Pace<br>Domenico Pace<br>Domenico Pace<br>Domenico Pace<br>Domenico Pace<br>Domenico Pace<br>Domenico Pace<br>Domenico Pace<br>Domenico Pace<br>Davide Saitta<br>Davide Saitta<br>Davide Saitta<br>Davide Saitta<br>Davide Saitta<br>Davide Saitta<br>Davide Saitta<br>Davide Saitta<br>Stephen timothy Maar<br>Stephen timothy Maar<br>Stephen timothy Maar<br>Stephen timothy Maar<br>Stephen timothy Maar<br>Stephen timothy Maar<br>Stephen timothy Maar<br>Stephen timothy Maar<br>Stephen timothy Maar<br>Ricardo lucarelli Souza<br>Ricardo lucarelli Souza<br>Agustin Loser<br>Agustin Loser<br>Agustin Loser<br>Agustin Loser<br>Agustin Loser<br>Agustin Loser<br>Agustin Loser<br>Eric Loeppky<br>Eric Loeppky<br>Eric Loeppky<br>Eric Loeppky<br>Eric Loeppky<br>Eric Loeppky<br>Michael Zanni<br>Michael Zanni<br>Michael Zanni<br>Michael Zanni<br>Michael Zanni<br>Michael Zanni<br>Kamil Rychlicki<br>Kamil Rychlicki<br>Kamil Rychlicki<br>Kamil Rychlicki<br>Omar Biglino<br>Omar Biglino<br>Omar Biglino<br>Severi Savonsalmi<br>Severi Savonsalmi<br>Gaspar Hugo<br>Gaspar Hugo<br>Nemanja Masulovic<br>Omar Biglino<br>Davide Saitta<br>Chris Ogink<br>Gokhan Gokgoz<br>Alperay Demirciler<br>Ricardo lucarelli Souza<br>Omar Biglino<br>Blair Bann<br>Theo Mohwinkel<br>Burutay Subasi<br>Batuhan Avci<br>Domenico Pace<br>Omar Biglino<br>Kamil Rychlicki<br>Agustin Loser<br>Theo Mohwinkel<br>Michael Zanni<br>Ricardo lucarelli Souza<br>Severi Savonsalmi<br>Omar Biglino<br>Davide Saitta<br>Kamil Rychlicki<br>Stephen timothy Maar<br>Tomi Saarinen<br>Domenico Pace<br>Burutay Subasi<br>Ibrahim Yucel halil<br>Stephen timothy Maar<br>Anton Brehme<br>Jonas Kaminski<br>David Seybering<br>Mariusz Magnuszewski<br>Jedrzej Gruszczynski<br>Wessel Keemink<br>Damian Czetowicz<br>Jakub Lewandowski<br>Pawel Rusin<br>Matthijs Verhanneman<br>Adrian Staszewski<br>Nemanja Masulovic<br>Alessandro Toscani<br>Fabian Plak<br>Domenico Pace<br>Domenico Pace<br>Nik Mujanovic<br>Domenico Pace<br>Alexandre Gabin<br>Omar Biglino<br>Alessandro Toscani<br>Nemanja Masulovic<br>Davide Saitta<br>Agustin Loser<br>Nemanja Masulovic<br>Ricardo lucarelli Souza<br>Giulio Pinali<br>Dusan Radivojevic<br>Alessandro Toscani<br>Ricardo lucarelli Souza<br>Giulio Pinali<br>Dusan Radivojevic<br>Giulio Pinali<br>Ricardo lucarelli Souza<br>Cosimo Balestra<br>Leonardo Barbanti<br>Alessandro Toscani<br>Agustin Loser<br>Marco Pellacani<br>Marco Valbusa<br>Domenico Pace<br>Agustin Loser<br>Cosimo Balestra<br>Riccardo Della ventura<br>Giulio Pinali<br>Cristian Andreoli<br>Valentino Righetti<br>Agustin Loser<br>Ricardo lucarelli Souza<br>Agustin Loser<br>Alessandro Menazza<br>Domenico Pace<br>Adrian Staszewski<br>Kamil Rychlicki<br>Nik Mujanovic<br>Marco Pellacani<br>Giulio Pinali<br>Davide Saitta<br>Eric Loeppky<br>Giulio Pinali<br>Nik Mujanovic<br>Kamil Rychlicki"], ["<PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON> venero<br><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON> <PERSON><PERSON><br><PERSON><PERSON><PERSON> hid<PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br>Hampus Ekstrand<br><PERSON><PERSON><br><PERSON><PERSON><PERSON> hidalgo<br>Wil<PERSON><PERSON> Leon venero<br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br>Wil<PERSON><PERSON> venero<br><PERSON><br><PERSON><br>Wil<PERSON><PERSON> venero<br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON>um<PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON>um<PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON> hidalgo<br>W<PERSON><PERSON><PERSON> venero<br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON> venero<br>Wil<PERSON>o Leon venero<br><PERSON>ladzislau <PERSON>skiba<br>Noumory Keita<br>Yoandy <PERSON>l hidalgo<br>Noumory Keita<br>Wilfredo Leon venero<br>Tommaso Guzzo<br>Vuk Todorovic<br>Timothée Carle<br>Ibrahim <PERSON>ani<br>Yoandy Leal hidalgo<br>Riccardo Pinelli<br>Yoandy Leal hidalgo<br>Earvin Ngapeth<br>Wilfredo Leon venero<br>Ivan Zaytsev<br>Yoandy Leal hidalgo<br>Earvin Ngapeth<br>Noumory Keita<br>Earvin Ngapeth<br>Earvin Ngapeth<br>Ivan Zaytsev<br>Tommaso Guzzo<br>Alessandro Toscani<br>Yoandy Leal hidalgo<br>Wilfredo Leon venero<br>Hannes Gerken<br>Jacob Ekman<br>Uladzislau Davyskiba<br>Yoandy Leal hidalgo<br>Hampus Ekstrand<br>Wilfredo Leon venero<br>Noumory Keita<br>Wilfredo Leon venero<br>Gyorgy Grozer<br>Hampus Ekstrand<br>Wilfredo Leon venero<br>Uladzislau Davyskiba<br>Yoandy Leal hidalgo<br>Ivan Zaytsev<br>Tommaso Guzzo<br>Hampus Ekstrand<br>Noumory Keita<br>Earvin Ngapeth<br>Hampus Ekstrand<br>Ramazan efe Mandiraci<br>Noumory Keita<br>Jean Patry<br>Riccardo Pinelli<br>Hampus Ekstrand<br>Yoandy Leal hidalgo<br>Jean Patry<br>Ivan Zaytsev<br>Yoandy Leal hidalgo<br>Uladzislau Davyskiba<br>Poriya Hossein khanzadeh<br>Yoandy Leal hidalgo<br>Jean Patry<br>Noumory Keita<br>Hampus Ekstrand<br>Earvin Ngapeth<br>Noumory Keita<br>Poriya Hossein khanzadeh<br>Jean Patry<br>Ivan Zaytsev<br>Noumory Keita<br>Jean Patry<br>Ivan Zaytsev<br>Alessandro Toscani<br>Tommaso Guzzo<br>Ivan Zaytsev<br>Tommaso Guzzo<br>Ibrahim Lawani<br>Yoandy Leal hidalgo<br>Ivan Zaytsev<br>Hampus Ekstrand<br>Jean Patry<br>Wilfredo Leon venero<br>Noumory Keita<br>Gyorgy Grozer<br>Noumory Keita<br>Yuga Tarumi<br>Jean Patry<br>Ivan Zaytsev<br>Tommaso Guzzo<br>Wilfredo Leon venero<br>Noumory Keita<br>Wilfredo Leon venero<br>Wilfredo Leon venero<br>Noumory Keita<br>Gyorgy Grozer<br>Earvin Ngapeth<br>Ivan Zaytsev<br>Noumory Keita<br>Noumory Keita<br>Ivan Zaytsev<br>Hampus Ekstrand<br>Noumory Keita<br>Noumory Keita<br>Noumory Keita<br>Jome Vandamme<br>Hannes Gerken<br>Ivan Zaytsev<br>Wilfredo Leon venero<br>Wilfredo Leon venero<br>Earvin Ngapeth<br>Vuk Todorovic<br>Earvin Ngapeth<br>Poriya Hossein khanzadeh<br>Yoandy Leal hidalgo<br>Gyorgy Grozer<br>Tommaso Guzzo<br>Earvin Ngapeth<br>Uladzislau Davyskiba<br>Ivan Zaytsev<br>Alessandro Toscani<br>Hampus Ekstrand<br>Wilfredo Leon venero<br>Hampus Ekstrand<br>Tommaso Guzzo<br>Wilfredo Leon venero<br>Jean Patry<br>Riccardo Pinelli<br>Noumory Keita<br>Yoandy Leal hidalgo<br>Hampus Ekstrand<br>Uladzislau Davyskiba<br>Riccardo Pinelli<br>Ivan Zaytsev<br>Hampus Ekstrand<br>Gyorgy Grozer<br>Tommaso Guzzo<br>Alessandro Toscani<br>Yoandy Leal hidalgo<br>Riccardo Pinelli<br>Tommaso Guzzo<br>Uladzislau Davyskiba<br>Hannes Gerken<br>Ibrahim Lawani<br>Hampus Ekstrand<br>Tommaso Guzzo<br>Poriya Hossein khanzadeh<br>Yuga Tarumi<br>Tommaso Guzzo<br>Yoandy Leal hidalgo<br>Ibrahim Lawani<br>Wilfredo Leon venero<br>Hampus Ekstrand<br>Yoandy Leal hidalgo<br>Hampus Ekstrand<br>Jean Patry<br>Noumory Keita<br>Ibrahim Lawani<br>Earvin Ngapeth<br>Yoandy Leal hidalgo<br>Hampus Ekstrand<br>Noumory Keita<br>Yuga Tarumi<br>Yoandy Leal hidalgo<br>Ivan Zaytsev<br>Yoandy Leal hidalgo<br>Ivan Zaytsev<br>Jean Patry<br>Uladzislau Davyskiba<br>Noumory Keita<br>Wilfredo Leon venero<br>Tommaso Guzzo<br>Tommaso Guzzo<br>Alessandro Toscani<br>Tommaso Guzzo<br>Wilfredo Leon venero<br>Alessandro Toscani<br>Tommaso Guzzo<br>Earvin Ngapeth<br>Riccardo Pinelli<br>Uladzislau Davyskiba<br>Poriya Hossein khanzadeh<br>Tommaso Guzzo<br>Noumory Keita<br>Noumory Keita<br>Ivan Zaytsev<br>Jean Patry<br>Jean Patry<br>Ibrahim Lawani<br>Wilfredo Leon venero<br>Ivan Zaytsev<br>Ivan Zaytsev<br>Uladzislau Davyskiba<br>Noumory Keita<br>Wilfredo Leon venero<br>Gyorgy Grozer<br>Tommaso Guzzo<br>Noumory Keita<br>Yuga Tarumi<br>Noumory Keita<br>Tommaso Guzzo<br>Ivan Zaytsev<br>Poriya Hossein khanzadeh<br>Wilfredo Leon venero<br>Wilfredo Leon venero<br>Yoandy Leal hidalgo<br>Yoandy Leal hidalgo<br>Yoandy Leal hidalgo<br>Wilfredo Leon venero<br>Noumory Keita<br>Tommaso Guzzo<br>Poriya Hossein khanzadeh<br>Ramazan efe Mandiraci<br>Earvin Ngapeth<br>Ramazan efe Mandiraci<br>Ramazan efe Mandiraci<br>Noumory Keita<br>Yoandy Leal hidalgo<br>Wilfredo Leon venero<br>Ivan Zaytsev<br>Tommaso Guzzo<br>Ivan Zaytsev<br>Hampus Ekstrand<br>Hampus Ekstrand<br>Noumory Keita<br>Alessandro Toscani<br>Earvin Ngapeth<br>Poriya Hossein khanzadeh<br>Noumory Keita<br>Ivan Zaytsev<br>Earvin Ngapeth<br>Noumory Keita<br>Ivan Zaytsev<br>Wilfredo Leon venero<br>Jean Patry<br>Noumory Keita<br>Alessandro Toscani<br>Noumory Keita<br>Yuga Tarumi<br>Ivan Zaytsev<br>Earvin Ngapeth<br>Noumory Keita<br>Wilfredo Leon venero<br>Noumory Keita<br>Wilfredo Leon venero<br>Ivan Zaytsev<br>Timothée Carle<br>Hampus Ekstrand<br>Ibrahim Lawani<br>Riccardo Pinelli<br>Tommaso Guzzo<br>Vuk Todorovic<br>Noumory Keita<br>Hampus Ekstrand<br>Ivan Zaytsev<br>Hampus Ekstrand<br>Yoandy Leal hidalgo<br>Noumory Keita<br>Yoandy Leal hidalgo<br>Jean Patry<br>Yuga Tarumi<br>Wilfredo Leon venero<br>Noumory Keita<br>Ramazan efe Mandiraci<br>Earvin Ngapeth<br>Noumory Keita<br>Hannes Gerken<br>Vuk Todorovic<br>Jean Patry<br>Poriya Hossein khanzadeh<br>Wilfredo Leon venero<br>Yoandy Leal hidalgo<br>Wilfredo Leon venero<br>Ivan Zaytsev<br>Poriya Hossein khanzadeh<br>Ramazan efe Mandiraci<br>Jean Patry<br>Uladzislau Davyskiba<br>Yoandy Leal hidalgo<br>Jean Patry<br>Tommaso Guzzo<br>Ramazan efe Mandiraci<br>Noumory Keita<br>Ivan Zaytsev<br>Gyorgy Grozer<br>Yoandy Leal hidalgo<br>Yuga Tarumi<br>Hampus Ekstrand<br>Poriya Hossein khanzadeh<br>Wilfredo Leon venero<br>Noumory Keita<br>Gyorgy Grozer<br>Hannes Gerken<br>Ibrahim Lawani<br>Wilfredo Leon venero<br>Uladzislau Davyskiba<br>Ibrahim Lawani<br>Yoandy Leal hidalgo<br>Alessandro Toscani<br>Ibrahim Lawani<br>Earvin Ngapeth<br>Hampus Ekstrand<br>Tommaso Guzzo<br>Gyorgy Grozer<br>Uladzislau Davyskiba<br>Yoandy Leal hidalgo<br>Yoandy Leal hidalgo<br>Yoandy Leal hidalgo<br>Yoandy Leal hidalgo<br>Yoandy Leal hidalgo<br>Tommaso Guzzo<br>Ivan Zaytsev<br>Poriya Hossein khanzadeh<br>Jean Patry<br>Hampus Ekstrand<br>Wilfredo Leon venero<br>Hannes Gerken<br>Ramazan efe Mandiraci<br>Earvin Ngapeth<br>Hampus Ekstrand<br>Uladzislau Davyskiba<br>Noumory Keita<br>Uladzislau Davyskiba<br>Riccardo Pinelli<br>Yuga Tarumi<br>Noumory Keita<br>Yoandy Leal hidalgo<br>Yoandy Leal hidalgo<br>Tommaso Guzzo<br>Yoandy Leal hidalgo<br>Ivan Zaytsev<br>Earvin Ngapeth<br>Earvin Ngapeth<br>Jean Patry<br>Tommaso Guzzo<br>Ramazan efe Mandiraci<br>Yoandy Leal hidalgo<br>Yuga Tarumi<br>Jean Patry<br>Yoandy Leal hidalgo<br>Ramazan efe Mandiraci<br>Wilfredo Leon venero<br>Tommaso Guzzo<br>Hampus Ekstrand<br>Yoandy Leal hidalgo<br>Jean Patry<br>Gyorgy Grozer<br>Yoandy Leal hidalgo<br>Yoandy Leal hidalgo<br>Riccardo Pinelli<br>Wilfredo Leon venero<br>Wilfredo Leon venero<br>Alessandro Toscani<br>Hampus Ekstrand<br>Hampus Ekstrand<br>Ramazan efe Mandiraci<br>Yuga Tarumi<br>Noumory Keita<br>Wilfredo Leon venero<br>Riccardo Pinelli<br>Yoandy Leal hidalgo<br>Noumory Keita<br>Gyorgy Grozer<br>Ivan Zaytsev<br>Tommaso Guzzo<br>Murat Tokgoz<br>Yuga Tarumi<br>Yuga Tarumi<br>Wilfredo Leon venero<br>Noumory Keita<br>Hampus Ekstrand<br>Jean Patry<br>Wilfredo Leon venero<br>Noumory Keita<br>Yoandy Leal hidalgo<br>Ivan Zaytsev<br>Yoandy Leal hidalgo<br>Ivan Zaytsev<br>Alessandro Toscani<br>Yuga Tarumi<br>Wilfredo Leon venero<br>Noumory Keita<br>Ramazan efe Mandiraci<br>Ibrahim Lawani<br>Earvin Ngapeth<br>Noumory Keita<br>Wilfredo Leon venero<br>Noumory Keita<br>Tommaso Guzzo<br>Riccardo Pinelli<br>Alessandro Toscani<br>Yoandy Leal hidalgo<br>Alessandro Toscani<br>Gyorgy Grozer<br>Wilfredo Leon venero<br>Jean Patry<br>Riccardo Pinelli<br>Poriya Hossein khanzadeh<br>Yoandy Leal hidalgo<br>Yoandy Leal hidalgo<br>Ramazan efe Mandiraci<br>Ivan Zaytsev<br>Ivan Zaytsev<br>Gyorgy Grozer<br>Gyorgy Grozer<br>Jean Patry<br>Tommaso Guzzo<br>Noumory Keita<br>Hampus Ekstrand<br>Alessandro Toscani<br>Earvin Ngapeth<br>Wilfredo Leon venero<br>Riccardo Pinelli<br>Earvin Ngapeth<br>Yuga Tarumi<br>Alessandro Toscani<br>Noumory Keita<br>Hampus Ekstrand<br>Jean Patry<br>Yoandy Leal hidalgo<br>Riccardo Pinelli<br>Ivan Zaytsev<br>Yoandy Leal hidalgo<br>Wilfredo Leon venero<br>Noumory Keita<br>Hampus Ekstrand<br>Earvin Ngapeth<br>Jean Patry<br>Yoandy Leal hidalgo<br>Tommaso Guzzo<br>Riccardo Pinelli<br>Earvin Ngapeth<br>Gyorgy Grozer<br>Yoandy Leal hidalgo<br>Yoandy Leal hidalgo<br>Tommaso Guzzo<br>Tommaso Guzzo<br>Hampus Ekstrand<br>Ramazan efe Mandiraci<br>Alessandro Toscani<br>Yoandy Leal hidalgo<br>Tommaso Guzzo<br>Gyorgy Grozer<br>Wilfredo Leon venero<br>Tommaso Guzzo<br>Ramazan efe Mandiraci<br>Earvin Ngapeth<br>Jacob Ekman<br>Alessandro Toscani<br>Noumory Keita<br>Noumory Keita<br>Poriya Hossein khanzadeh<br>Hannes Gerken<br>Poriya Hossein khanzadeh<br>Earvin Ngapeth<br>Ivan Zaytsev<br>Gyorgy Grozer<br>Ivan Zaytsev<br>Ivan Zaytsev<br>Poriya Hossein khanzadeh<br>Riccardo Pinelli<br>Ivan Zaytsev<br>Noumory Keita<br>Yoandy Leal hidalgo<br>Wilfredo Leon venero<br>Ramazan efe Mandiraci<br>Ibrahim Lawani<br>Yuga Tarumi<br>Earvin Ngapeth<br>Noumory Keita<br>Alessandro Toscani<br>Hampus Ekstrand<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Fabrizio Gironi<br>Tommaso Guzzo<br>Tommaso Guzzo<br>Tommaso Guzzo<br>Tommaso Guzzo<br>Tommaso Guzzo<br>Tommaso Guzzo<br>Tommaso Guzzo<br>Tommaso Guzzo<br>Tommaso Guzzo<br>Gyorgy Grozer<br>Gyorgy Grozer<br>Gyorgy Grozer<br>Gyorgy Grozer<br>Gyorgy Grozer<br>Gyorgy Grozer<br>Gyorgy Grozer<br>Gyorgy Grozer<br>Gyorgy Grozer<br>Earvin Ngapeth<br>Earvin Ngapeth<br>Earvin Ngapeth<br>Earvin Ngapeth<br>Earvin Ngapeth<br>Earvin Ngapeth<br>Earvin Ngapeth<br>Earvin Ngapeth<br>Earvin Ngapeth<br>Earvin Ngapeth<br>Earvin Ngapeth<br>Yoandy Leal hidalgo<br>Jean Patry<br>Jean Patry<br>Jean Patry<br>Jean Patry<br>Jean Patry<br>Jean Patry<br>Jean Patry<br>Noumory Keita<br>Noumory Keita<br>Noumory Keita<br>Noumory Keita<br>Noumory Keita<br>Hampus Ekstrand<br>Hampus Ekstrand<br>Ivan Zaytsev<br>Ivan Zaytsev<br>Ivan Zaytsev<br>Ivan Zaytsev<br>Ivan Zaytsev<br>Ivan Zaytsev<br>Ivan Zaytsev<br>Ivan Zaytsev<br>Wilfredo Leon venero<br>Wilfredo Leon venero<br>Wilfredo Leon venero<br>Wilfredo Leon venero<br>Wilfredo Leon venero<br>Riccardo Pinelli<br>Riccardo Pinelli<br>Jacob Ekman<br>Jacob Ekman<br>Vaino Rahko<br>Vuk Todorovic<br>Jome Vandamme<br>Abdullah Cam<br>Gyorgy Grozer<br>Hampus Ekstrand<br>Hampus Ekstrand<br>Hampus Ekstrand<br>Dejan Vincic<br>Hannes Gerken<br>Noumory Keita<br>Yoandy Leal hidalgo<br>Noumory Keita<br>Ozkan Hayirli<br>Riccardo Pinelli<br>Wilfredo Leon venero<br>Earvin Ngapeth<br>Ryan Coenen<br>Noumory Keita<br>Hampus Ekstrand<br>Ivan Zaytsev<br>Earvin Ngapeth<br>Jacob Ekman<br>Riccardo Pinelli<br>Noumory Keita<br>Tommaso Guzzo<br>Gyorgy Grozer<br>Ramazan efe Mandiraci<br>Earvin Ngapeth<br>Timothée Carle<br>Iven fietje Ferch<br>Voitto aleksi Koykka<br>Tommaso Guzzo<br>Bartosz Filipiak<br>Kewin Sasak<br>Pawel Pietraszko<br>Marcin Kania<br>Daniel Gasior<br>Bartlomiej Kluth<br>Bartlomiej Kluth<br>Veeti Nikkinen<br>Earvin Ngapeth<br>Noumory Keita<br>Wilfredo Leon venero<br>Hampus Ekstrand<br>Hampus Ekstrand<br>Nicolas Mendez<br>Wilfredo Leon venero<br>Jean Patry<br>Noumory Keita<br>Yoandy Leal hidalgo<br>Salem Abdulrahman<br>Tommaso Guzzo<br>Noumory Keita<br>Wilfredo Leon venero<br>Noumory Keita<br>Uladzislau Davyskiba<br>Yoandy Leal hidalgo<br>Poriya Hossein khanzadeh<br>Fabrizio Gironi<br>Ramazan efe Mandiraci<br>Wilfredo Leon venero<br>Ibrahim Lawani<br>Hampus Ekstrand<br>Uladzislau Davyskiba<br>Ramazan efe Mandiraci<br>Noumory Keita<br>Yoandy Leal hidalgo<br>Yuga Tarumi<br>Riccardo Pinelli<br>Wilfredo Leon venero<br>Murat Tokgoz<br>Marc-anthony Honore<br>Marc-anthony Honore<br>Murat Tokgoz<br>Ivan Zaytsev<br>Ugur Gunes<br>Hampus Ekstrand<br>Ibrahim Lawani<br>Ivan Zaytsev<br>Noumory Keita<br>Ibrahim Lawani<br>Uladzislau Davyskiba<br>Noumory Keita"], ["Amin <PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br>Amin <PERSON><br><PERSON><br><PERSON><PERSON> <PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br>Wassi<PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br>C<PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><PERSON><br><PERSON><br><PERSON>si<PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON>ic<br><PERSON><PERSON><PERSON>o<br><PERSON><br><PERSON>sim <PERSON> tara<br><PERSON>i<br><PERSON> <PERSON><br>Jacopo La<PERSON>za<br>Jaco<PERSON> La<PERSON>za<br><PERSON> <PERSON>i<br>Wassim Ben tara<br><PERSON> <PERSON><br><PERSON> Comp<PERSON><br><PERSON>cci<PERSON>i<br><PERSON> <PERSON>ei<br><PERSON> Vol<PERSON>to<br>Crt<PERSON>r <PERSON>s<PERSON>k<br><PERSON> <PERSON>cci<PERSON>i<br>Flavio Morazzini<br>Gianluca Rossi<br>Leonardo Scanferla<br>Roberto Pinali<br>Gabriel Garcia<br>Adis Lagumdzija<br>Wassim Ben tara<br>Martin Chevalier<br>Juan ignacio Finoli<br>Flavio Morazzini<br>Cristian Frumuselu<br>Leonardo Scanferla<br>Yann niclas Bohme<br>Lorenzo Sala<br>Adis Lagumdzija<br>Alessandro Piccinelli<br>Cristian Frumuselu<br>Lorenzo Sala<br>Flavio Morazzini<br>Andrea Innocenzi<br>Maicon jose Leite costa<br>Wassim Ben tara<br>Flavio Morazzini<br>Cristian Frumuselu<br>Yann niclas Bohme<br>Jacopo Tosti<br>Wassim Ben tara<br>Adis Lagumdzija<br>Martin Berger<br>Lorenzo Sala<br>Adis Lagumdzija<br>Amin Esmaeilnezhad<br>Wassim Ben tara<br>Jacopo Tosti<br>Martin Berger<br>Tommaso Barotto<br>Filippo Lanza<br>Amin Esmaeilnezhad<br>Leonardo Scanferla<br>Luca Galiazzo<br>Martin Chevalier<br>Lorenzo Sala<br>Ryan joseph Sclater<br>Alessandro Piccinelli<br>Adis Lagumdzija<br>Jacopo Tosti<br>Cristian Frumuselu<br>Tommaso Barotto<br>Leonardo Scanferla<br>Yann niclas Bohme<br>Wassim Ben tara<br>Leonardo Scanferla<br>Martin Berger<br>Barthélémy Chinenyeze<br>Jacopo Tosti<br>Luca Galiazzo<br>Tommaso Barotto<br>Leonardo Scanferla<br>Martin Berger<br>Alessandro Piccinelli<br>Leonardo Scanferla<br>Gabriel Garcia<br>Lorenzo Sala<br>Leonardo Scanferla<br>Adis Lagumdzija<br>Andrea Mattei<br>Leonardo Scanferla<br>Martin Berger<br>Lorenzo Sala<br>Tommaso Barotto<br>Gabriel Garcia<br>Adis Lagumdzija<br>Martin Berger<br>Martin Berger<br>Barthélémy Chinenyeze<br>Flavio Morazzini<br>Amin Esmaeilnezhad<br>Martin Berger<br>Gabriel Garcia<br>Leonardo Scanferla<br>Leonardo Scanferla<br>Leonardo Scanferla<br>Barthélémy Chinenyeze<br>Alessandro Piccinelli<br>Jacopo Larizza<br>Martin Berger<br>Martin Berger<br>Yann niclas Bohme<br>Juan ignacio Finoli<br>Cristian Frumuselu<br>Wassim Ben tara<br>Leonardo Scanferla<br>Francesco Comparoni<br>Gabriel Garcia<br>Wassim Ben tara<br>Martin Chevalier<br>Luca Galiazzo<br>Martin Berger<br>Gianluca Rossi<br>Wassim Ben tara<br>Martin Berger<br>Lorenzo Sala<br>Mikko Karjarinta<br>Luca Galiazzo<br>Martin Chevalier<br>Gianluca Rossi<br>Flavio Morazzini<br>Filippo Lanza<br>Jacopo Tosti<br>Roberto Pinali<br>Luca Galiazzo<br>Leonardo Scanferla<br>Lorenzo Sala<br>Leonardo Scanferla<br>Jacopo Larizza<br>Barthélémy Chinenyeze<br>Francesco Comparoni<br>Gabriel Garcia<br>Wassim Ben tara<br>Andrea Mattei<br>Filippo Lanza<br>Wassim Ben tara<br>Juan ignacio Finoli<br>Lorenzo Sala<br>Gabriel Garcia<br>Flavio Morazzini<br>Flavio Morazzini<br>Arslan Eksi<br>Leonardo Scanferla<br>Alessandro Piccinelli<br>Amin Esmaeilnezhad<br>Roberto Pinali<br>Roberto Pinali<br>Martin Berger<br>Cristian Frumuselu<br>Daniel Mc donnell<br>Lorenzo Sala<br>Gabriel Garcia<br>Barthélémy Chinenyeze<br>Juan ignacio Finoli<br>Alessandro Piccinelli<br>Marco Volpato<br>Roberto Pinali<br>Jacopo Tosti<br>Martin Berger<br>Andrea Innocenzi<br>Martin Berger<br>Filippo Lanza<br>Adis Lagumdzija<br>Adis Lagumdzija<br>Andrea Innocenzi<br>Alessandro Piccinelli<br>Gabriel Garcia<br>Gianluca Rossi<br>Adis Lagumdzija<br>Alessandro Piccinelli<br>Adis Lagumdzija<br>Wassim Ben tara<br>Lorenzo Sala<br>Jacopo Larizza<br>Adis Lagumdzija<br>Flavio Morazzini<br>Adis Lagumdzija<br>Tommaso Barotto<br>Martin Chevalier<br>Filippo Lanza<br>Flavio Morazzini<br>Tommaso Barotto<br>Marco Volpato<br>Gabriel Garcia<br>Filippo Lanza<br>Alessandro Piccinelli<br>Wassim Ben tara<br>Filippo Lanza<br>Amin Esmaeilnezhad<br>Andrea Innocenzi<br>Gabriel Garcia<br>Adis Lagumdzija<br>Juan ignacio Finoli<br>Wassim Ben tara<br>Martin Chevalier<br>Flavio Morazzini<br>Adis Lagumdzija<br>Martin Berger<br>Luca Galiazzo<br>Adis Lagumdzija<br>Alessandro Piccinelli<br>Marco Volpato<br>Filippo Lanza<br>Martin Berger<br>Flavio Morazzini<br>Luca Galiazzo<br>Cristian Frumuselu<br>Francesco Comparoni<br>Andrea Innocenzi<br>Daniel Malescha<br>Flavio Morazzini<br>Leonardo Scanferla<br>Marco Volpato<br>Martin Berger<br>Martin Chevalier<br>Leonardo Scanferla<br>Roberto Pinali<br>Amin Esmaeilnezhad<br>Francesco Comparoni<br>Juan ignacio Finoli<br>Tommaso Barotto<br>Leonardo Scanferla<br>Amin Esmaeilnezhad<br>Filippo Lanza<br>Leonardo Scanferla<br>Martin Berger<br>Roberto Pinali<br>Andrea Mattei<br>Alessandro Piccinelli<br>Filippo Lanza<br>Roberto Pinali<br>Roberto Pinali<br>Gianluca Rossi<br>Martin Berger<br>Gabriel Garcia<br>Leonardo Scanferla<br>Leonardo Scanferla<br>Adis Lagumdzija<br>Adis Lagumdzija<br>Leonardo Scanferla<br>Wassim Ben tara<br>Wassim Ben tara<br>Cristian Frumuselu<br>Leonardo Scanferla<br>Leonardo Scanferla<br>Francesco Comparoni<br>Marco Volpato<br>Roberto Pinali<br>Andrea Mattei<br>Adis Lagumdzija<br>Gabriel Garcia<br>Andrea Innocenzi<br>Andrea Innocenzi<br>Juan ignacio Finoli<br>Lorenzo Sala<br>Francesco Comparoni<br>Leonardo Scanferla<br>Gabriel Garcia<br>Crtomir Bosnjak<br>Juan ignacio Finoli<br>Lorenzo Sala<br>Leonardo Scanferla<br>Luca Galiazzo<br>Martin Berger<br>Flavio Morazzini<br>Cristian Frumuselu<br>Martin Berger<br>Martin Berger<br>Alessandro Piccinelli<br>Flavio Morazzini<br>Lorenzo Sala<br>Wassim Ben tara<br>Flavio Morazzini<br>Leonardo Scanferla<br>Martin Chevalier<br>Juan ignacio Finoli<br>Martin Berger<br>Leonardo Scanferla<br>Filippo Lanza<br>Jacopo Tosti<br>Amin Esmaeilnezhad<br>Alessandro Piccinelli<br>Leonardo Scanferla<br>Adis Lagumdzija<br>Wassim Ben tara<br>Alessandro Piccinelli<br>Marco Volpato<br>Leonardo Scanferla<br>Lorenzo Sala<br>Amin Esmaeilnezhad<br>Andrea Mattei<br>Leonardo Scanferla<br>Leonardo Scanferla<br>Gianluca Rossi<br>Luca Galiazzo<br>Adis Lagumdzija<br>Roberto Pinali<br>Daniel Mc donnell<br>Wassim Ben tara<br>Alessandro Piccinelli<br>Filippo Lanza<br>Barthélémy Chinenyeze<br>Francesco Comparoni<br>Barthélémy Chinenyeze<br>Adis Lagumdzija<br>Gianluca Rossi<br>Roberto Pinali<br>Flavio Morazzini<br>Jacopo Larizza<br>Jacopo Larizza<br>Leonardo Scanferla<br>Filippo Lanza<br>Filippo Lanza<br>Filippo Lanza<br>Martin Berger<br>Andrea Innocenzi<br>Lorenzo Sala<br>Andrea Innocenzi<br>Amin Esmaeilnezhad<br>Marco Volpato<br>Roberto Pinali<br>Jacopo Larizza<br>Marco Volpato<br>Jacopo Tosti<br>Martin Berger<br>Filippo Lanza<br>Filippo Lanza<br>Francesco Comparoni<br>Gabriel Garcia<br>Martin Berger<br>Andrea Mattei<br>Gabriel Garcia<br>Tommaso Barotto<br>Gilles Vandecaveye<br>Marco Volpato<br>Gianluca Rossi<br>Gabriel Garcia<br>Leonardo Scanferla<br>Filippo Lanza<br>Andrea Innocenzi<br>Leonardo Scanferla<br>Martin Berger<br>Andrea Mattei<br>Marco Volpato<br>Jacopo Larizza<br>Wassim Ben tara<br>Leonardo Scanferla<br>Andrea Mattei<br>Marco Volpato<br>Amin Esmaeilnezhad<br>Andrea Mattei<br>Leonardo Scanferla<br>Barthélémy Chinenyeze<br>Lorenzo Sala<br>Leonardo Scanferla<br>Martin Berger<br>Jacopo Larizza<br>Adis Lagumdzija<br>Andrea Mattei<br>Alessandro Piccinelli<br>Roberto Pinali<br>Andrea Innocenzi<br>Martin Berger<br>Martin Berger<br>Leonardo Scanferla<br>Gabriel Garcia<br>Martin Chevalier<br>Lorenzo Sala<br>Marco Volpato<br>Tommaso Barotto<br>Leonardo Scanferla<br>Amin Esmaeilnezhad<br>Francesco Comparoni<br>Tommaso Barotto<br>Andrea Mattei<br>Alessandro Piccinelli<br>Martin Berger<br>Martin Berger<br>Roberto Pinali<br>Amin Esmaeilnezhad<br>Barthélémy Chinenyeze<br>Alessandro Piccinelli<br>Leonardo Scanferla<br>Jacopo Tosti<br>Lorenzo Sala<br>Gianluca Rossi<br>Wassim Ben tara<br>Leonardo Scanferla<br>Leonardo Scanferla<br>Amin Esmaeilnezhad<br>Wassim Ben tara<br>Barthélémy Chinenyeze<br>Leonardo Scanferla<br>Jacopo Tosti<br>Martin Berger<br>Filippo Lanza<br>Tommaso Barotto<br>Francesco Comparoni<br>Barthélémy Chinenyeze<br>Amin Esmaeilnezhad<br>Lorenzo Sala<br>Cristian Frumuselu<br>Roberto Pinali<br>Tommaso Barotto<br>Roberto Pinali<br>Leonardo Scanferla<br>Adis Lagumdzija<br>Andrea Mattei<br>Gabriel Garcia<br>Leonardo Scanferla<br>Martin Chevalier<br>Flavio Morazzini<br>Filippo Lanza<br>Cristian Frumuselu<br>Luca Galiazzo<br>Jacopo Tosti<br>Alessandro Piccinelli<br>Marco Volpato<br>Jacopo Tosti<br>Leonardo Scanferla<br>Alessandro Piccinelli<br>Gabriel Garcia<br>Wassim Ben tara<br>Leonardo Scanferla<br>Alessandro Piccinelli<br>Leonardo Scanferla<br>Martin Berger<br>Francesco Comparoni<br>Alessandro Piccinelli<br>Luca Galiazzo<br>Wassim Ben tara<br>Leonardo Scanferla<br>Martin Chevalier<br>Maicon jose Leite costa<br>Adis Lagumdzija<br>Lorenzo Sala<br>Filippo Lanza<br>Leonardo Scanferla<br>Lorenzo Sala<br>Mikko Karjarinta<br>Martin Chevalier<br>Juan ignacio Finoli<br>Filippo Lanza<br>Barthélémy Chinenyeze<br>Gabriel Garcia<br>Roberto Pinali<br>Barthélémy Chinenyeze<br>Cristian Frumuselu<br>Jacopo Tosti<br>Gabriel Garcia<br>Marco Volpato<br>Cristian Frumuselu<br>Gabriel Garcia<br>Andrea Innocenzi<br>Lorenzo Sala<br>Lorenzo Sala<br>Filippo Lanza<br>Francesco Comparoni<br>Filippo Lanza<br>Wassim Ben tara<br>Luca Galiazzo<br>Andrea Innocenzi<br>Crtomir Bosnjak<br>Juan ignacio Finoli<br>Martin Chevalier<br>Gabriel Garcia<br>Juan ignacio Finoli<br>Lorenzo Sala<br>Marco Volpato<br>Jacopo Larizza<br>Leonardo Scanferla<br>Tommaso Barotto<br>Lorenzo Sala<br>Leonardo Scanferla<br>Alessandro Piccinelli<br>Alessandro Piccinelli<br>Flavio Morazzini<br>Filippo Lanza<br>Flavio Morazzini<br>Barthélémy Chinenyeze<br>Alessandro Piccinelli<br>Martin Berger<br>Andrea Innocenzi<br>Gabriel Garcia<br>Gabriel Garcia<br>Flavio Morazzini<br>Amin Esmaeilnezhad<br>Alessandro Piccinelli<br>Leonardo Scanferla<br>Alessandro Piccinelli<br>Roberto Pinali<br>Wassim Ben tara<br>Flavio Morazzini<br>Marco Volpato<br>Barthélémy Chinenyeze<br>Jacopo Tosti<br>Gabriel Garcia<br>Andrea Innocenzi<br>Martin Berger<br>Tommaso Barotto<br>Leonardo Scanferla<br>Wassim Ben tara<br>Andrea Mattei<br>Leonardo Scanferla<br>Filippo Lanza<br>Barthélémy Chinenyeze<br>Gianluca Rossi<br>Flavio Morazzini<br>Wassim Ben tara<br>Andrea Mattei<br>Andrea Innocenzi<br>Roberto Pinali<br>Barthélémy Chinenyeze<br>Bartosz Bednorz<br>Martin Berger<br>Jacopo Larizza<br>Jacopo Larizza<br>Juan ignacio Finoli<br>Cristian Frumuselu<br>Alessandro Piccinelli<br>Adis Lagumdzija<br>Roberto Pinali<br>Juan ignacio Finoli<br>Martin Berger<br>Alessandro Piccinelli<br>Francesco Comparoni<br>Jacopo Larizza<br>Yann niclas Bohme<br>Wassim Ben tara<br>Adis Lagumdzija<br>Andrea Innocenzi<br>Alessandro Piccinelli<br>Lorenzo Sala<br>Leonardo Scanferla<br>Marco Volpato<br>Barthélémy Chinenyeze<br>Wassim Ben tara<br>Alessandro Piccinelli<br>Leonardo Scanferla<br>Andrea Innocenzi<br>Amin Esmaeilnezhad<br>Martin Berger<br>Leonardo Scanferla<br>Barthélémy Chinenyeze<br>Flavio Morazzini<br>Wassim Ben tara<br>Roberto Pinali<br>Alessandro Piccinelli<br>Jacopo Tosti<br>Adis Lagumdzija<br>Tommaso Barotto<br>Adis Lagumdzija<br>Francesco Comparoni<br>Wassim Ben tara<br>Leonardo Scanferla<br>Alessandro Piccinelli<br>Luca Galiazzo<br>Alessandro Piccinelli<br>Wassim Ben tara<br>Roberto Pinali<br>Gabriel Garcia<br>Gabriel Garcia<br>Martin Berger<br>Leonardo Scanferla<br>Juan ignacio Finoli<br>Alessandro Piccinelli<br>Leonardo Scanferla<br>Amin Esmaeilnezhad<br>Wassim Ben tara<br>Alessandro Piccinelli<br>Francesco Comparoni<br>Adis Lagumdzija<br>Leonardo Scanferla<br>Alessandro Piccinelli<br>Amin Esmaeilnezhad<br>Martin Berger<br>Lorenzo Sala<br>Martin Berger<br>Cristian Frumuselu<br>Amin Esmaeilnezhad<br>Andrea Innocenzi<br>Andrea Mattei<br>Leonardo Scanferla<br>Martin Chevalier<br>Wassim Ben tara<br>Tommaso Barotto<br>Leonardo Scanferla<br>Adis Lagumdzija<br>Cristian Frumuselu<br>Marco Volpato<br>Lorenzo Sala<br>Marco Volpato<br>Leonardo Scanferla<br>Martin Berger<br>Leonardo Scanferla<br>Alessandro Piccinelli<br>Gabriel Garcia<br>Alessandro Piccinelli<br>Leonardo Scanferla<br>Barthélémy Chinenyeze<br>Roberto Pinali<br>Alessandro Piccinelli<br>Leonardo Scanferla<br>Roberto Pinali<br>Gabriel Garcia<br>Leonardo Scanferla<br>Nicolò Garello<br>Luca Galiazzo<br>Gianluca Rossi<br>Wassim Ben tara<br>Wassim Ben tara<br>Gabriel Garcia<br>Filippo Lanza<br>Lorenzo Sala<br>Juan ignacio Finoli<br>Amin Esmaeilnezhad<br>Alessandro Piccinelli<br>Alessandro Piccinelli<br>Amin Esmaeilnezhad<br>Andrea Mattei<br>Francesco Comparoni<br>Arslan Eksi<br>Gabriel Garcia<br>Daniel Mc donnell<br>Gabriel Garcia<br>Martin Chevalier<br>Luca Galiazzo<br>Luca Galiazzo<br>Martin Berger<br>Martin Berger<br>Alessandro Piccinelli<br>Wassim Ben tara<br>Lorenzo Sala<br>Alessandro Piccinelli<br>Flavio Morazzini<br>Amin Esmaeilnezhad<br>Jacopo Larizza<br>Jacopo Larizza<br>Andrea Innocenzi<br>Alessandro Piccinelli<br>Gabriel Garcia<br>Filippo Lanza<br>Filippo Lanza<br>Filippo Lanza<br>Filippo Lanza<br>Filippo Lanza<br>Filippo Lanza<br>Filippo Lanza<br>Filippo Lanza<br>Filippo Lanza<br>Filippo Lanza<br>Alessandro Piccinelli<br>Martin Berger<br>Martin Berger<br>Martin Berger<br>Martin Berger<br>Martin Berger<br>Martin Berger<br>Martin Berger<br>Martin Berger<br>Martin Berger<br>Martin Berger<br>Lorenzo Sala<br>Lorenzo Sala<br>Lorenzo Sala<br>Lorenzo Sala<br>Lorenzo Sala<br>Lorenzo Sala<br>Lorenzo Sala<br>Lorenzo Sala<br>Lorenzo Sala<br>Lorenzo Sala<br>Lorenzo Sala<br>Lorenzo Sala<br>Lorenzo Sala<br>Marco Volpato<br>Marco Volpato<br>Marco Volpato<br>Marco Volpato<br>Marco Volpato<br>Marco Volpato<br>Marco Volpato<br>Marco Volpato<br>Marco Volpato<br>Umberto Caporossi<br>Umberto Caporossi<br>Leonardo Scanferla<br>Leonardo Scanferla<br>Leonardo Scanferla<br>Leonardo Scanferla<br>Leonardo Scanferla<br>Leonardo Scanferla<br>Leonardo Scanferla<br>Tommaso Ichino<br>Jacopo Larizza<br>Jacopo Larizza<br>Jacopo Larizza<br>Jacopo Larizza<br>Jacopo Larizza<br>Jacopo Larizza<br>Barthélémy Chinenyeze<br>Barthélémy Chinenyeze<br>Barthélémy Chinenyeze<br>Barthélémy Chinenyeze<br>Barthélémy Chinenyeze<br>Barthélémy Chinenyeze<br>Barthélémy Chinenyeze<br>Barthélémy Chinenyeze<br>Andrea Mattei<br>Andrea Mattei<br>Andrea Mattei<br>Andrea Mattei<br>Andrea Mattei<br>Andrea Mattei<br>Alessandro Piccinelli<br>Alessandro Piccinelli<br>Alessandro Piccinelli<br>Alessandro Piccinelli<br>Alessandro Piccinelli<br>Juan ignacio Finoli<br>Juan ignacio Finoli<br>Juan ignacio Finoli<br>Juan ignacio Finoli<br>Mikko Karjarinta<br>Mikko Karjarinta<br>Pablo Machado<br>Pablo Machado<br>Janne Pyhajarvi<br>Crtomir Bosnjak<br>Gilles Vandecaveye<br>Emre Batur<br>Nikola Pekovic<br>Yann niclas Bohme<br>Leonardo Scanferla<br>Daniel Mc donnell<br>Ali deniz Yilmaz<br>Martin Berger<br>Lorenzo Sala<br>Alessandro Piccinelli<br>Daniel Mc donnell<br>Yann niclas Bohme<br>Maarten Bartels<br>Barthélémy Chinenyeze<br>Jacopo Larizza<br>Lorenzo Sala<br>Mikko Karjarinta<br>Juan ignacio Finoli<br>Alessandro Piccinelli<br>Gianluca Rossi<br>Martin Berger<br>Daniel Mc donnell<br>Arslan Eksi<br>Gianluca Rossi<br>Thiago vanole Nogueira silva<br>Jan Roling<br>Miran Kujundzic<br>Remigiusz Kapica<br>Damian Domagala<br>Maicon jose Leite costa<br>Bartosz Bednorz<br>Alessandro Piccinelli<br>Jolan Cox<br>Barthélémy Chinenyeze<br>Flavio Morazzini<br>Roberto Pinali<br>Wassim Ben tara<br>Cristian Frumuselu<br>Filippo Lanza<br>Amin Esmaeilnezhad<br>Roberto Pinali<br>Lorenzo Sala<br>Amin Esmaeilnezhad<br>Martin Berger<br>Wassim Ben tara<br>Maicon jose Leite costa<br>Filippo Lanza<br>Martin Berger<br>Daniel Malescha<br>Aboubacar Drame neto<br>Filippo Lanza<br>Roberto Pinali<br>Wassim Ben tara<br>Alessandro Piccinelli<br>Andrea Innocenzi<br>Leonardo Scanferla<br>Ryan joseph Sclater<br>Amin Esmaeilnezhad<br>Cristian Frumuselu<br>Andrea Innocenzi<br>Flavio Morazzini<br>Wassim Ben tara<br>Amin Esmaeilnezhad<br>Leonardo Scanferla<br>Roberto Pinali<br>Roberto Pinali<br>Leonardo Scanferla<br>Luca Galiazzo<br>Nicolò Garello<br>Tommaso Barotto<br>Andrea Innocenzi<br>Filippo Lanza<br>Martin Berger<br>Andrea Innocenzi<br>Leonardo Scanferla<br>Francesco Comparoni<br>Alessandro Piccinelli<br>Amin Esmaeilnezhad<br>Martin Chevalier<br>Wassim Ben tara<br>Leonardo Scanferla<br>Leonardo Scanferla<br>Alessandro Piccinelli<br>Crtomir Bosnjak<br>Martin Berger<br>Leonardo Scanferla<br>Alessandro Piccinelli<br>Martin Chevalier<br>Gabriel Garcia<br>Leonardo Scanferla<br>Adis Lagumdzija<br>Filippo Lanza<br>Adis Lagumdzija<br>Cristian Frumuselu<br>Amin Esmaeilnezhad<br>Lorenzo Sala<br>Martin Berger"], ["<PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON> wolf was<PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON> ii<br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><br>Th<PERSON><PERSON><br>R<PERSON><PERSON> raul <PERSON> arc<PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br>Aleksan<PERSON> Nikolov<br>Aleksandar Nikolov<br><PERSON><PERSON><PERSON><br>Th<PERSON><PERSON>re<br>Th<PERSON><PERSON><br>Alek<PERSON><PERSON> Nikolov<br><PERSON><PERSON><PERSON><PERSON><br>R<PERSON><PERSON> <PERSON>aul <PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><br>Aleks Grozdanov<br>Matteo <PERSON>n<br><PERSON>n<br>Riccardo Gollini<br>Gianluca Galassi<br>Jeffrey Jendryk ii<br>Jeffrey Jendryk ii<br>Matteo Piano<br>Gianluca Galassi<br>Sebastian Solé<br><PERSON> Sani<br><PERSON> Tammearu<br>Andrea Baldi<br>Tobias Krick<br>Gianluca Galassi<br>Aleksandar Nikolov<br>Matteo Pedron<br>Aleks Grozdanov<br>Aleksandar Nikolov<br>Filippo Mancini<br>Kamil Rychlicki<br>Andrea Baldi<br>Matteo Piano<br>Jeffrey Jendryk ii<br>Aleks Grozdanov<br>Riccardo Gollini<br>Matteo Piano<br>Andrea Baldi<br>Aleks Grozdanov<br>Federico Pereyra<br>Niccolò Depalma<br>Aleks Grozdanov<br>Riccardo Gollini<br>Jiri Leinonen<br>Filippo Mancini<br>Théo Faure<br>Petar Dirlic<br>Gianluca Galassi<br>Aleksandar Nikolov<br>Matteo Piano<br>Filippo Mancini<br>Aleksandar Nikolov<br>Matteo Piano<br>Tobias Krick<br>Aleksandar Nikolov<br>Aleksandar Nikolov<br>Matteo Pedron<br>Théo Faure<br>Aleks Grozdanov<br>Aleksandar Nikolov<br>Aleksandar Nikolov<br>Riccardo Gollini<br>Matteo Piano<br>Riccardo Gollini<br>Matteo Piano<br>Tobias Krick<br>Andrea Baldi<br>Niccolò Depalma<br>Sebastian Solé<br>Matteo Piano<br>Petar Dirlic<br>Riccardo Gollini<br>Gianluca Galassi<br>Théo Faure<br>Kamil Rychlicki<br>Kamil Rychlicki<br>Federico Pereyra<br>Sebastian Solé<br>Niccolò Depalma<br>Jeffrey Jendryk ii<br>Aleksandar Nikolov<br>Théo Faure<br>Matteo Pedron<br>Kamil Rychlicki<br>Riccardo Gollini<br>Matteo Piano<br>Matteo Piano<br>Aleksandar Nikolov<br>Xander wolf wassenaar Ketrzynski<br>Riccardo Gollini<br>Théo Faure<br>Petar Dirlic<br>Aleksandar Nikolov<br>Filippo Mancini<br>Aleks Grozdanov<br>Kamil Rychlicki<br>Sebastian Solé<br>Aleksandar Nikolov<br>Aleksandar Nikolov<br>Aleks Grozdanov<br>Aleksandar Nikolov<br>Gianluca Galassi<br>Gianluca Galassi<br>Aleks Grozdanov<br>Théo Faure<br>Aleksandar Nikolov<br>Sebastian Solé<br>Théo Faure<br>Andrea Baldi<br>Aleks Grozdanov<br>Matteo Piano<br>Gianluca Galassi<br>Gianluca Galassi<br>Aleks Grozdanov<br>Gianluca Galassi<br>Riccardo Gollini<br>Roamy raul Alonso arce<br>Matteo Piano<br>Matteo Piano<br>Théo Faure<br>Niccolò Depalma<br>Kamil Rychlicki<br>Sebastian Solé<br>Matteo Piano<br>Matteo Piano<br>Aleks Grozdanov<br>Tobias Krick<br>Andrea Baldi<br>Aleksandar Nikolov<br>Andrea Baldi<br>Sebastian Solé<br>Riccardo Gollini<br>Tobias Krick<br>Sebastian Solé<br>Tobias Krick<br>Jeffrey Jendryk ii<br>Matteo Piano<br>Niccolò Depalma<br>Niccolò Depalma<br>Gianluca Galassi<br>Théo Faure<br>Sebastian Solé<br>Francesco Sani<br>Jan Kasan<br>Kamil Rychlicki<br>Angel suarez Perez<br>Mart Tammearu<br>Aleks Grozdanov<br>Riccardo Gollini<br>Matteo Piano<br>Roamy raul Alonso arce<br>Niccolò Depalma<br>Kamil Rychlicki<br>Filippo Mancini<br>Aleksandar Nikolov<br>Kamil Rychlicki<br>Francesco Sani<br>Francesco Sani<br>Petar Dirlic<br>Matteo Piano<br>Jeffrey Jendryk ii<br>Gianluca Galassi<br>Roamy raul Alonso arce<br>Gianluca Galassi<br>Matteo Piano<br>Federico Pereyra<br>Aleksandar Nikolov<br>Aleks Grozdanov<br>Riccardo Gollini<br>Tobias Krick<br>Kamil Rychlicki<br>Matteo Piano<br>Aleks Grozdanov<br>Matteo Piano<br>Tobias Krick<br>Théo Faure<br>Andrea Baldi<br>Aleksandar Nikolov<br>Matteo Piano<br>Gianluca Galassi<br>Niccolò Depalma<br>Danijel Koncilja<br>Jeffrey Jendryk ii<br>Matteo Piano<br>Matteo Piano<br>Niccolò Depalma<br>Sebastian Solé<br>Riccardo Gollini<br>Petar Dirlic<br>Sebastian Solé<br>Andrea Baldi<br>Riccardo Gollini<br>Théo Faure<br>Francesco Sani<br>Dick Kooy<br>Aleksandar Nikolov<br>Roamy raul Alonso arce<br>Sebastian Solé<br>Roamy raul Alonso arce<br>Gianluca Galassi<br>Gianluca Galassi<br>Sebastian Solé<br>Sebastian Solé<br>Kamil Rychlicki<br>Francesco Sani<br>Filippo Mancini<br>Kamil Rychlicki<br>Gianluca Galassi<br>Sebastian Solé<br>Aleks Grozdanov<br>Francesco Sani<br>Roamy raul Alonso arce<br>Kamil Rychlicki<br>Angel suarez Perez<br>Matteo Piano<br>Matteo Piano<br>Sebastian Solé<br>Niccolò Depalma<br>Riccardo Gollini<br>Aleksandar Nikolov<br>Matteo Piano<br>Matteo Piano<br>Andrea Baldi<br>Sebastian Solé<br>Théo Faure<br>Riccardo Gollini<br>Gianluca Galassi<br>Aleksandar Nikolov<br>Aleks Grozdanov<br>Francesco Sani<br>Federico Pereyra<br>Sebastian Solé<br>Sebastian Solé<br>Federico Pereyra<br>Tim Degruyter<br>Filippo Mancini<br>Federico Pereyra<br>Riccardo Gollini<br>Petar Dirlic<br>Dick Kooy<br>Sebastian Solé<br>Matteo Piano<br>Sebastian Solé<br>Aleksandar Nikolov<br>Niccolò Depalma<br>Aleksandar Nikolov<br>Tobias Krick<br>Théo Faure<br>Matteo Piano<br>Filippo Mancini<br>Aleks Grozdanov<br>Cody Kessel<br>Tobias Krick<br>Aleksandar Nikolov<br>Gianluca Galassi<br>Roamy raul Alonso arce<br>Kamil Rychlicki<br>Aleksandar Nikolov<br>Sebastian Solé<br>Aleksandar Nikolov<br>Aleksandar Nikolov<br>Filippo Mancini<br>Aleks Grozdanov<br>Tobias Krick<br>Théo Faure<br>Sebastian Solé<br>Riccardo Gollini<br>Kamil Rychlicki<br>Théo Faure<br>Théo Faure<br>Gianluca Galassi<br>Sebastian Solé<br>Aleksandar Nikolov<br>Jeffrey Jendryk ii<br>Niccolò Depalma<br>Sebastian Solé<br>Théo Faure<br>Sebastian Solé<br>Francesco Sani<br>Théo Faure<br>Petar Dirlic<br>Matteo Piano<br>Aleks Grozdanov<br>Matteo Pedron<br>Niccolò Depalma<br>Jeffrey Jendryk ii<br>Matteo Piano<br>Ibrahim Lawani<br>Aleksandar Nikolov<br>Sebastian Solé<br>Sebastian Solé<br>Riccardo Gollini<br>Tobias Krick<br>Roamy raul Alonso arce<br>Sebastian Solé<br>Sebastian Solé<br>Sebastian Solé<br>Gianluca Galassi<br>Federico Pereyra<br>Kamil Rychlicki<br>Aleksandar Nikolov<br>Kamil Rychlicki<br>Niccolò Depalma<br>Matteo Piano<br>Sebastian Solé<br>Gianluca Galassi<br>Kamil Rychlicki<br>Aleksandar Nikolov<br>Sebastian Solé<br>Matteo Piano<br>Danijel Koncilja<br>Aleks Grozdanov<br>Riccardo Gollini<br>Riccardo Gollini<br>Aleks Grozdanov<br>Aleksandar Nikolov<br>Sebastian Solé<br>Aleksandar Nikolov<br>Matteo Pedron<br>Riccardo Gollini<br>Aleks Grozdanov<br>Xander wolf wassenaar Ketrzynski<br>Tobias Krick<br>Théo Faure<br>Matteo Piano<br>Théo Faure<br>Gianluca Galassi<br>Sebastian Solé<br>Matteo Piano<br>Théo Faure<br>Matteo Piano<br>Aleks Grozdanov<br>Riccardo Gollini<br>Sebastian Solé<br>Kamil Rychlicki<br>Aleks Grozdanov<br>Aleksandar Nikolov<br>Kamil Rychlicki<br>Kamil Rychlicki<br>Sebastian Solé<br>Aleks Grozdanov<br>Matteo Pedron<br>Tobias Krick<br>Filippo Mancini<br>Matteo Pedron<br>Gianluca Galassi<br>Petar Dirlic<br>Aleks Grozdanov<br>Andrea Baldi<br>Francesco Sani<br>Matteo Piano<br>Sebastian Solé<br>Kamil Rychlicki<br>Matteo Pedron<br>Kamil Rychlicki<br>Roamy raul Alonso arce<br>Sebastian Solé<br>Aleksandar Nikolov<br>Tobias Krick<br>Roamy raul Alonso arce<br>Aleks Grozdanov<br>Andrea Baldi<br>Matteo Piano<br>Petar Dirlic<br>Federico Pereyra<br>Sebastian Solé<br>Federico Pereyra<br>Tobias Krick<br>Niccolò Depalma<br>Aleksandar Nikolov<br>Aleksandar Nikolov<br>Matteo Pedron<br>Mart Tammearu<br>Gianluca Galassi<br>Riccardo Gollini<br>Federico Pereyra<br>Niccolò Depalma<br>Aleks Grozdanov<br>Petar Dirlic<br>Aleksandar Nikolov<br>Riccardo Gollini<br>Kamil Rychlicki<br>Tobias Krick<br>Ibrahim Lawani<br>Xander wolf wassenaar Ketrzynski<br>Sebastian Solé<br>Niccolò Depalma<br>Sebastian Solé<br>Kamil Rychlicki<br>Kamil Rychlicki<br>Riccardo Gollini<br>Aleksandar Nikolov<br>Tobias Krick<br>Gianluca Galassi<br>Niccolò Depalma<br>Kaan Firincioglu<br>Matteo Piano<br>Sebastian Solé<br>Federico Pereyra<br>Marcus Bohme<br>Riccardo Gollini<br>Francesco Sani<br>Aleks Grozdanov<br>Riccardo Gollini<br>Sebastian Solé<br>Matteo Piano<br>Aleksandar Nikolov<br>Federico Pereyra<br>Gianluca Galassi<br>Gianluca Galassi<br>Gianluca Galassi<br>Gianluca Galassi<br>Gianluca Galassi<br>Gianluca Galassi<br>Matteo Piano<br>Théo Faure<br>Dick Kooy<br>Aleksandar Nikolov<br>Aleks Grozdanov<br>Matteo Pedron<br>Matteo Piano<br>Kamil Rychlicki<br>Kamil Rychlicki<br>Petar Dirlic<br>Riccardo Gollini<br>Aleks Grozdanov<br>Francesco Sani<br>Sebastian Solé<br>Kamil Rychlicki<br>Aleksandar Nikolov<br>Tobias Krick<br>Aleksandar Nikolov<br>Aleksandar Nikolov<br>Francesco Sani<br>Roamy raul Alonso arce<br>Angel suarez Perez<br>Matteo Pedron<br>Tobias Krick<br>Gianluca Galassi<br>Aleks Grozdanov<br>Sebastian Solé<br>Francesco Sani<br>Gianluca Galassi<br>Aleksandar Nikolov<br>Riccardo Gollini<br>Andrea Baldi<br>Gianluca Galassi<br>Roamy raul Alonso arce<br>Niccolò Depalma<br>Théo Faure<br>Gianluca Galassi<br>Dick Kooy<br>Petar Dirlic<br>Xander wolf wassenaar Ketrzynski<br>Jeffrey Jendryk ii<br>Roamy raul Alonso arce<br>Aleksandar Nikolov<br>Tobias Krick<br>Tobias Krick<br>Gianluca Galassi<br>Aleksandar Nikolov<br>Sebastian Solé<br>Théo Faure<br>Aleksandar Nikolov<br>Niccolò Depalma<br>Tobias Krick<br>Riccardo Gollini<br>Dick Kooy<br>Théo Faure<br>Matteo Piano<br>Jeffrey Jendryk ii<br>Ibrahim Lawani<br>Théo Faure<br>Aleksandar Nikolov<br>Riccardo Gollini<br>Matteo Piano<br>Matteo Pedron<br>Niccolò Depalma<br>Riccardo Gollini<br>Sebastian Solé<br>Matteo Piano<br>Aleksandar Nikolov<br>Niccolò Depalma<br>Aleks Grozdanov<br>Gianluca Galassi<br>Sebastian Solé<br>Riccardo Gollini<br>Théo Faure<br>Kamil Rychlicki<br>Kamil Rychlicki<br>Gianluca Galassi<br>Riccardo Gollini<br>Gianluca Galassi<br>Aleksandar Nikolov<br>Petar Dirlic<br>Niccolò Depalma<br>Kamil Rychlicki<br>Gianluca Galassi<br>Kamil Rychlicki<br>Gianluca Galassi<br>Aleksandar Nikolov<br>Danijel Koncilja<br>Petar Dirlic<br>Kamil Rychlicki<br>Aleksandar Nikolov<br>Kamil Rychlicki<br>Filippo Mancini<br>Aleksandar Nikolov<br>Francesco Sani<br>Gianluca Galassi<br>Jeffrey Jendryk ii<br>Jiri Leinonen<br>Kamil Rychlicki<br>Riccardo Gollini<br>Sebastian Solé<br>Tobias Krick<br>Tobias Krick<br>Tobias Krick<br>Riccardo Gollini<br>Andrea Baldi<br>Riccardo Gollini<br>Francesco Sani<br>Danijel Koncilja<br>Matteo Piano<br>Riccardo Gollini<br>Aleks Grozdanov<br>Filippo Mancini<br>Kaan Firincioglu<br>Théo Faure<br>Xander wolf wassenaar Ketrzynski<br>Matteo Piano<br>Tobias Krick<br>Matteo Piano<br>Riccardo Gollini<br>Aleks Grozdanov<br>Federico Pereyra<br>Théo Faure<br>Matteo Pedron<br>Sebastian Solé<br>Matteo Piano<br>Aleks Grozdanov<br>Matteo Pedron<br>Aleks Grozdanov<br>Jeffrey Jendryk ii<br>Riccardo Gollini<br>Gianluca Galassi<br>Niccolò Depalma<br>Kamil Rychlicki<br>Gianluca Galassi<br>Matteo Piano<br>Aleksandar Nikolov<br>Théo Faure<br>Théo Faure<br>Jeffrey Jendryk ii<br>Gianluca Galassi<br>Théo Faure<br>Francesco Sani<br>Angel suarez Perez<br>Francesco Sani<br>Sebastian Solé<br>Francesco Sani<br>Matteo Piano<br>Andrea Baldi<br>Gianluca Galassi<br>Aleksandar Nikolov<br>Filippo Mancini<br>Aleksandar Nikolov<br>Andrea Baldi<br>Petar Dirlic<br>Petar Dirlic<br>Matteo Piano<br>Aleksandar Nikolov<br>Riccardo Gollini<br>Matteo Piano<br>Jeffrey Jendryk ii<br>Federico Pereyra<br>Kamil Rychlicki<br>Gianluca Galassi<br>Jeffrey Jendryk ii<br>Sebastian Solé<br>Matteo Piano<br>Gianluca Galassi<br>Riccardo Gollini<br>Dick Kooy<br>Aleks Grozdanov<br>Théo Faure<br>Matteo Piano<br>Roamy raul Alonso arce<br>Sebastian Solé<br>Aleksandar Nikolov<br>Matteo Piano<br>Théo Faure<br>Aleksandar Nikolov<br>Gianluca Galassi<br>Théo Faure<br>Aleks Grozdanov<br>Sebastian Solé<br>Sebastian Solé<br>Gianluca Galassi<br>Gianluca Galassi<br>Kamil Rychlicki<br>Aleks Grozdanov<br>Aleksandar Nikolov<br>Cody Kessel<br>Aleks Grozdanov<br>Filippo Mancini<br>Kamil Rychlicki<br>Roamy raul Alonso arce<br>Sebastian Solé<br>Gianluca Galassi<br>Aleks Grozdanov<br>Aleksandar Nikolov<br>Riccardo Gollini<br>Matteo Pedron<br>Théo Faure<br>Tobias Krick<br>Aleksandar Nikolov<br>Filippo Mancini<br>Aleksandar Nikolov<br>Kamil Rychlicki<br>Aleksandar Nikolov<br>Aleks Grozdanov<br>Matteo Piano<br>Riccardo Gollini<br>Petar Dirlic<br>Théo Faure<br>Niccolò Depalma<br>Niccolò Depalma<br>Niccolò Depalma<br>Niccolò Depalma<br>Niccolò Depalma<br>Niccolò Depalma<br>Niccolò Depalma<br>Niccolò Depalma<br>Niccolò Depalma<br>Niccolò Depalma<br>Tobias Krick<br>Tobias Krick<br>Tobias Krick<br>Tobias Krick<br>Tobias Krick<br>Tobias Krick<br>Tobias Krick<br>Tobias Krick<br>Tobias Krick<br>Tobias Krick<br>Tobias Krick<br>Tobias Krick<br>Tobias Krick<br>Matteo Piano<br>Matteo Piano<br>Matteo Piano<br>Matteo Piano<br>Matteo Piano<br>Matteo Piano<br>Matteo Piano<br>Gianluca Galassi<br>Aleks Grozdanov<br>Aleks Grozdanov<br>Aleks Grozdanov<br>Aleks Grozdanov<br>Aleks Grozdanov<br>Aleks Grozdanov<br>Aleksandar Nikolov<br>Aleksandar Nikolov<br>Aleksandar Nikolov<br>Aleksandar Nikolov<br>Aleksandar Nikolov<br>Aleksandar Nikolov<br>Aleksandar Nikolov<br>Aleksandar Nikolov<br>Petar Dirlic<br>Petar Dirlic<br>Petar Dirlic<br>Petar Dirlic<br>Petar Dirlic<br>Petar Dirlic<br>Sebastian Solé<br>Sebastian Solé<br>Sebastian Solé<br>Sebastian Solé<br>Sebastian Solé<br>Federico Pereyra<br>Federico Pereyra<br>Federico Pereyra<br>Federico Pereyra<br>Jiri Leinonen<br>Jiri Leinonen<br>Aaro Nikula<br>Aaro Nikula<br>Eetu Kulmala<br>Sebastian Solé<br>Danijel Koncilja<br>Tim Degruyter<br>Halit Kurtulus<br>Ibrahim Emet<br>Tobias Krick<br>Marcus Bohme<br>Xander wolf wassenaar Ketrzynski<br>Matteo Piano<br>Aleks Grozdanov<br>Angel suarez Perez<br>Aleks Grozdanov<br>Dogan Karakoc<br>Niccolò Depalma<br>Sebastian Solé<br>Tobias Krick<br>Angel suarez Perez<br>Xander wolf wassenaar Ketrzynski<br>Geoffrey Van gent<br>Aleks Grozdanov<br>Tobias Krick<br>Jiri Leinonen<br>Federico Pereyra<br>Sebastian Solé<br>Gianluca Galassi<br>Federico Pereyra<br>Angel suarez Perez<br>Gianluca Galassi<br>Cody Kessel<br>Linus jonas Engelmann<br>Ibrahim Lawani<br>Andreas Takvam<br>Dick Kooy<br>Jan Kasan<br>Dominik Czerny<br>Jakub Nowosielski<br>Mart Tammearu<br>Aleksander Sliwka<br>Dick Kooy<br>Aleksander Sliwka<br>Martin Perin<br>Théo Faure<br>Andrea Baldi<br>Jeffrey Jendryk ii<br>Tobias Krick<br>Riccardo Gollini<br>Aleks Grozdanov<br>Kamil Rychlicki<br>Mart Tammearu<br>Sebastian Solé<br>Andrea Baldi<br>Mart Tammearu<br>Jeffrey Jendryk ii<br>Kamil Rychlicki<br>Sebastian Solé<br>Kamil Rychlicki<br>Jeffrey Jendryk ii<br>Riccardo Gollini<br>Théo Faure<br>Sebastian Solé<br>Matteo Piano<br>Théo Faure<br>Andrea Baldi<br>Aleks Grozdanov<br>Kamil Rychlicki<br>Aleks Grozdanov<br>Andrea Baldi<br>Sebastian Rosler<br>Riccardo Gollini<br>Gianluca Galassi<br>Sebastian Solé<br>Aleks Grozdanov<br>Riccardo Gollini<br>Matteo Pedron<br>Filippo Mancini<br>Roamy Alonso<br>Matteo Piano<br>Théo Faure<br>Aleksandar Nikolov<br>Aleks Grozdanov<br>Jeffrey Jendryk ii<br>Matteo Piano<br>Roamy Alonso<br>Riccardo Gollini<br>Filippo Mancini<br>Roamy raul Alonso arce<br>Riccardo Gollini<br>Théo Faure<br>Matteo Piano<br>Filippo Mancini<br>Sebastian Solé<br>Sebastian Solé<br>Kaan Firincioglu<br>Tobias Krick<br>Kamil Rychlicki<br>Sebastian Solé<br>Aleksandar Nikolov<br>Jeffrey Jendryk ii<br>Aleksandar Nikolov<br>Andrea Baldi<br>Federico Pereyra<br>Sebastian Solé<br>Aleks Grozdanov<br>Filippo Mancini<br>Riccardo Gollini"], ["<PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON> kyed <PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON> <PERSON><PERSON><br><PERSON><PERSON> <PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON> kyed <PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON> c<PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON>iu<PERSON> Magal<PERSON><br><PERSON> <PERSON><br><PERSON> <PERSON>amantini<br><PERSON> <PERSON><br>Giu<PERSON><PERSON><br><PERSON> <PERSON>hn<PERSON><br><PERSON> <PERSON>amantini<br>Adis Lagumdzi<PERSON><br><PERSON>s kyed Jensen<br>Giu<PERSON> Magal<PERSON><br>Maksim <PERSON><PERSON>hkov<br><PERSON> <PERSON>ster<br>Fabio R<PERSON>ci<br><PERSON> <PERSON><br>Maksim <PERSON>pozhkov<br>Ran Ta<PERSON>hashi<br>Enrico Cester<br>Mads kyed Jensen<br>Enrico Cester<br>Mads kyed Jensen<br>Adis Lagumdzija<br>Enrico Diamantini<br>Enrico Diamantini<br>Ran Takahashi<br>Maksim Sapozhkov<br>Giulio Magalini<br>Dusan Petkovic<br>Mads kyed Jensen<br>Adis Lagumdzija<br>Adis Lagumdzija<br>Enrico Diamantini<br>Mads kyed Jensen<br>Julio cesar Cardenas morales<br>Enrico Diamantini<br>Enrico Diamantini<br>Fabio Ricci<br>Giulio Magalini<br>Mads kyed Jensen<br>Dusan Petkovic<br>Roberto Russo<br>Giulio Magalini<br>Maksim Sapozhkov<br>Dusan Petkovic<br>Adis Lagumdzija<br>Fabio Ricci<br>Swan Ngapeth<br>Enrico Diamantini<br>Yosvany Hernandez cardonell<br>Yosvany Hernandez cardonell<br>Adis Lagumdzija<br>Giulio Magalini<br>Roberto Russo<br>Enrico Diamantini<br>Dusan Petkovic<br>Lukas Maase<br>Dusan Petkovic<br>Fabio Ricci<br>Enrico Cester<br>Adis Lagumdzija<br>Roberto Russo<br>Giulio Magalini<br>Fabio Ricci<br>Roberto Russo<br>Swan Ngapeth<br>Enrico Diamantini<br>Leandro ausibio Mosca<br>Fabio Ricci<br>Enrico Diamantini<br>Fabio Ricci<br>Bennie Tuinstra<br>Francesco Bernardis<br>Jordan Schnitzer<br>Mads kyed Jensen<br>Enrico Diamantini<br>Enrico Diamantini<br>Giulio Magalini<br>Enrico Diamantini<br>Ran Takahashi<br>Adis Lagumdzija<br>Enrico Diamantini<br>Ran Takahashi<br>Bertug Ondes<br>Giulio Magalini<br>Enrico Diamantini<br>Ran Takahashi<br>Ran Takahashi<br>Adis Lagumdzija<br>Ran Takahashi<br>Giulio Magalini<br>Mads kyed Jensen<br>Lukas Maase<br>Giulio Magalini<br>Dusan Petkovic<br>Giulio Magalini<br>Mads kyed Jensen<br>Enrico Diamantini<br>Maksim Sapozhkov<br>Adis Lagumdzija<br>Adis Lagumdzija<br>Roberto Russo<br>Dusan Petkovic<br>Enrico Cester<br>Filip Sestan<br>Lukas Vasina<br>Dusan Petkovic<br>Julio cesar Cardenas morales<br>Adis Lagumdzija<br>Jordan Schnitzer<br>Fabio Ricci<br>Enrico Diamantini<br>Enrico Diamantini<br>Ran Takahashi<br>Jordan Schnitzer<br>Jordan Schnitzer<br>Mads kyed Jensen<br>Giulio Magalini<br>Lukas Maase<br>Mads kyed Jensen<br>Giulio Magalini<br>Jordan Schnitzer<br>Adis Lagumdzija<br>Adis Lagumdzija<br>Enrico Cester<br>Yosvany Hernandez cardonell<br>Adis Lagumdzija<br>Ran Takahashi<br>Roberto Russo<br>Julio cesar Cardenas morales<br>Julio cesar Cardenas morales<br>Roberto Russo<br>Enrico Diamantini<br>Giulio Magalini<br>Mads kyed Jensen<br>Mads kyed Jensen<br>Giulio Magalini<br>Maksim Sapozhkov<br>Adis Lagumdzija<br>Giulio Magalini<br>Julio cesar Cardenas morales<br>Dusan Petkovic<br>Dusan Petkovic<br>Adis Lagumdzija<br>Ran Takahashi<br>Maksim Sapozhkov<br>Enrico Diamantini<br>Ran Takahashi<br>Giulio Magalini<br>Mads kyed Jensen<br>Yosvany Hernandez cardonell<br>Seppe Rotty<br>Enrico Cester<br>Enrico Diamantini<br>Enrico Cester<br>Enrico Diamantini<br>Lukas Maase<br>Jordan Schnitzer<br>Dusan Petkovic<br>Fabio Ricci<br>Dusan Petkovic<br>Enrico Cester<br>Lukas Vasina<br>Dusan Petkovic<br>Enrico Diamantini<br>Giulio Magalini<br>Adis Lagumdzija<br>Enrico Diamantini<br>Dusan Petkovic<br>Adis Lagumdzija<br>Ran Takahashi<br>Giulio Magalini<br>Bennie Tuinstra<br>Mads kyed Jensen<br>Adis Lagumdzija<br>Enrico Cester<br>Adis Lagumdzija<br>Giulio Magalini<br>Enrico Diamantini<br>Julio cesar Cardenas morales<br>Maksim Sapozhkov<br>Seppe Rotty<br>Filip Sestan<br>Dusan Petkovic<br>Fabio Ricci<br>Adis Lagumdzija<br>Jordan Schnitzer<br>Adis Lagumdzija<br>Roberto Russo<br>Fabio Ricci<br>Ran Takahashi<br>Mads kyed Jensen<br>Dusan Petkovic<br>Giulio Magalini<br>Saso Stalekar<br>Enrico Diamantini<br>Enrico Cester<br>Fabio Ricci<br>Roberto Russo<br>Dusan Petkovic<br>Adis Lagumdzija<br>Enrico Diamantini<br>Yosvany Hernandez cardonell<br>Ran Takahashi<br>Dusan Petkovic<br>Adis Lagumdzija<br>Maksim Sapozhkov<br>Enrico Diamantini<br>Enrico Cester<br>Giulio Magalini<br>Dusan Petkovic<br>Roberto Russo<br>Maksim Sapozhkov<br>Enrico Cester<br>Jordan Schnitzer<br>Seppe Rotty<br>Mads kyed Jensen<br>Giulio Magalini<br>Enrico Diamantini<br>Fabio Ricci<br>Enrico Cester<br>Enrico Diamantini<br>Jordan Schnitzer<br>Enrico Diamantini<br>Francesco Bernardis<br>Mads kyed Jensen<br>Giulio Magalini<br>Swan Ngapeth<br>Adis Lagumdzija<br>Mads kyed Jensen<br>Adis Lagumdzija<br>Adis Lagumdzija<br>Lukas Maase<br>Jordan Schnitzer<br>Fabio Ricci<br>Maksim Sapozhkov<br>Roberto Russo<br>Fabio Ricci<br>Ran Takahashi<br>Enrico Diamantini<br>Enrico Diamantini<br>Giulio Magalini<br>Ran Takahashi<br>Enrico Diamantini<br>Fabio Ricci<br>Enrico Diamantini<br>Giulio Magalini<br>Saso Stalekar<br>Matìj Pastròák<br>Enrico Cester<br>Enrico Diamantini<br>Jordan Schnitzer<br>Julio cesar Cardenas morales<br>Mads kyed Jensen<br>Adis Lagumdzija<br>Enrico Diamantini<br>Fabio Ricci<br>Enrico Diamantini<br>Giulio Magalini<br>Mads kyed Jensen<br>Francesco Bergamasco<br>Fabio Ricci<br>Giulio Magalini<br>Adis Lagumdzija<br>Roberto Russo<br>Fabio Ricci<br>Yosvany Hernandez cardonell<br>Mads kyed Jensen<br>Dusan Petkovic<br>Jordan Schnitzer<br>Enrico Cester<br>Enrico Diamantini<br>Mads kyed Jensen<br>Mads kyed Jensen<br>Filip Sestan<br>Lukas Vasina<br>Enrico Cester<br>Enrico Diamantini<br>Giulio Magalini<br>Julio cesar Cardenas morales<br>Mads kyed Jensen<br>Fabio Ricci<br>Maksim Sapozhkov<br>Enrico Diamantini<br>Bertug Ondes<br>Enrico Diamantini<br>Julio cesar Cardenas morales<br>Julio cesar Cardenas morales<br>Roberto Russo<br>Alessandro Toscani<br>Francesco Bergamasco<br>Enrico Diamantini<br>Mads kyed Jensen<br>Adis Lagumdzija<br>Mads kyed Jensen<br>Enrico Diamantini<br>Enrico Diamantini<br>Mads kyed Jensen<br>Enrico Diamantini<br>Maksim Sapozhkov<br>Roberto Russo<br>Enrico Diamantini<br>Jordan Schnitzer<br>Maksim Sapozhkov<br>Adis Lagumdzija<br>Roberto Russo<br>Lukas Vasina<br>Enrico Cester<br>Dusan Petkovic<br>Enrico Diamantini<br>Julio cesar Cardenas morales<br>Ran Takahashi<br>Adis Lagumdzija<br>Enrico Diamantini<br>Mads kyed Jensen<br>Roberto Russo<br>Mads kyed Jensen<br>Filip Sestan<br>Lukas Maase<br>Mads kyed Jensen<br>Maksim Sapozhkov<br>Enrico Cester<br>Roberto Russo<br>Giulio Magalini<br>Mads kyed Jensen<br>Enrico Diamantini<br>Dusan Petkovic<br>Yosvany Hernandez cardonell<br>Enrico Diamantini<br>Adis Lagumdzija<br>Adis Lagumdzija<br>Enrico Diamantini<br>Enrico Cester<br>Julio cesar Cardenas morales<br>Roberto Russo<br>Lukas Vasina<br>Adis Lagumdzija<br>Swan Ngapeth<br>Enrico Diamantini<br>Adis Lagumdzija<br>Adis Lagumdzija<br>Mads kyed Jensen<br>Maksim Sapozhkov<br>Enrico Cester<br>Giulio Magalini<br>Giulio Magalini<br>Ran Takahashi<br>Enrico Diamantini<br>Maksim Sapozhkov<br>Giulio Magalini<br>Adis Lagumdzija<br>Maksim Sapozhkov<br>Roberto Russo<br>Jordan Schnitzer<br>Ran Takahashi<br>Giulio Magalini<br>Enrico Cester<br>Adis Lagumdzija<br>Enrico Cester<br>Mads kyed Jensen<br>Mads kyed Jensen<br>Dusan Petkovic<br>Ran Takahashi<br>Bertug Ondes<br>Bertug Ondes<br>Leandro ausibio Mosca<br>Enrico Diamantini<br>Maksim Sapozhkov<br>Roberto Russo<br>Roberto Russo<br>Lukas Vasina<br>Enrico Diamantini<br>Mads kyed Jensen<br>Adis Lagumdzija<br>Ran Takahashi<br>Maksim Sapozhkov<br>Dusan Petkovic<br>Giulio Magalini<br>Giulio Magalini<br>Filip Sestan<br>Giulio Magalini<br>Francesco Bernardis<br>Maksim Sapozhkov<br>Dusan Petkovic<br>Seppe Rotty<br>Maksim Sapozhkov<br>Adis Lagumdzija<br>Enrico Diamantini<br>Enrico Diamantini<br>Giulio Magalini<br>Swan Ngapeth<br>Enrico Diamantini<br>Maksim Sapozhkov<br>Mads kyed Jensen<br>Enrico Diamantini<br>Fabio Ricci<br>Enrico Diamantini<br>Yosvany Hernandez cardonell<br>Enrico Diamantini<br>Ran Takahashi<br>Enrico Cester<br>Adis Lagumdzija<br>Julio cesar Cardenas morales<br>Giulio Magalini<br>Roberto Russo<br>Mads kyed Jensen<br>Ran Takahashi<br>Adis Lagumdzija<br>Maksim Sapozhkov<br>Yosvany Hernandez cardonell<br>Dusan Petkovic<br>Ran Takahashi<br>Fabio Ricci<br>Roberto Russo<br>Mads kyed Jensen<br>Enrico Cester<br>Fabio Ricci<br>Mads kyed Jensen<br>Enrico Diamantini<br>Dusan Petkovic<br>Mads kyed Jensen<br>Lukas Vasina<br>Maksim Sapozhkov<br>Enrico Diamantini<br>Giulio Magalini<br>Giulio Magalini<br>Julio cesar Cardenas morales<br>Lukas Vasina<br>Enrico Diamantini<br>Maksim Sapozhkov<br>Julio cesar Cardenas morales<br>Fabio Ricci<br>Gianluca Galassi<br>Gianluca Galassi<br>Gianluca Galassi<br>Gianluca Galassi<br>Gianluca Galassi<br>Gianluca Galassi<br>Gianluca Galassi<br>Gianluca Galassi<br>Gianluca Galassi<br>Gianluca Galassi<br>Gianluca Galassi<br>Gianluca Galassi<br>Gianluca Galassi<br>Gianluca Galassi<br>Gianluca Galassi<br>Gianluca Galassi<br>Gianluca Galassi<br>Gianluca Galassi<br>Gianluca Galassi<br>Roberto Russo<br>Francesco Bernardis<br>Dusan Petkovic<br>Dusan Petkovic<br>Dusan Petkovic<br>Dusan Petkovic<br>Dusan Petkovic<br>Dusan Petkovic<br>Dusan Petkovic<br>Dusan Petkovic<br>Dusan Petkovic<br>Gioele adeola Taiwo<br>Gioele adeola Taiwo<br>Gabriele Mariani<br>Gabriele Mariani<br>Gabriele Mariani<br>Adis Lagumdzija<br>Adis Lagumdzija<br>Adis Lagumdzija<br>Adis Lagumdzija<br>Adis Lagumdzija<br>Adis Lagumdzija<br>Adis Lagumdzija<br>Adis Lagumdzija<br>Adis Lagumdzija<br>Adis Lagumdzija<br>Adis Lagumdzija<br>Adis Lagumdzija<br>Adis Lagumdzija<br>Enrico Cester<br>Enrico Cester<br>Enrico Cester<br>Enrico Cester<br>Enrico Cester<br>Mads kyed Jensen<br>Mads kyed Jensen<br>Mads kyed Jensen<br>Mads kyed Jensen<br>Mads kyed Jensen<br>Enrico Diamantini<br>Enrico Diamantini<br>Enrico Diamantini<br>Enrico Diamantini<br>Enrico Diamantini<br>Enrico Diamantini<br>Enrico Diamantini<br>Enrico Diamantini<br>Roberto Russo<br>Roberto Russo<br>Roberto Russo<br>Roberto Russo<br>Roberto Russo<br>Swan Ngapeth<br>Swan Ngapeth<br>Joonas Jokela<br>Bennie Tuinstra<br>Filip Sestan<br>Yusuf Eken<br>Swan Ngapeth<br>Lukas Maase<br>Enrico Cester<br>Bertug Ondes<br>Mads kyed Jensen<br>Adis Lagumdzija<br>Roberto Russo<br>Bertug Ondes<br>Tieme De jong<br>Mads kyed Jensen<br>Enrico Diamantini<br>Adis Lagumdzija<br>Dusan Petkovic<br>Bertug Ondes<br>Saso Stalekar<br>Jakob Gunthor<br>Lukasz Rudzewicz<br>Lukas Vasina<br>Matìj Pastròák<br>Florian Krage<br>Pawel Woicki<br>Seppe Rotty<br>Adis Lagumdzija<br>Renet Vanker<br>Yosvany Hernandez cardonell<br>Maksim Sapozhkov<br>Adis Lagumdzija<br>Giulio Magalini<br>Mads kyed Jensen<br>Seppe Rotty<br>Giulio Magalini<br>Giulio Magalini<br>Maksim Sapozhkov<br>Fabio Ricci<br>Mads kyed Jensen<br>Giulio Magalini<br>Ran Takahashi<br>Fabio Ricci<br>Gianluca Galassi<br>Maksim Sapozhkov<br>Fabio Ricci<br>Francesco Bergamasco<br>Jordan Schnitzer<br>Gianluca Galassi<br>Dusan Petkovic<br>Enrico Diamantini<br>Fabio Ricci<br>Enrico Cester<br>Giulio Magalini<br>Maksim Sapozhkov<br>Joao Infante<br>Joao Infante<br>Yosvany Hernandez cardonell<br>Gabriele Mariani<br>Swan Ngapeth<br>Julio cesar Cardenas morales<br>Fabio Ricci<br>Adis Lagumdzija<br>Enrico Cester<br>Enrico Cester<br>Enrico Cester<br>Enrico Diamantini<br>Fabio Ricci"], ["<PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON> aties<br><PERSON><PERSON><br><PERSON><PERSON><PERSON> aties<br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON> aties<br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON> aties<br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON> aties<br><PERSON><PERSON><PERSON> aties<br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON> aties<br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON> aties<br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON> aties<br><PERSON>imo <PERSON>ci<br><PERSON> <PERSON><br><PERSON> <PERSON>o<br><PERSON>e <PERSON>zano<br><PERSON>ogo <PERSON><br><PERSON>imo Colaci<br><PERSON>er Depovere<br><PERSON>e <PERSON>zano<br><PERSON> <PERSON>o<br><PERSON>retta<br><PERSON> <PERSON>o<br><PERSON> <PERSON>o<br><PERSON>imo Colaci<br><PERSON>imo Colaci<br><PERSON><PERSON> Gottardo<br><PERSON>landy <PERSON> aties<br><PERSON> Beretta<br><PERSON>imo Colaci<br><PERSON>e <PERSON>zano<br>Robertlandy Simon aties<br>Thomas Beretta<br>Julian Zenger<br>Massimo Colaci<br>Luca Spirito<br>Thomas Beretta<br>Julian Zenger<br>Luca Spirito<br>Diogo Fevereiro<br>Robertlandy Simon aties<br>Andrea Rossi<br>Julian Zenger<br>Robertlandy Simon aties<br>Thomas Beretta<br>Robertlandy Simon aties<br>Robertlandy Simon aties<br>Robertlandy Simon aties<br>Robertlandy Simon aties<br>Robertlandy Simon aties<br>Robertlandy Simon aties<br>Luca Spirito<br>Massimo Colaci<br>Thomas Beretta<br>Gabriele Laurenzano<br>Thomas Beretta<br>Robertlandy Simon aties<br>Diogo Fevereiro<br>Luca Spirito<br>Julian Zenger<br>Thomas Beretta<br>Thomas Beretta<br>Charalampos Andreopoulos<br>Mattia Gottardo<br>Diogo Fevereiro<br>Mattia Gottardo<br>Robertlandy Simon aties<br>Luca Spirito<br>Michele Fedrizzi<br>Robertlandy Simon aties<br>Luca Spirito<br>Massimo Colaci<br>Massimo Colaci<br>Julian Zenger<br>Massimo Colaci<br>Filippo Pochini<br>Luca Spirito<br>Gabriele Laurenzano<br>Andrea Rossi<br>Luca Spirito<br>Jurii Gladyr<br>Robertlandy Simon aties<br>Andrea Rossi<br>Massimo Colaci<br>Andrea Rossi<br>Charalampos Andreopoulos<br>Robertlandy Simon aties<br>Burakhan Tosun<br>Robertlandy Simon aties<br>Thomas Beretta<br>Diogo Fevereiro<br>Thomas Beretta<br>Thomas Beretta<br>Filippo Pochini<br>Jani Kovacic<br>Sander Depovere<br>Gabriele Laurenzano<br>Luca Spirito<br>Luca Spirito<br>Julian Zenger<br>Mattia Gottardo<br>Thomas Beretta<br>Gabriele Laurenzano<br>Robertlandy Simon aties<br>Diogo Fevereiro<br>Luca Spirito<br>Luca Spirito<br>Julian Zenger<br>Michele Fedrizzi<br>Thomas Beretta<br>Robertlandy Simon aties<br>Luca Spirito<br>Diogo Fevereiro<br>Andrea Rossi<br>Massimo Colaci<br>Robertlandy Simon aties<br>Robertlandy Simon aties<br>Robertlandy Simon aties<br>Luca Spirito<br>Massimo Colaci<br>Diogo Fevereiro<br>Michele Fedrizzi<br>Gabriele Laurenzano<br>Robertlandy Simon aties<br>Thomas Beretta<br>Michele Fedrizzi<br>Charalampos Andreopoulos<br>Gabriele Laurenzano<br>Julian Zenger<br>Gabriele Laurenzano<br>Gabriele Laurenzano<br>Michele Fedrizzi<br>Thomas Beretta<br>Thomas Beretta<br>Luca Spirito<br>Robertlandy Simon aties<br>Thomas Beretta<br>Andrea Rossi<br>Gabriele Laurenzano<br>Robertlandy Simon aties<br>Gabriele Laurenzano<br>Julian Zenger<br>Filippo Pochini<br>Charalampos Andreopoulos<br>Robertlandy Simon aties<br>Charalampos Andreopoulos<br>Gabriele Laurenzano<br>Thomas Beretta<br>Massimo Colaci<br>Thomas Beretta<br>Luca Spirito<br>Luca Spirito<br>Robertlandy Simon aties<br>Mattia Gottardo<br>Robertlandy Simon aties<br>Thomas Beretta<br>Massimo Colaci<br>Gabriele Laurenzano<br>Sander Depovere<br>Charalampos Andreopoulos<br>Charalampos Andreopoulos<br>Robertlandy Simon aties<br>Robertlandy Simon aties<br>Thomas Beretta<br>Massimo Colaci<br>Charalampos Andreopoulos<br>Julian Zenger<br>Luca Spirito<br>Luca Spirito<br>Robertlandy Simon aties<br>Massimo Colaci<br>Robertlandy Simon aties<br>Robertlandy Simon aties<br>Julian Zenger<br>Robertlandy Simon aties<br>Gabriele Laurenzano<br>Federico Menchetti<br>Robertlandy Simon aties<br>Julian Zenger<br>Andrea Rossi<br>Robertlandy Simon aties<br>Andrea Rossi<br>Thomas Beretta<br>Luca Spirito<br>Massimo Colaci<br>Thomas Beretta<br>Andrea Rossi<br>Thomas Beretta<br>Luca Spirito<br>Andrea Rossi<br>Massimo Colaci<br>Thomas Beretta<br>Gabriele Laurenzano<br>Thomas Beretta<br>Robertlandy Simon aties<br>Robertlandy Simon aties<br>Julian Zenger<br>Thomas Beretta<br>Gabriele Laurenzano<br>Luca Spirito<br>Luca Spirito<br>Massimo Colaci<br>Massimo Colaci<br>Luca Spirito<br>Thomas Beretta<br>Gabriele Laurenzano<br>Robertlandy Simon aties<br>Luca Spirito<br>Luca Spirito<br>Julian Zenger<br>Thomas Beretta<br>Luca Spirito<br>Massimo Colaci<br>Luca Spirito<br>Luca Spirito<br>Robertlandy Simon aties<br>Thomas Beretta<br>Diogo Fevereiro<br>Luca Spirito<br>Julian Zenger<br>Andrea Rossi<br>Andrea Rossi<br>Andrea Rossi<br>Luca Spirito<br>Thomas Beretta<br>Julian Zenger<br>Luca Spirito<br>Robertlandy Simon aties<br>Mattia Gottardo<br>Luca Spirito<br>Massimo Colaci<br>Julian Zenger<br>Massimo Colaci<br>Charalampos Andreopoulos<br>Andrea Rossi<br>Gabriele Laurenzano<br>Filippo Pochini<br>Julian Zenger<br>Robertlandy Simon aties<br>Diogo Fevereiro<br>Robertlandy Simon aties<br>Luca Spirito<br>Thomas Beretta<br>Massimo Colaci<br>Gabriele Laurenzano<br>Andrea Rossi<br>Luca Spirito<br>Michele Fedrizzi<br>Robertlandy Simon aties<br>Julian Zenger<br>Gabriele Laurenzano<br>Michele Fedrizzi<br>Gabriele Laurenzano<br>Michele Fedrizzi<br>Julian Zenger<br>Gabriele Laurenzano<br>Julian Zenger<br>Thomas Beretta<br>Mattia Gottardo<br>Mattia Gottardo<br>Massimo Colaci<br>Thomas Beretta<br>Robertlandy Simon aties<br>Charalampos Andreopoulos<br>Julian Zenger<br>Thomas Beretta<br>Filippo Pochini<br>Massimo Colaci<br>Robertlandy Simon aties<br>Luca Spirito<br>Massimo Colaci<br>Thomas Beretta<br>Andrea Rossi<br>Julian Zenger<br>Luca Spirito<br>Thomas Beretta<br>Massimo Colaci<br>Gabriele Laurenzano<br>Mattia Gottardo<br>Thomas Beretta<br>Gabriele Laurenzano<br>Julian Zenger<br>Gabriele Laurenzano<br>Mattia Gottardo<br>Robertlandy Simon aties<br>Diogo Fevereiro<br>Gabriele Laurenzano<br>Thomas Beretta<br>Julian Zenger<br>Gabriele Laurenzano<br>Julian Zenger<br>Thomas Beretta<br>Thomas Beretta<br>Andrea Rossi<br>Robertlandy Simon aties<br>Filippo Pochini<br>Gabriele Laurenzano<br>Massimo Colaci<br>Mattia Gottardo<br>Thomas Beretta<br>Michele Fedrizzi<br>Robertlandy Simon aties<br>Gabriele Laurenzano<br>Andrea Rossi<br>Robertlandy Simon aties<br>Andrea Rossi<br>Thomas Beretta<br>Mattia Gottardo<br>Luca Spirito<br>Massimo Colaci<br>Massimo Colaci<br>Robertlandy Simon aties<br>Massimo Colaci<br>Michele Fedrizzi<br>Gabriele Laurenzano<br>Thomas Beretta<br>Gabriele Laurenzano<br>Gabriele Laurenzano<br>Massimo Colaci<br>Gabriele Laurenzano<br>Michele Fedrizzi<br>Julian Zenger<br>Ruben Schott<br>Gabriele Laurenzano<br>Massimo Colaci<br>Luca Spirito<br>Mattia Gottardo<br>Michele Fedrizzi<br>Luca Spirito<br>Gabriele Laurenzano<br>Massimo Colaci<br>Robertlandy Simon aties<br>Lukasz Wiese<br>Andrea Rossi<br>Gabriele Laurenzano<br>Filippo Pochini<br>Gabriele Laurenzano<br>Massimo Colaci<br>Jani Kovacic<br>Massimo Colaci<br>Massimo Colaci<br>Massimo Colaci<br>Robertlandy Simon aties<br>Massimo Colaci<br>Thomas Beretta<br>Luca Spirito<br>Andrea Rossi<br>Robertlandy Simon aties<br>Robertlandy Simon aties<br>Diogo Fevereiro<br>Massimo Colaci<br>Jani Kovacic<br>Luca Spirito<br>Mattia Gottardo<br>Massimo Colaci<br>Robertlandy Simon aties<br>Mattia Gottardo<br>Thomas Beretta<br>Massimo Colaci<br>Julian Zenger<br>Massimo Colaci<br>Michele Fedrizzi<br>Gabriele Laurenzano<br>Gabriele Laurenzano<br>Thomas Beretta<br>Luca Spirito<br>Andrea Rossi<br>Gabriele Laurenzano<br>Andrea Rossi<br>Massimo Colaci<br>Thomas Beretta<br>Robertlandy Simon aties<br>Julian Zenger<br>Julian Zenger<br>Thomas Beretta<br>Andrea Rossi<br>Julian Zenger<br>Robertlandy Simon aties<br>Jani Kovacic<br>Luca Spirito<br>Thomas Beretta<br>Diogo Fevereiro<br>Luca Spirito<br>Massimo Colaci<br>Robertlandy Simon aties<br>Massimo Colaci<br>Massimo Colaci<br>Robertlandy Simon aties<br>Filippo Pochini<br>Andrea Rossi<br>Massimo Colaci<br>Sander Depovere<br>Julian Zenger<br>Luca Spirito<br>Andrea Rossi<br>Massimo Colaci<br>Gabriele Laurenzano<br>Gabriele Laurenzano<br>Julian Zenger<br>Massimo Colaci<br>Gabriele Laurenzano<br>Gabriele Laurenzano<br>Luca Spirito<br>Mattia Gottardo<br>Robertlandy Simon aties<br>Massimo Colaci<br>Luca Spirito<br>Mattia Gottardo<br>Michele Fedrizzi<br>Robertlandy Simon aties<br>Federico Menchetti<br>Mattia Gottardo<br>Filippo Pochini<br>Gabriele Laurenzano<br>Gabriele Laurenzano<br>Robertlandy Simon aties<br>Andrea Rossi<br>Gabriele Laurenzano<br>Mattia Gottardo<br>Thomas Beretta<br>Luca Spirito<br>Mattia Gottardo<br>Ruben Schott<br>Julian Zenger<br>Massimo Colaci<br>Robertlandy Simon aties<br>Thomas Beretta<br>Luca Spirito<br>Julian Zenger<br>Thomas Beretta<br>Charalampos Andreopoulos<br>Robertlandy Simon aties<br>Gabriele Laurenzano<br>Andrea Rossi<br>Julian Zenger<br>Andrea Rossi<br>Filippo Pochini<br>Filippo Pochini<br>Gabriele Laurenzano<br>Robertlandy Simon aties<br>Julian Zenger<br>Charalampos Andreopoulos<br>Massimo Colaci<br>Thomas Beretta<br>Thomas Beretta<br>Thomas Beretta<br>Filippo Pochini<br>Massimo Colaci<br>Robertlandy Simon aties<br>Gabriele Laurenzano<br>Robertlandy Simon aties<br>Charalampos Andreopoulos<br>Thomas Beretta<br>Thomas Beretta<br>Thomas Beretta<br>Robertlandy Simon aties<br>Andrea Rossi<br>Diogo Fevereiro<br>Mattia Gottardo<br>Julian Zenger<br>Luca Spirito<br>Diogo Fevereiro<br>Luca Spirito<br>Andrea Rossi<br>Luca Spirito<br>Thomas Beretta<br>Filippo Pochini<br>Luca Spirito<br>Massimo Colaci<br>Robertlandy Simon aties<br>Robertlandy Simon aties<br>Luca Spirito<br>Gabriele Laurenzano<br>Charalampos Andreopoulos<br>Massimo Colaci<br>Gabriele Laurenzano<br>Gabriele Laurenzano<br>Luca Spirito<br>Julian Zenger<br>Thomas Beretta<br>Filippo Pochini<br>Gabriele Laurenzano<br>Luca Spirito<br>Massimo Colaci<br>Gabriele Laurenzano<br>Massimo Colaci<br>Luca Spirito<br>Filippo Pochini<br>Diogo Fevereiro<br>Massimo Colaci<br>Gabriele Laurenzano<br>Vojin Cacic<br>Gabriele Laurenzano<br>Burakhan Tosun<br>Massimo Colaci<br>Diogo Fevereiro<br>Luca Spirito<br>Julian Zenger<br>Jani Kovacic<br>Luca Spirito<br>Robertlandy Simon aties<br>Thomas Beretta<br>Davide Gardini<br>Davide Gardini<br>Davide Gardini<br>Davide Gardini<br>Davide Gardini<br>Davide Gardini<br>Davide Gardini<br>Davide Gardini<br>Davide Gardini<br>Davide Gardini<br>Davide Gardini<br>Davide Gardini<br>Davide Gardini<br>Davide Gardini<br>Davide Gardini<br>Davide Gardini<br>Davide Gardini<br>Davide Gardini<br>Davide Gardini<br>Julian Zenger<br>Julian Zenger<br>Julian Zenger<br>Julian Zenger<br>Julian Zenger<br>Julian Zenger<br>Julian Zenger<br>Julian Zenger<br>Julian Zenger<br>Thomas Beretta<br>Thomas Beretta<br>Thomas Beretta<br>Thomas Beretta<br>Thomas Beretta<br>Thomas Beretta<br>Thomas Beretta<br>Thomas Beretta<br>Thomas Beretta<br>Robertlandy Simon aties<br>Luca Spirito<br>Luca Spirito<br>Luca Spirito<br>Luca Spirito<br>Luca Spirito<br>Luca Spirito<br>Charalampos Andreopoulos<br>Charalampos Andreopoulos<br>Charalampos Andreopoulos<br>Charalampos Andreopoulos<br>Charalampos Andreopoulos<br>Charalampos Andreopoulos<br>Mattia Gottardo<br>Mattia Gottardo<br>Mattia Gottardo<br>Mattia Gottardo<br>Mattia Gottardo<br>Mattia Gottardo<br>Mattia Gottardo<br>Mattia Gottardo<br>Andrea Rossi<br>Andrea Rossi<br>Andrea Rossi<br>Andrea Rossi<br>Andrea Rossi<br>Andrea Rossi<br>Massimo Colaci<br>Massimo Colaci<br>Massimo Colaci<br>Massimo Colaci<br>Massimo Colaci<br>Gabriele Laurenzano<br>Gabriele Laurenzano<br>Gabriele Laurenzano<br>Gabriele Laurenzano<br>Gabriele Laurenzano<br>Gabriele Laurenzano<br>Filippo Pochini<br>Filippo Pochini<br>Filippo Pochini<br>Filippo Pochini<br>Filippo Pochini<br>Massimo Colaci<br>Burakhan Tosun<br>Jani Kovacic<br>Ali onur Edis<br>Filippo Pochini<br>Andrea Rossi<br>Vojin Cacic<br>Luca Spirito<br>Robertlandy Simon aties<br>Luca Spirito<br>Gabriele Laurenzano<br>Gabriele Laurenzano<br>Massimo Colaci<br>Charalampos Andreopoulos<br>Massimo Colaci<br>Julian Zenger<br>Thomas Beretta<br>Thomas Beretta<br>Ruben Schott<br>Severin Brandt<br>Julian Zenger<br>Fedor Ivanov<br>Adrian Buchowski<br>Lukasz Wiese<br>Sebastian Adamczyk<br>Mateusz Maslowski<br>Sander Depovere<br>Gabriele Laurenzano<br>Mattia Gottardo<br>Elias Thys<br>Luca Spirito<br>Gabriele Laurenzano<br>Massimo Colaci<br>Sander Depovere<br>Robertlandy Simon aties<br>Andrea Rossi<br>Gabriele Laurenzano<br>Gabriele Laurenzano<br>Julian Zenger<br>Federico Menchetti<br>Massimo Colaci<br>Andrea Rossi<br>Andrea Rossi<br>Robertlandy Simon aties<br>Thomas Beretta<br>Jurii Gladyr<br>Luca Spirito<br>Gabriele Laurenzano<br>Gabriele Laurenzano<br>Thomas Beretta<br>Massimo Colaci<br>Luca Spirito<br>Robertlandy Simon aties<br>Robertlandy Simon aties<br>Michele Fedrizzi<br>Davide Gardini<br>Diogo Fevereiro<br>Massimo Colaci<br>Andrea Rossi<br>Diogo Fevereiro<br>Andrea Rossi<br>Diogo Fevereiro<br>Thomas Beretta<br>Robertlandy Simon aties<br>Kamil Semeniuk<br>Massimo Colaci<br>Gabriele Laurenzano<br>Julian Zenger<br>Massimo Colaci<br>Luca Spirito<br>Olli Kunnari<br>Olli Kunnari<br>Thomas Beretta<br>Massimo Colaci<br>None Akira"], ["<PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON> <PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON> <PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON> <PERSON><PERSON><br><PERSON><br><PERSON> krel<PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON> krel<PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br>Yuki <PERSON><br><PERSON> C<PERSON>win<PERSON><br><PERSON>a<br><PERSON><br>Alessio Font<PERSON><br><PERSON> Gil kreling<br><PERSON>ia Or<PERSON>i<br><PERSON> <PERSON> kreling<br><PERSON> Brehme<br><PERSON> Rous<PERSON>x<br>Ran <PERSON>hashi<br><PERSON> Gil kreling<br><PERSON> <PERSON>ri<br><PERSON> Brehme<br>Yuki <PERSON>hi<PERSON><br><PERSON> <PERSON>is<br>Yuki Ishikawa<br>Michael Czerwinski<br>Yuki Ishikawa<br>Fernando Gil kreling<br>Ran Takahashi<br>Yuki Ishikawa<br>Ionut alin Ambrose<br>Michael Czerwinski<br>Ran Takahashi<br>Alessio Fontani<br>Fernando Gil kreling<br>Alessio Fontani<br>Fernando Gil kreling<br>Alessio Fontani<br>Fernando Gil kreling<br>Michael Czerwinski<br>Fernando Gil kreling<br>Michael Czerwinski<br>Ionut alin Ambrose<br>Yuki Ishikawa<br>Aleksander Atanasijevic<br>Yuki Ishikawa<br>Yuki Ishikawa<br>Ran Takahashi<br>Yuki Ishikawa<br>Vojtìch Patoèka<br>Aleksander Atanasijevic<br>Fernando Gil kreling<br>Ionut alin Ambrose<br>Ran Takahashi<br>Yuki Ishikawa<br>Ran Takahashi<br>Yuki Ishikawa<br>Yuki Ishikawa<br>Yuki Ishikawa<br>Michael Czerwinski<br>Michael Czerwinski<br>Ionut alin Ambrose<br>Yuki Ishikawa<br>Yuki Ishikawa<br>Anton Brehme<br>Ionut alin Ambrose<br>Yuki Ishikawa<br>Michael Czerwinski<br>Lorenzo Sala<br>Yuki Ishikawa<br>Nikola Gjorgiev<br>Ran Takahashi<br>Fernando Gil kreling<br>Nikola Gjorgiev<br>Ran Takahashi<br>Yuki Ishikawa<br>Fernando Gil kreling<br>Fernando Gil kreling<br>Michael Czerwinski<br>Ionut alin Ambrose<br>Mohammadjavad Manavinezhad<br>Yuki Ishikawa<br>Yuki Ishikawa<br>Anton Brehme<br>Mattia Orioli<br>Anton Brehme<br>Tomas Rousseaux<br>Ionut alin Ambrose<br>Fernando Gil kreling<br>Fernando Gil kreling<br>Yuki Ishikawa<br>Fernando Gil kreling<br>Fernando Gil kreling<br>Michael Czerwinski<br>Fernando Gil kreling<br>Francesco Pierri<br>Yuki Ishikawa<br>Michael Czerwinski<br>Lorenzo Sala<br>Yuki Ishikawa<br>Anton Brehme<br>Tomas Rousseaux<br>Anton Brehme<br>Fernando Gil kreling<br>Yuki Ishikawa<br>Ionut alin Ambrose<br>Yuki Ishikawa<br>Ionut alin Ambrose<br>Michael Czerwinski<br>Anton Brehme<br>Mattia Orioli<br>Tomas Rousseaux<br>Fernando Gil kreling<br>Aleksander Atanasijevic<br>Yuki Ishikawa<br>Mohammadjavad Manavinezhad<br>Alessio Fontani<br>Michael Czerwinski<br>Aleksander Atanasijevic<br>Yuki Ishikawa<br>Yuki Ishikawa<br>Fernando Gil kreling<br>Nikola Gjorgiev<br>Fernando Gil kreling<br>Mattia Orioli<br>Alessio Fontani<br>Anton Brehme<br>Ran Takahashi<br>Yuki Ishikawa<br>Fernando Gil kreling<br>Ran Takahashi<br>Michael Czerwinski<br>Yuki Ishikawa<br>Anton Brehme<br>Francesco Pierri<br>Fernando Gil kreling<br>Mattia Orioli<br>Lorenzo Sala<br>Fernando Gil kreling<br>Ionut alin Ambrose<br>Enrico Diamantini<br>Michael Czerwinski<br>Michael Czerwinski<br>Mattia Orioli<br>Michael Czerwinski<br>Tomas Rousseaux<br>Francesco Bernardis<br>Yuki Ishikawa<br>Mattia Orioli<br>Tomas Rousseaux<br>Yuki Ishikawa<br>Yuki Ishikawa<br>Tomas Rousseaux<br>Fernando Gil kreling<br>Lorenzo Sala<br>Gunes Faik samet<br>Aleksander Atanasijevic<br>Tomas Rousseaux<br>Lorenzo Sala<br>Nikola Gjorgiev<br>Michael Czerwinski<br>Michael Czerwinski<br>Mattia Orioli<br>Ionut alin Ambrose<br>Lorenzo Sala<br>Francesco Pierri<br>Fernando Gil kreling<br>Yuki Ishikawa<br>Aleksander Atanasijevic<br>Lorenzo Sala<br>Yuki Ishikawa<br>Lorenzo Sala<br>Francesco Pierri<br>Fernando Gil kreling<br>Yuki Ishikawa<br>Ionut alin Ambrose<br>Leonardo Sandu<br>Yuki Ishikawa<br>Anton Brehme<br>Alessio Fontani<br>Fernando Gil kreling<br>Ionut alin Ambrose<br>Mohammadjavad Manavinezhad<br>Yuki Ishikawa<br>Tomas Rousseaux<br>Yuki Ishikawa<br>Jannis Hopt<br>Fernando Gil kreling<br>Francesco Pierri<br>Fernando Gil kreling<br>Lorenzo Sala<br>Francesco Pierri<br>Ran Takahashi<br>Lorenzo Sala<br>Michael Czerwinski<br>Mattia Orioli<br>Yuki Ishikawa<br>Fernando Gil kreling<br>Michael Czerwinski<br>Yuki Ishikawa<br>Yuki Ishikawa<br>Anton Brehme<br>Mattia Orioli<br>Mattia Orioli<br>Anton Brehme<br>Ran Takahashi<br>Fernando Gil kreling<br>Ran Takahashi<br>Fernando Gil kreling<br>Yuki Ishikawa<br>Tomas Rousseaux<br>Fernando Gil kreling<br>Nikola Gjorgiev<br>Ran Takahashi<br>Ran Takahashi<br>Tomas Rousseaux<br>Yuki Ishikawa<br>Lorenzo Sala<br>Yuki Ishikawa<br>Michael Czerwinski<br>Francesco Pierri<br>Yuki Ishikawa<br>Anton Brehme<br>Michael Czerwinski<br>Michael Czerwinski<br>Yuki Ishikawa<br>Alessio Fontani<br>Mattia Orioli<br>Ran Takahashi<br>Tomas Rousseaux<br>Yuki Ishikawa<br>Yuki Ishikawa<br>Yuki Ishikawa<br>Yuki Ishikawa<br>Lorenzo Sala<br>Michael Czerwinski<br>Mattia Orioli<br>Yuki Ishikawa<br>Fernando Gil kreling<br>Mohammadjavad Manavinezhad<br>Yuki Ishikawa<br>Fernando Gil kreling<br>Leon Dervisaj<br>Anton Brehme<br>Yuki Ishikawa<br>Fernando Gil kreling<br>Yuki Ishikawa<br>Fernando Gil kreling<br>Leonardo gabriel Sandu<br>Alessio Fontani<br>Aleksander Atanasijevic<br>Francesco Pierri<br>Francesco Pierri<br>Tomas Rousseaux<br>Leonardo Sandu<br>Francesco Pierri<br>Anton Brehme<br>Anton Brehme<br>Yuki Ishikawa<br>Michael Czerwinski<br>Fernando Gil kreling<br>Francesco Pierri<br>Ionut alin Ambrose<br>Michael Czerwinski<br>Yuki Ishikawa<br>Tomas Rousseaux<br>Fernando Gil kreling<br>Tomas Rousseaux<br>Fernando Gil kreling<br>Michael Czerwinski<br>Ionut alin Ambrose<br>Michael Czerwinski<br>Anton Brehme<br>Anton Brehme<br>Yuki Ishikawa<br>Anton Brehme<br>Tomas Rousseaux<br>Michael Czerwinski<br>Ionut alin Ambrose<br>Anton Brehme<br>Michael Czerwinski<br>Michael Czerwinski<br>Yuki Ishikawa<br>Francesco Pierri<br>Yuki Ishikawa<br>Anton Brehme<br>Ran Takahashi<br>Ran Takahashi<br>Ran Takahashi<br>Ran Takahashi<br>Ran Takahashi<br>Ran Takahashi<br>Ran Takahashi<br>Ran Takahashi<br>Ran Takahashi<br>Gabriele Mariani<br>Gabriele Mariani<br>Yuki Ishikawa<br>Yuki Ishikawa<br>Yuki Ishikawa<br>Yuki Ishikawa<br>Yuki Ishikawa<br>Yuki Ishikawa<br>Yuki Ishikawa<br>Francesco Pierri<br>Francesco Pierri<br>Francesco Pierri<br>Francesco Pierri<br>Francesco Pierri<br>Francesco Pierri<br>Ionut alin Ambrose<br>Ionut alin Ambrose<br>Ionut alin Ambrose<br>Ionut alin Ambrose<br>Ionut alin Ambrose<br>Ionut alin Ambrose<br>Ionut alin Ambrose<br>Lucas Santos<br>Lucas Santos<br>Aatu Vihola<br>Gunes Faik samet<br>Nikola Gjorgiev<br>Niklas Breiling<br>Gokcen Yuksel<br>Ziga Stern<br>Gokcen Yuksel<br>Alessio Fontani<br>Francesco Pierri<br>Ionut alin Ambrose<br>Ran Takahashi<br>Ionut alin Ambrose<br>Tomas Rousseaux<br>Cezary Sapinski<br>Aleksander Atanasijevic<br>Vojtìch Patoèka<br>Aleksander Atanasijevic<br>Miguel angel Fornes<br>Niklas Breiling<br>Ionut alin Ambrose<br>Michael Czerwinski<br>Anton Brehme<br>Mohammadjavad Manavinezhad<br>Anton Brehme<br>Gildas Prevert<br>Lorenzo Sala<br>Lorenzo Sala<br>Ran Takahashi<br>Lorenzo Sala<br>Anton Brehme<br>Michael Czerwinski<br>Yuki Ishikawa<br>Leon Dervisaj<br>Hernandez Yosvani<br>Yuki Ishikawa<br>Anton Brehme<br>Fernando Gil kreling<br>Anton Brehme<br>Anton Brehme<br>Mattia Orioli<br>Yuki Ishikawa<br>Leonardo gabriel Sandu<br>Lorenzo Sala<br>Jannis Hopt<br>Jannis Hopt<br>Yuki Ishikawa<br>Fernando Gil kreling<br>Anton Brehme<br>Gianluca Rossi<br>Davide Boschini<br>Francesco Pierri<br>Gunes Faik samet<br>Ilar Toimela<br>Ilar Toimela<br>Lorenzo Sala<br>Fernando Gil kreling<br>Arshdeep Dosanjh<br>Yuki Ishikawa"], ["<PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON> g<PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON>lav<PERSON> gual<PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><PERSON><br><PERSON>lav<PERSON> gual<PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON> g<PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON> <PERSON>af<PERSON><br><PERSON><br>Flavio Resende gualberto<br><PERSON>lav<PERSON>sende gualberto<br><PERSON>e Lavia<br><PERSON>e Marchiani<br>Uladzislau <PERSON>skiba<br><PERSON>a <PERSON>i<br><PERSON>bert Andringa<br><PERSON><br><PERSON> De cecco<br><PERSON>e Lavia<br><PERSON>ijs <PERSON><br><PERSON> <PERSON>isoli<br><PERSON>a <PERSON>i<br><PERSON> Russell<br>Matteo Staforini<br>Daniele Lavia<br>Robbert Andringa<br>Elia Bossi<br>Daniele Lavia<br>Daniele Lavia<br>Mathijs Desmet<br>Flavio Resende gualberto<br>Robbert Andringa<br>Mathijs Desmet<br>Mathijs Desmet<br>Mathijs Desmet<br>Uladzislau Davyskiba<br>Mathijs Desmet<br>Elia Bossi<br>Robbert Andringa<br>Flavio Resende gualberto<br>Manuele Marchiani<br>Robbert Andringa<br>Kyle Russell<br>Flavio Resende gualberto<br>Mathijs Desmet<br>Flavio Resende gualberto<br>Tatsunori Otsuka<br>Daniele Lavia<br>Flavio Resende gualberto<br>Tatsunori Otsuka<br>Uladzislau Davyskiba<br>Flavio Resende gualberto<br>Mathijs Desmet<br>Robbert Andringa<br>Grzegorz Lomacz<br>Luciano De cecco<br>Daniele Lavia<br>Flavio Resende gualberto<br>Tatsunori Otsuka<br>Flavio Resende gualberto<br>Robbert Andringa<br>Uladzislau Davyskiba<br>Flavio Resende gualberto<br>Daniele Lavia<br>Robbert Andringa<br>Grzegorz Lomacz<br>Uladzislau Davyskiba<br>Luciano De cecco<br>Petar Dirlic<br>Luciano De cecco<br>Daniele Lavia<br>Mathijs Desmet<br>Mathijs Desmet<br>Tatsunori Otsuka<br>Elia Bossi<br>Robbert Andringa<br>Daniele Lavia<br>Daniele Lavia<br>Federico Pellegrini<br>Robbert Andringa<br>Mathijs Desmet<br>Luciano De cecco<br>Daniele Lavia<br>Uladzislau Davyskiba<br>Daniele Lavia<br>Uladzislau Davyskiba<br>Pietro Bonisoli<br>Elia Bossi<br>Mathijs Desmet<br>Robbert Andringa<br>Matteo Staforini<br>Uladzislau Davyskiba<br>Pietro Bonisoli<br>Daniele Lavia<br>Elia Bossi<br>Mathijs Desmet<br>Pietro Bonisoli<br>Mathijs Desmet<br>Luciano De cecco<br>Pietro Bonisoli<br>Uladzislau Davyskiba<br>Luciano De cecco<br>Pietro Bonisoli<br>Daniele Lavia<br>Mathijs Desmet<br>Luciano De cecco<br>Daniele Lavia<br>Pietro Bonisoli<br>Robbert Andringa<br>Daniele Lavia<br>Mathijs Desmet<br>Flavio Resende gualberto<br>Mathijs Desmet<br>Petar Dirlic<br>Luciano De cecco<br>Uladzislau Davyskiba<br>Mathijs Desmet<br>Luciano De cecco<br>Pietro Bonisoli<br>Robbert Andringa<br>Manuele Marchiani<br>Elia Bossi<br>Pietro Bonisoli<br>Luciano De cecco<br>Uladzislau Davyskiba<br>Petar Dirlic<br>Pietro Bonisoli<br>Elia Bossi<br>Uladzislau Davyskiba<br>Mathijs Desmet<br>Pietro Bonisoli<br>Robbert Andringa<br>Luciano De cecco<br>Elia Bossi<br>Uladzislau Davyskiba<br>Uladzislau Davyskiba<br>Matteo Staforini<br>Luciano De cecco<br>Daniele Lavia<br>Luciano De cecco<br>Uladzislau Davyskiba<br>Robbert Andringa<br>Luciano De cecco<br>Luciano De cecco<br>Luciano De cecco<br>Luciano De cecco<br>Matteo Staforini<br>Flavio Resende gualberto<br>Daniele Lavia<br>Daniele Lavia<br>Luciano De cecco<br>Tatsunori Otsuka<br>Mattia Labarile<br>Robbert Andringa<br>Petar Dirlic<br>Robbert Andringa<br>Elia Bossi<br>Flavio Resende gualberto<br>Daniele Lavia<br>Flavio Resende gualberto<br>Pietro Bonisoli<br>Pietro Bonisoli<br>Flavio Resende gualberto<br>Kyle Russell<br>Flavio Resende gualberto<br>Elia Bossi<br>Uladzislau Davyskiba<br>Pietro Bonisoli<br>Luciano De cecco<br>Uladzislau Davyskiba<br>Elia Bossi<br>Luciano De cecco<br>Luciano De cecco<br>Pietro Bonisoli<br>Robbert Andringa<br>Matteo Staforini<br>Daniele Lavia<br>Petar Dirlic<br>Robbert Andringa<br>Luciano De cecco<br>Elia Bossi<br>Pietro Bonisoli<br>Kyle Russell<br>Pietro Bonisoli<br>Mathijs Desmet<br>Flavio Resende gualberto<br>Luciano De cecco<br>Flavio Resende gualberto<br>Robbert Andringa<br>Daniele Lavia<br>Uladzislau Davyskiba<br>Daniele Lavia<br>Daniele Lavia<br>Elia Bossi<br>Grzegorz Lomacz<br>Tatsunori Otsuka<br>Pietro Bonisoli<br>Kyle Russell<br>Tatsunori Otsuka<br>Mikolaj Staporek<br>Daniele Lavia<br>Pietro Bonisoli<br>Mathijs Desmet<br>Manuele Marchiani<br>Robbert Andringa<br>Mathijs Desmet<br>Mathijs Desmet<br>Matteo Staforini<br>Elia Bossi<br>Mathijs Desmet<br>Pietro Bonisoli<br>Pietro Bonisoli<br>Pietro Bonisoli<br>Daniele Lavia<br>Petar Dirlic<br>Pietro Bonisoli<br>Mathijs Desmet<br>Pietro Bonisoli<br>Elia Bossi<br>Robbert Andringa<br>Tatsunori Otsuka<br>Tatsunori Otsuka<br>Petar Dirlic<br>Flavio Resende gualberto<br>Luciano De cecco<br>Uladzislau Davyskiba<br>Federico Pellegrini<br>Mathijs Desmet<br>Daniele Lavia<br>Daniele Lavia<br>Luciano De cecco<br>Elia Bossi<br>Manuele Marchiani<br>Flavio Resende gualberto<br>Robbert Andringa<br>Luciano De cecco<br>Daniele Lavia<br>Daniele Lavia<br>Mathijs Desmet<br>Matteo Staforini<br>Pietro Bonisoli<br>Elia Bossi<br>Flavio Resende gualberto<br>Luciano De cecco<br>Daniele Lavia<br>Mathijs Desmet<br>Manuele Marchiani<br>Jordi Ramón ferragut<br>Daniele Lavia<br>Petar Dirlic<br>Kyle Russell<br>Kyle Russell<br>Tatsunori Otsuka<br>Uladzislau Davyskiba<br>Flavio Resende gualberto<br>Mathijs Desmet<br>Elia Bossi<br>Robbert Andringa<br>Daniele Lavia<br>Kyle Russell<br>Elia Bossi<br>Elia Bossi<br>Luciano De cecco<br>Flavio Resende gualberto<br>Pietro Bonisoli<br>Mathijs Desmet<br>Uladzislau Davyskiba<br>Tatsunori Otsuka<br>Uladzislau Davyskiba<br>Pietro Bonisoli<br>Uladzislau Davyskiba<br>Pietro Bonisoli<br>Robbert Andringa<br>Daniele Lavia<br>Robbert Andringa<br>Luciano De cecco<br>Uladzislau Davyskiba<br>Federico Pellegrini<br>Danilo De santis<br>Kyle Russell<br>Kyle Russell<br>Tatsunori Otsuka<br>Pietro Bonisoli<br>Tatsunori Otsuka<br>Luciano De cecco<br>Luciano De cecco<br>Daniele Lavia<br>Pietro Bonisoli<br>Daniele Lavia<br>Petar Dirlic<br>Mathijs Desmet<br>Flavio Resende gualberto<br>Mathijs Desmet<br>Flavio Resende gualberto<br>Robbert Andringa<br>Pietro Bonisoli<br>Pietro Bonisoli<br>Daniele Lavia<br>Daniele Lavia<br>Grzegorz Lomacz<br>Elia Bossi<br>Pietro Bonisoli<br>Pietro Bonisoli<br>Pietro Bonisoli<br>Uladzislau Davyskiba<br>Robbert Andringa<br>Elia Bossi<br>Luciano De cecco<br>Uladzislau Davyskiba<br>Mathijs Desmet<br>Luciano De cecco<br>Robbert Andringa<br>Daniele Lavia<br>Elia Bossi<br>Luciano De cecco<br>Robbert Andringa<br>Petar Dirlic<br>Petar Dirlic<br>Luciano De cecco<br>Pietro Bonisoli<br>Pietro Bonisoli<br>Kyle Russell<br>Uladzislau Davyskiba<br>Robbert Andringa<br>Robbert Andringa<br>Daniele Lavia<br>Pietro Bonisoli<br>Federico Pellegrini<br>Uladzislau Davyskiba<br>Grzegorz Lomacz<br>Robbert Andringa<br>Elia Bossi<br>Manuele Marchiani<br>Robbert Andringa<br>Tatsunori Otsuka<br>Pietro Bonisoli<br>Manuele Marchiani<br>Flavio Resende gualberto<br>Elia Bossi<br>Robbert Andringa<br>Robbert Andringa<br>Uladzislau Davyskiba<br>Petar Dirlic<br>Elia Bossi<br>Pietro Bonisoli<br>Mathijs Desmet<br>Daniele Lavia<br>Luciano De cecco<br>Daniele Lavia<br>Petar Dirlic<br>Elia Bossi<br>Elia Bossi<br>Luciano De cecco<br>Uladzislau Davyskiba<br>Robbert Andringa<br>Robbert Andringa<br>Uladzislau Davyskiba<br>Robbert Andringa<br>Petar Dirlic<br>Manuele Marchiani<br>Petar Dirlic<br>Uladzislau Davyskiba<br>Elia Bossi<br>Uladzislau Davyskiba<br>Daniele Lavia<br>Daniele Lavia<br>Luciano De cecco<br>Elia Bossi<br>Robbert Andringa<br>Pietro Bonisoli<br>Pietro Bonisoli<br>Elia Bossi<br>Flavio Resende gualberto<br>Luciano De cecco<br>Uladzislau Davyskiba<br>Flavio Resende gualberto<br>Elia Bossi<br>Daniele Lavia<br>Daniele Lavia<br>Flavio Resende gualberto<br>Robbert Andringa<br>Elia Bossi<br>Pietro Bonisoli<br>Flavio Resende gualberto<br>Matteo Staforini<br>Daniele Lavia<br>Elia Bossi<br>Luciano De cecco<br>Flavio Resende gualberto<br>Manuele Marchiani<br>Pietro Bonisoli<br>Daniele Lavia<br>Flavio Resende gualberto<br>Daniele Lavia<br>Daniele Lavia<br>Robbert Andringa<br>Mathijs Desmet<br>Luciano De cecco<br>Pietro Bonisoli<br>Kyle Russell<br>Flavio Resende gualberto<br>Flavio Resende gualberto<br>Elia Bossi<br>Manuele Marchiani<br>Tatsunori Otsuka<br>Pietro Bonisoli<br>Kyle Russell<br>Uladzislau Davyskiba<br>Manuele Marchiani<br>Daniele Lavia<br>Elia Bossi<br>Elia Bossi<br>Luciano De cecco<br>Tobias Kjaer<br>Elia Bossi<br>Robbert Andringa<br>Daniele Lavia<br>Flavio Resende gualberto<br>Pietro Bonisoli<br>Uladzislau Davyskiba<br>Mathijs Desmet<br>Elia Bossi<br>Matteo Staforini<br>Uladzislau Davyskiba<br>Pietro Bonisoli<br>Uladzislau Davyskiba<br>Daniele Lavia<br>Manuele Marchiani<br>Flavio Resende gualberto<br>Robbert Andringa<br>Uladzislau Davyskiba<br>Robbert Andringa<br>Pietro Bonisoli<br>Flavio Resende gualberto<br>Mathijs Desmet<br>Mathijs Desmet<br>Matteo Staforini<br>Tatsunori Otsuka<br>Uladzislau Davyskiba<br>Uladzislau Davyskiba<br>Pietro Bonisoli<br>Manuele Marchiani<br>Pietro Bonisoli<br>Daniele Lavia<br>Elia Bossi<br>Kyle Russell<br>Matteo Staforini<br>Grzegorz Lomacz<br>Daniele Lavia<br>Pietro Bonisoli<br>Elia Bossi<br>Petar Dirlic<br>Flavio Resende gualberto<br>Kyle Russell<br>Flavio Resende gualberto<br>Flavio Resende gualberto<br>Elia Bossi<br>Luciano De cecco<br>Pietro Bonisoli<br>Daniele Lavia<br>Pietro Bonisoli<br>Uladzislau Davyskiba<br>Kyle Russell<br>Uladzislau Davyskiba<br>Uladzislau Davyskiba<br>Daniele Lavia<br>Grzegorz Lomacz<br>Elia Bossi<br>Elia Bossi<br>Pietro Bonisoli<br>Luciano De cecco<br>Mathijs Desmet<br>Pietro Bonisoli<br>Flavio Resende gualberto<br>Elia Bossi<br>Luciano De cecco<br>Mathijs Desmet<br>Manuele Marchiani<br>Pietro Bonisoli<br>Elia Bossi<br>Luciano De cecco<br>Matteo Staforini<br>Pietro Bonisoli<br>Mathijs Desmet<br>Robbert Andringa<br>Mathijs Desmet<br>Elia Bossi<br>Tatsunori Otsuka<br>Luciano De cecco<br>Flavio Resende gualberto<br>Mathijs Desmet<br>Petar Dirlic<br>Danilo De santis<br>Luciano De cecco<br>Pietro Bonisoli<br>Elia Bossi<br>Flavio Resende gualberto<br>Luciano De cecco<br>Luciano De cecco<br>Flavio Resende gualberto<br>Flavio Resende gualberto<br>Uladzislau Davyskiba<br>Pietro Bonisoli<br>Pietro Bonisoli<br>Matteo Staforini<br>Kyle Russell<br>Pietro Bonisoli<br>Daniele Lavia<br>Flavio Resende gualberto<br>Pietro Bonisoli<br>Luciano De cecco<br>Manuele Marchiani<br>Daniele Lavia<br>Elia Bossi<br>Elia Bossi<br>Luciano De cecco<br>Luciano De cecco<br>Luciano De cecco<br>Luciano De cecco<br>Luciano De cecco<br>Luciano De cecco<br>Luciano De cecco<br>Luciano De cecco<br>Luciano De cecco<br>Luciano De cecco<br>Luciano De cecco<br>Luciano De cecco<br>Luciano De cecco<br>Luciano De cecco<br>Luciano De cecco<br>Luciano De cecco<br>Mattia Labarile<br>Elia Bossi<br>Elia Bossi<br>Elia Bossi<br>Elia Bossi<br>Elia Bossi<br>Elia Bossi<br>Elia Bossi<br>Elia Bossi<br>Elia Bossi<br>Elia Bossi<br>Elia Bossi<br>Elia Bossi<br>Elia Bossi<br>Mathijs Desmet<br>Mathijs Desmet<br>Mathijs Desmet<br>Mathijs Desmet<br>Mathijs Desmet<br>Mathijs Desmet<br>Mathijs Desmet<br>Mathijs Desmet<br>Mathijs Desmet<br>Uladzislau Davyskiba<br>Uladzislau Davyskiba<br>Uladzislau Davyskiba<br>Uladzislau Davyskiba<br>Uladzislau Davyskiba<br>Uladzislau Davyskiba<br>Uladzislau Davyskiba<br>Uladzislau Davyskiba<br>Uladzislau Davyskiba<br>Daniele Lavia<br>Daniele Lavia<br>Daniele Lavia<br>Daniele Lavia<br>Daniele Lavia<br>Daniele Lavia<br>Daniele Lavia<br>Daniele Lavia<br>Pietro Bonisoli<br>Pietro Bonisoli<br>Pietro Bonisoli<br>Pietro Bonisoli<br>Pietro Bonisoli<br>Pietro Bonisoli<br>Luciano De cecco<br>Luciano De cecco<br>Luciano De cecco<br>Luciano De cecco<br>Luciano De cecco<br>Luciano De cecco<br>Luciano De cecco<br>Luciano De cecco<br>Matteo Staforini<br>Matteo Staforini<br>Matteo Staforini<br>Matteo Staforini<br>Matteo Staforini<br>Matteo Staforini<br>Flavio Resende gualberto<br>Flavio Resende gualberto<br>Flavio Resende gualberto<br>Flavio Resende gualberto<br>Flavio Resende gualberto<br>Eetu Maki<br>Flavio Resende gualberto<br>Tobias Kjaer<br>Furkan Gok<br>Alessandro Bonizzato<br>Pietro Bonisoli<br>Pietro Bonisoli<br>Daniele Lavia<br>Elia Bossi<br>Flavio Resende gualberto<br>Jacob Kern<br>Federico Pellegrini<br>Flavio Resende gualberto<br>Uladzislau Davyskiba<br>Mathijs Desmet<br>Daniele Lavia<br>Uladzislau Davyskiba<br>Ian Schein<br>Lorenz Karlitzek<br>Pawel Halaba<br>Grzegorz Lomacz<br>Jakub Ihnát<br>Matthew West<br>David Smith<br>David Smith<br>Daniele Lavia<br>Robbert Andringa<br>Kyle Russell<br>Uladzislau Davyskiba<br>Elia Bossi<br>Pietro Bonisoli<br>Daniele Lavia<br>Mattia Labarile<br>Flavio Resende gualberto<br>Uladzislau Davyskiba<br>Mattia Labarile<br>Bastien Scherer<br>Robbert Andringa<br>Daniele Lavia<br>Matteo Staforini<br>Daniele Lavia<br>Uladzislau Davyskiba<br>Kyle Russell<br>Luciano De cecco<br>Flavio Resende gualberto<br>Robbert Andringa<br>Uladzislau Davyskiba<br>Mikolaj Staporek<br>Daniele Lavia<br>Daniele Lavia<br>Pietro Bonisoli<br>Uladzislau Davyskiba<br>Flavio Resende gualberto<br>Uladzislau Davyskiba<br>Pietro Bonisoli<br>Robbert Andringa<br>Luciano De cecco<br>Uladzislau Davyskiba<br>Manuele Marchiani<br>Tatsunori Otsuka<br>Flavio Resende gualberto<br>Manuele Marchiani<br>Luciano De cecco<br>Pietro Bonisoli<br>Robbert Andringa<br>Uladzislau Davyskiba<br>Risto Ruohola<br>Risto Ruohola<br>Danilo De santis<br>Eduardo Brito<br>Eduardo Brito<br>Daniele Lavia<br>Elia Bossi<br>Mathijs Desmet<br>Kyle Russell<br>Pietro Bonisoli<br>Luciano De cecco<br>Elia Bossi<br>Pietro Bonisoli<br>Luciano De cecco<br>Elia Bossi<br>Metin Toy<br>Daniele Lavia"], ["<PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br>Kamil Semeniuk<br><PERSON>i<br><PERSON> <PERSON>rro<br>Paolo Porro<br><PERSON>rro<br>Kamil Semeniuk<br><PERSON> <PERSON><br><PERSON> Sal<PERSON><br><PERSON> <PERSON><br><PERSON> <PERSON><br>Kamil Se<PERSON>iuk<br><PERSON> Porro<br><PERSON> <PERSON><br>Kamil Se<PERSON>iuk<br><PERSON> <PERSON><br>Ka<PERSON>l Semeniuk<br><PERSON> <PERSON>i<br><PERSON> Salsi<br>Francesco Sani<br>Francesco Sani<br>Paolo Porro<br>Paolo Porro<br>Nicola Salsi<br>Paolo Porro<br>Paolo Porro<br>Kamil Semeniuk<br>Paolo Porro<br>Paolo Porro<br>Paolo Porro<br>Jordan Schnitzer<br>Nicola Salsi<br>Nicola Salsi<br>Nicola Salsi<br>Nicola Salsi<br>Nicola Salsi<br>Paolo Porro<br>Francesco Sani<br>Paolo Porro<br>Nicola Salsi<br>Kamil Semeniuk<br>Kamil Semeniuk<br>Paolo Porro<br>Kamil Semeniuk<br>Kamil Semeniuk<br>Paolo Porro<br>Kamil Semeniuk<br>Paolo Porro<br>Paolo Porro<br>Francesco Sani<br>Francesco Sani<br>Nicola Salsi<br>Paolo Porro<br>Kamil Semeniuk<br>Kamil Semeniuk<br>Kamil Semeniuk<br>Francesco Sani<br>Kamil Semeniuk<br>Kamil Semeniuk<br>Kamil Semeniuk<br>Kamil Semeniuk<br>Kamil Semeniuk<br>Kamil Semeniuk<br>Paolo Porro<br>Kamil Semeniuk<br>Paolo Porro<br>Paolo Porro<br>Kamil Semeniuk<br>Nicola Salsi<br>Paolo Porro<br>Paolo Porro<br>Kamil Semeniuk<br>Nicola Salsi<br>Kamil Semeniuk<br>Leonardo Sandu<br>Kamil Semeniuk<br>Kamil Semeniuk<br>Kamil Semeniuk<br>Paolo Porro<br>Nicola Salsi<br>Nicola Salsi<br>Nicola Salsi<br>Kamil Semeniuk<br>Kamil Semeniuk<br>Kamil Semeniuk<br>Kamil Semeniuk<br>Paolo Porro<br>Kamil Semeniuk<br>Kamil Semeniuk<br>Nicola Salsi<br>Kamil Semeniuk<br>Nicola Salsi<br>Kamil Semeniuk<br>Simon Kohn<br>Kamil Semeniuk<br>Nicola Salsi<br>Paolo Porro<br>Kamil Semeniuk<br>Nicola Salsi<br>Kamil Semeniuk<br>Paolo Porro<br>Kamil Semeniuk<br>Francesco Sani<br>Paolo Porro<br>Paolo Porro<br>Paolo Porro<br>Kamil Semeniuk<br>Paolo Porro<br>Francesco Sani<br>Paolo Porro<br>Francesco Sani<br>Kamil Semeniuk<br>Paolo Porro<br>Paolo Porro<br>Kamil Semeniuk<br>Paolo Porro<br>Paolo Porro<br>Paolo Porro<br>Jordan Schnitzer<br>Kamil Semeniuk<br>Francesco Sani<br>Paolo Porro<br>Kamil Semeniuk<br>Kamil Semeniuk<br>Kamil Semeniuk<br>Paolo Porro<br>Nicola Salsi<br>Francesco Sani<br>Francesco Sani<br>Paolo Porro<br>Nicola Salsi<br>Francesco Sani<br>Paolo Porro<br>Nicola Salsi<br>Paolo Porro<br>Francesco Sani<br>Paolo Porro<br>Paolo Porro<br>Pablo sergio Koukartsev<br>Nicola Salsi<br>Francesco Sani<br>Kamil Semeniuk<br>Paolo Porro<br>Paolo Porro<br>Nicola Salsi<br>Nicola Salsi<br>Paolo Porro<br>Kamil Semeniuk<br>Pablo sergio Koukartsev<br>Paolo Porro<br>Kamil Semeniuk<br>Francesco Sani<br>Paolo Porro<br>Nicola Salsi<br>Paolo Porro<br>Paolo Porro<br>Nicola Salsi<br>Paolo Porro<br>Nicola Salsi<br>Nicola Salsi<br>Kamil Semeniuk<br>Kamil Semeniuk<br>Kamil Semeniuk<br>Kamil Semeniuk<br>Nicola Salsi<br>Aaro Pihlajamaki<br>Aaro Pihlajamaki<br>Kamil Semeniuk<br>Nicola Salsi<br>Nicola Salsi<br>Nicola Salsi<br>Nicola Salsi<br>Nicola Salsi<br>Nicola Salsi<br>Nicola Salsi<br>Nicola Salsi<br>Nicola Salsi<br>Nicola Salsi<br>Nicola Salsi<br>Nicola Salsi<br>Nicola Salsi<br>Paolo Porro<br>Paolo Porro<br>Paolo Porro<br>Paolo Porro<br>Paolo Porro<br>Paolo Porro<br>Paolo Porro<br>Kamil Semeniuk<br>Kamil Semeniuk<br>Kamil Semeniuk<br>Kamil Semeniuk<br>Kamil Semeniuk<br>Alessandro Pisoni<br>Simon Kohn<br>Jordan Schnitzer<br>Paolo Porro<br>Amel Dautefendic<br>Kamil Semeniuk<br>Nicola Salsi<br>Paolo Porro<br>Jordan Schnitzer<br>Kamil Semeniuk<br>Pawel Filipowicz<br>Jedrzej Kazmierczak<br>Piotr Hain<br>Pablo sergio Koukartsev<br>Francesco Sani<br>Nicola Salsi<br>Kamil Semeniuk<br>Pablo sergio Koukartsev<br>Kamil Semeniuk<br>Paolo Porro<br>Francesco Sani<br>Heydari Mostafa<br>Paolo Porro<br>Kamil Semeniuk<br>Paolo Porro<br>Kamil Semeniuk<br>Leonardo Sandu<br>Andrea Romiti<br>Paolo Porro<br>Francesco Sani<br>Kalembka Tomasz<br>Flavio antonio santos Soares<br>Flavio antonio santos Soares<br>Francesco Sani<br>Nicola Salsi<br>Dilmenler Berk"], ["<PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON> samu<PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON> An<PERSON>i<br><PERSON> B<PERSON>gger<br><PERSON><br><PERSON> Po<PERSON><br><PERSON> An<PERSON>i<br><PERSON><br><PERSON><br><PERSON> B<PERSON>gger<br><PERSON> samuele maria <PERSON><br><PERSON><br><PERSON> <PERSON><PERSON><br><PERSON>h Plot<PERSON>i<br><PERSON> An<PERSON>i<br>Michele Baranowicz<br>Luca Porro<br>Luca samuele maria Colombo<br>Federico Bonacchi<br>Yuri Romanò<br>Michele Baranowicz<br>Oleh Plotnytskyi<br>Oleh Plotnytskyi<br>Matic Videcnik<br>Yuri Romanò<br>Matteo Picchio<br>Jan Zimmermann<br>Oleh Plotnytskyi<br>Paul Buchegger<br>Luca Porro<br>Yuri Romanò<br>Oleh Plotnytskyi<br>Luca Porro<br>Oleh Plotnytskyi<br>Michele Baranowicz<br>Simone Anzani<br>Simone Anzani<br>Yacine Louati<br>Matteo Picchio<br>Michele Baranowicz<br>Oleh Plotnytskyi<br>Simone Anzani<br>Luca samuele maria Colombo<br>Matteo Picchio<br>Federico Bonacchi<br>Luca Porro<br>Luca samuele maria Colombo<br>Matteo Picchio<br>Michele Baranowicz<br>Simone Anzani<br>Luca samuele maria Colombo<br>Michele Baranowicz<br>Luca samuele maria Colombo<br>Yacine Louati<br>Oleh Plotnytskyi<br>Matteo Picchio<br>Simone Anzani<br>Yuri Romanò<br>Yuri Romanò<br>Jan Zimmermann<br>Oleh Plotnytskyi<br>Simone Anzani<br>Yuri Romanò<br>Filippo Federici<br>Luca Porro<br>Oleh Plotnytskyi<br>Jan Zimmermann<br>Yacine Louati<br>Yuri Romanò<br>Simone Anzani<br>Luca Porro<br>Simone Anzani<br>Yuri Romanò<br>Luca Porro<br>Colton mark Cowell<br>Matic Videcnik<br>Luca samuele maria Colombo<br>Michele Baranowicz<br>Paul Buchegger<br>Yuri Romanò<br>Yuri Romanò<br>Jan Zimmermann<br>Michele Baranowicz<br>Simone Anzani<br>Simone Anzani<br>Caner Dengin<br>Yacine Louati<br>Oleh Plotnytskyi<br>Simone Anzani<br>Oleh Plotnytskyi<br>Simone Anzani<br>Yuri Romanò<br>Michele Baranowicz<br>Luca Porro<br>Yacine Louati<br>Luca samuele maria Colombo<br>Federico Bonacchi<br>Jan Zimmermann<br>Simone Anzani<br>Yuri Romanò<br>Michele Baranowicz<br>Luca samuele maria Colombo<br>Yuri Romanò<br>Yacine Louati<br>Yuri Romanò<br>Oleh Plotnytskyi<br>Yuri Romanò<br>Yuri Romanò<br>Luca samuele maria Colombo<br>Luca Porro<br>Federico Bonacchi<br>Oleh Plotnytskyi<br>Luca samuele maria Colombo<br>Matteo Picchio<br>Yuri Romanò<br>Matteo Picchio<br>Luca Porro<br>Luca samuele maria Colombo<br>Simone Anzani<br>Filippo Federici<br>Caner Dengin<br>Luca samuele maria Colombo<br>Yuri Romanò<br>Luca samuele maria Colombo<br>Oleh Plotnytskyi<br>Caner Dengin<br>Paul Buchegger<br>Luca samuele maria Colombo<br>Simone Anzani<br>Oleh Plotnytskyi<br>Paul Buchegger<br>Oleh Plotnytskyi<br>Yuri Romanò<br>Oleh Plotnytskyi<br>Yuri Romanò<br>Marco Vitelli<br>Luca samuele maria Colombo<br>Luca samuele maria Colombo<br>Yacine Louati<br>Federico Bonacchi<br>Oleh Plotnytskyi<br>Paul Buchegger<br>Luca samuele maria Colombo<br>Matic Videcnik<br>Oleh Plotnytskyi<br>Yuri Romanò<br>Federico Bonacchi<br>Oleh Plotnytskyi<br>Michele Baranowicz<br>Oleh Plotnytskyi<br>Simone Anzani<br>Yuri Romanò<br>Yuri Romanò<br>Federico Bonacchi<br>Michele Baranowicz<br>Yuri Romanò<br>Luca samuele maria Colombo<br>Oleh Plotnytskyi<br>Oleh Plotnytskyi<br>Luca samuele maria Colombo<br>Oleh Plotnytskyi<br>Rune Fasteland<br>Oleh Plotnytskyi<br>Marco Vitelli<br>Michele Baranowicz<br>Oleh Plotnytskyi<br>Filippo Federici<br>Simone Anzani<br>Federico Bonacchi<br>Yacine Louati<br>Simone Anzani<br>Luca samuele maria Colombo<br>Michele Baranowicz<br>Marco Vitelli<br>Filippo Federici<br>Federico Bonacchi<br>Yuri Romanò<br>Oleh Plotnytskyi<br>Simone Anzani<br>Luca samuele maria Colombo<br>Luca samuele maria Colombo<br>Luca Porro<br>Michele Baranowicz<br>Marco Vitelli<br>Michele Baranowicz<br>Luca samuele maria Colombo<br>Caner Dengin<br>Paul Buchegger<br>Oleh Plotnytskyi<br>Simone Anzani<br>Luca samuele maria Colombo<br>Yuri Romanò<br>Luca Porro<br>Oleh Plotnytskyi<br>Luca Porro<br>Michele Baranowicz<br>Luca Porro<br>Yuri Romanò<br>Paul Buchegger<br>Simone Anzani<br>Oleh Plotnytskyi<br>Yuri Romanò<br>Simone Anzani<br>Oleh Plotnytskyi<br>Federico Bonacchi<br>Jan Zimmermann<br>Simone Anzani<br>Matteo Picchio<br>Simone Anzani<br>Yuri Romanò<br>Oleh Plotnytskyi<br>Luca samuele maria Colombo<br>Oleh Plotnytskyi<br>Michele Baranowicz<br>Matteo Picchio<br>Luca samuele maria Colombo<br>Simone Anzani<br>Michele Baranowicz<br>Oleh Plotnytskyi<br>Simone Anzani<br>Luca samuele maria Colombo<br>Michele Baranowicz<br>Yuri Romanò<br>Yuri Romanò<br>Marco Vitelli<br>Luca samuele maria Colombo<br>Oleh Plotnytskyi<br>Luca samuele maria Colombo<br>Matic Videcnik<br>Yuri Romanò<br>Yuri Romanò<br>Oleh Plotnytskyi<br>Luca Porro<br>Oleh Plotnytskyi<br>Marco Vitelli<br>Yuri Romanò<br>Yacine Louati<br>Michele Baranowicz<br>Joel Laakso<br>Yuri Romanò<br>Yuri Romanò<br>Yuri Romanò<br>Michele Baranowicz<br>Michele Baranowicz<br>Colton mark Cowell<br>Yuri Romanò<br>Jan Zimmermann<br>Luca samuele maria Colombo<br>Luca samuele maria Colombo<br>Luca Porro<br>Marco Vitelli<br>Matteo Picchio<br>Yuri Romanò<br>Michele Baranowicz<br>Yuri Romanò<br>Matteo Picchio<br>Oleh Plotnytskyi<br>Oleh Plotnytskyi<br>Filippo Federici<br>Yacine Louati<br>Simone Anzani<br>Marco Vitelli<br>Jan Zimmermann<br>Michele Baranowicz<br>Luca samuele maria Colombo<br>Marco Vitelli<br>Oleh Plotnytskyi<br>Yuri Romanò<br>Luca samuele maria Colombo<br>Yuri Romanò<br>Oleh Plotnytskyi<br>Luca samuele maria Colombo<br>Michele Baranowicz<br>Rune Fasteland<br>Colton mark Cowell<br>Yuri Romanò<br>Michele Baranowicz<br>Luca Porro<br>Federico Bonacchi<br>Yuri Romanò<br>Matteo Picchio<br>Paul Buchegger<br>Luca Porro<br>Oleh Plotnytskyi<br>Oleh Plotnytskyi<br>Michele Baranowicz<br>Yuri Romanò<br>Yuri Romanò<br>Luca samuele maria Colombo<br>Luca Porro<br>Yuri Romanò<br>Luca Porro<br>Luca Porro<br>Oleh Plotnytskyi<br>Michele Baranowicz<br>Simone Anzani<br>Jan Zimmermann<br>Luca samuele maria Colombo<br>Luca Porro<br>Oleh Plotnytskyi<br>Luca Porro<br>Federico Bonacchi<br>Simone Anzani<br>Yuri Romanò<br>Luca samuele maria Colombo<br>Matteo Picchio<br>Simone Anzani<br>Jan Zimmermann<br>Yuri Romanò<br>Yuri Romanò<br>Yuri Romanò<br>Simone Anzani<br>Oleh Plotnytskyi<br>Luca samuele maria Colombo<br>Luca Porro<br>Matteo Picchio<br>Oleh Plotnytskyi<br>Luca Porro<br>Jan Zimmermann<br>Michele Baranowicz<br>Jan Zimmermann<br>Marco Vitelli<br>Paul Buchegger<br>Simone Anzani<br>Luca Porro<br>Luca Porro<br>Michele Baranowicz<br>Oleh Plotnytskyi<br>Filippo Federici<br>Luca samuele maria Colombo<br>Simone Anzani<br>Michele Baranowicz<br>Oleh Plotnytskyi<br>Marco Vitelli<br>Simone Anzani<br>Miguel angel Martinez palacios<br>Simone Anzani<br>Yuri Romanò<br>Simone Anzani<br>Yuri Romanò<br>Michele Baranowicz<br>Jan Zimmermann<br>Oleh Plotnytskyi<br>Yuri Romanò<br>Joel Laakso<br>Yuri Romanò<br>Yuri Romanò<br>Oleh Plotnytskyi<br>Filippo Federici<br>Yuri Romanò<br>Luca Porro<br>Marco Vitelli<br>Filippo Federici<br>Oleh Plotnytskyi<br>Yuri Romanò<br>Matteo Picchio<br>Marco Vitelli<br>Federico Bonacchi<br>Luca samuele maria Colombo<br>Michele Baranowicz<br>Paul Buchegger<br>Simone Anzani<br>Oleh Plotnytskyi<br>Jan Zimmermann<br>Jan Zimmermann<br>Luca Porro<br>Jan Zimmermann<br>Luca Porro<br>Michele Baranowicz<br>Paul Buchegger<br>Yacine Louati<br>Yuri Romanò<br>Michele Baranowicz<br>Michele Baranowicz<br>Luca Porro<br>Oleh Plotnytskyi<br>Michele Baranowicz<br>Yuri Romanò<br>Jan Zimmermann<br>Luca Porro<br>Colton mark Cowell<br>Simone Anzani<br>Yacine Louati<br>Marco Vitelli<br>Oleh Plotnytskyi<br>Jan Zimmermann<br>Oleh Plotnytskyi<br>Yuri Romanò<br>Yuri Romanò<br>Oleh Plotnytskyi<br>Simone Anzani<br>Oleh Plotnytskyi<br>Michele Baranowicz<br>Michele Baranowicz<br>Michele Baranowicz<br>Matteo Picchio<br>Paul Buchegger<br>Michele Baranowicz<br>Marco Vitelli<br>Yuri Romanò<br>Filippo Federici<br>Luca Porro<br>Luca samuele maria Colombo<br>Colton mark Cowell<br>Jan Zimmermann<br>Oleh Plotnytskyi<br>Oleh Plotnytskyi<br>Filippo Federici<br>Yacine Louati<br>Michele Baranowicz<br>Yacine Louati<br>Michele Baranowicz<br>Jan Zimmermann<br>Yuri Romanò<br>Luca samuele maria Colombo<br>Simone Anzani<br>Oleh Plotnytskyi<br>Simone Anzani<br>Luca Porro<br>Colton mark Cowell<br>Oleh Plotnytskyi<br>Yuri Romanò<br>Simone Anzani<br>Jan Zimmermann<br>Simone Anzani<br>Oleh Plotnytskyi<br>Luca samuele maria Colombo<br>Filippo Federici<br>Matteo Picchio<br>Yuri Romanò<br>Simone Anzani<br>Yuri Romanò<br>Simone Anzani<br>Yuri Romanò<br>Yacine Louati<br>Federico Bonacchi<br>Filippo Federici<br>Paul Buchegger<br>Jan Zimmermann<br>Jan Zimmermann<br>Jan Zimmermann<br>Jan Zimmermann<br>Jan Zimmermann<br>Jan Zimmermann<br>Jan Zimmermann<br>Jan Zimmermann<br>Jan Zimmermann<br>Jan Zimmermann<br>Jan Zimmermann<br>Jan Zimmermann<br>Jan Zimmermann<br>Jan Zimmermann<br>Jan Zimmermann<br>Jan Zimmermann<br>Simone Anzani<br>Simone Anzani<br>Simone Anzani<br>Simone Anzani<br>Simone Anzani<br>Simone Anzani<br>Simone Anzani<br>Simone Anzani<br>Simone Anzani<br>Simone Anzani<br>Simone Anzani<br>Simone Anzani<br>Simone Anzani<br>Simone Anzani<br>Luca samuele maria Colombo<br>Luca samuele maria Colombo<br>Luca samuele maria Colombo<br>Luca samuele maria Colombo<br>Luca samuele maria Colombo<br>Luca samuele maria Colombo<br>Luca samuele maria Colombo<br>Luca samuele maria Colombo<br>Yuri Romanò<br>Yuri Romanò<br>Yuri Romanò<br>Yuri Romanò<br>Yuri Romanò<br>Yuri Romanò<br>Simone Anzani<br>Simone Anzani<br>Simone Anzani<br>Simone Anzani<br>Simone Anzani<br>Simone Anzani<br>Simone Anzani<br>Simone Anzani<br>Michele Baranowicz<br>Michele Baranowicz<br>Michele Baranowicz<br>Michele Baranowicz<br>Michele Baranowicz<br>Michele Baranowicz<br>Oleh Plotnytskyi<br>Oleh Plotnytskyi<br>Oleh Plotnytskyi<br>Oleh Plotnytskyi<br>Oleh Plotnytskyi<br>Joel Laakso<br>Joel Laakso<br>Tiago Violas<br>Tiago Violas<br>Jan Zimmermann<br>Jan Zimmermann<br>Matic Videcnik<br>Onur Altinkaya<br>Miguel angel Martinez palacios<br>Michele Baranowicz<br>Luca samuele maria Colombo<br>Yuri Romanò<br>Caner Dengin<br>Muhammet Kaya<br>Oleh Plotnytskyi<br>Luca samuele maria Colombo<br>Caner Dengin<br>Colton mark Cowell<br>Niels De vries<br>Yuri Romanò<br>Simone Anzani<br>Joel Laakso<br>Yuri Romanò<br>Oleh Plotnytskyi<br>Jan Zimmermann<br>Jan Zimmermann<br>Caner Dengin<br>Marek Sotola<br>Leonard Graven<br>Martin Duspiva<br>Seganov Georgi<br>Bartosz Firszt<br>Rune Fasteland<br>Oleh Plotnytskyi<br>Luca samuele maria Colombo<br>Paul Buchegger<br>Luca Porro<br>Filippo Federici<br>Rune Fasteland<br>Oleh Plotnytskyi<br>Federico Bonacchi<br>Zeljko Coric<br>Yuri Romanò<br>Federico Bonacchi<br>Paul Buchegger<br>Michele Baranowicz<br>Oleh Plotnytskyi<br>Luca samuele maria Colombo<br>Yuri Romanò<br>Marek Sotola<br>Filippo Federici<br>Simone Anzani<br>Luca samuele maria Colombo<br>Oleh Plotnytskyi<br>Yuri Romanò<br>Simone Anzani<br>Yuri Romanò<br>Filippo Federici<br>Simone Anzani<br>Filippo Federici<br>Matteo Picchio<br>Jan Zimmermann<br>Yacine Louati<br>Oleh Plotnytskyi<br>Luca samuele maria Colombo<br>Filippo Federici<br>Michele Baranowicz<br>Jan Zimmermann<br>Yuri Romanò<br>Simone Anzani<br>Marco Vitelli<br>Jan Zimmermann<br>Simone Anzani<br>Simone Anzani<br>Yacine Louati<br>Luca samuele maria Colombo<br>Jan Zimmermann<br>Marco Vitelli<br>Michele Baranowicz<br>Yuri Romanò<br>Filippo Federici<br>Burak Cevik<br>Luca Porro<br>Federico Bonacchi<br>Paul Buchegger<br>Simone Anzani<br>Luca samuele maria Colombo<br>Simone Anzani<br>Matteo Picchio<br>Simone Anzani"], ["<PERSON><br><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON> au<PERSON><PERSON><br><PERSON><PERSON><PERSON> au<PERSON><PERSON><br><PERSON><PERSON>rt<PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON> au<PERSON><br><PERSON><PERSON><PERSON> au<PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON> au<PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON> au<PERSON><PERSON><br><PERSON><PERSON><PERSON> au<PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON> Ma<PERSON><br><PERSON><PERSON> Ma<PERSON><br><PERSON>ej <PERSON>k<br><PERSON><PERSON><br><PERSON><PERSON>lfi<br><PERSON>e Ma<PERSON>ne<br><PERSON> P<PERSON>i<br><PERSON>o <PERSON>sca<PERSON><br><PERSON>ndro ausibio <PERSON>sca<br>Ah<PERSON> <PERSON>tas<br><PERSON>nd<PERSON> ausibio Mosca<br><PERSON> Truocchio<br><PERSON>e <PERSON> mart<PERSON><br><PERSON> Truocchio<br><PERSON> Sigh<PERSON>lfi<br>Daniele Mazzone<br>Nicholas Sighinolfi<br>Marko Podrascanin<br>Marko Podrascanin<br>Edoardo Caneschi<br>Gabriele Di martino<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Leandro ausibio Mosca<br>Gabriele Di martino<br>Daniele Mazzone<br>Marko Podrascanin<br>Andrea Truocchio<br>Daniele Mazzone<br>Marko Podrascanin<br>Marko Podrascanin<br>Francesco Cottarelli<br>Marko Podrascanin<br>Luigi Randazzo<br>Daniele Mazzone<br>Marko Podrascanin<br>Gabriele Di martino<br>Gabriele Di martino<br>Andrea Truocchio<br>Leandro ausibio Mosca<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Nicholas Sighinolfi<br>Daniele Mazzone<br>Andrea Truocchio<br>Marko Podrascanin<br>Nicola Pesaresi<br>Daniele Mazzone<br>Marko Podrascanin<br>Matej Kok<br>Nicholas Sighinolfi<br>Gabriele Di martino<br>Daniele Mazzone<br>Gabriele Di martino<br>Leandro ausibio Mosca<br>Luigi Randazzo<br>Gabriele Di martino<br>Gabriele Di martino<br>Leandro ausibio Mosca<br>Andrea Truocchio<br>Luigi Randazzo<br>Luigi Randazzo<br>Daniele Mazzone<br>Matteo Lelli<br>Andrea Truocchio<br>Nicholas Sighinolfi<br>Nicola Pesaresi<br>Gabriele Di martino<br>Marko Podrascanin<br>Marko Podrascanin<br>Daniele Mazzone<br>Andrea Truocchio<br>Gabriele Di martino<br>Gabriele Di martino<br>Edoardo Caneschi<br>Ahmet Karatas<br>Luigi Randazzo<br>Edoardo Caneschi<br>Gabriele Di martino<br>Francesco Cottarelli<br>Matteo Lelli<br>Luigi Randazzo<br>Edoardo Caneschi<br>Marko Podrascanin<br>Edoardo Caneschi<br>Gabriele Di martino<br>Edoardo Caneschi<br>Marko Podrascanin<br>Marko Podrascanin<br>Andrea Truocchio<br>Nicola Pesaresi<br>Edoardo Caneschi<br>Gabriele Di martino<br>Nicola Pesaresi<br>Marko Podrascanin<br>Nicola Pesaresi<br>Francesco Cottarelli<br>Nicola Pesaresi<br>Gabriele Di martino<br>Marko Podrascanin<br>Daniele Mazzone<br>Nicola Pesaresi<br>Nicholas Sighinolfi<br>Marko Podrascanin<br>Daniele Mazzone<br>Andrea Truocchio<br>Luigi Randazzo<br>Andrea Truocchio<br>Ahmet Karatas<br>Nicholas Sighinolfi<br>Matteo Lelli<br>Daniele Mazzone<br>Edoardo Caneschi<br>Marko Podrascanin<br>Edoardo Caneschi<br>Gabriele Di martino<br>Matteo Lelli<br>Gabriele Di martino<br>Robert Milczarek<br>Daniele Mazzone<br>Nicola Pesaresi<br>Marko Podrascanin<br>Leandro ausibio Mosca<br>Gabriele Di martino<br>Nicola Pesaresi<br>Edoardo Caneschi<br>Marko Podrascanin<br>Andrea Truocchio<br>Haci Sahin<br>Marko Podrascanin<br>Edoardo Caneschi<br>Matteo Lelli<br>Edoardo Caneschi<br>Marko Podrascanin<br>Daniele Mazzone<br>Marko Podrascanin<br>Gabriele Di martino<br>Gabriele Di martino<br>Marko Podrascanin<br>Marko Podrascanin<br>Leandro ausibio Mosca<br>Marko Podrascanin<br>Luigi Randazzo<br>Edoardo Caneschi<br>Nicholas Sighinolfi<br>Leandro ausibio Mosca<br>Leandro ausibio Mosca<br>Daniele Mazzone<br>Marko Podrascanin<br>Marko Podrascanin<br>Leandro ausibio Mosca<br>Andrea Truocchio<br>Daniele Mazzone<br>Edoardo Caneschi<br>Leandro ausibio Mosca<br>Leandro ausibio Mosca<br>Gabriele Di martino<br>Gabriele Di martino<br>Edoardo Caneschi<br>Gabriele Di martino<br>Francesco Cottarelli<br>Daniele Mazzone<br>Gabriele Di martino<br>Andrea Truocchio<br>Daniele Mazzone<br>Daniele Mazzone<br>Francesco Cottarelli<br>Gabriele Di martino<br>Daniele Mazzone<br>Leandro ausibio Mosca<br>Edoardo Caneschi<br>Luigi Randazzo<br>Nicholas Sighinolfi<br>Martijn Colson<br>Matej Kok<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Marko Podrascanin<br>Nicola Pesaresi<br>Leandro ausibio Mosca<br>Francesco Cottarelli<br>Francesco Cottarelli<br>Edoardo Caneschi<br>Daniele Mazzone<br>Andrea Truocchio<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Nicholas Sighinolfi<br>Nicola Pesaresi<br>Edoardo Caneschi<br>Leandro ausibio Mosca<br>Nicola Pesaresi<br>Edoardo Caneschi<br>Leandro ausibio Mosca<br>Robert Milczarek<br>Matteo Lelli<br>Edoardo Caneschi<br>Marko Podrascanin<br>Andrea Truocchio<br>Leandro ausibio Mosca<br>Francesco Cottarelli<br>Marko Podrascanin<br>Leandro ausibio Mosca<br>Andrea Truocchio<br>Gabriele Di martino<br>Gabriele Di martino<br>Edoardo Caneschi<br>Francesco Cottarelli<br>Robert Milczarek<br>Nicola Pesaresi<br>Matteo Lelli<br>Andrea Truocchio<br>Francesco Cottarelli<br>Francesco Cottarelli<br>Daniele Mazzone<br>Edoardo Caneschi<br>Leandro ausibio Mosca<br>Daniele Mazzone<br>Marko Podrascanin<br>Francesco Cottarelli<br>Marko Podrascanin<br>Leandro ausibio Mosca<br>Marko Podrascanin<br>Leandro ausibio Mosca<br>Andrea Truocchio<br>Edoardo Caneschi<br>Daniele Mazzone<br>Daniele Mazzone<br>Gabriele Di martino<br>Robert Milczarek<br>Daniele Mazzone<br>Nicola Pesaresi<br>Nicholas Sighinolfi<br>Luigi Randazzo<br>Luigi Randazzo<br>Marko Podrascanin<br>Edoardo Caneschi<br>Marko Podrascanin<br>Andrea Truocchio<br>Matteo Lelli<br>Marko Podrascanin<br>Gabriele Di martino<br>Edoardo Caneschi<br>Andrea Truocchio<br>Daniele Mazzone<br>Gabriele Di martino<br>Nicholas Sighinolfi<br>Nicola Pesaresi<br>Andrea Truocchio<br>Edoardo Caneschi<br>Marko Podrascanin<br>Edoardo Caneschi<br>Gabriele Di martino<br>Edoardo Caneschi<br>Leandro ausibio Mosca<br>Leandro ausibio Mosca<br>Leandro ausibio Mosca<br>Gabriele Di martino<br>Daniele Mazzone<br>Francesco Cottarelli<br>Nicola Pesaresi<br>Gabriele Di martino<br>Leandro ausibio Mosca<br>Marko Podrascanin<br>Luigi Randazzo<br>Gabriele Di martino<br>Edoardo Caneschi<br>Nicholas Sighinolfi<br>Marko Podrascanin<br>Gabriele Di martino<br>Andrea Truocchio<br>Edoardo Caneschi<br>Matteo Lelli<br>Leandro ausibio Mosca<br>Nicholas Sighinolfi<br>Edoardo Caneschi<br>Andrea Truocchio<br>Gabriele Di martino<br>Marko Podrascanin<br>Gabriele Di martino<br>Daniele Mazzone<br>Nicholas Sighinolfi<br>Edoardo Caneschi<br>Daniele Mazzone<br>Matteo Lelli<br>Robert Milczarek<br>Marko Podrascanin<br>Edoardo Caneschi<br>Gabriele Di martino<br>Nicholas Sighinolfi<br>Gabriele Di martino<br>Gabriele Di martino<br>Andrea Truocchio<br>Matteo Lelli<br>Daniele Mazzone<br>Marko Podrascanin<br>Marko Podrascanin<br>Daniele Mazzone<br>Nicholas Sighinolfi<br>Andrea Truocchio<br>Marko Podrascanin<br>Edoardo Caneschi<br>Daniele Mazzone<br>Gabriele Di martino<br>Gabriele Di martino<br>Andrea Truocchio<br>Luigi Randazzo<br>Edoardo Caneschi<br>Matteo Lelli<br>Leandro ausibio Mosca<br>Matej Kok<br>Edoardo Caneschi<br>Nicola Pesaresi<br>Ahmet Karatas<br>Haci Sahin<br>Leandro ausibio Mosca<br>Leandro ausibio Mosca<br>Marko Podrascanin<br>Daniele Mazzone<br>Marko Podrascanin<br>Francesco Cottarelli<br>Nicholas Sighinolfi<br>Andrea Truocchio<br>Leandro ausibio Mosca<br>Gabriele Di martino<br>Nicholas Sighinolfi<br>Nicholas Sighinolfi<br>Gabriele Di martino<br>Matteo Lelli<br>Andrea Truocchio<br>Marko Podrascanin<br>Gabriele Di martino<br>Nicola Pesaresi<br>Gabriele Di martino<br>Nicholas Sighinolfi<br>Gabriele Di martino<br>Leandro ausibio Mosca<br>Marko Podrascanin<br>Marko Podrascanin<br>Daniele Mazzone<br>Robert Milczarek<br>Gabriele Di martino<br>Daniele Mazzone<br>Leandro ausibio Mosca<br>Edoardo Caneschi<br>Robert Milczarek<br>Marko Podrascanin<br>Leandro ausibio Mosca<br>Leandro ausibio Mosca<br>Leandro ausibio Mosca<br>Matteo Lelli<br>Gabriele Di martino<br>Matteo Lelli<br>Andrea Truocchio<br>Nicola Pesaresi<br>Gabriele Di martino<br>Daniele Mazzone<br>Leandro ausibio Mosca<br>Gabriele Di martino<br>Nicholas Sighinolfi<br>Andrea Truocchio<br>Edoardo Caneschi<br>Daniele Mazzone<br>Nicola Pesaresi<br>Daniele Mazzone<br>Matteo Lelli<br>Edoardo Caneschi<br>Andrea Truocchio<br>Marko Podrascanin<br>Leandro ausibio Mosca<br>Daniele Mazzone<br>Gabriele Di martino<br>Leandro ausibio Mosca<br>Daniele Mazzone<br>Leandro ausibio Mosca<br>Gabriele Di martino<br>Luigi Randazzo<br>Edoardo Caneschi<br>Marko Podrascanin<br>Gabriele Di martino<br>Daniele Mazzone<br>Leandro ausibio Mosca<br>Daniele Mazzone<br>Leandro ausibio Mosca<br>Nicholas Sighinolfi<br>Daniele Mazzone<br>Luigi Randazzo<br>Daniele Mazzone<br>Gabriele Di martino<br>Matteo Lelli<br>Marko Podrascanin<br>Gabriele Di martino<br>Marko Podrascanin<br>Daniele Mazzone<br>Leandro ausibio Mosca<br>Luigi Randazzo<br>Nicholas Sighinolfi<br>Gabriele Di martino<br>Marko Podrascanin<br>Andrea Truocchio<br>Leandro ausibio Mosca<br>Marko Podrascanin<br>Gabriele Di martino<br>Nicola Pesaresi<br>Edoardo Caneschi<br>Matteo Lelli<br>Marko Podrascanin<br>Robert Milczarek<br>Marko Podrascanin<br>Nicholas Sighinolfi<br>Marko Podrascanin<br>Andrea Truocchio<br>Daniele Mazzone<br>Edoardo Caneschi<br>Marko Podrascanin<br>Matteo Lelli<br>Edoardo Caneschi<br>Marko Podrascanin<br>Francesco Cottarelli<br>Daniele Mazzone<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Stefano Dell'osso<br>Matteo Lelli<br>Matteo Lelli<br>Matteo Lelli<br>Matteo Lelli<br>Matteo Lelli<br>Matteo Lelli<br>Matteo Lelli<br>Matteo Lelli<br>Gabriele Di martino<br>Gabriele Di martino<br>Gabriele Di martino<br>Gabriele Di martino<br>Gabriele Di martino<br>Gabriele Di martino<br>Gabriele Di martino<br>Gabriele Di martino<br>Gabriele Di martino<br>Marko Podrascanin<br>Marko Podrascanin<br>Marko Podrascanin<br>Marko Podrascanin<br>Marko Podrascanin<br>Marko Podrascanin<br>Marko Podrascanin<br>Marko Podrascanin<br>Nicola Pesaresi<br>Nicola Pesaresi<br>Nicola Pesaresi<br>Nicola Pesaresi<br>Nicola Pesaresi<br>Nicola Pesaresi<br>Nicola Pesaresi<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Leandro ausibio Mosca<br>Leandro ausibio Mosca<br>Francesco Cottarelli<br>Francesco Cottarelli<br>Francesco Cottarelli<br>Francesco Cottarelli<br>Francesco Cottarelli<br>Francesco Cottarelli<br>Daniele Mazzone<br>Daniele Mazzone<br>Daniele Mazzone<br>Daniele Mazzone<br>Daniele Mazzone<br>Andrè ryuma oto Aleixo<br>Andrè ryuma oto Aleixo<br>Matej Kok<br>Martijn Colson<br>Dmitrii Bahov<br>Iereshchenko Ian<br>Edoardo Caneschi<br>Andre Brown<br>Marko Podrascanin<br>Nicola Pesaresi<br>Leandro ausibio Mosca<br>Edoardo Caneschi<br>Ahmet Karatas<br>Leandro ausibio Mosca<br>Marko Podrascanin<br>Ahmet Karatas<br>Edoardo Caneschi<br>Matteo Lelli<br>Daniele Mazzone<br>Marko Podrascanin<br>Nicola Pesaresi<br>Ahmet Karatas<br>Matheus Krauchuk<br>Maciej Borris<br>Noah Baxpohler<br>Robert Milczarek<br>Ilia Kovalov<br>Maciej Nowowsiak<br>Gabriele Di martino<br>Marko Podrascanin<br>Nicola Pesaresi<br>Edoardo Caneschi<br>Matteo Lelli<br>Nicholas Sighinolfi<br>Luigi Randazzo<br>Andrea Truocchio<br>Nicholas Sighinolfi<br>Marko Podrascanin<br>Marko Podrascanin<br>Marko Podrascanin<br>Daniele Mazzone<br>Andrea Truocchio<br>Edoardo Caneschi<br>Leandro ausibio Mosca<br>Marko Podrascanin<br>Jalen Penrose<br>Gabriele Di martino<br>Edoardo Caneschi<br>Edoardo Caneschi<br>Leandro ausibio Mosca<br>Nicholas Sighinolfi<br>Nicholas Sighinolfi<br>Edoardo Caneschi<br>Nicholas Sighinolfi<br>Diego Foresi<br>Gabriele Di martino<br>Leandro ausibio Mosca<br>Daniele Mazzone<br>Edoardo Caneschi<br>Nicholas Sighinolfi<br>Haci Sahin<br>Andrea Truocchio<br>Nicola Pesaresi<br>Marko Podrascanin<br>Nicola Pesaresi<br>Andrea Truocchio<br>Marko Podrascanin<br>Leandro ausibio Mosca<br>Luigi Randazzo<br>Andrea Truocchio<br>Leandro ausibio Mosca<br>Daniele Mazzone<br>Matteo Lelli<br>Michal Hoszman"], ["<PERSON>k <PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br>Willner enrique R<PERSON> quijada<br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br>Willner enrique Rivas quijada<br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br>R<PERSON><br><PERSON>k <PERSON>i<PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br>Willner enrique R<PERSON>s quijada<br>R<PERSON><br><PERSON><br><PERSON><br>R<PERSON><br><PERSON><br>R<PERSON><br><PERSON>k <PERSON><br>Rok <PERSON><br>Willner enrique R<PERSON> quija<PERSON><br>R<PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON>tar<br><PERSON> <PERSON><br><PERSON> <PERSON><br>Rok <PERSON>c<br><PERSON> Dias<br>R<PERSON><br>Bruno Dias<br>Taylor Averill<br><PERSON> <PERSON><br><PERSON> Lostritto<br>Rok Mozic<br>K<PERSON>en <PERSON><br>Rok Mozic<br>Rok Mo<PERSON>c<br>Rok Mozic<br>Freek De weijer<br>Rok <PERSON>zic<br>K<PERSON>en Sen<br><PERSON> <PERSON><br>Freek De weijer<br>Willner enrique Rivas quijada<br>Bruno Dias<br>Rok Mozic<br>Taylor Averill<br>Alessandro Finauri<br>Roberto Russo<br>Rok Mozic<br>Rok Mozic<br>Bruno Dias<br>Willner enrique Rivas quijada<br>Francesco Lostritto<br>Rok Mozic<br>Rok Mozic<br>Roberto Russo<br>Roberto Russo<br>Taylor Averill<br>Bruno Dias<br>Rok Mozic<br>Bruno Dias<br>Bruno Dias<br>Bruno Dias<br>Rok Mozic<br>Rok Mozic<br>Willner enrique Rivas quijada<br>Willner enrique Rivas quijada<br>Rok Mozic<br>Taylor Averill<br>Taylor Averill<br>Roberto Russo<br>Rok Mozic<br>Rok Mozic<br>Willner enrique Rivas quijada<br>Bruno Dias<br>Roberto Russo<br>Rok Mozic<br>Rok Mozic<br>Rok Mozic<br>Rok Mozic<br>Rok Mozic<br>Rok Mozic<br>Rok Mozic<br>Roberto Russo<br>Rok Mozic<br>Klemen Sen<br>Roberto Russo<br>Freek De weijer<br>Roberto Russo<br>Freek De weijer<br>Taylor Averill<br>Bruno Dias<br>Rok Mozic<br>Rok Mozic<br>Willner enrique Rivas quijada<br>Roberto Russo<br>Taylor Averill<br>Bruno Dias<br>Roberto Russo<br>Rok Mozic<br>Rok Mozic<br>Rok Mozic<br>Willner enrique Rivas quijada<br>Rok Mozic<br>Freek De weijer<br>Jacopo Tosti<br>Bruno Dias<br>Rok Mozic<br>Bruno Dias<br>Rok Mozic<br>Rok Mozic<br>Freek De weijer<br>Willner enrique Rivas quijada<br>Dmytro Pashytskyy<br>Rok Mozic<br>Roberto Russo<br>Rok Mozic<br>Roberto Russo<br>Freek De weijer<br>Roberto Russo<br>Mathieu Garcia<br>Taylor Averill<br>Roberto Russo<br>Rok Mozic<br>Freek De weijer<br>Rok Mozic<br>Freek De weijer<br>Freek De weijer<br>Rok Mozic<br>Taylor Averill<br>Rok Mozic<br>Roberto Russo<br>Freek De weijer<br>Berkay Bayraktar<br>Willner enrique Rivas quijada<br>Rok Mozic<br>Freek De weijer<br>Rok Mozic<br>Rok Mozic<br>Taylor Averill<br>Rok Mozic<br>Freek De weijer<br>Rok Mozic<br>Roberto Russo<br>Roberto Russo<br>Freek De weijer<br>Roberto Russo<br>Taylor Averill<br>Erhan Hamarat<br>Roberto Russo<br>Rok Mozic<br>Rok Mozic<br>Roberto Russo<br>Roberto Russo<br>Freek De weijer<br>Rok Mozic<br>Roberto Russo<br>Roberto Russo<br>Rok Mozic<br>Freek De weijer<br>Rok Mozic<br>Roberto Russo<br>Rok Mozic<br>Hugo Fischer<br>Willner enrique Rivas quijada<br>Rok Mozic<br>Klemen Sen<br>Roberto Russo<br>Willner enrique Rivas quijada<br>Rok Mozic<br>Freek De weijer<br>Bruno Dias<br>Rok Mozic<br>Rok Mozic<br>Rok Mozic<br>Rok Mozic<br>Bruno Dias<br>Bruno Dias<br>Rok Mozic<br>Rok Mozic<br>Freek De weijer<br>Rok Mozic<br>Erhan Hamarat<br>Roberto Russo<br>Taylor Averill<br>Roberto Russo<br>Freek De weijer<br>Mathieu Garcia<br>Bruno Dias<br>Willner enrique Rivas quijada<br>Rok Mozic<br>Roberto Russo<br>Roberto Russo<br>Rok Mozic<br>Rok Mozic<br>Rok Mozic<br>Paul Buchegger<br>Paul Buchegger<br>Paul Buchegger<br>Paul Buchegger<br>Paul Buchegger<br>Paul Buchegger<br>Paul Buchegger<br>Paul Buchegger<br>Paul Buchegger<br>Paul Buchegger<br>Paul Buchegger<br>Paul Buchegger<br>Paul Buchegger<br>Paul Buchegger<br>Paul Buchegger<br>Paul Buchegger<br>Paul Buchegger<br>Paul Buchegger<br>Roberto Russo<br>Freek De weijer<br>Freek De weijer<br>Freek De weijer<br>Freek De weijer<br>Freek De weijer<br>Freek De weijer<br>Rok Mozic<br>Berkay Bayraktar<br>Klemen Sen<br>Erhan Hamarat<br>Hugo Fischer<br>Francesco Lostritto<br>Rok Mozic<br>Freek De weijer<br>Erhan Hamarat<br>Rok Mozic<br>Rok Mozic<br>Erhan Hamarat<br>Rok Mozic<br>Javier facundo Martinez<br>Francesco Lostritto<br>Gonzalo Quiroga<br>Dmytro Pashytskyy<br>Bruno Dias<br>Roberto Russo<br>Alexandre Strehlau<br>Roberto Russo<br>Roberto Russo<br>Bruno Dias<br>Alessandro Bristot<br>Mathieu Garcia<br>Roberto Russo<br>Bruno Dias<br>Bruno Dias<br>Paul Buchegger<br>Willner enrique Rivas quijada<br>Taylor Averill<br>Roberto Russo<br>Rok Mozic<br>Alessandro Finauri<br>Paul Buchegger<br>Paul Buchegger<br>Willner enrique Rivas quijada<br>Rok Mozic<br>Bruno Dias<br>Ville Jarnvall<br>Ville Jarnvall<br>Carlos a. Silva puron<br>Carlos a. Silva puron<br>Giovanni Favaro<br>Freek De weijer<br>Rok Mozic<br>Roberto Russo<br>Freek De weijer"], ["<PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><PERSON>ciger<br>Efe Bay<PERSON><br><PERSON> Ropret<br>Efe Bayram<br>Efe Bay<PERSON><br><PERSON> Gaggini<br>Efe Bayram<br>Efe Bayram<br>Efe Bayram<br>Efe Bayram<br>Efe Bayram<br>Srecko Lisinac<br>Gregor Ropret<br><PERSON> Gaggini<br><PERSON> Gaggini<br><PERSON> Gaggini<br>Gregor Ropret<br><PERSON>l<PERSON> Hoffer<br>Marco Gaggini<br>Efe Bayram<br>Wataru Taniguchi<br>Efe Bayram<br>Efe Bayram<br>Gregor Ropret<br>Gregor Ropret<br>Efe Bayram<br>Gregor Ropret<br>Gregor Ropret<br>Efe Bayram<br>Gabrijel Cvanciger<br>Efe Bayram<br>Srecko Lisinac<br>Efe Bayram<br>Mustafa Cengiz<br>Srecko Lisinac<br>Efe Bayram<br>Srecko Lisinac<br>Gabrijel Cvanciger<br>Gregor Ropret<br>Marco Gaggini<br>Marco Gaggini<br>Efe Bayram<br>Marco Gaggini<br>Mateusz Bieniek<br>Gregor Ropret<br>Marco Gaggini<br>Marco Gaggini<br>Marco Gaggini<br>Marco Gaggini<br>Gregor Ropret<br>Marco Gaggini<br>Mateusz Bieniek<br>Marco Gaggini<br>Marco Gaggini<br>Efe Bayram<br>Gregor Ropret<br>Marco Gaggini<br>Marco Gaggini<br>Gregor Ropret<br>Mustafa Cengiz<br>Efe Bayram<br>Efe Bayram<br>Efe Bayram<br>Marco Gaggini<br>Efe Bayram<br>Efe Bayram<br>Gregor Ropret<br>Marco Gaggini<br>Marco Gaggini<br>Marco Gaggini<br>Srecko Lisinac<br>Marco Gaggini<br>Gregor Ropret<br>Gregor Ropret<br>Wataru Taniguchi<br>Efe Bayram<br>Efe Bayram<br>Efe Bayram<br>Efe Bayram<br>Marco Gaggini<br>Gregor Ropret<br>Efe Bayram<br>Gabrijel Cvanciger<br>Efe Bayram<br>Marco Gaggini<br>Marco Gaggini<br>Gabrijel Cvanciger<br>Gregor Ropret<br>Gregor Ropret<br>Gregor Ropret<br>Gregor Ropret<br>Srecko Lisinac<br>Wataru Taniguchi<br>Mateusz Bieniek<br>Marco Gaggini<br>Marco Gaggini<br>Gabrijel Cvanciger<br>Gregor Ropret<br>Efe Bayram<br>Efe Bayram<br>Efe Bayram<br>Srecko Lisinac<br>Efe Bayram<br>Marco Gaggini<br>Mateusz Bieniek<br>Efe Bayram<br>Marco Gaggini<br>Wataru Taniguchi<br>Efe Bayram<br>Efe Bayram<br>Marco Gaggini<br>Gregor Ropret<br>Marco Gaggini<br>Srecko Lisinac<br>Gregor Ropret<br>Mateusz Bieniek<br>Efe Bayram<br>Aykut Gedik<br>Srecko Lisinac<br>Gregor Ropret<br>Gabrijel Cvanciger<br>Marco Gaggini<br>Efe Bayram<br>Gregor Ropret<br>Efe Bayram<br>Gabrijel Cvanciger<br>Wataru Taniguchi<br>Gabrijel Cvanciger<br>Gregor Ropret<br>Marco Gaggini<br>Marco Gaggini<br>Marco Gaggini<br>Efe Bayram<br>Wataru Taniguchi<br>Wataru Taniguchi<br>Srecko Lisinac<br>Gregor Ropret<br>Mustafa Cengiz<br>Efe Bayram<br>Efe Bayram<br>Gabrijel Cvanciger<br>Wataru Taniguchi<br>Srecko Lisinac<br>Gabrijel Cvanciger<br>Wataru Taniguchi<br>Srecko Lisinac<br>Gregor Ropret<br>Marco Gaggini<br>Srecko Lisinac<br>Wataru Taniguchi<br>Gregor Ropret<br>Marco Gaggini<br>Efe Bayram<br>Efe Bayram<br>Gabrijel Cvanciger<br>Marco Gaggini<br>Gabrijel Cvanciger<br>Marco Gaggini<br>Marco Gaggini<br>Wataru Taniguchi<br>Marco Gaggini<br>Efe Bayram<br>Efe Bayram<br>Gabrijel Cvanciger<br>Srecko Lisinac<br>Marco Gaggini<br>Gregor Ropret<br>Marco Gaggini<br>Marco Gaggini<br>Efe Bayram<br>Gregor Ropret<br>Marco Gaggini<br>Gabrijel Cvanciger<br>Gregor Ropret<br>Marco Gaggini<br>Marco Gaggini<br>Gregor Ropret<br>Marco Gaggini<br>Marco Gaggini<br>Marco Gaggini<br>Marco Gaggini<br>Gregor Ropret<br>Gregor Ropret<br>Gabrijel Cvanciger<br>Srecko Lisinac<br>Srecko Lisinac<br>Gregor Ropret<br>Efe Bayram<br>Marco Gaggini<br>Marco Gaggini<br>Mateusz Bieniek<br>Marco Gaggini<br>Nicolò Hoffer<br>Efe Bayram<br>Marco Gaggini<br>Efe Bayram<br>Davide Brignach<br>Srecko Lisinac<br>Srecko Lisinac<br>Srecko Lisinac<br>Srecko Lisinac<br>Srecko Lisinac<br>Srecko Lisinac<br>Srecko Lisinac<br>Srecko Lisinac<br>Nicolò Hoffer<br>Marco Gaggini<br>Marco Gaggini<br>Marco Gaggini<br>Marco Gaggini<br>Marco Gaggini<br>Marco Gaggini<br>Efe Bayram<br>Efe Bayram<br>Efe Bayram<br>Efe Bayram<br>Efe Bayram<br>Efe Bayram<br>Gregor Ropret<br>Gregor Ropret<br>Gregor Ropret<br>Gregor Ropret<br>Gregor Ropret<br>Aykut Gedik<br>Mustafa Cengiz<br>Efe Bayram<br>Srecko Lisinac<br>Marco Gaggini<br>Mustafa Cengiz<br>Marco Gaggini<br>Srecko Lisinac<br>Gregor Ropret<br>Mustafa Cengiz<br>Jeffrey Klok<br>Marco Gaggini<br>Gregor Ropret<br>Mateusz Bieniek<br>Mateusz Bieniek<br>Srecko Lisinac<br>Gregor Ropret<br>Gregor Ropret<br>Gregor Ropret<br>Jakub Klajmon<br>Gregor Ropret<br>Efe Bayram<br>Gregor Ropret<br>Gabrijel Cvanciger<br>Gregor Ropret<br>Gabrijel Cvanciger<br>Marco Gaggini<br>Marco Gaggini<br>Pedro Soares<br>Pedro Soares<br>Marko Radosavljevic<br>Gregor Ropret<br>Gregor Ropret<br>Grzegorz Jacznik<br>Efe Bayram<br>Efe Bayram<br>Aykut Gedik"], ["<PERSON><PERSON><br><PERSON><br><PERSON> cesar <PERSON> morales<br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br>Julio cesar <PERSON>s morales<br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><br>Julio cesar Cardenas morales<br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br>Julio cesar <PERSON> morales<br>Julio cesar <PERSON>enas morales<br><PERSON><PERSON><br>Julio cesar <PERSON> morales<br><PERSON><br>Julio cesar <PERSON> morale<PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON> <PERSON>ni<br><PERSON> <PERSON><br><PERSON><PERSON> <PERSON><PERSON><PERSON><br><PERSON><PERSON>ttolo<br><PERSON><PERSON> <PERSON><PERSON>lo<br><PERSON>ia <PERSON><br><PERSON> Z<PERSON><br><PERSON> Z<PERSON><br><PERSON><PERSON> <PERSON>tto<PERSON><br><PERSON><PERSON> <PERSON><PERSON><PERSON><br><PERSON>ia <PERSON><PERSON>lo<br><PERSON> Zanotti<br>Salvatore Rossini<br>Salvatore Rossini<br>Salvatore Rossini<br>Julio cesar Cardenas morales<br>Andrea Zanotti<br>Andrea Zanotti<br>Andrea Zanotti<br>Andrea Zanotti<br>Andrea Zanotti<br>Salvatore Rossini<br>Andrea Zanotti<br>Mattia Bottolo<br>Andrea Zanotti<br>Salvatore Rossini<br>Salvatore Rossini<br>Julio cesar Cardenas morales<br>Mattia Bottolo<br>Salvatore Rossini<br>Saverio De santis<br>Andrea Zanotti<br>Andrea Zanotti<br>Georgi Tatarov<br>Andrea Zanotti<br>Julio cesar Cardenas morales<br>Jakub Rybicki<br>Andrea Zanotti<br>Georgi Tatarov<br>Mattia Bottolo<br>Mattia Bottolo<br>Andrea Zanotti<br>Julio cesar Cardenas morales<br>Georgi Tatarov<br>Andrea Zanotti<br>Julio cesar Cardenas morales<br>Andrea Zanotti<br>Mattia Bottolo<br>Mattia Bottolo<br>Salvatore Rossini<br>Julio cesar Cardenas morales<br>Andrea Zanotti<br>Andrea Zanotti<br>Salvatore Rossini<br>Mattia Bottolo<br>Mattia Bottolo<br>Andrea Zanotti<br>Andrea Zanotti<br>Salvatore Rossini<br>Salvatore Rossini<br>Mattia Bottolo<br>Salvatore Rossini<br>Mattia Bottolo<br>Andrea Zanotti<br>Mattia Bottolo<br>Mattia Bottolo<br>Georgi Tatarov<br>Andrea Zanotti<br>Mattia Bottolo<br>Julio cesar Cardenas morales<br>Salvatore Rossini<br>Andrea Zanotti<br>Salvatore Rossini<br>Mattia Bottolo<br>Andrea Zanotti<br>Andrea Zanotti<br>Andrea Zanotti<br>Georgi Tatarov<br>Andrea Zanotti<br>Julio cesar Cardenas morales<br>Salvatore Rossini<br>Mattia Bottolo<br>Julio cesar Cardenas morales<br>Mattia Bottolo<br>Georgi Tatarov<br>Georgi Tatarov<br>Mattia Bottolo<br>Salvatore Rossini<br>Julio cesar Cardenas morales<br>Andrea Zanotti<br>Julio cesar Cardenas morales<br>Mattia Bottolo<br>Andrea Zanotti<br>Saverio De santis<br>Mattia Bottolo<br>Andrea Zanotti<br>Mattia Bottolo<br>Georgi Tatarov<br>Andrea Zanotti<br>Julio cesar Cardenas morales<br>Salvatore Rossini<br>Mattia Bottolo<br>Mattia Bottolo<br>Mattia Bottolo<br>Mattia Bottolo<br>Andrea Zanotti<br>Mattia Bottolo<br>Julio cesar Cardenas morales<br>Mattia Bottolo<br>Andrea Zanotti<br>Raivis Oiguss<br>Mattia Bottolo<br>Andrea Zanotti<br>Andrea Zanotti<br>Mattia Bottolo<br>Mattia Bottolo<br>Andrea Zanotti<br>Andrea Zanotti<br>Salvatore Rossini<br>Raivis Oiguss<br>Mattia Bottolo<br>Andrea Zanotti<br>Julio cesar Cardenas morales<br>Andrea Zanotti<br>Andrea Zanotti<br>Mattia Bottolo<br>Mattia Bottolo<br>Tomasz Fornal<br>Andrea Zanotti<br>Andrea Zanotti<br>Mattia Bottolo<br>Andrea Zanotti<br>Salvatore Rossini<br>Georgi Tatarov<br>Mattia Bottolo<br>Georgi Tatarov<br>Mattia Bottolo<br>Mattia Bottolo<br>Mattia Bottolo<br>Julio cesar Cardenas morales<br>Andrea Zanotti<br>Saverio De santis<br>Andrea Zanotti<br>Julio cesar Cardenas morales<br>Salvatore Rossini<br>Mattia Bottolo<br>Andrea Zanotti<br>Julio cesar Cardenas morales<br>Julio cesar Cardenas morales<br>Salvatore Rossini<br>Andrea Zanotti<br>Julio cesar Cardenas morales<br>Mattia Bottolo<br>Andrea Zanotti<br>Andrea Zanotti<br>Andrea Zanotti<br>Andrea Zanotti<br>Mattia Bottolo<br>Andrea Zanotti<br>Salvatore Rossini<br>Andrea Zanotti<br>Mattia Bottolo<br>Mattia Bottolo<br>Mattia Bottolo<br>Jakub Rybicki<br>Salvatore Rossini<br>Andrea Zanotti<br>Julio cesar Cardenas morales<br>Mattia Bottolo<br>Mattia Bottolo<br>Andrea Zanotti<br>Andrea Zanotti<br>Mattia Bottolo<br>Mattia Bottolo<br>Jakub Rybicki<br>Andrea Zanotti<br>Andrea Zanotti<br>Salvatore Rossini<br>Andrea Zanotti<br>Georgi Tatarov<br>Julio cesar Cardenas morales<br>Jakub Rybicki<br>Salvatore Rossini<br>Jakub Rybicki<br>Andrea Zanotti<br>Salvatore Rossini<br>Salvatore Rossini<br>Salvatore Rossini<br>Salvatore Rossini<br>Salvatore Rossini<br>Salvatore Rossini<br>Salvatore Rossini<br>Salvatore Rossini<br>Salvatore Rossini<br>Salvatore Rossini<br>Salvatore Rossini<br>Salvatore Rossini<br>Salvatore Rossini<br>Salvatore Rossini<br>Salvatore Rossini<br>Andrea Zanotti<br>Andrea Zanotti<br>Andrea Zanotti<br>Andrea Zanotti<br>Andrea Zanotti<br>Andrea Zanotti<br>Mattia Bottolo<br>Mattia Bottolo<br>Mattia Bottolo<br>Mattia Bottolo<br>Mattia Bottolo<br>Mattia Bottolo<br>Mattia Bottolo<br>Mattia Bottolo<br>Julio cesar Cardenas morales<br>Julio cesar Cardenas morales<br>Julio cesar Cardenas morales<br>Julio cesar Cardenas morales<br>Raivis Oiguss<br>Raivis Oiguss<br>Andrea Zanotti<br>Andrea Zanotti<br>Julio cesar Cardenas morales<br>Salvatore Rossini<br>Raivis Oiguss<br>Salvatore Rossini<br>Lorenzo Zanellotti<br>Jakub Rybicki<br>Martin Koèka<br>Wojciech Zalinski<br>Wojciech Zalinski<br>Jakub Rybicki<br>Mattia Bottolo<br>Tomasz Fornal<br>Tobias Krick<br>Andrea Zanotti<br>Georgi Tatarov<br>Andrea Zanotti<br>Saverio De santis<br>Andrea Zanotti<br>Salvatore Rossini<br>Julio cesar Cardenas morales<br>Julio cesar Cardenas morales<br>Salvatore Rossini<br>Francisco Moura<br>Francisco Moura<br>Julio cesar Cardenas morales<br>Mattia Bottolo<br>Salvatore Rossini<br>Andrea Zanotti<br>Mattia Bottolo<br>Salvatore Rossini"], ["<PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON> ghara h.<br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON> floria<PERSON><br><PERSON><br><PERSON><PERSON> ghara h.<br><PERSON><br><PERSON><br><PERSON><br><PERSON>lad <PERSON>bad<PERSON> ghara h.<br><PERSON> mi<PERSON><br><PERSON><br><PERSON> migue<PERSON> suare<PERSON><br><PERSON> miguel <PERSON> suarez<br><PERSON> miguel <PERSON> suare<PERSON><br>Milad <PERSON>bad<PERSON>our ghara h.<br>Milad <PERSON> ghara h.<br>Milad <PERSON>bad<PERSON>our ghara h.<br>Bela florian <PERSON><br><PERSON><PERSON><PERSON><br><PERSON> miguel <PERSON> suarez<br>Milad <PERSON> ghara h.<br><PERSON><PERSON> ghara h.<br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON> miguel Gutierrez suarez<br><PERSON> Crosato<br><PERSON> C<PERSON>ato<br>Bela florian Bartha<br>Bela florian Bartha<br>Milad Ebadipour ghara h.<br>Bela florian Bartha<br>Moustapha Mbaye<br>Jose miguel Gutierrez suarez<br>Jacopo Larizza<br>Jose miguel Gutierrez suarez<br>Milad Ebadipour ghara h.<br>Jacopo Larizza<br>Federico Crosato<br>Jacopo Larizza<br>Jose miguel Gutierrez suarez<br>Jacopo Larizza<br>Federico Crosato<br>Enes Atli<br>Federico Crosato<br>Jose miguel Gutierrez suarez<br>Federico Crosato<br>Federico Crosato<br>Milad Ebadipour ghara h.<br>Jose miguel Gutierrez suarez<br>Jose miguel Gutierrez suarez<br>Jacopo Larizza<br>Bela florian Bartha<br>Milad Ebadipour ghara h.<br>Jacopo Larizza<br>Bela florian Bartha<br>Jose miguel Gutierrez suarez<br>Milad Ebadipour ghara h.<br>Federico Crosato<br>Jose miguel Gutierrez suarez<br>Federico Crosato<br>Federico Crosato<br>Jacopo Larizza<br>Milad Ebadipour ghara h.<br>Federico Crosato<br>Jose miguel Gutierrez suarez<br>Federico Crosato<br>Milad Ebadipour ghara h.<br>Federico Crosato<br>Jose miguel Gutierrez suarez<br>Federico Crosato<br>Federico Crosato<br>Federico Crosato<br>Milad Ebadipour ghara h.<br>Federico Crosato<br>Jose miguel Gutierrez suarez<br>Federico Crosato<br>Jose miguel Gutierrez suarez<br>Federico Crosato<br>Jacopo Larizza<br>Federico Crosato<br>Jacopo Larizza<br>Federico Crosato<br>Federico Crosato<br>Jose miguel Gutierrez suarez<br>Federico Crosato<br>Federico Crosato<br>Bela florian Bartha<br>Federico Crosato<br>Jose miguel Gutierrez suarez<br>Jacopo Larizza<br>Federico Crosato<br>Federico Crosato<br>Federico Crosato<br>Enes Atli<br>Jose miguel Gutierrez suarez<br>Jacopo Larizza<br>Jose miguel Gutierrez suarez<br>Jose miguel Gutierrez suarez<br>Jacopo Larizza<br>Federico Crosato<br>Federico Crosato<br>Jose miguel Gutierrez suarez<br>Bela florian Bartha<br>Jacopo Larizza<br>Federico Crosato<br>Enes Atli<br>Jose miguel Gutierrez suarez<br>Federico Crosato<br>Jose miguel Gutierrez suarez<br>Jose miguel Gutierrez suarez<br>Milad Ebadipour ghara h.<br>Federico Crosato<br>Federico Crosato<br>Milad Ebadipour ghara h.<br>Jose miguel Gutierrez suarez<br>Bela florian Bartha<br>Jose miguel Gutierrez suarez<br>Jacopo Larizza<br>Federico Crosato<br>Jose miguel Gutierrez suarez<br>Federico Crosato<br>Federico Crosato<br>Jose miguel Gutierrez suarez<br>Bela florian Bartha<br>Federico Crosato<br>Federico Crosato<br>Bela florian Bartha<br>Jose miguel Gutierrez suarez<br>Bela florian Bartha<br>Milad Ebadipour ghara h.<br>Milad Ebadipour ghara h.<br>Milad Ebadipour ghara h.<br>Milad Ebadipour ghara h.<br>Jose miguel Gutierrez suarez<br>Jose miguel Gutierrez suarez<br>Jose miguel Gutierrez suarez<br>Jose miguel Gutierrez suarez<br>Federico Crosato<br>Jacopo Larizza<br>Federico Crosato<br>Federico Crosato<br>Jacopo Larizza<br>Bela florian Bartha<br>Jacopo Larizza<br>Jose miguel Gutierrez suarez<br>Federico Crosato<br>Jacopo Larizza<br>Federico Crosato<br>Jose miguel Gutierrez suarez<br>Jose miguel Gutierrez suarez<br>Jacopo Larizza<br>Federico Crosato<br>Jose miguel Gutierrez suarez<br>Jacopo Larizza<br>Federico Crosato<br>Jose miguel Gutierrez suarez<br>Jose miguel Gutierrez suarez<br>Jose miguel Gutierrez suarez<br>Jose miguel Gutierrez suarez<br>Jose miguel Gutierrez suarez<br>Jacopo Larizza<br>Jose miguel Gutierrez suarez<br>Federico Crosato<br>Jose miguel Gutierrez suarez<br>Federico Crosato<br>Jose miguel Gutierrez suarez<br>Federico Crosato<br>Federico Crosato<br>Milad Ebadipour ghara h.<br>Jacopo Larizza<br>Jose miguel Gutierrez suarez<br>Andrea Malavasi<br>Federico Crosato<br>Federico Crosato<br>Federico Crosato<br>Federico Crosato<br>Federico Crosato<br>Federico Crosato<br>Federico Crosato<br>Federico Crosato<br>Milad Ebadipour ghara h.<br>Milad Ebadipour ghara h.<br>Milad Ebadipour ghara h.<br>Milad Ebadipour ghara h.<br>Milad Ebadipour ghara h.<br>Milad Ebadipour ghara h.<br>Milad Ebadipour ghara h.<br>Jose miguel Gutierrez suarez<br>Jose miguel Gutierrez suarez<br>Jose miguel Gutierrez suarez<br>Jose miguel Gutierrez suarez<br>Joao Varela<br>Joao Varela<br>Jose miguel Gutierrez suarez<br>Enes Atli<br>Jose miguel Gutierrez suarez<br>Enes Atli<br>Federico Crosato<br>Milad Ebadipour ghara h.<br>Enes Atli<br>Jose miguel Gutierrez suarez<br>Przemyslaw Smolinski<br>Erik Shoji<br>Federico Crosato<br>Erik Shoji<br>Milad Ebadipour ghara h.<br>Milad Ebadipour ghara h.<br>Michael Parkinson<br>Jacopo Larizza<br>Jose miguel Gutierrez suarez<br>Federico Crosato<br>Moustapha Mbaye<br>Pietro Melato<br>Jose miguel Gutierrez suarez<br>Jose miguel Gutierrez suarez<br>Jose miguel Gutierrez suarez<br>Bela florian Bartha<br>Patocka Vojtech<br>Federico Crosato<br>Jose miguel Gutierrez suarez<br>Jacopo Larizza<br>Jacopo Larizza<br>Federico Crosato"], ["Marlon Yant herrera<br><PERSON><br><PERSON><PERSON>t herrera<br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON> Yant herrera<br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON>lon Yant herrera<br>Marlon Yant herrera<br><PERSON>lon Yant herrera<br><PERSON>lon Yant herrera<br><PERSON>lon Yant herrera<br><PERSON><br><PERSON>lon Yant herrera<br><PERSON><PERSON> Yant herrera<br><PERSON><br><PERSON>lon Yant herrera<br><PERSON><PERSON> Yant herrera<br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br>Marlon Yant herrera<br>Marlon Yant herrera<br>Flav<PERSON> gualberto<br>Marlon Yant herrera<br><PERSON><br><PERSON><br><PERSON><br>Marlon Yant herrera<br>Marlon Yant herrera<br>Marlon Yant herrera<br>Marlon Yant herrera<br>Marlon Yant herrera<br>Flav<PERSON> gualberto<br><PERSON><br>Flav<PERSON> gualberto<br><PERSON><br>Marlon Yant herrera<br>Mar<PERSON> Yant herrera<br><PERSON><br><PERSON>lav<PERSON> gualberto<br><PERSON><PERSON> Yant herrera<br><PERSON>vo<PERSON>a<br>Matt<PERSON><PERSON>halk<br><PERSON><br>Marlon <PERSON>t herrera<br>Marlon Yant herrera<br><PERSON> alberto Bovo<PERSON>a<br><PERSON> Truht<PERSON><br><PERSON> <PERSON>go<PERSON><br><PERSON> <PERSON><PERSON><br><PERSON><PERSON>o Schalk<br>Marlon Yant herrera<br>Marlon Yant herrera<br><PERSON> <PERSON>go<PERSON><br>Flavio Re<PERSON>de gualberto<br><PERSON> alberto Bovo<PERSON>a<br>Stefano Mengozzi<br>Mattéo Schalk<br>Marlon Yant herrera<br>Alessandro alberto Bovolenta<br>Marlon Yant herrera<br>Marlon Yant herrera<br>Stefano Mengozzi<br>Marlon Yant herrera<br>Flavio Resende gualberto<br>Marlon Yant herrera<br>Marlon Yant herrera<br>Marlon Yant herrera<br>Flavio Resende gualberto<br>Stefano Mengozzi<br>Stefano Mengozzi<br>Marlon Yant herrera<br>Flavio Resende gualberto<br>Mattéo Schalk<br>Flavio Resende gualberto<br>Marlon Yant herrera<br>Stefano Mengozzi<br>Alessandro alberto Bovolenta<br>Mattéo Schalk<br>Stefano Mengozzi<br>Flavio Resende gualberto<br>Marlon Yant herrera<br>Stefano Mengozzi<br>Marlon Yant herrera<br>Stefano Mengozzi<br>Alessandro alberto Bovolenta<br>Flavio Resende gualberto<br>Flavio Resende gualberto<br>Marlon Yant herrera<br>Mattéo Schalk<br>Alessandro alberto Bovolenta<br>Marlon Yant herrera<br>Antti sakari Makinen<br>Mattéo Schalk<br>Flavio Resende gualberto<br>Marlon Yant herrera<br>Mattéo Schalk<br>Alessandro alberto Bovolenta<br>Alessandro alberto Bovolenta<br>Marlon Yant herrera<br>Stefano Mengozzi<br>Alessandro alberto Bovolenta<br>Flavio Resende gualberto<br>Alessandro alberto Bovolenta<br>Flavio Resende gualberto<br>Alessandro alberto Bovolenta<br>Marlon Yant herrera<br>Marlon Yant herrera<br>Marlon Yant herrera<br>Marlon Yant herrera<br>Marlon Yant herrera<br>Marlon Yant herrera<br>Alessandro alberto Bovolenta<br>Marlon Yant herrera<br>Mattéo Schalk<br>Flavio Resende gualberto<br>Stefano Mengozzi<br>Marlon Yant herrera<br>Marlon Yant herrera<br>Mattéo Schalk<br>Marlon Yant herrera<br>Flavio Resende gualberto<br>Alessandro alberto Bovolenta<br>Alessandro alberto Bovolenta<br>Flavio Resende gualberto<br>Flavio Resende gualberto<br>Mattéo Schalk<br>Alessandro alberto Bovolenta<br>Marlon Yant herrera<br>Antti sakari Makinen<br>Marlon Yant herrera<br>Stefano Mengozzi<br>Stefano Mengozzi<br>Alessandro alberto Bovolenta<br>Alessandro alberto Bovolenta<br>Marlon Yant herrera<br>Marlon Yant herrera<br>Marlon Yant herrera<br>Marlon Yant herrera<br>Marlon Yant herrera<br>Marlon Yant herrera<br>Marlon Yant herrera<br>Marlon Yant herrera<br>Stefano Mengozzi<br>Stefano Mengozzi<br>Stefano Mengozzi<br>Stefano Mengozzi<br>Stefano Mengozzi<br>Antti sakari Makinen<br>Stefano Mengozzi<br>Antti sakari Makinen<br>Antti sakari Makinen<br>Stefano Mengozzi<br>Aleksander Berger<br>Dawid Ogórek<br>Stefano Mengozzi<br>Axel Truhtchev<br>Axel Truhtchev<br>Alessandro alberto Bovolenta<br>None Grue<br>Mattéo Schalk<br>Alessandro alberto Bovolenta<br>Marlon Yant herrera<br>Marlon Yant herrera<br>Ernest Kaciczak<br>Stefano Mengozzi<br>Stefano Mengozzi<br>Marlon Yant herrera<br>Stefano Mengozzi<br>Marlon Yant herrera<br>Matteo Pauli"], ["<PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON>uss<PERSON> Gueye<br><PERSON> A<PERSON>qua<PERSON><br><PERSON>vi Ten<PERSON> cape<PERSON>i<br><PERSON> A<PERSON>quarone<br><PERSON> <PERSON>si<br><PERSON> Acquarone<br><PERSON> Acquarone<br><PERSON>vi <PERSON><PERSON> cape<PERSON>i<br><PERSON> <PERSON>aba<br><PERSON> Zaytsev<br><PERSON>si<br><PERSON>usse Gueye<br><PERSON> Acquarone<br><PERSON>usse Gueye<br><PERSON>usse Gueye<br>Alessandro Acquarone<br>Davi Tenorio capeloci<br>Mousse Gueye<br>Ahmed Abulubaba<br>Mousse Gueye<br>Mousse Gueye<br>Ahmed Abulubaba<br>Ahmed Abulubaba<br>Ivan Zaytsev<br>Mousse Gueye<br>Ahmed Abulubaba<br>Davi Tenorio capeloci<br>Andrea Malavasi<br>Mousse Gueye<br>Ahmed Abulubaba<br>Ahmed Abulubaba<br>Mousse Gueye<br>Mousse Gueye<br>Andrea Malavasi<br>Alessandro Acquarone<br>Ahmed Abulubaba<br>Mousse Gueye<br>Mousse Gueye<br>Ivan Zaytsev<br>Ahmed Abulubaba<br>Alessandro Acquarone<br>Davi Tenorio capeloci<br>Mousse Gueye<br>Andrea Malavasi<br>Davi Tenorio capeloci<br>Mousse Gueye<br>Ahmed Abulubaba<br>Ahmed Abulubaba<br>Andrea Malavasi<br>Andrea Malavasi<br>Andrea Malavasi<br>Andrea Malavasi<br>Andrea Malavasi<br>Andrea Malavasi<br>Andrea Malavasi<br>Andrea Malavasi<br>Hamdi Erkan<br>Mateusz Czunkiewicz<br>Alessandro Acquarone<br>Alessandro Acquarone<br>Alessandro Acquarone<br>Alessandro Acquarone<br>Alessandro Acquarone<br>Abdulubaba Amhed<br>Ahmed Abulubaba<br>Mousse Gueye<br>Davi Tenorio capeloci<br>Ahmed Abulubaba<br>Alessandro Acquarone<br>Ahmed Abulubaba<br>Andrea Malavasi<br>Alessandro Acquarone<br>Alessandro Acquarone<br>Davi Tenorio capeloci<br>Andrea Malavasi<br>Ivan Zaytsev<br>Andrea Malavasi"], ["<PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON>"], ["<PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON>"], ["<PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON>lunga<br><PERSON>lung<PERSON><br><PERSON>lunga<br><PERSON> <PERSON>avasi<br><PERSON>i<br><PERSON> Bron<PERSON><PERSON><br><PERSON> Bron<PERSON><PERSON><br><PERSON> <PERSON>lunga<br><PERSON> <PERSON>lunga<br><PERSON> <PERSON>lunga<br><PERSON> <PERSON>reti<br><PERSON>lunga<br><PERSON> <PERSON>glialunga<br>Andrade Jose ped<PERSON><br>Andrade Jose ped<PERSON><br><PERSON> <PERSON>lunga<br><PERSON> Paglialunga"], ["<PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON>"], ["Aleksan<PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br>Al<PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br>Aleksandar Nedeljkovic<br>Aleksandar Nedeljkovic<br>Aleksandar Nedeljkovic<br>Aleksandar Nedeljkovic<br>Aleksandar Nedeljkovic<br>Aleksandar Nedeljkovic<br>Aleksandar Nedeljkovic<br>Aleksandar Nedeljkovic<br>Aleksandar Nedeljkovic<br>Aleksandar Nedeljkovic<br>Aleksandar Nedeljkovic<br>Aleksandar Nedeljkovic<br>Aleksandar Nedeljkovic"], ["<PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON> ant<PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON> ant<PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON> ant<PERSON><br><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON>"], ["<PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br>Arthur <PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON> jar<PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON>z<PERSON>c<br>Arthur S<PERSON>c<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Sz<PERSON>c<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Szwarc<br>Arthur Szwarc<br>Mateusz jaroslaw Jozwik<br>Arthur Szwarc<br>Arthur Szwarc<br>Matias Tihumaki<br>Matias Tihumaki"], ["<PERSON><br><PERSON>"], ["<PERSON><br><PERSON><br><PERSON><PERSON><PERSON><PERSON>"], ["<PERSON><PERSON><br><PERSON><PERSON>"], ["<PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON>"], ["<PERSON><PERSON>"], ["<PERSON><PERSON><PERSON>"], ["<PERSON><PERSON><PERSON>"], ["<PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON>"], ["<PERSON><br><PERSON><PERSON>"], ["<PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><br><PERSON>. <PERSON>"], ["<PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><PERSON>"], ["<PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON>paruhov<br><PERSON><PERSON><PERSON>ov<br><PERSON><PERSON><PERSON>paruhov<br><PERSON><PERSON><PERSON>paruhov<br><PERSON><PERSON><PERSON>ov<br><PERSON><PERSON><PERSON>paruhov<br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON>ov<br><PERSON><PERSON><PERSON>paruhov<br><PERSON><PERSON><PERSON>paruhov<br><PERSON><PERSON><PERSON>ov<br><PERSON><PERSON><PERSON>par<PERSON>ov<br><PERSON><PERSON><PERSON>paruhov<br><PERSON><PERSON><PERSON>ov"], ["<PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON>di<br><PERSON><PERSON>o Rinaldi<br>Tom<PERSON>o Rinaldi<br><PERSON><PERSON>o <PERSON>inaldi<br><PERSON><PERSON><PERSON> Rinaldi<br><PERSON>maso Rinaldi<br><PERSON><PERSON>o Rinaldi<br><PERSON><PERSON>o Rinaldi<br>Tommaso Rinaldi<br><PERSON>maso Rinaldi<br><PERSON><PERSON>o Rinaldi<br>Tom<PERSON>o Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Andrea Innocenzi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi<br>Tommaso Rinaldi"], ["<PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><PERSON><PERSON><br><PERSON><br><PERSON><PERSON><PERSON>"], ["Pav<PERSON><br>Pav<PERSON><br>Pav<PERSON><br>Pa<PERSON><PERSON><br>Pav<PERSON><br>Pa<PERSON><PERSON><br>Pav<PERSON><br>Pav<PERSON><br>Pav<PERSON><br>Pav<PERSON><br>Pav<PERSON><br>Pav<PERSON><br>Pav<PERSON><br>Pav<PERSON><br>Pav<PERSON><br>Pav<PERSON><br>Pav<PERSON><br>Pav<PERSON><br>Pav<PERSON><br>Pav<PERSON><br>Pav<PERSON><br>Pav<PERSON><br>Pavle <PERSON><br>Pavle <PERSON><br>Pav<PERSON><br>Pav<PERSON><br>Pav<PERSON>"], ["<PERSON><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON><br><PERSON><PERSON> Norbert<br><PERSON><PERSON><br><PERSON><PERSON><PERSON><br>Marni<PERSON>"]], "hovertemplate": "<b>Numero: %{x}</b><br><b>Frequenza: %{y}</b><br><br>", "legendgroup": "", "marker": {"color": "#636efa", "pattern": {"shape": ""}}, "name": "", "orientation": "v", "showlegend": false, "textposition": "auto", "texttemplate": "%{y}", "type": "bar", "x": {"bdata": "AAECAwQFBgcICQoLDA0ODxAREhMUFRYXGBkaGxwdHh8hIiMsN0JDR0lNV1haW2Jj", "dtype": "i1"}, "xaxis": "x", "y": {"bdata": "AQDoAogC0QLKApsCXwODA7MCoQJtA0EDaAK8AqIBzgIVAYYCYgIoATQBMAHsAK0AewAUAAsAQwAGAC0AHwBWAAIAAwACACkAAQABAAEAAgACAC8ABgAeAGwACQAbABsA", "dtype": "i2"}, "yaxis": "y"}], "layout": {"bargap": 0.15, "barmode": "relative", "height": 500, "hoverlabel": {"align": "left", "bgcolor": "white", "font": {"size": 12}}, "legend": {"tracegroupgap": 0}, "showlegend": false, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "Numero di partite disputate per ogni numero di maglia"}, "xaxis": {"anchor": "y", "domain": [0, 1], "title": {"text": "Numero di Maglia"}}, "yaxis": {"anchor": "x", "domain": [0, 1], "title": {"text": "Frequenza"}}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["import plotly.express as px  # Aggiungo l'import di plotly.express\n", "\n", "df_players_each_game = pd.read_sql_query(\"SELECT * FROM players_each_game\", engine)\n", "\n", "# Raggruppa i dati per numero di maglia e crea la lista dei giocatori\n", "maglia_details = df_players_each_game.groupby('NumeroMaglia').agg({\n", "    'Nome': list,\n", "    'Cognome': list,\n", "}).reset_index()\n", "\n", "# <PERSON><PERSON> le frequenze\n", "maglia_details['Frequenza'] = maglia_details['Nome'].apply(len)\n", "\n", "# Crea la stringa di hover con i dettagli dei giocatori\n", "def create_hover_text(row):\n", "    players = []\n", "    for nome, cognome in zip(row['Nome'], row['Cognome']):\n", "        players.append(f\"{nome} {cognome}\")\n", "    return \"<br>\".join(players)\n", "\n", "maglia_details['hover_text'] = maglia_details.apply(create_hover_text, axis=1)\n", "\n", "# Crea il grafico a barre\n", "fig = px.bar(maglia_details, \n", "             x='NumeroMaglia', \n", "             y='Frequenza',\n", "             title='Numero di partite disputate per ogni numero di maglia',\n", "             labels={'NumMaglia': 'Numero di Maglia', \n", "                    'Frequenza': 'Numero di Occorrenze'},\n", "             text_auto=True,\n", "             custom_data=['hover_text'])\n", "\n", "# Personalizza il layout e il formato dell'hover\n", "fig.update_traces(\n", "    hovertemplate=\"<b>Numero: %{x}</b><br>\" +\n", "                  \"<b>Frequenza: %{y}</b><br><br>\"\n", ")\n", "\n", "fig.update_layout(\n", "    xaxis_title=\"Numero di Maglia\",\n", "    yaxis_title=\"Frequenza\",\n", "    bargap=0.15,  #distanza tra le barre\n", "    showlegend=False,\n", "    hoverlabel=dict(\n", "        bgcolor=\"white\",\n", "        font_size=12,\n", "        align=\"left\"\n", "    )\n", ")\n", "\n", "# Mostra il grafico\n", "fig.update_layout(height=500)\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Henrique Pedro ha NumeroMaglia 0.\n", "\n", "<PERSON>lie lo aggiorno a mano. (no, non so qual è e non lo trovo)"]}, {"cell_type": "code", "execution_count": 218, "metadata": {}, "outputs": [], "source": ["conn.rollback()"]}, {"cell_type": "code", "execution_count": 219, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "99db979125454fbcabb038b68fa9d299", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(Text(value='', description='GameID:', layout=Layout(width='50%'), placeholder='Inserisci GameID…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#Mostro un grafico a barre del numero di tocchi in un'azione con Seaborn\n", "import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import ipywidgets as widgets\n", "from IPython.display import display, clear_output\n", "\n", "# Widget per inserire il GameID\n", "gameid_input = widgets.Text(\n", "    placeholder='Inserisci GameID (opzionale)',\n", "    description='GameID:',\n", "    style={'description_width': 'initial'},\n", "    layout=widgets.Layout(width='50%')\n", ")\n", "\n", "# Bottone per eseguire\n", "run_button = widgets.<PERSON>ton(description=\"Mostra grafico\")\n", "output = widgets.Output()\n", "\n", "\n", "def plot_touch_distribution(game_id=None):\n", "    # Costruzione query dinamica\n", "    if game_id:\n", "        query = f\"\"\"\n", "        SELECT max_touch, COUNT(*) AS num_azioni\n", "        FROM (\n", "            SELECT \n", "                \"GameID\", \n", "                \"SetNumber\", \n", "                \"ActionNumber\", \n", "                MAX(\"TouchNumber\") AS max_touch\n", "            FROM rilevations\n", "            WHERE \"GameID\" = '{game_id}'\n", "            GROUP BY \"GameID\", \"SetNumber\", \"ActionNumber\"\n", "        ) AS sottoquery\n", "        GROUP BY max_touch\n", "        ORDER BY max_touch;\n", "        \"\"\"\n", "    else:\n", "        query = \"\"\"\n", "        SELECT max_touch, COUNT(*) AS num_azioni\n", "        FROM (\n", "            SELECT \n", "                \"GameID\", \n", "                \"SetNumber\", \n", "                \"ActionNumber\", \n", "                MAX(\"TouchNumber\") AS max_touch\n", "            FROM rilevations\n", "            GROUP BY \"GameID\", \"SetNumber\", \"ActionNumber\"\n", "        ) AS sottoquery\n", "        GROUP BY max_touch\n", "        ORDER BY max_touch;\n", "        \"\"\"\n", "\n", "    df = pd.read_sql_query(query, engine)\n", "    \n", "    df['max_touch'] = df['max_touch'].astype('Int32')\n", "\n", "    # Crea il grafico con plotly\n", "    fig = px.bar(df,\n", "                x=\"max_touch\", \n", "                y=\"num_azioni\", \n", "                title=\"Numero di azioni per massimo numero di tocchi\",\n", "                labels={'max_touch': 'Numero di tocchi', \n", "                        'num_azioni': 'Numero di azioni'},\n", "                #color=\"max_touch\",  # Puoi colorare i barre in base ai tocchi\n", "                text=\"num_azioni\")\n", "\n", "    # Mostra il grafico\n", "    fig.update_layout(height=600)\n", "    fig.show()\n", "\n", "\n", "def on_button_clicked(b):\n", "    with output:\n", "        clear_output(wait=True)\n", "        game_id = gameid_input.value.strip()\n", "        game_id = game_id if game_id != \"\" else None\n", "        plot_touch_distribution(game_id)\n", "\n", "run_button.on_click(on_button_clicked)\n", "\n", "\n", "display(widgets.VBox([gameid_input, run_button, output]))\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["Ora aggiusto manualmente i nomi e/o cognomi che a volte vengono scritti in modo diverso. Poi aggiusto anche eventuali ID che non sono (più avanti nel codice) corretti automaticamente."]}, {"cell_type": "code", "execution_count": 220, "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\nplayer_id_col = f\\'{prefisso}Player{i}ID\\'  #colonna = prefisso Player i ID (es HomePlayer2ID)\\nplayer_name_col = f\\'{prefisso}Player{i}Nome\\'  #colonna = prefisso Player i Nome (es HomePlayer4Nome)\\nplayer_surname_col = f\\'{prefisso}Player{i}Cognome\\'  #colonna = prefisso Player i Cognome (es HomePlayer5Cognome)\\nplayer_role_col = f\\'{prefisso}Player{i}Role\\'\\nplayer_shirt_col = f\\'{prefisso}Player{i}NumMaglia\\'\\nplayer_teamid_col = f\\'ID{prefisso}Team\\'\\nprint(player_id_col, player_name_col, player_surname_col, player_role_col, player_shirt_col, player_teamid_col)\\n\\n\\n#Aggiorniamo i cognomi (e direttamente anche gli ID)\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_surname_col} = \"Mergare<PERSON> hernandez\", {player_id_col} = \"151814\"                                        --setto in una volta sola sia il cognome giusto che l\\'ID\\nWHERE {player_id_col} IN (\"mer-osn-97\", \"MER-OSN-97\") AND {player_name_col} = \"Osniel lazaro\" AND {player_surname_col} = \"Mergarejo\";\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_surname_col} = \"Simon aties\", {player_id_col} = \"114422\"\\nWHERE {player_id_col} IN (\"SIM-ROB-87\", \"sim-rob-87\") AND {player_name_col} = \"Robertlandy\" AND {player_surname_col} = \"Simón\";\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_surname_col} = \"Abulubaba\", {player_id_col} = \"175749\"\\nWHERE {player_id_col} = \"ikh-\" AND {player_name_col} = \"Ahmed\" AND {player_surname_col} = \"Ikhbayri\";\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_surname_col} = \"Sen\", {player_id_col} = \"164913\"\\nWHERE ({player_id_col} = \"72601\" OR {player_id_col} = \"4631\") AND {player_name_col} = \"Klemen\" AND ({player_surname_col} = \"Sen\" OR {player_surname_col} = \"\\x8aEn\");   --forse devi mettere \\x8aen\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_surname_col} = \"Ebadipour ghara h.\"\\nWHERE {player_id_col} = \"eba-mil-93\" AND {player_name_col} = \"Milad\" AND {player_surname_col} = \"Ebadipour\";\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_surname_col} = \"Solé\", {player_id_col} = \"133730\"\\nWHERE {player_id_col} = \"50073\" AND {player_name_col} = \"Sebastian\" AND {player_surname_col} = \"Sole\\'\";\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_surname_col} = \"Täht\"\\nWHERE {player_id_col} = \"33400\" AND {player_name_col} = \"Robert\" AND {player_surname_col} = \"TÄht\";\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_surname_col} = \"Niklas\", {player_name_col} = \"Breilin\"\\nWHERE {player_id_col} = \"-332755\" AND {player_name_col} = \"Niklas aleski\" AND {player_surname_col} = \"Breiling\";\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_surname_col} = \"Roamy raul\", {player_name_col} = \"Alonso arce\"\\nWHERE {player_id_col} = \"alo-roa-97\" AND {player_name_col} = \"Roamy\" AND {player_surname_col} = \"Alonso\";\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_surname_col} = \"Ondes\"\\nWHERE {player_name_col} = \"Bertug\" AND {player_surname_col} = \"Ndes\";\\n\"\"\"\\ncursor.execute(query)\\n\\n\\n\\nconn.commit()\\n\\n\\n\\n#Aggiorniamo i nomi (e direttamente anche gli ID)\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_name_col} = \"Uros\", {player_id_col} = \"124926\"\\nWHERE {player_id_col} IN (\"kov-uro-93\", \"KOV-URO-93\") AND ({player_name_col} = \"Uro\\x9a\" OR {player_name_col} = \"UroÂš\") AND {player_surname_col} = \"Kovacevic\";\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_name_col} = \"Ionut alin\", {player_id_col} = \"182412\"\\nWHERE {player_id_col} = \"amb-ion-04\" AND {player_name_col} = \"Ionut\" AND {player_surname_col} = \"Ambrose\";\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_name_col} = \"Alessandro Alberto\", {player_id_col} = \"182409\"\\nWHERE {player_id_col} = \"bov-ale-04\" AND {player_name_col} = \"Alessandro\" AND {player_surname_col} = \"Bovolenta\";\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_name_col} = \"Barthélémy\", {player_id_col} = \"152494\"\\nWHERE {player_id_col} = \"chi-bar-98\" AND {player_name_col} = \"Barthelemy\" AND {player_surname_col} = \"Chinenyeze\";\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_name_col} = \"Massimo\", {player_id_col} = \"134596\"\\nWHERE {player_id_col} = \"28115\" AND {player_name_col} = \"Massimo vito\" AND {player_surname_col} = \"Colaci\";\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_name_col} = \"Luca samuele maria\", {player_id_col} = \"195201\"\\nWHERE {player_id_col} = \"col-luc-04\" AND {player_name_col} = \"Luca\" AND {player_surname_col} = \"Colombo\";\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_name_col} = \"Uladzislau\", {player_id_col} = \"163450\"\\nWHERE {player_id_col} = \"dav-vla\" AND {player_name_col} = \"Vlad\" AND {player_surname_col} = \"Davyskiba\";\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_name_col} = \"Gabriel\", {player_id_col} = \"152845\"\\nWHERE {player_id_col} = \"gar-gab-99\" AND {player_name_col} = \"Gabi\" AND {player_surname_col} = \"Garcia\";\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_name_col} = \"Jacopo\", {player_id_col} = \"180378\"\\nWHERE {player_id_col} = \"lar-iac\" AND {player_name_col} = \"Iacopo\" AND {player_surname_col} = \"Larizza\";\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_name_col} = \"Stephen timothy\", {player_id_col} = \"140061\"\\nWHERE {player_id_col} = \"maa-step-94\" AND {player_name_col} = \"Stephen\" AND {player_surname_col} = \"Maar\";\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_name_col} = \"Silvester\", {player_id_col} = \"188784\"\\nWHERE {player_id_col} = \"mei-sil\" AND {player_name_col} = \"Sil\" AND {player_surname_col} = \"Meijs\";\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_name_col} = \"Leonardo gabriel\", {player_id_col} = \"217157\"\\nWHERE {player_id_col} = \"00000\" AND {player_name_col} = \"Leonardo\" AND {player_surname_col} = \"Sandu\";\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_name_col} = \"Gioele adeola\", {player_id_col} = \"185882\"\\nWHERE {player_id_col} = \"tai-gio-06\" AND {player_name_col} = \"Gioele\" AND {player_surname_col} = \"Taiwo\";\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_name_col} = \"Jose miguel\", {player_surname_col} = \"Gutierrez suarez\"\\nWHERE {player_id_col} = \"gut-jos\" AND ({player_name_col} = \"Josè miguel\" OR {player_name_col} = \"Jose miguel\") AND ({player_surname_col} = \"Gutierrez\" OR {player_surname_col} = \"Gutierrez suarez\");\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_name_col} = \"Pasquale\", {player_id_col} = \"113218\"\\nWHERE {player_id_col} = \"sot-dan-79\" AND {player_name_col} = \"Daniele\" AND {player_surname_col} = \"Sottile\";\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_name_col} = \"Twan\"\\nWHERE ({player_id_col} = \"-399360\" OR {player_shirt_col} = 7) AND {player_name_col} = \"Twam\" AND {player_surname_col} = \"Wiltenburg\";\\n\"\"\"\\ncursor.execute(query)\\n\\n\\nconn.commit()\\n\\n\\n\\n#Sistemiamo quelli che hanno ID 0\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_id_col} = \"10001\"\\nWHERE {player_id_col} = \"00000\" AND {player_name_col} = \"Nicolò\" AND {player_surname_col} = \"Garello\";\\n\"\"\"\\ncursor.execute(query)\\n\\n\\nconn.commit()\\n\\n\\n\\n#Alcuni giocatori hanno più ID diversi, e li unifico se più righe hanno stesso nome AND cognome AND ((ruolo AND numeromaglia) OR (numeromaglia AND teamID))\\n#Alcuni però, non rispettano questa condizione perchè magari cambiano sia numero maglia che il Team, ma sono comunque la stessa persona, quindi dopo aver verificato che sono la stessa persona, li cambio a mano qui sotto\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_id_col} = \"164985\"\\nWHERE {player_name_col} = \"Aleksandar\" AND {player_surname_col} = \"Nedeljkovic\" AND ({player_role_col} = 4 OR {player_shirt_col} = 1 OR {player_teamid_col} = 8001);\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_id_col} = \"206628\"\\nWHERE {player_name_col} = \"Andrea\" AND {player_surname_col} = \"Innocenzi\" AND ({player_role_col} = 4 OR {player_shirt_col} = 90 OR {player_teamid_col} = 2);\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_id_col} = \"162081\"\\nWHERE {player_name_col} = \"Anton\" AND {player_surname_col} = \"Brehme\" AND ({player_role_col} = 4 OR {player_shirt_col} = 8 OR {player_teamid_col} = 1002);\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_id_col} = \"10231\"\\nWHERE {player_name_col} = \"Arshdeep\" AND {player_surname_col} = \"Dosanjh\" AND ({player_role_col} = 5 OR ({player_shirt_col} IN (14, 28)) OR {player_teamid_col} = 1);\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_id_col} = \"119804\"\\nWHERE {player_name_col} = \"Benjamin\" AND {player_surname_col} = \"Toniutti\" AND ({player_role_col} = 0 OR {player_role_col} = 5) AND {player_shirt_col} = 6 AND ({player_teamid_col} IN (4000, 9000));\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_id_col} = \"191774\"\\nWHERE {player_name_col} = \"Diego\" AND {player_surname_col} = \"Frascio\" AND {player_role_col} = 2 AND {player_shirt_col} = 4 AND {player_teamid_col} = 14;\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_id_col} = \"176172\"\\nWHERE {player_name_col} = \"Ferre\" AND {player_surname_col} = \"Reggers\" AND {player_role_col} = 2 AND {player_shirt_col} = 2 AND {player_teamid_col} = 8000;\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_id_col} = \"124846\"\\nWHERE {player_name_col} = \"Filippo\" AND {player_surname_col} = \"Lanza\" AND {player_role_col} = 2 AND {player_shirt_col} = 91 AND {player_teamid_col} = 6000;\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_id_col} = \"195866\"\\nWHERE {player_name_col} = \"Francesco\" AND {player_surname_col} = \"Bernardis\" AND {player_role_col} = 5 AND {player_shirt_col} = 6 AND {player_teamid_col} = 11;\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_id_col} = \"185882\"\\nWHERE {player_name_col} = \"Gioele adeola\" AND {player_surname_col} = \"Taiwo\" AND {player_role_col} = 4 AND {player_shirt_col} = 12 AND {player_teamid_col} = 14;\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_id_col} = \"150440\"\\nWHERE {player_name_col} = \"Jacopo\" AND {player_surname_col} = \"Massari\" AND {player_role_col} = 2 AND {player_shirt_col} = 6 AND {player_teamid_col} = 9;\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_id_col} = \"147473\"\\nWHERE {player_name_col} = \"Jordan\" AND {player_surname_col} = \"Ewert\" AND {player_role_col} = 2 AND {player_shirt_col} = 4 AND {player_teamid_col} = 7003;\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_id_col} = \"183276\"\\nWHERE {player_name_col} = \"Jordan\" AND {player_surname_col} = \"Schnitzer\" AND {player_role_col} = 4 AND {player_shirt_col} = 16 AND {player_teamid_col} = 7003;\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_id_col} = \"168417\"\\nWHERE {player_name_col} = \"Jose miguel\" AND {player_surname_col} = \"Gutierrez suarez\" AND {player_role_col} = 2 AND {player_shirt_col} = 22 AND {player_teamid_col} = 1;\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_id_col} = \"153395\"\\nWHERE {player_name_col} = \"Kamil\" AND {player_surname_col} = \"Semeniuk\" AND ({player_role_col} IN (0, 2)) AND {player_shirt_col} = 13 AND {player_teamid_col} = 9000;\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_id_col} = \"174531\"\\nWHERE {player_name_col} = \"Lorenzo\" AND {player_surname_col} = \"Sala\" AND {player_role_col} = 3 AND {player_shirt_col} = 91 AND {player_teamid_col} = 2;\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_id_col} = \"150435\"\\nWHERE {player_name_col} = \"Marco\" AND {player_surname_col} = \"Vitelli\" AND ({player_role_col} IN (0, 4)) AND {player_shirt_col} = 6 AND {player_teamid_col} = 2;\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_id_col} = \"133730\"\\nWHERE {player_name_col} = \"Sebastian\" AND {player_surname_col} = \"Solé\" AND {player_role_col} = 4 AND {player_shirt_col} = 11 AND {player_teamid_col} = 14;\\n\"\"\"\\ncursor.execute(query)\\n\\nquery = f\"\"\"\\nUPDATE Games\\nSET {player_id_col} = \"152349\"\\nWHERE {player_name_col} = \"Tobias\" AND {player_surname_col} = \"Krick\" AND {player_role_col} = 4 AND {player_shirt_col} = 21 AND {player_teamid_col} = 1002;\\n\"\"\"\\ncursor.execute(query)\\n\\n\\nconn.commit()\\n'"]}, "execution_count": 220, "metadata": {}, "output_type": "execute_result"}], "source": ["'''\n", "player_id_col = f'{prefisso}Player{i}ID'  #colonna = prefisso Player i ID (es HomePlayer2ID)\n", "player_name_col = f'{prefisso}Player{i}Nome'  #colonna = prefisso Player i Nome (es HomePlayer4Nome)\n", "player_surname_col = f'{prefisso}Player{i}Cognome'  #colonna = prefisso Player i Cognome (es HomePlayer5Cognome)\n", "player_role_col = f'{prefisso}Player{i}Role'\n", "player_shirt_col = f'{prefisso}Player{i}NumMaglia'\n", "player_teamid_col = f'ID{prefisso}Team'\n", "print(player_id_col, player_name_col, player_surname_col, player_role_col, player_shirt_col, player_teamid_col)\n", "\n", "\n", "#Aggiorniamo i cognomi (e direttamente anche gli ID)\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_surname_col} = \"<PERSON><PERSON><PERSON><PERSON>\", {player_id_col} = \"151814\"                                        --setto in una volta sola sia il cognome giusto che l'ID\n", "WHERE {player_id_col} IN (\"mer-osn-97\", \"MER-OSN-97\") AND {player_name_col} = \"Osniel lazaro\" AND {player_surname_col} = \"Mergarejo\";\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_surname_col} = \"<PERSON>\", {player_id_col} = \"114422\"\n", "WHERE {player_id_col} IN (\"SIM-ROB-87\", \"sim-rob-87\") AND {player_name_col} = \"<PERSON><PERSON><PERSON>\" AND {player_surname_col} = \"Sim<PERSON>\";\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_surname_col} = \"Abulub<PERSON>\", {player_id_col} = \"175749\"\n", "WHERE {player_id_col} = \"ikh-\" AND {player_name_col} = \"<PERSON>\" AND {player_surname_col} = \"<PERSON><PERSON><PERSON><PERSON>\";\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_surname_col} = \"<PERSON>\", {player_id_col} = \"164913\"\n", "WHERE ({player_id_col} = \"72601\" OR {player_id_col} = \"4631\") AND {player_name_col} = \"<PERSON>lemen\" AND ({player_surname_col} = \"Sen\" OR {player_surname_col} = \"En\");   --forse devi mettere en\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_surname_col} = \"Ebad<PERSON>our ghara h.\"\n", "WHERE {player_id_col} = \"eba-mil-93\" AND {player_name_col} = \"Milad\" AND {player_surname_col} = \"Ebad<PERSON><PERSON>\";\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_surname_col} = \"<PERSON><PERSON>\", {player_id_col} = \"133730\"\n", "WHERE {player_id_col} = \"50073\" AND {player_name_col} = \"<PERSON>\" AND {player_surname_col} = \"Sole'\";\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_surname_col} = \"Täht\"\n", "WHERE {player_id_col} = \"33400\" AND {player_name_col} = \"<PERSON>\" AND {player_surname_col} = \"TÄht\";\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_surname_col} = \"<PERSON><PERSON>\", {player_name_col} = \"<PERSON><PERSON><PERSON><PERSON>\"\n", "WHERE {player_id_col} = \"-332755\" AND {player_name_col} = \"<PERSON><PERSON>\" AND {player_surname_col} = \"Breiling\";\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_surname_col} = \"Roamy raul\", {player_name_col} = \"<PERSON>\"\n", "WHERE {player_id_col} = \"alo-roa-97\" AND {player_name_col} = \"Roamy\" AND {player_surname_col} = \"<PERSON>\";\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_surname_col} = \"Ondes\"\n", "WHERE {player_name_col} = \"<PERSON><PERSON>\" AND {player_surname_col} = \"Ndes\";\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "\n", "\n", "conn.commit()\n", "\n", "\n", "\n", "#A<PERSON>rn<PERSON>o i nomi (e direttamente anche gli ID)\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_name_col} = \"Uros\", {player_id_col} = \"124926\"\n", "WHERE {player_id_col} IN (\"kov-uro-93\", \"KOV-URO-93\") AND ({player_name_col} = \"Uro\" OR {player_name_col} = \"<PERSON><PERSON>\") AND {player_surname_col} = \"<PERSON><PERSON><PERSON><PERSON>\";\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_name_col} = \"<PERSON><PERSON> alin\", {player_id_col} = \"182412\"\n", "WHERE {player_id_col} = \"amb-ion-04\" AND {player_name_col} = \"Ionut\" AND {player_surname_col} = \"<PERSON>\";\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_name_col} = \"<PERSON>\", {player_id_col} = \"182409\"\n", "WHERE {player_id_col} = \"bov-ale-04\" AND {player_name_col} = \"<PERSON>\" AND {player_surname_col} = \"<PERSON><PERSON><PERSON><PERSON>\";\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_name_col} = \"<PERSON><PERSON><PERSON><PERSON><PERSON>\", {player_id_col} = \"152494\"\n", "WHERE {player_id_col} = \"chi-bar-98\" AND {player_name_col} = \"<PERSON>helemy\" AND {player_surname_col} = \"Chinenyeze\";\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_name_col} = \"<PERSON><PERSON>\", {player_id_col} = \"134596\"\n", "WHERE {player_id_col} = \"28115\" AND {player_name_col} = \"Massimo vito\" AND {player_surname_col} = \"<PERSON><PERSON>\";\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_name_col} = \"Luca samuele maria\", {player_id_col} = \"195201\"\n", "WHERE {player_id_col} = \"col-luc-04\" AND {player_name_col} = \"<PERSON>\" AND {player_surname_col} = \"Colombo\";\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_name_col} = \"Uladzislau\", {player_id_col} = \"163450\"\n", "WHERE {player_id_col} = \"dav-vla\" AND {player_name_col} = \"<PERSON>\" AND {player_surname_col} = \"Davy<PERSON><PERSON>\";\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_name_col} = \"<PERSON>\", {player_id_col} = \"152845\"\n", "WHERE {player_id_col} = \"gar-gab-99\" AND {player_name_col} = \"<PERSON><PERSON>\" AND {player_surname_col} = \"<PERSON>\";\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_name_col} = \"<PERSON><PERSON><PERSON>\", {player_id_col} = \"180378\"\n", "WHERE {player_id_col} = \"lar-iac\" AND {player_name_col} = \"<PERSON><PERSON><PERSON>\" AND {player_surname_col} = \"Larizza\";\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_name_col} = \"<PERSON> timothy\", {player_id_col} = \"140061\"\n", "WHERE {player_id_col} = \"maa-step-94\" AND {player_name_col} = \"<PERSON>\" AND {player_surname_col} = \"Maar\";\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_name_col} = \"<PERSON><PERSON><PERSON>\", {player_id_col} = \"188784\"\n", "WHERE {player_id_col} = \"mei-sil\" AND {player_name_col} = \"Sil\" AND {player_surname_col} = \"<PERSON>j<PERSON>\";\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_name_col} = \"Leonardo gabriel\", {player_id_col} = \"217157\"\n", "WHERE {player_id_col} = \"00000\" AND {player_name_col} = \"<PERSON>\" AND {player_surname_col} = \"<PERSON><PERSON>\";\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_name_col} = \"<PERSON><PERSON><PERSON> adeola\", {player_id_col} = \"185882\"\n", "WHERE {player_id_col} = \"tai-gio-06\" AND {player_name_col} = \"<PERSON><PERSON><PERSON>\" AND {player_surname_col} = \"<PERSON><PERSON>\";\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_name_col} = \"<PERSON>\", {player_surname_col} = \"<PERSON><PERSON><PERSON><PERSON>\"\n", "WHERE {player_id_col} = \"gut-jos\" AND ({player_name_col} = \"<PERSON><PERSON><PERSON>\" OR {player_name_col} = \"<PERSON> miguel\") AND ({player_surname_col} = \"<PERSON><PERSON><PERSON>rez\" OR {player_surname_col} = \"<PERSON><PERSON><PERSON><PERSON> suarez\");\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_name_col} = \"Pas<PERSON><PERSON>\", {player_id_col} = \"113218\"\n", "WHERE {player_id_col} = \"sot-dan-79\" AND {player_name_col} = \"<PERSON><PERSON>\" AND {player_surname_col} = \"Sottile\";\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_name_col} = \"Twan\"\n", "WHERE ({player_id_col} = \"-399360\" OR {player_shirt_col} = 7) AND {player_name_col} = \"Twam\" AND {player_surname_col} = \"Wiltenburg\";\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "\n", "conn.commit()\n", "\n", "\n", "\n", "#Sistemiamo quelli che hanno ID 0\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_id_col} = \"10001\"\n", "WHERE {player_id_col} = \"00000\" AND {player_name_col} = \"Nico<PERSON><PERSON>\" AND {player_surname_col} = \"<PERSON><PERSON><PERSON>\";\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "\n", "conn.commit()\n", "\n", "\n", "\n", "#Alcuni giocatori hanno più ID diversi, e li unifico se più righe hanno stesso nome AND cognome AND ((ruolo AND numeromaglia) OR (numeromaglia AND teamID))\n", "#<PERSON><PERSON><PERSON>, non rispettano questa condizione perchè magari cambiano sia numero maglia che il Team, ma sono comunque la stessa persona, quindi dopo aver verificato che sono la stessa persona, li cambio a mano qui sotto\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_id_col} = \"164985\"\n", "WHERE {player_name_col} = \"<PERSON><PERSON><PERSON><PERSON>\" AND {player_surname_col} = \"<PERSON><PERSON><PERSON><PERSON><PERSON>\" AND ({player_role_col} = 4 OR {player_shirt_col} = 1 OR {player_teamid_col} = 8001);\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_id_col} = \"206628\"\n", "WHERE {player_name_col} = \"<PERSON>\" AND {player_surname_col} = \"Innocenzi\" AND ({player_role_col} = 4 OR {player_shirt_col} = 90 OR {player_teamid_col} = 2);\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_id_col} = \"162081\"\n", "WHERE {player_name_col} = \"<PERSON>\" AND {player_surname_col} = \"<PERSON><PERSON>hme\" AND ({player_role_col} = 4 OR {player_shirt_col} = 8 OR {player_teamid_col} = 1002);\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_id_col} = \"10231\"\n", "WHERE {player_name_col} = \"<PERSON>rsh<PERSON><PERSON>\" AND {player_surname_col} = \"Dosanjh\" AND ({player_role_col} = 5 OR ({player_shirt_col} IN (14, 28)) OR {player_teamid_col} = 1);\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_id_col} = \"119804\"\n", "WHERE {player_name_col} = \"<PERSON>\" AND {player_surname_col} = \"<PERSON><PERSON><PERSON>\" AND ({player_role_col} = 0 OR {player_role_col} = 5) AND {player_shirt_col} = 6 AND ({player_teamid_col} IN (4000, 9000));\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_id_col} = \"191774\"\n", "WHERE {player_name_col} = \"<PERSON>\" AND {player_surname_col} = \"<PERSON><PERSON><PERSON>\" AND {player_role_col} = 2 AND {player_shirt_col} = 4 AND {player_teamid_col} = 14;\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_id_col} = \"176172\"\n", "WHERE {player_name_col} = \"<PERSON><PERSON>\" AND {player_surname_col} = \"Reggers\" AND {player_role_col} = 2 AND {player_shirt_col} = 2 AND {player_teamid_col} = 8000;\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_id_col} = \"124846\"\n", "WHERE {player_name_col} = \"<PERSON><PERSON><PERSON>\" AND {player_surname_col} = \"Lanza\" AND {player_role_col} = 2 AND {player_shirt_col} = 91 AND {player_teamid_col} = 6000;\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_id_col} = \"195866\"\n", "WHERE {player_name_col} = \"<PERSON>\" AND {player_surname_col} = \"<PERSON><PERSON>\" AND {player_role_col} = 5 AND {player_shirt_col} = 6 AND {player_teamid_col} = 11;\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_id_col} = \"185882\"\n", "WHERE {player_name_col} = \"<PERSON><PERSON><PERSON> adeola\" AND {player_surname_col} = \"<PERSON><PERSON>\" AND {player_role_col} = 4 AND {player_shirt_col} = 12 AND {player_teamid_col} = 14;\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_id_col} = \"150440\"\n", "WHERE {player_name_col} = \"<PERSON><PERSON><PERSON>\" AND {player_surname_col} = \"<PERSON><PERSON>\" AND {player_role_col} = 2 AND {player_shirt_col} = 6 AND {player_teamid_col} = 9;\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_id_col} = \"147473\"\n", "WHERE {player_name_col} = \"Jordan\" AND {player_surname_col} = \"Ewert\" AND {player_role_col} = 2 AND {player_shirt_col} = 4 AND {player_teamid_col} = 7003;\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_id_col} = \"183276\"\n", "WHERE {player_name_col} = \"Jordan\" AND {player_surname_col} = \"<PERSON><PERSON><PERSON>\" AND {player_role_col} = 4 AND {player_shirt_col} = 16 AND {player_teamid_col} = 7003;\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_id_col} = \"168417\"\n", "WHERE {player_name_col} = \"<PERSON>\" AND {player_surname_col} = \"<PERSON><PERSON><PERSON><PERSON> suare<PERSON>\" AND {player_role_col} = 2 AND {player_shirt_col} = 22 AND {player_teamid_col} = 1;\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_id_col} = \"153395\"\n", "WHERE {player_name_col} = \"<PERSON><PERSON><PERSON>\" AND {player_surname_col} = \"<PERSON><PERSON><PERSON><PERSON>\" AND ({player_role_col} IN (0, 2)) AND {player_shirt_col} = 13 AND {player_teamid_col} = 9000;\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_id_col} = \"174531\"\n", "WHERE {player_name_col} = \"<PERSON>\" AND {player_surname_col} = \"<PERSON><PERSON>\" AND {player_role_col} = 3 AND {player_shirt_col} = 91 AND {player_teamid_col} = 2;\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_id_col} = \"150435\"\n", "WHERE {player_name_col} = \"<PERSON>\" AND {player_surname_col} = \"Vitelli\" AND ({player_role_col} IN (0, 4)) AND {player_shirt_col} = 6 AND {player_teamid_col} = 2;\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_id_col} = \"133730\"\n", "WHERE {player_name_col} = \"<PERSON>\" AND {player_surname_col} = \"<PERSON>é\" AND {player_role_col} = 4 AND {player_shirt_col} = 11 AND {player_teamid_col} = 14;\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "query = f\"\"\"\n", "UPDATE Games\n", "SET {player_id_col} = \"152349\"\n", "WHERE {player_name_col} = \"<PERSON>\" AND {player_surname_col} = \"<PERSON><PERSON>\" AND {player_role_col} = 4 AND {player_shirt_col} = 21 AND {player_teamid_col} = 1002;\n", "\"\"\"\n", "cursor.execute(query)\n", "\n", "\n", "conn.commit()\n", "'''"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["Ora che ho finito, posso rimuovere le colonne HomeAway, Year, <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 221, "metadata": {}, "outputs": [], "source": ["conn.rollback()"]}, {"cell_type": "code", "execution_count": 222, "metadata": {}, "outputs": [], "source": ["cur.execute(\"\"\"\n", "ALTER TABLE players_each_game\n", "DROP COLUMN IF EXISTS\"HomeAway\",\n", "DROP COLUMN IF EXISTS \"Year\",\n", "DROP COLUMN IF EXISTS \"Annata\"\n", "\"\"\")\n", "conn.commit()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["SU DBeaver fai un check di integrità per sicurezza"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": ".venv (3.13.2)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 0}