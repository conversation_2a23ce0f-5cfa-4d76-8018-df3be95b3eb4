"""
Modulo per caricare i dati dal database
"""
import pandas as pd
from sqlalchemy import create_engine
import numpy as np

np.random.seed(0)

def load_data():
    """
    Carica tutti i dataframe dal database e li restituisce in un dizionario
    """
    # Connessione al database
    engine = create_engine('postgresql+psycopg://postgres:AcquaLevissima1@localhost:5432/db_modena')
    
    data_dict = {}
    
    try:
        # Carica df_games
        data_dict['df_games'] = pd.read_sql_query("""
            SELECT * FROM games_view
        """, engine)
        data_dict['df_games']['Date'] = pd.to_datetime(data_dict['df_games']['Date'], dayfirst=True)
        data_dict['df_games']['DateStr'] = data_dict['df_games']['Date'].dt.strftime('%d-%m-%Y')
        
        # Carica df_rilevations
        data_dict['df_rilevations'] = pd.read_sql_query("""
            SELECT * FROM rilevations_libero_view LIMIT 50000
        """, engine)


        # Carica df_players
        data_dict['df_players'] = pd.read_sql_query("""
            SELECT * FROM players_latest
        """, engine)
        
        # Carica df_players_each_season
        data_dict['df_players_each_season'] = pd.read_sql_query("""
            SELECT * FROM players_each_season_view
        """, engine)

        # Carica df_players_each_month
        data_dict['df_players_each_month'] = pd.read_sql_query("""
            SELECT * FROM players_each_month_view
        """, engine)
        
        # Carica df_players_each_game
        data_dict['df_players_each_game'] = pd.read_sql_query("""
            SELECT * FROM players_each_game_view
        """, engine)
        

        # Carica df_teams
        data_dict['df_teams'] = pd.read_sql_query("""
            SELECT * FROM teams
        """, engine)
        
        # Carica df_teams_each_season
        data_dict['df_teams_each_season'] = pd.read_sql_query("""
            SELECT * FROM teams_each_season_view
        """, engine)
        
        # Carica df_teams_each_month
        data_dict['df_teams_each_month'] = pd.read_sql_query("""
            SELECT * FROM teams_each_month_view
        """, engine)

        # Carica df_teams_each_game
        data_dict['df_teams_each_game'] = pd.read_sql_query("""
            SELECT * FROM teams_each_game_view
        """, engine)


        
        print("Dati caricati con successo!")
        
        # Stampa informazioni sui dati caricati
        for key, df in data_dict.items():
            print(f"{key}: {df.shape}")
        
            
    except Exception as e:
        print(f"Errore nel caricamento dei dati: {e}")
        # Carica dati di fallback se necessario
        data_dict = load_fallback_data()
    
    finally:
        engine.dispose()
    
    return data_dict

def load_fallback_data():
    """
    Carica dati di fallback se il database non è disponibile
    """
    print("Caricamento dati di fallback...")
    
    # Qui puoi caricare dati da file CSV o creare dati dummy
    data_dict = {
        'df_games': pd.DataFrame({
            'GameID': range(1, 11),
            'Date': pd.date_range('2023-01-01', periods=10),
            'Annata': [2023] * 10,
            'Campionato': ['Serie A'] * 10,
            'Competition': ['Regular Season'] * 10,
            'HomeTeamNameShort': ['Team' + str(i) for i in range(1, 11)],
            'VisitorTeamNameShort': ['Team' + str(i+10) for i in range(1, 11)],
            'HomeTeamSetWon': [3] * 10,
            'VisitorTeamSetWon': [1] * 10,
            'IDHomeTeam': range(1, 11),
            'IDVisitorTeam': range(11, 21),
            'DateStr': pd.date_range('2023-01-01', periods=10).strftime('%d-%m-%Y')
        }),
        'df_players': pd.DataFrame({
            'PlayerID': range(1, 21),
            'Nome': ['Player' + str(i) for i in range(1, 21)],
            'Cognome': ['Surname' + str(i) for i in range(1, 21)],
            'RuoloCalc': np.random.choice([1, 2, 3, 4, 5], 20)
        }),
        'df_teams': pd.DataFrame({
            'TeamID': range(1, 21),
            'TeamNameShort': ['Team' + str(i) for i in range(1, 21)]
        }),
        'df_rilevations': pd.DataFrame(),
        'df_players_each_game': pd.DataFrame(),
        'df_players_each_season': pd.DataFrame(),
        'df_teams_each_game': pd.DataFrame()
    }
    
    return data_dict

def get_data_info(data_dict):
    """
    Restituisce informazioni sui dati caricati
    """
    info = {}
    for key, df in data_dict.items():
        if isinstance(df, pd.DataFrame):
            info[key] = {
                'rows': len(df),
                'columns': len(df.columns),
                'memory_usage': df.memory_usage(deep=True).sum(),
                'dtypes': df.dtypes.to_dict()
            }
    return info