# Install with pip install firecrawl-py
from firecrawl import Firecrawl<PERSON>pp
import json
from pathlib import Path
import os

# Directory per salvare i dati
DATA_DIR = Path(os.path.expanduser("~")) / "Documents" / "ModenaVolley" / "VolleyballScraper" / "data"
DATA_DIR.mkdir(parents=True, exist_ok=True)

app = FirecrawlApp(api_key='fc-64e4f21960c74bce9a0eecd51b477c28')

# Esegui il crawling
crawl_result = app.crawl_url('https://volleybox.net/it', params={
    'urlFilters': [
        r'^https://volleybox\.net/it/.*/clubs$'  # Regex per matchare URLs che finiscono con /clubs
    ],
    'limit': 10,
    'maxDepth': 3,
    'scrapeOptions': {
        'formats': ['markdown', 'html']  # Aggiungiamo anche HTML per avere più dati
    }
})

# Salva il risultato del crawling
result_file = DATA_DIR / 'volleyball_clubs_crawl.json'
with open(result_file, 'w', encoding='utf-8') as f:
    json.dump(crawl_result, f, indent=2, ensure_ascii=False)

print(f"Risultati salvati in: {result_file}")

# Stampa alcune statistiche
print(f"Numero di URL trovati: {len(crawl_result.get('urls', []))}")



