from bs4 import BeautifulSoup
import requests
import random
import time

def find_volleybox_url(name, surname):
    def try_google_search():
        query = f"site:volleybox.net/it {name} {surname}"
        url = "https://www.google.com/search"
        
        headers = {
            "User-Agent": random.choice([
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.109 Safari/537.36",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.54 Safari/537.36"
            ]),
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5"
        }
        
        parameters = {'q': query}

        try:
            print("Trying Google search...")
            response = requests.get(url, headers=headers, params=parameters)
            response.raise_for_status()
            
            if "detected unusual traffic" in response.text.lower() or "captcha" in response.text.lower():
                print("Google detected automated traffic, switching to DuckDuckGo...")
                return None
                
            soup = BeautifulSoup(response.text, "html.parser")
            links = soup.find_all('a')
            
            for link in links:
                href = link.get('href', '')
                if href.startswith('http') and 'volleybox.net/it' in href:
                    if 'url?q=' in href:
                        actual_url = href.split('url?q=')[1].split('&')[0]
                    else:
                        actual_url = href
                    
                    if 'volleybox.net/it' in actual_url:
                        print(f"Found URL via Google: {actual_url}")
                        return actual_url
            
            return None
            
        except Exception as e:
            print(f"Google search failed: {e}")
            return None

    def try_duckduckgo_search():
        query = f"site:volleybox.net/it {name} {surname}"
        url = f"https://html.duckduckgo.com/html/?q={query.replace(' ', '+')}"
        
        headers = {
            "User-Agent": random.choice([
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.109 Safari/537.36",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.54 Safari/537.36"
            ])
        }

        try:
            print("Trying DuckDuckGo search...")
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, "html.parser")
            
            for result in soup.select(".result__url"):
                url_text = result.text.strip()
                if not url_text.startswith('http'):
                    url_text = "https://" + url_text
                
                if 'volleybox.net/it' in url_text:
                    print(f"Found URL via DuckDuckGo: {url_text}")
                    return url_text
            
            return None
            
        except Exception as e:
            print(f"DuckDuckGo search failed: {e}")
            return None


    # Try all methods in sequence
    time.sleep(random.uniform(2, 8))
    
    # Try Google first
    url = try_google_search()
    if url:
        return url
        
    # If Google fails, try DuckDuckGo
    url = try_duckduckgo_search()
    if url:
        return url
        
    print("No Volleybox URL found through any method")
    return None


# Example usage
name = "Simone"
surname = "Giannelli"
result = find_volleybox_url(name, surname)

if result:
    print(f"\nFinal result - Found URL: {result}")
else:
    print("\nFinal result: No URL found")



    
    