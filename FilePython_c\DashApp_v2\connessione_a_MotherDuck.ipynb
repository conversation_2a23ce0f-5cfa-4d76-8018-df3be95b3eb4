import duckdb

token_database_motherduck = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************.Dl_50BGVlT2f1hIIIWyJoHe-aSALpFZFx911eo5kyv4"

con = duckdb.connect('md:DatabaseModena', config={"motherduck_token": token_database_motherduck})


con.execute("SHOW DATABASES").fetchall()

df = con.execute('SELECT * FROM teams_each_season_view').fetchdf()
df

df_rilevations = con.execute('SELECT * FROM rilevations').fetchdf()
df_rilevations.shape

#Prendo le righe di df_rilevations che hanno "Foundamental" = 'S'
df_rilevations_S = df_rilevations[df_rilevations['Foundamental'] == 'S']