#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Volleyball Scraper - Programma per estrarre dati dei giocatori da volleybox.net
Versione che cerca gli URL in url_giocatori.txt basandosi sui nomi normalizzati
"""

import sys
import re
import unicodedata
import time
import json
import random
import os
import requests
import subprocess
from bs4 import BeautifulSoup
from pathlib import Path
from urllib.parse import urlparse

import numpy as np
import pandas as pd
import polars as pl
from sqlalchemy import create_engine

# Directory per salvare i cookie e i dati
DATA_DIR = Path(os.path.expanduser("~")) / "Documents" / "ModenaVolley" / "VolleyballScraper" / "data"
COOKIES_FILE = DATA_DIR / 'cookies.json'
RESULTS_DIR = DATA_DIR / 'results'

# Assicurati che le directory esistano
DATA_DIR.mkdir(parents=True, exist_ok=True)
RESULTS_DIR.mkdir(parents=True, exist_ok=True)

def normalize_name(name):
    """
    Normalizza il nome per la ricerca.
    Rimuove accenti, converte in minuscolo.
    """
    # Rimuovi accenti e caratteri speciali
    name = unicodedata.normalize('NFKD', name).encode('ASCII', 'ignore').decode('utf-8')
    # Converti in minuscolo
    name = name.lower()
    return name

def download_with_wget(url, output_file="downloaded_page.html"):
    """
    Scarica una pagina web usando wget con impostazioni per evitare il rilevamento come bot.

    Args:
        url (str): URL della pagina da scaricare
        output_file (str): Nome del file in cui salvare la pagina scaricata

    Returns:
        bool: True se il download è riuscito, False altrimenti
    """
    try:
        # Configura gli header e le opzioni per wget
        user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36"

        # Costruisci il comando wget con varie opzioni per evitare il rilevamento
        wget_command = [
            "wget",
            "--quiet",                          # Non mostrare l'output
            "--no-check-certificate",           # Non controllare il certificato SSL
            "--random-wait",                    # Aggiungi un ritardo casuale tra le richieste
            "--tries=3",                        # Numero di tentativi
            "--timeout=30",                     # Timeout in secondi
            "--wait=2",                         # Attesa tra i tentativi
            "--user-agent=" + user_agent,       # User agent personalizzato
            "--header=Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "--header=Accept-Language: it-IT,it;q=0.8,en-US;q=0.5,en;q=0.3",
            "--header=DNT: 1",
            "--header=Connection: keep-alive",
            "--header=Upgrade-Insecure-Requests: 1",
            "--header=Cache-Control: max-age=0",
            "-O", output_file,                  # File di output
            url                                 # URL da scaricare
        ]

        print(f"Scaricando {url} con wget...")

        # Esegui il comando wget
        result = subprocess.run(wget_command, capture_output=True, text=True)

        # Verifica se il download è riuscito
        if result.returncode == 0 and os.path.exists(output_file) and os.path.getsize(output_file) > 0:
            print(f"Download completato con successo: {output_file}")
            return True
        else:
            print(f"Errore durante il download: {result.stderr}")
            return False

    except Exception as e:
        print(f"Eccezione durante il download con wget: {str(e)}")
        return False

def extract_player_data_from_html(html_content):
    """
    Estrae i dati del giocatore da una pagina HTML usando BeautifulSoup.

    Args:
        html_content (str): Contenuto HTML della pagina del giocatore

    Returns:
        dict: Dizionario con i dati del giocatore
    """
    player_info = {
        "nazionalità": None,
        "posizione": None,
        "data_di_nascita": None,
        "altezza": None,
        "peso": None,
        "schiacciata": None,
        "muro": None,
        "mano_dominante": None,
        "errore": None
    }

    try:
        # Analizza l'HTML con BeautifulSoup
        soup = BeautifulSoup(html_content, 'html.parser')

        # Cerca il div con classe "new_box pRelative" che contiene i dati del giocatore
        player_data_div = soup.find('div', class_='new_box pRelative')

        if player_data_div:
            print("Div 'new_box pRelative' trovato con BeautifulSoup")

            # Cerca tutte le coppie dt/dd all'interno del div
            dts = player_data_div.find_all('dt', class_='info-header')
            dds = player_data_div.find_all('dd', class_='info-data')

            if dts and dds:
                print(f"Trovati {len(dts)} elementi dt e {len(dds)} elementi dd")

                for i in range(min(len(dts), len(dds))):
                    label = dts[i].get_text().strip().lower()
                    value = dds[i].get_text().strip()

                    print(f"Coppia {i}: '{label}' = '{value}'")

                    if "nazionalità" in label:
                        player_info["nazionalità"] = value
                    elif "posizione" in label:
                        player_info["posizione"] = value
                    elif "data di nascita" in label:
                        player_info["data_di_nascita"] = value
                    elif "altezza" in label:
                        player_info["altezza"] = value
                    elif "peso" in label:
                        player_info["peso"] = value
                    elif "schiacciata" in label:
                        player_info["schiacciata"] = value
                    elif "muro" in label:
                        player_info["muro"] = value
                    elif "mano dominante" in label:
                        player_info["mano_dominante"] = value
            else:
                print("Nessun elemento dt/dd trovato nel div")
        else:
            print("Div 'new_box pRelative' non trovato con BeautifulSoup")

            # Prova a cercare in modo più generico
            print("Tentativo di ricerca più generico...")

            # Cerca qualsiasi div che potrebbe contenere informazioni sul giocatore
            info_divs = soup.find_all('div', class_=lambda c: c and ('info' in c.lower() or 'player' in c.lower() or 'data' in c.lower()))

            if info_divs:
                print(f"Trovati {len(info_divs)} div potenzialmente rilevanti")

                for div in info_divs:
                    # Cerca coppie di etichette e valori
                    labels = div.find_all(['dt', 'th', 'strong', 'label'])
                    values = div.find_all(['dd', 'td', 'span'])

                    if labels and values and len(labels) == len(values):
                        print(f"Trovate {len(labels)} coppie etichetta-valore")

                        for i in range(len(labels)):
                            label = labels[i].get_text().strip().lower()
                            value = values[i].get_text().strip()

                            print(f"Coppia {i}: '{label}' = '{value}'")

                            if "nazionalità" in label or "nationality" in label:
                                player_info["nazionalità"] = value
                            elif "posizione" in label or "position" in label:
                                player_info["posizione"] = value
                            elif "data di nascita" in label or "birth" in label:
                                player_info["data_di_nascita"] = value
                            elif "altezza" in label or "height" in label:
                                player_info["altezza"] = value
                            elif "peso" in label or "weight" in label:
                                player_info["peso"] = value
                            elif "schiacciata" in label or "spike" in label:
                                player_info["schiacciata"] = value
                            elif "muro" in label or "block" in label:
                                player_info["muro"] = value
                            elif "mano dominante" in label or "hand" in label:
                                player_info["mano_dominante"] = value

        # Verifica se abbiamo trovato almeno un dato
        found_data = any([
            player_info["nazionalità"],
            player_info["posizione"],
            player_info["data_di_nascita"],
            player_info["altezza"],
            player_info["peso"],
            player_info["schiacciata"],
            player_info["muro"],
            player_info["mano_dominante"]
        ])

        if not found_data:
            print("ATTENZIONE: Nessun dato trovato per il giocatore!")

            # Salva l'HTML per debug
            with open("debug_page.html", "w", encoding="utf-8") as f:
                f.write(html_content)
            print("HTML della pagina salvato in debug_page.html per analisi")
        else:
            print("Dati estratti con successo!")

    except Exception as e:
        player_info["errore"] = f"Errore nell'estrazione dati: {str(e)}"
        print(f"Eccezione durante l'estrazione: {str(e)}")

        # Salva l'HTML per debug in caso di errore
        try:
            with open("error_page.html", "w", encoding="utf-8") as f:
                f.write(html_content)
            print("HTML della pagina con errore salvato in error_page.html")
        except:
            pass

    return player_info

def get_player_info_with_wget(url):
    """
    Estrae le informazioni di un giocatore da un URL di volleybox.net usando wget.

    Args:
        url (str): URL del giocatore

    Returns:
        dict: Dizionario contenente le informazioni del giocatore
    """
    # Inizializza il dizionario per i risultati
    player_info = {
        "url": url,
        "nazionalità": None,
        "posizione": None,
        "data_di_nascita": None,
        "altezza": None,
        "peso": None,
        "schiacciata": None,
        "muro": None,
        "mano_dominante": None,
        "errore": None
    }

    # Funzione per aggiornare lo stato
    def update_status(message):
        print(message)

    try:
        # Modifica l'URL per andare alla pagina principale del giocatore invece che alla pagina dei club
        # L'URL originale è del tipo https://volleybox.net/it/nome-cognome-pXXXXX/clubs
        # Vogliamo https://volleybox.net/it/nome-cognome-pXXXXX
        player_url = url.replace("/clubs", "")
        update_status(f"URL modificato: {player_url}")

        # Nome del file temporaneo per salvare la pagina
        temp_file = f"player_{player_url.split('/')[-1]}.html"

        # Scarica la pagina con wget
        download_success = download_with_wget(player_url, temp_file)

        if download_success:
            update_status(f"Pagina scaricata con successo in {temp_file}")

            # Leggi il contenuto del file
            try:
                with open(temp_file, 'r', encoding='utf-8') as f:
                    html_content = f.read()

                # Verifica se la pagina contiene un messaggio di errore 404
                if "404" in html_content or "non trovato" in html_content.lower():
                    player_info["errore"] = "Giocatore non trovato (errore 404)"
                    update_status("Errore 404: Giocatore non trovato")
                    return player_info

                # Verifica se siamo stati bloccati da Cloudflare
                if "Cloudflare" in html_content or "challenge" in html_content or "Ci siamo quasi" in html_content:
                    player_info["errore"] = "Bloccato da Cloudflare"
                    update_status("Rilevato challenge Cloudflare. Prova a usare un proxy o a cambiare IP.")

                    # Salva l'HTML per debug
                    with open("cloudflare_challenge.html", "w", encoding="utf-8") as f:
                        f.write(html_content)
                    update_status("HTML del challenge Cloudflare salvato in cloudflare_challenge.html")

                    return player_info

                # Estrai le informazioni dalla pagina HTML
                update_status("Estraendo le informazioni del giocatore...")
                extracted_info = extract_player_data_from_html(html_content)

                # Aggiorna il dizionario con le informazioni estratte
                for key, value in extracted_info.items():
                    player_info[key] = value

            except Exception as e:
                player_info["errore"] = f"Errore durante la lettura del file: {str(e)}"
                update_status(f"Errore durante la lettura del file: {str(e)}")
        else:
            player_info["errore"] = "Errore durante il download della pagina"
            update_status("Errore durante il download della pagina")

        # Pulizia: rimuovi il file temporaneo
        try:
            if os.path.exists(temp_file):
                os.remove(temp_file)
                update_status(f"File temporaneo {temp_file} rimosso")
        except Exception as e:
            update_status(f"Errore durante la rimozione del file temporaneo: {str(e)}")

    except Exception as e:
        player_info["errore"] = f"Errore durante lo scraping: {str(e)}"
        update_status(f"Errore durante lo scraping: {str(e)}")

    update_status("Estrazione completata")
    return player_info

def get_player_info_from_url(url):
    """
    Estrae le informazioni di un giocatore da un URL di volleybox.net.
    Questa funzione è un wrapper per get_player_info_with_wget.

    Args:
        url (str): URL del giocatore

    Returns:
        dict: Dizionario contenente le informazioni del giocatore
    """
    return get_player_info_with_wget(url)

def find_player_url(nome, cognome, url_file_path="url_giocatori.txt"):
    """
    Cerca l'URL di un giocatore nel file url_giocatori.txt basandosi sul nome e cognome normalizzati.

    Args:
        nome (str): Nome del giocatore
        cognome (str): Cognome del giocatore
        url_file_path (str): Percorso del file contenente gli URL

    Returns:
        str or None: URL del giocatore se trovato, altrimenti None
    """
    # Normalizza nome e cognome
    nome_norm = normalize_name(nome)
    cognome_norm = normalize_name(cognome)

    print(f"Nome normalizzato: '{nome_norm}', Cognome normalizzato: '{cognome_norm}'")

    try:
        with open(url_file_path, 'r', encoding='utf-8') as file:
            for line in file:
                url = line.strip()
                # Estrai la parte del nome dall'URL
                url_parts = url.split('/')
                if len(url_parts) >= 5:  # Assicurati che l'URL abbia abbastanza parti
                    player_part = url_parts[4]  # La parte con nome-cognome
                    player_part_lower = player_part.lower()

                    # Verifica se entrambi nome e cognome sono presenti nella parte del giocatore
                    if nome_norm in player_part_lower and cognome_norm in player_part_lower:
                        print(f"Match esatto trovato: {player_part}")
                        return url

                    # Cerca anche corrispondenze parziali o con ordine invertito
                    if f"{nome_norm}-{cognome_norm}" in player_part_lower or f"{cognome_norm}-{nome_norm}" in player_part_lower:
                        print(f"Match con pattern trovato: {player_part}")
                        return url
    except Exception as e:
        print(f"Errore nella lettura del file URL: {str(e)}")

    return None

def process_players_dataframe(limit=None):
    """
    Legge il dataframe dalla tabella players_latest filtrando solo le righe che hanno
    tutti i valori nulli nelle colonne di interesse (Nazionalità, DataNascita, Altezza, ecc.),
    cerca gli URL dei giocatori, estrae le informazioni e aggiorna il dataframe.

    Args:
        limit (int, optional): Numero massimo di giocatori da elaborare. Se None, elabora tutti.

    Returns:
        pandas.DataFrame: Il dataframe aggiornato con i dati dei giocatori che avevano valori nulli
    """
    # Crea un engine per poter usare pandas
    engine = create_engine('postgresql+psycopg://postgres:AcquaLevissima1@localhost:5432/db_modena')

    # Leggi solo le righe dalla tabella players_latest che hanno tutti i valori nulli nelle colonne di interesse
    df_players_latest = pd.read_sql_query("""
    SELECT * FROM players_latest
    WHERE "Nazionalità" IS NULL
    AND "DataNascita" IS NULL
    AND "Altezza" IS NULL
    AND "Peso" IS NULL
    AND "Schiacciata" IS NULL
    AND "Muro" IS NULL
    AND "ManoDominante" IS NULL
    """, engine)

    # Applica il limite se specificato
    if limit is not None and limit > 0 and limit < len(df_players_latest):
        print(f"Limitando l'elaborazione a {limit} giocatori dei {len(df_players_latest)} trovati")
        df_players_latest = df_players_latest.head(limit)

    print(f"Lette {len(df_players_latest)} righe dalla tabella players_latest con tutti i valori nulli nelle colonne di interesse")

    # Itera su ogni riga del dataframe
    for index, row in df_players_latest.iterrows():
        nome = row["Nome"]
        cognome = row["Cognome"]

        print(f"\nElaborazione di {nome} {cognome}...")

        # Cerca l'URL del giocatore
        player_url = find_player_url(nome, cognome)

        if player_url:
            print(f"URL trovato per {nome} {cognome}: {player_url}")

            # Aggiungi un ritardo casuale tra le richieste per evitare di essere bloccati
            if index > 0:  # Non ritardare la prima richiesta
                delay = random.uniform(30, 60)  # Ritardo casuale tra 30 e 60 secondi
                print(f"Attendo {delay:.1f} secondi prima della prossima richiesta...")
                time.sleep(delay)

            # Ottieni i dati del giocatore
            player_info = get_player_info_from_url(player_url)

            # Aggiorna le colonne del dataframe con i dati ottenuti
            if not player_info["errore"]:
                print(f"Dati trovati per {nome} {cognome}")

                # Stampa i dati trovati
                print("=" * 40)
                print(f"DATI ESTRATTI PER {nome} {cognome}:")
                print(f"Nazionalità: {player_info['nazionalità']}")
                print(f"Posizione: {player_info['posizione']}")
                print(f"Data di nascita: {player_info['data_di_nascita']}")
                print(f"Altezza: {player_info['altezza']}")
                print(f"Peso: {player_info['peso']}")
                print(f"Schiacciata: {player_info['schiacciata']}")
                print(f"Muro: {player_info['muro']}")
                print(f"Mano dominante: {player_info['mano_dominante']}")
                print("=" * 40)

                # Aggiorna le colonne del dataframe
                df_players_latest.at[index, "Nazionalità"] = player_info["nazionalità"]
                df_players_latest.at[index, "DataNascita"] = player_info["data_di_nascita"]
                df_players_latest.at[index, "Altezza"] = player_info["altezza"]
                df_players_latest.at[index, "Peso"] = player_info["peso"]
                df_players_latest.at[index, "Schiacciata"] = player_info["schiacciata"]
                df_players_latest.at[index, "Muro"] = player_info["muro"]
                df_players_latest.at[index, "ManoDominante"] = player_info["mano_dominante"]
            else:
                print(f"Errore per {nome} {cognome}: {player_info['errore']}")
        else:
            print(f"Nessun URL trovato per {nome} {cognome}")

    # Salva il dataframe con le righe che avevano tutti i valori nulli come CSV
    csv_path = DATA_DIR / "players_latest_updated.csv"
    df_players_latest.to_csv(csv_path, index=False)
    print(f"\nDataframe salvato come CSV in: {csv_path}")

    # Stampa un riepilogo dei dati raccolti
    print("\n" + "=" * 60)
    print("RIEPILOGO DEI DATI RACCOLTI (RIGHE PRECEDENTEMENTE NULLE):")
    print("=" * 60)
    for index, row in df_players_latest.iterrows():
        print(f"{row['Nome']} {row['Cognome']}:")
        print(f"  Nazionalità: {row['Nazionalità']}")
        print(f"  Data di nascita: {row['DataNascita']}")
        print(f"  Altezza: {row['Altezza']}")
        print(f"  Peso: {row['Peso']}")
        print(f"  Schiacciata: {row['Schiacciata']}")
        print(f"  Muro: {row['Muro']}")
        print(f"  Mano dominante: {row['ManoDominante']}")
        print("-" * 40)

    # Salva il dataframe aggiornato nel database
    print("\nSalvataggio delle modifiche nel database...")
    try:
        df_players_latest.to_sql('players_latest', engine, if_exists='replace', index=False)
        print("Dati salvati con successo nel database!")

        # Verifica che i dati siano stati salvati correttamente
        df_check = pd.read_sql_query("SELECT * FROM players_latest", engine)
        print(f"Numero di righe nel database: {len(df_check)}")
        print(f"Numero di valori non nulli in Nazionalità: {df_check['Nazionalità'].count()}")
        print(f"Numero di valori non nulli in Altezza: {df_check['Altezza'].count()}")
    except Exception as e:
        print(f"Errore durante il salvataggio nel database: {e}")

    return df_players_latest

def main():
    """
    Funzione principale per l'esecuzione da riga di comando.
    """
    import argparse
    import shutil

    # Verifica se wget è installato
    wget_available = shutil.which("wget") is not None
    if not wget_available:
        print("ERRORE: wget non è installato o non è disponibile nel PATH.")
        print("Per favore, installa wget prima di eseguire questo script.")
        print("Su Windows, puoi scaricarlo da: https://eternallybored.org/misc/wget/")
        print("Su Linux/Mac, usa il gestore pacchetti del tuo sistema (apt, brew, ecc.)")
        return

    # Crea un parser per gli argomenti da riga di comando
    parser = argparse.ArgumentParser(description="Volleyball Scraper - Estrai informazioni sui giocatori da volleybox.net")
    parser.add_argument("--limit", type=int, default=None, help="Limita il numero di giocatori da elaborare")
    parser.add_argument("--delay", type=int, default=30, help="Ritardo in secondi tra le richieste (default: 30)")
    parser.add_argument("--timeout", type=int, default=30, help="Timeout in secondi per le richieste wget (default: 30)")
    parser.add_argument("--retries", type=int, default=3, help="Numero di tentativi per wget (default: 3)")
    args = parser.parse_args()

    print("Volleyball Scraper - Estrai informazioni sui giocatori da volleybox.net usando wget")
    print("-"*80)

    if args.limit:
        print(f"Elaborazione limitata a {args.limit} giocatori.")

    print(f"Ritardo tra le richieste: {args.delay} secondi")
    print(f"Timeout per le richieste: {args.timeout} secondi")
    print(f"Tentativi per richiesta: {args.retries}")

    # Aggiorna le impostazioni globali per wget
    global download_with_wget

    def download_with_wget_with_args(url, output_file="downloaded_page.html"):
        # Modifica il comando wget con i parametri specificati da riga di comando
        try:
            # Configura gli header e le opzioni per wget
            user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36"

            # Costruisci il comando wget con varie opzioni per evitare il rilevamento
            wget_command = [
                "wget",
                "--quiet",                          # Non mostrare l'output
                "--no-check-certificate",           # Non controllare il certificato SSL
                "--random-wait",                    # Aggiungi un ritardo casuale tra le richieste
                f"--tries={args.retries}",          # Numero di tentativi
                f"--timeout={args.timeout}",        # Timeout in secondi
                f"--wait={args.delay}",             # Attesa tra i tentativi
                "--user-agent=" + user_agent,       # User agent personalizzato
                "--header=Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "--header=Accept-Language: it-IT,it;q=0.8,en-US;q=0.5,en;q=0.3",
                "--header=DNT: 1",
                "--header=Connection: keep-alive",
                "--header=Upgrade-Insecure-Requests: 1",
                "--header=Cache-Control: max-age=0",
                "-O", output_file,                  # File di output
                url                                 # URL da scaricare
            ]

            print(f"Scaricando {url} con wget...")

            # Esegui il comando wget
            result = subprocess.run(wget_command, capture_output=True, text=True)

            # Verifica se il download è riuscito
            if result.returncode == 0 and os.path.exists(output_file) and os.path.getsize(output_file) > 0:
                print(f"Download completato con successo: {output_file}")
                return True
            else:
                print(f"Errore durante il download: {result.stderr}")
                return False

        except Exception as e:
            print(f"Eccezione durante il download con wget: {str(e)}")
            return False

    # Sostituisci la funzione originale con quella modificata
    download_with_wget = download_with_wget_with_args

    try:
        # Elabora il dataframe e aggiorna con i dati dei giocatori
        process_players_dataframe(limit=args.limit)
        print("\nOperazione completata con successo!")
    except KeyboardInterrupt:
        print("\nOperazione interrotta dall'utente.")
    except Exception as e:
        print(f"\nErrore durante l'esecuzione: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
