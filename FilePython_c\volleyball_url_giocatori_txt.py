import asyncio
from playwright.async_api import async_playwright
import re
import argparse
import random
import time

#Questo script serve per ottenere tutti gli url di tutti i giocatori su volleybox, prendendoli dalle varie pagine tipo https://volleybox.net/it/players?page=2

OUTPUT_FILE = "url_giocatori.txt"
BASE_PLAYER_LIST_URL = "https://volleybox.net/it/players"
# Numero di pagine da scansionare
MAX_PAGES = 2600  # Puoi ridurre questo numero se vuoi scansionare meno pagine
# Pattern per identificare gli URL dei giocatori
PLAYER_URL_PATTERN = re.compile(r"/it/[a-z0-9-]+-p\d+")

# Funzione per simulare comportamenti umani
async def simulate_human_behavior(page):
    # Movimento casuale del mouse
    await page.mouse.move(random.randint(100, 400), random.randint(100, 300))

    # Attesa casuale
    await page.wait_for_timeout(random.randint(50, 100))  # da 100 a 200 ms

    # Scrolling naturale
    for _ in range(random.randint(2, 5)):
        await page.mouse.wheel(0, random.randint(100, 300))
        await page.wait_for_timeout(random.randint(50, 100))   #da 100 a 200

    # Altra attesa casuale
    await page.wait_for_timeout(random.randint(50, 100))  #da 100 a 200

async def extract_player_urls_from_page(page, page_num):
    """Estrae gli URL dei giocatori da una singola pagina"""
    print(f"🔍 Estrazione URL giocatori dalla pagina {page_num}...")

    # Simuliamo comportamenti umani
    await simulate_human_behavior(page)

    # Scrolling più naturale per caricare tutto il contenuto
    previous_height = await page.evaluate("document.body.scrollHeight")
    scroll_attempts = 0
    max_scroll_attempts = 4

    while scroll_attempts < max_scroll_attempts:
        # Scrolling con velocità variabile
        scroll_amount = random.randint(200, 500)   #da 200 a 500
        await page.evaluate(f"window.scrollBy(0, {scroll_amount})")

        # Pausa casuale tra gli scroll
        await page.wait_for_timeout(random.randint(50, 100))  #da 100 a 200

        # Controlliamo se siamo arrivati in fondo
        current_height = await page.evaluate("document.body.scrollHeight")
        if current_height == previous_height:
            scroll_attempts += 1
        else:
            scroll_attempts = 0
            previous_height = current_height

    # Torniamo in cima alla pagina (comportamento umano)
    await page.evaluate("window.scrollTo(0, 0)")
    await page.wait_for_timeout(random.randint(50, 100))  #da 100 a 200

    # Estrazione diretta del contenuto HTML
    html_content = await page.content()

    # Cerchiamo tutti i match del pattern degli URL dei giocatori
    player_urls_matches = PLAYER_URL_PATTERN.findall(html_content)
    unique_player_urls = set(player_urls_matches)

    # Convertiamo in URL completi e aggiungiamo /clubs
    player_links = []
    for url in unique_player_urls:
        if not url.endswith("/clubs"):
            url = url + "/clubs" if not url.endswith("/") else url + "clubs"
        player_links.append(url)

    print(f"Trovati {len(player_links)} URL di giocatori nella pagina {page_num}")
    return player_links

async def process_single_page(page_num, all_player_urls):
    """Processa una singola pagina e restituisce gli URL trovati"""
    #print(f"\n📄 Elaborazione della pagina {page_num}/{MAX_PAGES}")

    # Creiamo una nuova sessione per ogni pagina
    async with async_playwright() as p:
        # Usiamo sempre headless=False per evitare problemi con Cloudflare
        browser = await p.chromium.launch(
            #channel="chrome",   #togli questo parametro se non va
            headless=False,
            slow_mo=50,  # Rallentiamo leggermente le operazioni per sembrare più umani
                        args=[
                '--window-position=-32000,-32000',
                '--start-minimized',
                '--disable-blink-features=AutomationControlled',
                '--disable-features=IsolateOrigins,site-per-process',
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu',
                '--disable-extensions',
                '--disable-plugins',
                '--single-process',
                '--disable-software-rasterizer',  # Disabilita il rendering software
                '--disable-javascript-harmony-shipping',  # Disabilita funzionalità JS sperimentali
                '--disable-logging',  # Disabilita il logging interno
                '--disable-remote-fonts',  # Disabilita i font remoti
                '--disable-threaded-scrolling',  # Disabilita lo scrolling multi-thread
                '--disable-threaded-animation',  # Disabilita le animazioni multi-thread
                '--disable-composited-antialiasing',  # Disabilita l'antialiasing composito
                '--disable-webgl',  # Disabilita WebGL
                '--disable-webgl2',  # Disabilita WebGL 2
                '--disable-3d-apis',  # Disabilita le API 3D
                '--disable-backgrounding-occluded-windows',  # Riduce il carico delle finestre nascoste
                '--disable-renderer-backgrounding',  # Impedisce il backgrounding del renderer
                '--disable-background-timer-throttling',  # Disabilita il throttling dei timer in background
                '--memory-pressure-off',  # Disabilita il monitoraggio della pressione della memoria
                '--disable-hang-monitor',  # Disabilita il monitoraggio dei freeze
                '--disable-component-update',  # Disabilita gli aggiornamenti dei componenti
                '--disable-background-networking',  # Disabilita il networking in background
                '--disable-breakpad',  # Disabilita il reporting dei crash
                '--disable-default-apps',  # Disabilita le app predefinite
                '--disable-sync',  # Disabilita la sincronizzazione
                '--disable-translate',  # Disabilita la traduzione
                '--disable-domain-reliability',  # Disabilita la raccolta di metriche di affidabilità
                '--disable-client-side-phishing-detection',  # Disabilita il rilevamento del phishing
                '--disable-ipc-flooding-protection',  # Disabilita la protezione IPC flooding
                '--enable-low-end-device-mode',  # Abilita la modalità per dispositivi a basse prestazioni
                '--js-flags="--max-old-space-size=128"'  # Limita l'uso della memoria di V8
            ]
        )

        # Creiamo un contesto con user agent realistico e altre impostazioni per sembrare più umani
        context = await browser.new_context(
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            viewport={"width": 800, "height": 600},   #1280, 800
            locale="it-IT",  # Impostiamo la lingua italiana
            timezone_id="Europe/Rome",  # Impostiamo il fuso orario italiano
            geolocation={"latitude": 41.9028, "longitude": 12.4964},  # Coordinate italiane (Roma)
            permissions=["geolocation"],
            color_scheme="light",
            has_touch=False,  # La maggior parte degli utenti desktop non ha touch
            is_mobile=False,  # Non siamo un dispositivo mobile
            device_scale_factor=1,  # Scala standard
            accept_downloads=True  # Accettiamo i download
        )

        # Abilitiamo JavaScript
        page = await context.new_page()

        try:
            # Costruiamo l'URL della pagina
            if page_num == 1:
                current_url = BASE_PLAYER_LIST_URL
            else:
                current_url = f"{BASE_PLAYER_LIST_URL}?page={page_num}"

            print(f"Navigazione a: {current_url}")
            await page.goto(current_url, wait_until="domcontentloaded")

            # Verifichiamo se siamo nella pagina di Cloudflare
            page_title = await page.title()
            page_content = await page.content()
            if "cloudflare" in page_title.lower() or "cloudflare" in page_content.lower():
                #print("\n⚠️ ATTENZIONE: Rilevato Cloudflare protection!")
                #print("Per favore, completa manualmente la verifica di Cloudflare nel browser.")
                #print("Attendo 30 secondi per permettere l'interazione manuale...")

                # Attendiamo che l'utente completi la verifica
                await page.wait_for_timeout(20)  # 200 ms

            # Estrai gli URL dei giocatori dalla pagina corrente
            player_links = await extract_player_urls_from_page(page, page_num)

            # Aggiungi gli URL trovati al set di tutti gli URL
            for href in player_links:
                if href.startswith("/"):
                    full_url = f"https://volleybox.net{href}"
                else:
                    full_url = href
                all_player_urls.add(full_url)

            print(f"Pagina {page_num}: trovati {len(player_links)} URL di giocatori")
            print(f"Totale URL trovati finora: {len(all_player_urls)}")

            # Chiudiamo il browser
            await browser.close()

            return len(player_links)

        except Exception as e:
            print(f"\n⚠️ Errore durante l'elaborazione della pagina {page_num}: {str(e)}")
            await browser.close()
            return 0

async def run(start_page=1):  #ripartiamo dalla 1300
    # Set per memorizzare tutti gli URL unici trovati, così non aggiungo più url uguali
    all_player_urls = set()  

    # Leggiamo eventuali URL già presenti nel file
    try:
        with open(OUTPUT_FILE, "r", encoding="utf-8") as f:
            existing_urls = f.read().splitlines()
            for url in existing_urls:
                if url.strip():  # Ignora righe vuote
                    all_player_urls.add(url.strip())
            if existing_urls:
                print(f"Caricati {len(all_player_urls)} URL esistenti dal file {OUTPUT_FILE}")
    except FileNotFoundError:
        print(f"File {OUTPUT_FILE} non trovato, verrà creato un nuovo file.")

    # Contatore per tenere traccia delle pagine senza URL
    empty_pages_count = 0
    max_empty_pages = 10  # Se troviamo 10 pagine vuote consecutive, ci fermiamo

    # Iteriamo attraverso le pagine, partendo dalla pagina specificata
    for current_page in range(start_page, MAX_PAGES + 1):
        print(f"\n📄 Elaborazione della pagina {current_page}/{MAX_PAGES}")

        # Processiamo la pagina corrente
        urls_found = await process_single_page(current_page, all_player_urls)

        # Aggiorniamo il contatore di pagine vuote
        if urls_found == 0:
            empty_pages_count += 1
            print(f"Pagina vuota #{empty_pages_count}")
            if empty_pages_count >= max_empty_pages:
                print(f"Trovate {max_empty_pages} pagine vuote consecutive. Interrompo la scansione.")
                break
        else:
            empty_pages_count = 0  # Reset del contatore se troviamo URL

        # Salviamo gli URL dopo ogni pagina per non perdere il lavoro fatto
        with open(OUTPUT_FILE, "w", encoding="utf-8") as f:
            for url in sorted(all_player_urls):
                f.write(url + "\n")

        print(f"Salvati {len(all_player_urls)} URL in '{OUTPUT_FILE}'")

        # Pausa casuale tra le pagine per evitare di sovraccaricare il server
        # e per sembrare più umani
        pause_time = random.randint(300, 600) / 1000  # Da 500 a 800 ms
        print(f"Pausa di {pause_time:.1f} secondi prima di passare alla pagina successiva...")
        time.sleep(pause_time)

    # Log per debugging: vediamo cosa viene estratto
    print(f"\n🔍 Riepilogo: Trovati {len(all_player_urls)} URL unici di giocatori")

    # Mostriamo alcuni esempi di URL trovati
    if all_player_urls:
        print("Esempi di URL trovati:")
        for i, url in enumerate(list(all_player_urls)[:5]):  # Mostriamo solo i primi 5
            print(f"  {i+1}. {url}")

        if len(all_player_urls) > 5:
            print(f"  ...e altri {len(all_player_urls) - 5} URL")

        print(f"✅ Trovati {len(all_player_urls)} URL validi e salvati in '{OUTPUT_FILE}'")
    else:
        print("❌ Nessun URL trovato. Controlla la struttura della pagina o i selettori.")
        print("\n⚠️ ATTENZIONE: Non sono stati trovati URL.")
        print("Questo potrebbe essere dovuto a:")
        print("1. Protezione anti-bot del sito web (Cloudflare o simili)")
        print("2. Cambiamenti nella struttura del sito")
        print("3. Problemi di connessione")

if __name__ == "__main__":
    # Parsing degli argomenti da riga di comando
    parser = argparse.ArgumentParser(description="Estrae URL di giocatori di pallavolo da Volleybox")
    parser.add_argument("-p", "--page", type=int, default=2400, help="Pagina iniziale (default: 1)")  #Parto dalla pagina 1700
    parser.add_argument("-m", "--max-pages", type=int, default=MAX_PAGES, help=f"Numero massimo di pagine da scansionare (default: {MAX_PAGES})")
    args = parser.parse_args()

    # Aggiorniamo il numero massimo di pagine se specificato
    if args.max_pages != MAX_PAGES:
        MAX_PAGES = args.max_pages
        print(f"Numero massimo di pagine impostato a {MAX_PAGES}")

    # Eseguiamo lo script con la pagina iniziale specificata
    print(f"Inizio scansione dalla pagina {args.page}")
    asyncio.run(run(args.page))




#https://www.makeuseof.com/tag/how-do-i-download-an-entire-website-for-offline-reading/
#https://www.youtube.com/watch?v=swK8CJ8Zlw8
#https://www.youtube.com/watch?v=Kv9Zr8YkKOc
#https://www.google.com/search?q=how+do+i+download+an+entire+website+and+its+paths%3F&oq=how+do+i+download+an+entire+website+and+its+paths%3F&gs_lcrp=EgZjaHJvbWUyBggAEEUYOTIHCAEQIRigATIHCAIQIRigATIHCAMQIRigAdIBCTE4NTc0ajFqN6gCALACAA&sourceid=chrome&ie=UTF-8
#https://serper.dev/dashboard




