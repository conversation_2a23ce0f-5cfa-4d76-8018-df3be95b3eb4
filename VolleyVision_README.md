# VolleyVision Setup Guide

This guide will help you set up and run VolleyVision, a volleyball detection and tracking system.

## Prerequisites

- Python 3.8 or higher
- Git (to clone the repository if not already done)
- A volleyball video for testing

## Setup Instructions

1. **Run the setup script**

   Double-click on the `setup_volleyvision.bat` file. This will:
   - Create a virtual environment
   - Install all required dependencies
   - Download the necessary model files
   - Launch Jupyter Notebook with the fixed notebook

2. **Select the correct kernel**

   When the notebook opens, make sure to select the "VolleyVision" kernel from the kernel menu.

3. **Run the notebook cells**

   Execute the cells in order. The notebook will:
   - Navigate to the correct directories
   - Install any remaining dependencies
   - Set up the volleyball detection and tracking system

4. **Provide a video file**

   To test the system, you'll need to provide a volleyball video. You can:
   - Use your own volleyball video
   - Download a sample volleyball video from the internet

   Place the video in the `FilePython_c\VolleyVision\Stage I - Volleyball` directory.

5. **Run the detection and tracking**

   Modify the cell that runs `volley_track.py` to use your video file:

   ```python
   !python volley_track.py --source your_video.mp4 --weights models/yolov7-tiny.pt --conf 0.25 --save-txt --save-conf
   ```

   Replace `your_video.mp4` with the name of your video file.

## Troubleshooting

- **Missing dependencies**: If you encounter errors about missing packages, run `pip install package_name` in a cell.
- **Model not found**: Make sure the model files were downloaded correctly. Check the `models` and `DaSiamRPN` directories.
- **Video not found**: Make sure your video file is in the correct directory and the path is specified correctly.
- **CUDA errors**: If you encounter CUDA errors, try running with CPU only by adding `--device cpu` to the command.

## Additional Information

- The output video will be saved in the `Output` directory.
- You can adjust the confidence threshold with the `--conf` parameter (default is 0.25).
- You can change the marker type with the `--marker` parameter (options: 'circle', 'box').
- You can change the color with the `--color` parameter (options: 'black', 'white', 'red', 'green', 'purple', 'blue', 'yellow', 'cyan', 'gray', 'navy').

## Example Command

```
python volley_track.py --source volleyball_match.mp4 --weights models/yolov7-tiny.pt --conf 0.3 --marker circle --color red
```

This will:
- Process the video `volleyball_match.mp4`
- Use the YOLOv7 tiny model
- Set the confidence threshold to 0.3
- Use a circle marker
- Use red color for the marker and trajectory
