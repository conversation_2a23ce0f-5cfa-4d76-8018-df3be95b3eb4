#Ora dobbiamo aggiungere alla tabella Rilevations le colonne che ci indicano per ogni giocatore il suo ID, invece che il suo numero di maglia.

import numpy as np 
import pandas as pd  #Realizzato con la versione di Pandas 2.2.3
import polars as pl
import os
import psycopg
from sqlalchemy import create_engine, text


conn = psycopg.connect(
    dbname="db_modena",           # database creato in pgAdmin4
    user="postgres",              # Il tuo nome utente PostgreSQL
    password="AcquaLevissima1",   # La password che hai scelto per 'postgres'
    host="localhost",             # 'localhost' se è sul tuo PC
    port=5432                     # La porta predefinita è 5432
)

cur = conn.cursor()

engine = create_engine('postgresql+psycopg://postgres:AcquaLevissima1@localhost:5432/db_modena')





#Controlliamo che in ogni riga di Rilevations i valori di HomePlayerX siano diversi, ovvero che non ci siano due giocatori con lo stesso numero di maglia in campo.
#Stampiamo le righe che danno problemi.

def controlla_giocatori_casa():
    # Query per creare un dataframe con i giocatori di casa della tabella Rilevations
    query_home = """SELECT "GameID", "RilevationNumber", "Player1", "Player2", "Player3", "Player4", "Player5", "Player6" FROM rilevations;"""
    df_home = pd.read_sql(query_home, engine)

    # Funzione per trovare duplicati in una riga
    def find_duplicates_home(row):  #prende in input una riga del dataframe df
        values = [row["Player1"], row["Player2"], row["Player3"], row["Player4"], row["Player5"], row["Player6"]]  #lista con 6 elementi, i 6 numeri di maglia
        duplicates = {x for x in values if values.count(x) > 1 and x is not None}  #set con i numeri di maglia che compaiono almeno due volte
        return duplicates if duplicates else None  #restituisci il set duplicates se questo ha almeno un elemento dentro

    # Applicare la funzione e filtrare righe con duplicati
    df_home["Duplicati"] = df_home.apply(find_duplicates_home, axis=1)  #applica la funzione find_duplicates_home a ogni riga del dataframe df, e salva il risultato nella colonna "Duplicati". Questa colonna in ogni riga contiene None se la funzione non restituisce niente, altrimenti contiene duplicates
    df_duplicati_home = df_home[df_home["Duplicati"].notna()]  #crea un nuovo dataframe df_duplicati_home che contiene solo le righe del dataframe df in cui la colonna "Duplicati" non è None

    # Stampare le righe problematiche
    if not df_duplicati_home.empty:  #se il dataframe df_duplicati_home non è vuoto
        print("⚠️ Attenzione! Trovati numeri di maglia duplicati nelle seguenti rilevazioni:")
        for _, row in df_duplicati_home.iterrows():
            print(f"GameID: {row["GameID"]} RilevationNumber: {row["RilevationNumber"]} - Numeri duplicati: {row['Duplicati']}")
    else:  #altrimenti, se è vuoto (non ha righe non nulle)
        print("✅ Nessun problema rilevato nei giocatori di casa.")

    
#stessa cosa ma tutto in SQL
'''
SELECT
  "RilevationID",
  "GameID",
  "RilevationNumber",
  ARRAY[
    "HomePlayer1",
    "HomePlayer2",
    "HomePlayer3",
    "HomePlayer4",
    "HomePlayer5",
    "HomePlayer6"
  ] AS maglie,
  ARRAY(
    SELECT UNNEST(ARRAY[
      "HomePlayer1",
      "HomePlayer2",
      "HomePlayer3",
      "HomePlayer4",
      "HomePlayer5",
      "HomePlayer6"
    ])
    GROUP BY 1
    HAVING COUNT(*) > 1
  ) AS duplicati
FROM rilevations
WHERE (
  SELECT COUNT(*) FROM (
    SELECT UNNEST(ARRAY[
      "HomePlayer1",
      "HomePlayer2",
      "HomePlayer3",
      "HomePlayer4",
      "HomePlayer5",
      "HomePlayer6"
    ]) val
  ) AS all_values
) >
(
  SELECT COUNT(DISTINCT val) FROM (
    SELECT UNNEST(ARRAY[
      "HomePlayer1",
      "HomePlayer2",
      "HomePlayer3",
      "HomePlayer4",
      "HomePlayer5",
      "HomePlayer6"
    ]) val
  ) AS unique_values
);

'''






#Stessi controlli per i giocatori ospiti.
def controlla_giocatori_ospiti():
    # Query per creare un dataframe con i giocatori ospiti della tabella Rilevations
    query_visitor = """SELECT "GameID", "RilevationNumber", "Player7", "Player8", "Player9", "Player10", "Player11", "Player12" FROM rilevations;"""
    df_visitor = pd.read_sql(query_visitor, engine)

    # Funzione per trovare duplicati in una riga
    def find_duplicates_visitor(row):  #prende in input una riga del dataframe df
        values = [row["Player7"], row["Player8"], row["Player9"], row["Player10"], row["Player11"], row["Player12"]]  #lista con 6 elementi, i 6 numeri di maglia
        duplicates = {x for x in values if values.count(x) > 1 and x is not None}  #set con i numeri di maglia che compaiono almeno due volte
        return duplicates if duplicates else None  #restituisci il set duplicates se questo ha almeno un elemento dentro

    # Applicare la funzione e filtrare righe con duplicati
    df_visitor["Duplicati"] = df_visitor.apply(find_duplicates_visitor, axis=1)  #applica la funzione find_duplicates_home a ogni riga del dataframe df, e salva il risultato nella colonna "Duplicati". Questa colonna in ogni riga contiene None se la funzione non restituisce niente, altrimenti contiene duplicates
    df_duplicati_visitor = df_visitor[df_visitor["Duplicati"].notna()]  #crea un nuovo dataframe df_duplicati_visitor che contiene solo le righe del dataframe df in cui la colonna "Duplicati" non è None

    # Stampare le righe problematiche
    if not df_duplicati_visitor.empty:  #se il dataframe df_duplicati_visitor non è vuoto
        print("⚠️ Attenzione! Trovati numeri di maglia duplicati nelle seguenti rilevazioni:")
        for _, row in df_duplicati_visitor.iterrows():
            print(f"GameID: {row["GameID"]} RilevationNumber: {row["RilevationNumber"]} - Numeri duplicati: {row['Duplicati']}")
    else:  #altrimenti, se è vuoto (non ha righe non nulle)
        print("✅ Nessun problema rilevato nei giocatori ospiti.")




#Io creo rilevations_view, trovando i vari Pcasa_ID, S1casa_ID, etc.
#Poi dopo in un file creerò pleayers_each_game_view, trovando RuoloCalc per tutti i giocatori
#Poi dopo in un file creerò rilevations_libero_view, in cui trovo i probHomePlayerX_ID, ovvero gli HomePlayerX_ID mettendo il libero quando credo ci sia.

#Infatti prima devo avere le colonne Pcasa_ID, S1casa_ID, etc, da queste posso calcolare NumeroAzioniDaRuoloX, da queste posso calcolare RuoloCalc, e da questo posso calcolare probHomePlayerX_ID





def crea_rilevationswithid():
    cur.execute("DROP VIEW IF EXISTS rilevationswithid CASCADE")
    conn.commit()


    #Creo la VIEW in due Step. Invece di creare due VIEW, uso WITH per crearle una alla volta all'interno della stessa query. Il secondo Step, ViewTemporanea2, usa le colonne del primo Step, ViewTemporanea1, per calcolare le sue colonne.
    cur.execute("""
    CREATE VIEW rilevationswithid AS


    WITH  --Mi permette di fare delle subquery come vari step, per formare la query principale. I miei 2 step sono ViewTemporanea1 e ViewTemporanea2
    ViewTemporanea1 AS (  --Inizio dello step1, creo la prima view temporanea che mi servirà per creare la seconda. Quello che sto facendo è una subquery.
        SELECT 
            r.*,   -- Tutte le colonne di Rilevations
            
            --Metto whichTeamID, dove al posto di 0 o 1, metto il TeamID (della squadra di casa se 0, della squadra ospite se 1)
            --CASE 
            --    WHEN r."whichTeam" = false THEN g."IDHomeTeam"
            --    WHEN r."whichTeam" = true THEN g."IDVisitorTeam"
            --    ELSE NULL
            --END AS "whichTeamID",

            -- JOIN dirette per i 6 giocatori di casa
            p1."PlayerID" AS "HomePlayer1_ID",
            p2."PlayerID" AS "HomePlayer2_ID",
            p3."PlayerID" AS "HomePlayer3_ID",
            p4."PlayerID" AS "HomePlayer4_ID",
            p5."PlayerID" AS "HomePlayer5_ID",
            p6."PlayerID" AS "HomePlayer6_ID",
            
            -- JOIN dirette per i 6 giocatori ospiti
            vp1."PlayerID" AS "VisitorPlayer1_ID",
            vp2."PlayerID" AS "VisitorPlayer2_ID",
            vp3."PlayerID" AS "VisitorPlayer3_ID",
            vp4."PlayerID" AS "VisitorPlayer4_ID",
            vp5."PlayerID" AS "VisitorPlayer5_ID",
            vp6."PlayerID" AS "VisitorPlayer6_ID"        

        FROM rilevations r                            --Prendo i dati dalla tabella Rilevations (che chiamo r)
        LEFT JOIN games_view g ON r."GameID" = g."GameID"  --e dalla tabella Games (che chiamo g)
        
        -- Home players
        LEFT JOIN players_each_game p1 ON p1."GameID" = r."GameID" AND p1."TeamID" = g."IDHomeTeam" AND p1."NumeroMaglia" = r."HomePlayer1"
        LEFT JOIN players_each_game p2 ON p2."GameID" = r."GameID" AND p2."TeamID" = g."IDHomeTeam" AND p2."NumeroMaglia" = r."HomePlayer2"
        LEFT JOIN players_each_game p3 ON p3."GameID" = r."GameID" AND p3."TeamID" = g."IDHomeTeam" AND p3."NumeroMaglia" = r."HomePlayer3"
        LEFT JOIN players_each_game p4 ON p4."GameID" = r."GameID" AND p4."TeamID" = g."IDHomeTeam" AND p4."NumeroMaglia" = r."HomePlayer4"
        LEFT JOIN players_each_game p5 ON p5."GameID" = r."GameID" AND p5."TeamID" = g."IDHomeTeam" AND p5."NumeroMaglia" = r."HomePlayer5"
        LEFT JOIN players_each_game p6 ON p6."GameID" = r."GameID" AND p6."TeamID" = g."IDHomeTeam" AND p6."NumeroMaglia" = r."HomePlayer6"

        -- Visitor players
        LEFT JOIN players_each_game vp1 ON vp1."GameID" = r."GameID" AND vp1."TeamID" = g."IDVisitorTeam" AND vp1."NumeroMaglia" = r."VisitorPlayer1"
        LEFT JOIN players_each_game vp2 ON vp2."GameID" = r."GameID" AND vp2."TeamID" = g."IDVisitorTeam" AND vp2."NumeroMaglia" = r."VisitorPlayer2"
        LEFT JOIN players_each_game vp3 ON vp3."GameID" = r."GameID" AND vp3."TeamID" = g."IDVisitorTeam" AND vp3."NumeroMaglia" = r."VisitorPlayer3"
        LEFT JOIN players_each_game vp4 ON vp4."GameID" = r."GameID" AND vp4."TeamID" = g."IDVisitorTeam" AND vp4."NumeroMaglia" = r."VisitorPlayer4"
        LEFT JOIN players_each_game vp5 ON vp5."GameID" = r."GameID" AND vp5."TeamID" = g."IDVisitorTeam" AND vp5."NumeroMaglia" = r."VisitorPlayer5"
        LEFT JOIN players_each_game vp6 ON vp6."GameID" = r."GameID" AND vp6."TeamID" = g."IDVisitorTeam" AND vp6."NumeroMaglia" = r."VisitorPlayer6"

    ),  --Fine dello step 1




    ViewTemporanea2 AS (
    SELECT 
        v1.*,

        -- JOIN per il giocatore che ha effettuato il tocco
        ptouch."PlayerID" AS "NumeroMaglia_ID"

    FROM ViewTemporanea1 v1

    -- JOIN per il giocatore che ha effettuato il tocco
    LEFT JOIN players_each_game ptouch ON ptouch."GameID" = v1."GameID" AND ptouch."TeamID" = v1."whichTeamID" AND ptouch."NumeroMaglia" = v1."NumeroMaglia"

    ),



    ViewTemporanea3 AS (  --Inizio dello step 3, creo una view che usa le colonne della view precedente e ne aggiungo altre
        SELECT 
            v2.*,   -- Tutte le colonne dello Step precedente, la view ViewTemporanea2, che abbrevio in v2

            -- Aggiungo Pcasa_ID, il palleggiatore di casa
            CASE 
                WHEN v2."PosizPalleggCasa" = 1 THEN "HomePlayer1_ID"
                WHEN v2."PosizPalleggCasa" = 6 THEN "HomePlayer6_ID"
                WHEN v2."PosizPalleggCasa" = 5 THEN "HomePlayer5_ID"
                WHEN v2."PosizPalleggCasa" = 4 THEN "HomePlayer4_ID"
                WHEN v2."PosizPalleggCasa" = 3 THEN "HomePlayer3_ID"
                WHEN v2."PosizPalleggCasa" = 2 THEN "HomePlayer2_ID"
                ELSE NULL
            END AS "Pcasa_ID",

            -- Aggiungo S1casa_ID, lo schiacciatore vicino al palleggiatore
            CASE 
                WHEN v2."PosizPalleggCasa" = 1 THEN "HomePlayer2_ID"
                WHEN v2."PosizPalleggCasa" = 6 THEN "HomePlayer1_ID"
                WHEN v2."PosizPalleggCasa" = 5 THEN "HomePlayer6_ID"
                WHEN v2."PosizPalleggCasa" = 4 THEN "HomePlayer5_ID"
                WHEN v2."PosizPalleggCasa" = 3 THEN "HomePlayer4_ID"
                WHEN v2."PosizPalleggCasa" = 2 THEN "HomePlayer3_ID"
                ELSE NULL
            END AS "S1casa_ID",
            
            -- Aggiungo C2casa_ID, il centrale lontano dal palleggiatore
            CASE 
                WHEN v2."PosizPalleggCasa" = 1 THEN "HomePlayer3_ID"
                WHEN v2."PosizPalleggCasa" = 6 THEN "HomePlayer2_ID"
                WHEN v2."PosizPalleggCasa" = 5 THEN "HomePlayer1_ID"
                WHEN v2."PosizPalleggCasa" = 4 THEN "HomePlayer6_ID"
                WHEN v2."PosizPalleggCasa" = 3 THEN "HomePlayer5_ID"
                WHEN v2."PosizPalleggCasa" = 2 THEN "HomePlayer4_ID"
                ELSE NULL
            END AS "C2casa_ID",
            
            -- Aggiungo Ocasa_ID, l'opposto
            CASE 
                WHEN v2."PosizPalleggCasa" = 1 THEN "HomePlayer4_ID"
                WHEN v2."PosizPalleggCasa" = 6 THEN "HomePlayer3_ID"
                WHEN v2."PosizPalleggCasa" = 5 THEN "HomePlayer2_ID"
                WHEN v2."PosizPalleggCasa" = 4 THEN "HomePlayer1_ID"
                WHEN v2."PosizPalleggCasa" = 3 THEN "HomePlayer6_ID"
                WHEN v2."PosizPalleggCasa" = 2 THEN "HomePlayer5_ID"
                ELSE NULL
            END AS "Ocasa_ID",

            -- Aggiungo S2casa_ID, lo schiacciatore lontano dal palleggiatore
            CASE 
                WHEN v2."PosizPalleggCasa" = 1 THEN "HomePlayer5_ID"
                WHEN v2."PosizPalleggCasa" = 6 THEN "HomePlayer4_ID"
                WHEN v2."PosizPalleggCasa" = 5 THEN "HomePlayer3_ID"
                WHEN v2."PosizPalleggCasa" = 4 THEN "HomePlayer2_ID"
                WHEN v2."PosizPalleggCasa" = 3 THEN "HomePlayer1_ID"
                WHEN v2."PosizPalleggCasa" = 2 THEN "HomePlayer6_ID"
                ELSE NULL
            END AS "S2casa_ID",

            -- Aggiungo C1casa_ID, il centrale vicino al palleggiatore
            CASE 
                WHEN v2."PosizPalleggCasa" = 1 THEN "HomePlayer6_ID"
                WHEN v2."PosizPalleggCasa" = 6 THEN "HomePlayer5_ID"
                WHEN v2."PosizPalleggCasa" = 5 THEN "HomePlayer4_ID"
                WHEN v2."PosizPalleggCasa" = 4 THEN "HomePlayer3_ID"
                WHEN v2."PosizPalleggCasa" = 3 THEN "HomePlayer2_ID"
                WHEN v2."PosizPalleggCasa" = 2 THEN "HomePlayer1_ID"
                ELSE NULL
            END AS "C1casa_ID",
            
            
            -- Aggiungo Pospite_ID, il palleggiatore ospite
            CASE 
                WHEN v2."PosizPalleggOspite" = 1 THEN "VisitorPlayer1_ID"
                WHEN v2."PosizPalleggOspite" = 6 THEN "VisitorPlayer6_ID"
                WHEN v2."PosizPalleggOspite" = 5 THEN "VisitorPlayer5_ID"
                WHEN v2."PosizPalleggOspite" = 4 THEN "VisitorPlayer4_ID"
                WHEN v2."PosizPalleggOspite" = 3 THEN "VisitorPlayer3_ID"
                WHEN v2."PosizPalleggOspite" = 2 THEN "VisitorPlayer2_ID"
                ELSE NULL
            END AS "Pospite_ID",

            -- Aggiungo S1ospite_ID, lo schiacciatore vicino al palleggiatore
            CASE 
                WHEN v2."PosizPalleggOspite" = 1 THEN "VisitorPlayer2_ID"
                WHEN v2."PosizPalleggOspite" = 6 THEN "VisitorPlayer1_ID"
                WHEN v2."PosizPalleggOspite" = 5 THEN "VisitorPlayer6_ID"
                WHEN v2."PosizPalleggOspite" = 4 THEN "VisitorPlayer5_ID"
                WHEN v2."PosizPalleggOspite" = 3 THEN "VisitorPlayer4_ID"
                WHEN v2."PosizPalleggOspite" = 2 THEN "VisitorPlayer3_ID"
                ELSE NULL
            END AS "S1ospite_ID",
            
            -- Aggiungo C2ospite_ID, il centrale lontano dal palleggiatore
            CASE 
                WHEN v2."PosizPalleggOspite" = 1 THEN "VisitorPlayer3_ID"
                WHEN v2."PosizPalleggOspite" = 6 THEN "VisitorPlayer2_ID"
                WHEN v2."PosizPalleggOspite" = 5 THEN "VisitorPlayer1_ID"
                WHEN v2."PosizPalleggOspite" = 4 THEN "VisitorPlayer6_ID"
                WHEN v2."PosizPalleggOspite" = 3 THEN "VisitorPlayer5_ID"
                WHEN v2."PosizPalleggOspite" = 2 THEN "VisitorPlayer4_ID"
                ELSE NULL
            END AS "C2ospite_ID",
            
            -- Aggiungo Oospite_ID, l'opposto
            CASE 
                WHEN v2."PosizPalleggOspite" = 1 THEN "VisitorPlayer4_ID"
                WHEN v2."PosizPalleggOspite" = 6 THEN "VisitorPlayer3_ID"
                WHEN v2."PosizPalleggOspite" = 5 THEN "VisitorPlayer2_ID"
                WHEN v2."PosizPalleggOspite" = 4 THEN "VisitorPlayer1_ID"
                WHEN v2."PosizPalleggOspite" = 3 THEN "VisitorPlayer6_ID"
                WHEN v2."PosizPalleggOspite" = 2 THEN "VisitorPlayer5_ID"
                ELSE NULL
            END AS "Oospite_ID",

            -- Aggiungo S2ospite_ID, lo schiacciatore lontano dal palleggiatore
            CASE 
                WHEN v2."PosizPalleggOspite" = 1 THEN "VisitorPlayer5_ID"
                WHEN v2."PosizPalleggOspite" = 6 THEN "VisitorPlayer4_ID"
                WHEN v2."PosizPalleggOspite" = 5 THEN "VisitorPlayer3_ID"
                WHEN v2."PosizPalleggOspite" = 4 THEN "VisitorPlayer2_ID"
                WHEN v2."PosizPalleggOspite" = 3 THEN "VisitorPlayer1_ID"
                WHEN v2."PosizPalleggOspite" = 2 THEN "VisitorPlayer6_ID"
                ELSE NULL
            END AS "S2ospite_ID",

            -- Aggiungo C1ospite_ID, il centrale vicino al palleggiatore
            CASE 
                WHEN v2."PosizPalleggOspite" = 1 THEN "VisitorPlayer6_ID"
                WHEN v2."PosizPalleggOspite" = 6 THEN "VisitorPlayer5_ID"
                WHEN v2."PosizPalleggOspite" = 5 THEN "VisitorPlayer4_ID"
                WHEN v2."PosizPalleggOspite" = 4 THEN "VisitorPlayer3_ID"
                WHEN v2."PosizPalleggOspite" = 3 THEN "VisitorPlayer2_ID"
                WHEN v2."PosizPalleggOspite" = 2 THEN "VisitorPlayer1_ID"
                ELSE NULL
            END AS "C1ospite_ID",
            
            --Aggiungo una colonna che mi dice se il palleggiatore di casa è in prima linea (1) o in seconda linea (0)
            CASE 
                WHEN v2."PosizPalleggCasa" IN (2,3,4) THEN 1
                ELSE 0
            END AS "Pcasa1linea",
            
            --Aggiungo una colonna che mi dice se il palleggiatore ospite è in prima linea (1) o in seconda linea (0)
            CASE 
                WHEN v2."PosizPalleggOspite" IN (2,3,4) THEN 1
                ELSE 0
            END AS "Pospite1linea"
            
        FROM ViewTemporanea2 v2  --Prendo i dati dallo Step precedente, la view temporanea appena creata, ViewTemporanea2, che abbrevio in v2
    )



    SELECT * FROM ViewTemporanea3;  --Alla fine, per la mia VIEW finale, RilevationsWithID, seleziono tutte le colonne della view temporanea appena creata, ViewTemporanea3
    """)



def crea_rilevationswithid1():
    cur.execute("DROP VIEW IF EXISTS rilevations_view CASCADE")
    conn.commit()


    #Creo la VIEW in due Step. Invece di creare due VIEW, uso WITH per crearle una alla volta all'interno della stessa query. Il secondo Step, ViewTemporanea2, usa le colonne del primo Step, ViewTemporanea1, per calcolare le sue colonne.
    cur.execute("""
    CREATE OR REPLACE VIEW rilevations_view AS
    WITH base AS (
        SELECT
            r.*,
            -- Assumiamo che questi ID siano disponibili nella tabella rilevations
            teg_home."TeamID_auto" AS IDHomeTeam_auto,
            teg_visitor."TeamID_auto" AS IDVisitorTeam_auto
        FROM rilevations r
        JOIN teams_each_game teg_home
            ON teg_home."GameID" = r."GameID" AND teg_home."HomeAway" = FALSE
        JOIN teams_each_game teg_visitor
            ON teg_visitor."GameID" = r."GameID" AND teg_visitor."HomeAway" = TRUE
    ),
    players_mapped AS (
        SELECT
            v1.*,

            -- HomePlayer1 → HomePlayer6 (numeri di maglia)
            CASE WHEN MOD(IDHomeTeam_auto, 2) = 1 THEN "Player1" ELSE "Player7" END AS "HomePlayer1",
            CASE WHEN MOD(IDHomeTeam_auto, 2) = 1 THEN "Player2" ELSE "Player8" END AS "HomePlayer2",
            CASE WHEN MOD(IDHomeTeam_auto, 2) = 1 THEN "Player3" ELSE "Player9" END AS "HomePlayer3",
            CASE WHEN MOD(IDHomeTeam_auto, 2) = 1 THEN "Player4" ELSE "Player10" END AS "HomePlayer4",
            CASE WHEN MOD(IDHomeTeam_auto, 2) = 1 THEN "Player5" ELSE "Player11" END AS "HomePlayer5",
            CASE WHEN MOD(IDHomeTeam_auto, 2) = 1 THEN "Player6" ELSE "Player12" END AS "HomePlayer6",

            -- VisitorPlayer1 → VisitorPlayer6
            CASE WHEN MOD(IDHomeTeam_auto, 2) = 1 THEN "Player7" ELSE "Player1" END AS "VisitorPlayer1",
            CASE WHEN MOD(IDHomeTeam_auto, 2) = 1 THEN "Player8" ELSE "Player2" END AS "VisitorPlayer2",
            CASE WHEN MOD(IDHomeTeam_auto, 2) = 1 THEN "Player9" ELSE "Player3" END AS "VisitorPlayer3",
            CASE WHEN MOD(IDHomeTeam_auto, 2) = 1 THEN "Player10" ELSE "Player4" END AS "VisitorPlayer4",
            CASE WHEN MOD(IDHomeTeam_auto, 2) = 1 THEN "Player11" ELSE "Player5" END AS "VisitorPlayer5",
            CASE WHEN MOD(IDHomeTeam_auto, 2) = 1 THEN "Player12" ELSE "Player6" END AS "VisitorPlayer6"

        FROM base b
    ),
    finale AS (
        SELECT
            pm.*,

            -- JOIN con players_each_game per trovare gli ID_auto dei giocatori di casa
            phe1."PlayerID_auto" AS "HomePlayer1_ID_auto",
            phe2."PlayerID_auto" AS "HomePlayer2_ID_auto",
            phe3."PlayerID_auto" AS "HomePlayer3_ID_auto",
            phe4."PlayerID_auto" AS "HomePlayer4_ID_auto",
            phe5."PlayerID_auto" AS "HomePlayer5_ID_auto",
            phe6."PlayerID_auto" AS "HomePlayer6_ID_auto",

            -- JOIN con players_each_game per trasferta
            pve1."PlayerID_auto" AS "VisitorPlayer1_ID_auto",
            pve2."PlayerID_auto" AS "VisitorPlayer2_ID_auto",
            pve3."PlayerID_auto" AS "VisitorPlayer3_ID_auto",
            pve4."PlayerID_auto" AS "VisitorPlayer4_ID_auto",
            pve5."PlayerID_auto" AS "VisitorPlayer5_ID_auto",
            pve6."PlayerID_auto" AS "VisitorPlayer6_ID_auto"

        FROM players_mapped pm

        LEFT JOIN players_each_game phe1
            ON phe1."GameID" = pm."GameID" AND phe1."TeamID_auto" = pm.IDHomeTeam_auto AND phe1."NumeroMaglia" = pm."HomePlayer1"
        LEFT JOIN players_each_game phe2 ON phe2."GameID" = pm."GameID" AND phe2."TeamID_auto" = pm.IDHomeTeam_auto AND phe2."NumeroMaglia" = pm."HomePlayer2"
        LEFT JOIN players_each_game phe3 ON phe3."GameID" = pm."GameID" AND phe3."TeamID_auto" = pm.IDHomeTeam_auto AND phe3."NumeroMaglia" = pm."HomePlayer3"
        LEFT JOIN players_each_game phe4 ON phe4."GameID" = pm."GameID" AND phe4."TeamID_auto" = pm.IDHomeTeam_auto AND phe4."NumeroMaglia" = pm."HomePlayer4"
        LEFT JOIN players_each_game phe5 ON phe5."GameID" = pm."GameID" AND phe5."TeamID_auto" = pm.IDHomeTeam_auto AND phe5."NumeroMaglia" = pm."HomePlayer5"
        LEFT JOIN players_each_game phe6 ON phe6."GameID" = pm."GameID" AND phe6."TeamID_auto" = pm.IDHomeTeam_auto AND phe6."NumeroMaglia" = pm."HomePlayer6"

        LEFT JOIN players_each_game pve1 ON pve1."GameID" = pm."GameID" AND pve1."TeamID_auto" = pm.IDVisitorTeam_auto AND pve1."NumeroMaglia" = pm."VisitorPlayer1"
        LEFT JOIN players_each_game pve2 ON pve2."GameID" = pm."GameID" AND pve2."TeamID_auto" = pm.IDVisitorTeam_auto AND pve2."NumeroMaglia" = pm."VisitorPlayer2"
        LEFT JOIN players_each_game pve3 ON pve3."GameID" = pm."GameID" AND pve3."TeamID_auto" = pm.IDVisitorTeam_auto AND pve3."NumeroMaglia" = pm."VisitorPlayer3"
        LEFT JOIN players_each_game pve4 ON pve4."GameID" = pm."GameID" AND pve4."TeamID_auto" = pm.IDVisitorTeam_auto AND pve4."NumeroMaglia" = pm."VisitorPlayer4"
        LEFT JOIN players_each_game pve5 ON pve5."GameID" = pm."GameID" AND pve5."TeamID_auto" = pm.IDVisitorTeam_auto AND pve5."NumeroMaglia" = pm."VisitorPlayer5"
        LEFT JOIN players_each_game pve6 ON pve6."GameID" = pm."GameID" AND pve6."TeamID_auto" = pm.IDVisitorTeam_auto AND pve6."NumeroMaglia" = pm."VisitorPlayer6"
    )

    SELECT * FROM finale;


    """)



def crea_rilevationswithid2():
    cur.execute("DROP VIEW IF EXISTS rilevations_view CASCADE")
    conn.commit()


    #Creo la VIEW in due Step. Invece di creare due VIEW, uso WITH per crearle una alla volta all'interno della stessa query. Il secondo Step, ViewTemporanea2, usa le colonne del primo Step, ViewTemporanea1, per calcolare le sue colonne.
    cur.execute("""
    CREATE OR REPLACE VIEW rilevations_view AS
    
    --Creo una tabella temporanea che mi dice quale è la squadra che batte
    WITH ServingTeam AS (
        -- Identifico quale squadra serve per ogni azione
        SELECT 
            "GameID",
            "SetNumber",
            "ActionNumber",
            CASE 
                WHEN "Foundamental" = 'S' AND "whichTeam" = FALSE THEN FALSE
                WHEN "Foundamental" = 'S' AND "whichTeam" = TRUE THEN TRUE
                ELSE NULL
            END AS "whichTeamServes"
        FROM rilevations
        WHERE "Foundamental" = 'S'  -- Solo le righe con servizio
    ),
    
    
    ViewTemporanea1 AS (
        SELECT
            r.*,
            -- Assumiamo che questi ID siano disponibili nella tabella rilevations
            teg_home."TeamID_auto" AS "IDHomeTeam_auto",
            teg_visitor."TeamID_auto" AS "IDVisitorTeam_auto",
            
            -- Aggiungo whichTeamServes dalla tabella ServingTeam
            st."whichTeamServes"
            
        FROM rilevations r
        JOIN teams_each_game teg_home
            ON teg_home."GameID" = r."GameID" AND teg_home."HomeAway" = FALSE
        JOIN teams_each_game teg_visitor
            ON teg_visitor."GameID" = r."GameID" AND teg_visitor."HomeAway" = TRUE
        -- Faccio JOIN con ServingTeam per mettere in ogni riga quale è la squadra che ha battuto in quell'azione
        LEFT JOIN ServingTeam st
            ON r."GameID" = st."GameID" 
            AND r."SetNumber" = st."SetNumber" 
            AND r."ActionNumber" = st."ActionNumber"
    ),
    
    
    ViewTemporanea2 AS (
        SELECT
            v1.*,
            
            --Metto tutti gli HomePlayerX e VisitorPlayerX in base a se peg1...peg6 hanno TeamID_auto = IDHomeTeam_auto, allora erano giusti fin da subito, ovvero i primi 6 giocatori erano quelli di casa, quindi metto in HomeTeamX PlayerX. Altrimenti se il TeamID_auto = IDVisitorTeam_auto, allora in HomePlayerX metto PlayerX+6, ovvero il giocatore che all'inizio credevo fosse in trasferta.
            CASE
                WHEN peg1."TeamID_auto" = v1."IDHomeTeam_auto" THEN peg1."NumeroMaglia"
                WHEN peg1."TeamID_auto" = v1."IDVisitorTeam_auto" THEN peg7."NumeroMaglia"
                ELSE NULL
            END AS "HomePlayer1",
            CASE
                WHEN peg2."TeamID_auto" = v1."IDHomeTeam_auto" THEN peg2."NumeroMaglia"
                WHEN peg2."TeamID_auto" = v1."IDVisitorTeam_auto" THEN peg8."NumeroMaglia"
                ELSE NULL
            END AS "HomePlayer2",
            CASE
                WHEN peg3."TeamID_auto" = v1."IDHomeTeam_auto" THEN peg3."NumeroMaglia"
                WHEN peg3."TeamID_auto" = v1."IDVisitorTeam_auto" THEN peg9."NumeroMaglia"
                ELSE NULL
            END AS "HomePlayer3",
            CASE
                WHEN peg4."TeamID_auto" = v1."IDHomeTeam_auto" THEN peg4."NumeroMaglia"
                WHEN peg4."TeamID_auto" = v1."IDVisitorTeam_auto" THEN peg10."NumeroMaglia"
                ELSE NULL
            END AS "HomePlayer4",
            CASE
                WHEN peg5."TeamID_auto" = v1."IDHomeTeam_auto" THEN peg5."NumeroMaglia"
                WHEN peg5."TeamID_auto" = v1."IDVisitorTeam_auto" THEN peg11."NumeroMaglia"
                ELSE NULL
            END AS "HomePlayer5",
            CASE
                WHEN peg6."TeamID_auto" = v1."IDHomeTeam_auto" THEN peg6."NumeroMaglia"
                WHEN peg6."TeamID_auto" = v1."IDVisitorTeam_auto" THEN peg12."NumeroMaglia"
                ELSE NULL
            END AS "HomePlayer6",
            CASE
                WHEN peg7."TeamID_auto" = v1."IDVisitorTeam_auto" THEN peg7."NumeroMaglia"
                WHEN peg7."TeamID_auto" = v1."IDHomeTeam_auto" THEN peg1."NumeroMaglia"
                ELSE NULL
            END AS "VisitorPlayer1",
            CASE
                WHEN peg8."TeamID_auto" = v1."IDVisitorTeam_auto" THEN peg8."NumeroMaglia"
                WHEN peg8."TeamID_auto" = v1."IDHomeTeam_auto" THEN peg2."NumeroMaglia"
                ELSE NULL
            END AS "VisitorPlayer2",
            CASE
                WHEN peg9."TeamID_auto" = v1."IDVisitorTeam_auto" THEN peg9."NumeroMaglia"
                WHEN peg9."TeamID_auto" = v1."IDHomeTeam_auto" THEN peg3."NumeroMaglia"
                ELSE NULL
            END AS "VisitorPlayer3",
            CASE
                WHEN peg10."TeamID_auto" = v1."IDVisitorTeam_auto" THEN peg10."NumeroMaglia"
                WHEN peg10."TeamID_auto" = v1."IDHomeTeam_auto" THEN peg4."NumeroMaglia"
                ELSE NULL
            END AS "VisitorPlayer4",
            CASE
                WHEN peg11."TeamID_auto" = v1."IDVisitorTeam_auto" THEN peg11."NumeroMaglia"
                WHEN peg11."TeamID_auto" = v1."IDHomeTeam_auto" THEN peg5."NumeroMaglia"
                ELSE NULL
            END AS "VisitorPlayer5",
            CASE
                WHEN peg12."TeamID_auto" = v1."IDVisitorTeam_auto" THEN peg12."NumeroMaglia"
                WHEN peg12."TeamID_auto" = v1."IDHomeTeam_auto" THEN peg6."NumeroMaglia"
                ELSE NULL
            END AS "VisitorPlayer6",
            
            --Metto gli HomePlayerX_ID secondo lo stesso criterio (metto i loro ID, non i loro ID_auto, perchè non credo mi servano)
            CASE
                WHEN peg1."TeamID_auto" = v1."IDHomeTeam_auto" THEN peg1."PlayerID"
                WHEN peg1."TeamID_auto" = v1."IDVisitorTeam_auto" THEN peg7."PlayerID"
                ELSE NULL
            END AS "HomePlayer1_ID",
            CASE
                WHEN peg2."TeamID_auto" = v1."IDHomeTeam_auto" THEN peg2."PlayerID"
                WHEN peg2."TeamID_auto" = v1."IDVisitorTeam_auto" THEN peg8."PlayerID"
                ELSE NULL
            END AS "HomePlayer2_ID",
            CASE
                WHEN peg3."TeamID_auto" = v1."IDHomeTeam_auto" THEN peg3."PlayerID"
                WHEN peg3."TeamID_auto" = v1."IDVisitorTeam_auto" THEN peg9."PlayerID"
                ELSE NULL
            END AS "HomePlayer3_ID",
            CASE
                WHEN peg4."TeamID_auto" = v1."IDHomeTeam_auto" THEN peg4."PlayerID"
                WHEN peg4."TeamID_auto" = v1."IDVisitorTeam_auto" THEN peg10."PlayerID"
                ELSE NULL
            END AS "HomePlayer4_ID",
            CASE
                WHEN peg5."TeamID_auto" = v1."IDHomeTeam_auto" THEN peg5."PlayerID"
                WHEN peg5."TeamID_auto" = v1."IDVisitorTeam_auto" THEN peg11."PlayerID"
                ELSE NULL
            END AS "HomePlayer5_ID",
            CASE
                WHEN peg6."TeamID_auto" = v1."IDHomeTeam_auto" THEN peg6."PlayerID"
                WHEN peg6."TeamID_auto" = v1."IDVisitorTeam_auto" THEN peg12."PlayerID"
                ELSE NULL
            END AS "HomePlayer6_ID",
            CASE
                WHEN peg7."TeamID_auto" = v1."IDVisitorTeam_auto" THEN peg7."PlayerID"
                WHEN peg7."TeamID_auto" = v1."IDHomeTeam_auto" THEN peg1."PlayerID"
                ELSE NULL
            END AS "VisitorPlayer1_ID",
            CASE
                WHEN peg8."TeamID_auto" = v1."IDVisitorTeam_auto" THEN peg8."PlayerID"
                WHEN peg8."TeamID_auto" = v1."IDHomeTeam_auto" THEN peg2."PlayerID"
                ELSE NULL
            END AS "VisitorPlayer2_ID",
            CASE
                WHEN peg9."TeamID_auto" = v1."IDVisitorTeam_auto" THEN peg9."PlayerID"
                WHEN peg9."TeamID_auto" = v1."IDHomeTeam_auto" THEN peg3."PlayerID"
                ELSE NULL
            END AS "VisitorPlayer3_ID",
            CASE
                WHEN peg10."TeamID_auto" = v1."IDVisitorTeam_auto" THEN peg10."PlayerID"
                WHEN peg10."TeamID_auto" = v1."IDHomeTeam_auto" THEN peg4."PlayerID"
                ELSE NULL
            END AS "VisitorPlayer4_ID",
            CASE
                WHEN peg11."TeamID_auto" = v1."IDVisitorTeam_auto" THEN peg11."PlayerID"
                WHEN peg11."TeamID_auto" = v1."IDHomeTeam_auto" THEN peg5."PlayerID"
                ELSE NULL
            END AS "VisitorPlayer5_ID",
            CASE
                WHEN peg12."TeamID_auto" = v1."IDVisitorTeam_auto" THEN peg12."PlayerID"
                WHEN peg12."TeamID_auto" = v1."IDHomeTeam_auto" THEN peg6."PlayerID"
                ELSE NULL
            END AS "VisitorPlayer6_ID",
            
            
            --Metto NumeroMagliaID, ovvero l'ID del giocatore che ha compiuto il tocco. Lo trovo in base a whichTeamID
            pegtouch."PlayerID" AS "NumeroMaglia_ID",
            
            --Creo PalleggCasa e PalleggOspite
            CASE
                WHEN pegpallegg1."TeamID_auto" = v1."IDHomeTeam_auto" THEN pegpallegg1."NumeroMaglia"      --Se il pallegg1 è del team di casa, allora è lui PalleggCasa
                WHEN pegpallegg1."TeamID_auto" = v1."IDVisitorTeam_auto" THEN pegpallegg2."NumeroMaglia"   --Altrimenti, se il pallegg1 è del team ospite, allora il pallegg2 è PalleggCasa
                ELSE NULL
            END AS "PalleggCasa",
            CASE
                WHEN pegpallegg2."TeamID_auto" = v1."IDVisitorTeam_auto" THEN pegpallegg2."NumeroMaglia"   --Se il pallegg2 è del team ospite, allora è lui PalleggOspite
                WHEN pegpallegg2."TeamID_auto" = v1."IDHomeTeam_auto" THEN pegpallegg1."NumeroMaglia"      --Altrimenti, se il pallegg2 è del team di casa, allora il pallegg1 è PalleggOspite
                ELSE NULL
            END AS "PalleggOspite",
            
            --Creo PosizPalleggCasa e PosizPalleggOspite
            CASE
                WHEN pegpallegg1."TeamID_auto" = v1."IDHomeTeam_auto" THEN v1."PosizPallegg1"      --Se il pallegg1 è del team di casa, allora la sua posizione è PosizPalleggCasa
                WHEN pegpallegg1."TeamID_auto" = v1."IDVisitorTeam_auto" THEN v1."PosizPallegg2"   --Altrimenti, se il pallegg1 è del team ospite, allora la posizione del pallegg2 è PosizPalleggCasa
                ELSE NULL
            END AS "PosizPalleggCasa",
            CASE
                WHEN pegpallegg2."TeamID_auto" = v1."IDVisitorTeam_auto" THEN v1."PosizPallegg2"   --Se il pallegg2 è del team ospite, allora la sua posizione è PosizPalleggOspite
                WHEN pegpallegg2."TeamID_auto" = v1."IDHomeTeam_auto" THEN v1."PosizPallegg1"      --Altrimenti, se il pallegg2 è del team di casa, allora la posizione del pallegg1 è PosizPalleggOspite
                ELSE NULL
            END AS "PosizPalleggOspite"
            
            --Creo PalleggCasa_ID e PalleggOspite_ID (metto il loro PlayerID, non PlayerID_auto, perchè non credo mi servano)
            --CASE
            --    WHEN pegpallegg1."TeamID_auto" = v1."IDHomeTeam_auto" THEN pegpallegg1."PlayerID"      --Se il pallegg1 è del team di casa, allora il suo ID è PalleggCasa_ID
            --    WHEN pegpallegg1."TeamID_auto" = v1."IDVisitorTeam_auto" THEN pegpallegg2."PlayerID"   --Altrimenti, se il pallegg1 è del team ospite, allora l'ID del pallegg2 è PalleggCasa_ID
            --    ELSE NULL
            --END AS "PalleggCasa_ID",
            --CASE
            --    WHEN pegpallegg2."TeamID_auto" = v1."IDVisitorTeam_auto" THEN pegpallegg2."PlayerID"  --Se il pallegg2 è del team ospite, allora il suo ID è PalleggOspite_ID
            --    WHEN pegpallegg2."TeamID_auto" = v1."IDHomeTeam_auto" THEN pegpallegg1."PlayerID"     --Altrimenti, se il pallegg2 è del team di casa, allora l'ID del pallegg1 è PalleggOspite_ID
            --    ELSE NULL
            --END AS "PalleggOspite_ID"
                        

        FROM ViewTemporanea1 v1
        

        -- 3) Per ogni PlayerX, faccio JOIN su (GameID, PlayerID_auto)
        LEFT JOIN players_each_game peg1
            ON peg1."GameID" = v1."GameID"
            AND peg1."PlayerID_auto" = v1."Player1_ID_auto"
            
        LEFT JOIN players_each_game peg2
            ON peg2."GameID" = v1."GameID"
            AND peg2."PlayerID_auto" = v1."Player2_ID_auto"
        
        LEFT JOIN players_each_game peg3
            ON peg3."GameID" = v1."GameID"
            AND peg3."PlayerID_auto" = v1."Player3_ID_auto"
        
        LEFT JOIN players_each_game peg4
            ON peg4."GameID" = v1."GameID"
            AND peg4."PlayerID_auto" = v1."Player4_ID_auto"
        
        LEFT JOIN players_each_game peg5
            ON peg5."GameID" = v1."GameID"
            AND peg5."PlayerID_auto" = v1."Player5_ID_auto"
        
        LEFT JOIN players_each_game peg6
            ON peg6."GameID" = v1."GameID"
            AND peg6."PlayerID_auto" = v1."Player6_ID_auto"

        LEFT JOIN players_each_game peg7
            ON peg7."GameID" = v1."GameID"
            AND peg7."PlayerID_auto" = v1."Player7_ID_auto"
        
        LEFT JOIN players_each_game peg8
            ON peg8."GameID" = v1."GameID"
            AND peg8."PlayerID_auto" = v1."Player8_ID_auto"
        
        LEFT JOIN players_each_game peg9
            ON peg9."GameID" = v1."GameID"
            AND peg9."PlayerID_auto" = v1."Player9_ID_auto"
        
        LEFT JOIN players_each_game peg10
            ON peg10."GameID" = v1."GameID"
            AND peg10."PlayerID_auto" = v1."Player10_ID_auto"
        
        LEFT JOIN players_each_game peg11
            ON peg11."GameID" = v1."GameID"
            AND peg11."PlayerID_auto" = v1."Player11_ID_auto"
        
        LEFT JOIN players_each_game peg12
            ON peg12."GameID" = v1."GameID"
            AND peg12."PlayerID_auto" = v1."Player12_ID_auto"
        
        --Per trovare l'ID di NumeroMaglia guardo in players_each_game la riga con lo stesso GameID, TeamID_auto (che è = a whichTeamID) e NumeroMaglia
        LEFT JOIN players_each_game pegtouch  
            ON pegtouch."GameID" = v1."GameID" 
            AND pegtouch."TeamID_auto" = v1."whichTeamID" 
            AND pegtouch."NumeroMaglia" = v1."NumeroMaglia"
            
        --Per trovare l'ID di PalleggCasa, mi prendo le righe in players_each_game di Pallegg1 e Pallegg2, e poi vedo a chi dei due appartiene l'ID di casa
        LEFT JOIN players_each_game pegpallegg1
            ON pegpallegg1."GameID" = v1."GameID"
            AND pegpallegg1."PlayerID_auto" = v1."Pallegg1_ID_auto"
            
        --Per trovare l'ID di PalleggOspite
        LEFT JOIN players_each_game pegpallegg2
            ON pegpallegg2."GameID" = v1."GameID"
            AND pegpallegg2."PlayerID_auto" = v1."Pallegg2_ID_auto"

    ),
    

    ViewTemporanea3 AS (  --Inizio dello step 3, creo una view che usa le colonne della view precedente e ne aggiungo altre
        SELECT 
            v2.*,   -- Tutte le colonne dello Step precedente, la view ViewTemporanea2, che abbrevio in v2

            -- Aggiungo Pcasa_ID, il palleggiatore di casa
            CASE 
                WHEN v2."PosizPalleggCasa" = 1 THEN "HomePlayer1_ID"
                WHEN v2."PosizPalleggCasa" = 6 THEN "HomePlayer6_ID"
                WHEN v2."PosizPalleggCasa" = 5 THEN "HomePlayer5_ID"
                WHEN v2."PosizPalleggCasa" = 4 THEN "HomePlayer4_ID"
                WHEN v2."PosizPalleggCasa" = 3 THEN "HomePlayer3_ID"
                WHEN v2."PosizPalleggCasa" = 2 THEN "HomePlayer2_ID"
                ELSE NULL
            END AS "Pcasa_ID",

            -- Aggiungo S1casa_ID, lo schiacciatore vicino al palleggiatore
            CASE 
                WHEN v2."PosizPalleggCasa" = 1 THEN "HomePlayer2_ID"
                WHEN v2."PosizPalleggCasa" = 6 THEN "HomePlayer1_ID"
                WHEN v2."PosizPalleggCasa" = 5 THEN "HomePlayer6_ID"
                WHEN v2."PosizPalleggCasa" = 4 THEN "HomePlayer5_ID"
                WHEN v2."PosizPalleggCasa" = 3 THEN "HomePlayer4_ID"
                WHEN v2."PosizPalleggCasa" = 2 THEN "HomePlayer3_ID"
                ELSE NULL
            END AS "S1casa_ID",
            
            -- Aggiungo C2casa_ID, il centrale lontano dal palleggiatore
            CASE 
                WHEN v2."PosizPalleggCasa" = 1 THEN "HomePlayer3_ID"
                WHEN v2."PosizPalleggCasa" = 6 THEN "HomePlayer2_ID"
                WHEN v2."PosizPalleggCasa" = 5 THEN "HomePlayer1_ID"
                WHEN v2."PosizPalleggCasa" = 4 THEN "HomePlayer6_ID"
                WHEN v2."PosizPalleggCasa" = 3 THEN "HomePlayer5_ID"
                WHEN v2."PosizPalleggCasa" = 2 THEN "HomePlayer4_ID"
                ELSE NULL
            END AS "C2casa_ID",
            
            -- Aggiungo Ocasa_ID, l'opposto
            CASE 
                WHEN v2."PosizPalleggCasa" = 1 THEN "HomePlayer4_ID"
                WHEN v2."PosizPalleggCasa" = 6 THEN "HomePlayer3_ID"
                WHEN v2."PosizPalleggCasa" = 5 THEN "HomePlayer2_ID"
                WHEN v2."PosizPalleggCasa" = 4 THEN "HomePlayer1_ID"
                WHEN v2."PosizPalleggCasa" = 3 THEN "HomePlayer6_ID"
                WHEN v2."PosizPalleggCasa" = 2 THEN "HomePlayer5_ID"
                ELSE NULL
            END AS "Ocasa_ID",

            -- Aggiungo S2casa_ID, lo schiacciatore lontano dal palleggiatore
            CASE 
                WHEN v2."PosizPalleggCasa" = 1 THEN "HomePlayer5_ID"
                WHEN v2."PosizPalleggCasa" = 6 THEN "HomePlayer4_ID"
                WHEN v2."PosizPalleggCasa" = 5 THEN "HomePlayer3_ID"
                WHEN v2."PosizPalleggCasa" = 4 THEN "HomePlayer2_ID"
                WHEN v2."PosizPalleggCasa" = 3 THEN "HomePlayer1_ID"
                WHEN v2."PosizPalleggCasa" = 2 THEN "HomePlayer6_ID"
                ELSE NULL
            END AS "S2casa_ID",

            -- Aggiungo C1casa_ID, il centrale vicino al palleggiatore
            CASE 
                WHEN v2."PosizPalleggCasa" = 1 THEN "HomePlayer6_ID"
                WHEN v2."PosizPalleggCasa" = 6 THEN "HomePlayer5_ID"
                WHEN v2."PosizPalleggCasa" = 5 THEN "HomePlayer4_ID"
                WHEN v2."PosizPalleggCasa" = 4 THEN "HomePlayer3_ID"
                WHEN v2."PosizPalleggCasa" = 3 THEN "HomePlayer2_ID"
                WHEN v2."PosizPalleggCasa" = 2 THEN "HomePlayer1_ID"
                ELSE NULL
            END AS "C1casa_ID",
            
            
            -- Aggiungo Pospite_ID, il palleggiatore ospite
            CASE 
                WHEN v2."PosizPalleggOspite" = 1 THEN "VisitorPlayer1_ID"
                WHEN v2."PosizPalleggOspite" = 6 THEN "VisitorPlayer6_ID"
                WHEN v2."PosizPalleggOspite" = 5 THEN "VisitorPlayer5_ID"
                WHEN v2."PosizPalleggOspite" = 4 THEN "VisitorPlayer4_ID"
                WHEN v2."PosizPalleggOspite" = 3 THEN "VisitorPlayer3_ID"
                WHEN v2."PosizPalleggOspite" = 2 THEN "VisitorPlayer2_ID"
                ELSE NULL
            END AS "Pospite_ID",

            -- Aggiungo S1ospite_ID, lo schiacciatore vicino al palleggiatore
            CASE 
                WHEN v2."PosizPalleggOspite" = 1 THEN "VisitorPlayer2_ID"
                WHEN v2."PosizPalleggOspite" = 6 THEN "VisitorPlayer1_ID"
                WHEN v2."PosizPalleggOspite" = 5 THEN "VisitorPlayer6_ID"
                WHEN v2."PosizPalleggOspite" = 4 THEN "VisitorPlayer5_ID"
                WHEN v2."PosizPalleggOspite" = 3 THEN "VisitorPlayer4_ID"
                WHEN v2."PosizPalleggOspite" = 2 THEN "VisitorPlayer3_ID"
                ELSE NULL
            END AS "S1ospite_ID",
            
            -- Aggiungo C2ospite_ID, il centrale lontano dal palleggiatore
            CASE 
                WHEN v2."PosizPalleggOspite" = 1 THEN "VisitorPlayer3_ID"
                WHEN v2."PosizPalleggOspite" = 6 THEN "VisitorPlayer2_ID"
                WHEN v2."PosizPalleggOspite" = 5 THEN "VisitorPlayer1_ID"
                WHEN v2."PosizPalleggOspite" = 4 THEN "VisitorPlayer6_ID"
                WHEN v2."PosizPalleggOspite" = 3 THEN "VisitorPlayer5_ID"
                WHEN v2."PosizPalleggOspite" = 2 THEN "VisitorPlayer4_ID"
                ELSE NULL
            END AS "C2ospite_ID",
            
            -- Aggiungo Oospite_ID, l'opposto
            CASE 
                WHEN v2."PosizPalleggOspite" = 1 THEN "VisitorPlayer4_ID"
                WHEN v2."PosizPalleggOspite" = 6 THEN "VisitorPlayer3_ID"
                WHEN v2."PosizPalleggOspite" = 5 THEN "VisitorPlayer2_ID"
                WHEN v2."PosizPalleggOspite" = 4 THEN "VisitorPlayer1_ID"
                WHEN v2."PosizPalleggOspite" = 3 THEN "VisitorPlayer6_ID"
                WHEN v2."PosizPalleggOspite" = 2 THEN "VisitorPlayer5_ID"
                ELSE NULL
            END AS "Oospite_ID",

            -- Aggiungo S2ospite_ID, lo schiacciatore lontano dal palleggiatore
            CASE 
                WHEN v2."PosizPalleggOspite" = 1 THEN "VisitorPlayer5_ID"
                WHEN v2."PosizPalleggOspite" = 6 THEN "VisitorPlayer4_ID"
                WHEN v2."PosizPalleggOspite" = 5 THEN "VisitorPlayer3_ID"
                WHEN v2."PosizPalleggOspite" = 4 THEN "VisitorPlayer2_ID"
                WHEN v2."PosizPalleggOspite" = 3 THEN "VisitorPlayer1_ID"
                WHEN v2."PosizPalleggOspite" = 2 THEN "VisitorPlayer6_ID"
                ELSE NULL
            END AS "S2ospite_ID",

            -- Aggiungo C1ospite_ID, il centrale vicino al palleggiatore
            CASE 
                WHEN v2."PosizPalleggOspite" = 1 THEN "VisitorPlayer6_ID"
                WHEN v2."PosizPalleggOspite" = 6 THEN "VisitorPlayer5_ID"
                WHEN v2."PosizPalleggOspite" = 5 THEN "VisitorPlayer4_ID"
                WHEN v2."PosizPalleggOspite" = 4 THEN "VisitorPlayer3_ID"
                WHEN v2."PosizPalleggOspite" = 3 THEN "VisitorPlayer2_ID"
                WHEN v2."PosizPalleggOspite" = 2 THEN "VisitorPlayer1_ID"
                ELSE NULL
            END AS "C1ospite_ID",
            
            --Aggiungo una colonna che mi dice se il palleggiatore di casa è in prima linea (1) o in seconda linea (0)
            CASE 
                WHEN v2."PosizPalleggCasa" IN (2,3,4) THEN 1
                ELSE 0
            END AS "Pcasa1linea",
            
            --Aggiungo una colonna che mi dice se il palleggiatore ospite è in prima linea (1) o in seconda linea (0)
            CASE 
                WHEN v2."PosizPalleggOspite" IN (2,3,4) THEN 1
                ELSE 0
            END AS "Pospite1linea"            
            
        FROM ViewTemporanea2 v2  --Prendo i dati dallo Step precedente, la view temporanea appena creata, ViewTemporanea2, che abbrevio in v2
    ),
    
    --Aggiungo la colonna whichTeamIDreal, che è come la colonna whichTeamID, ma invece dell'ID_auto, mette quello vero
    ViewTemporanea4 AS (
        SELECT 
            v3.*,   -- Tutte le colonne dello Step precedente, la view ViewTemporanea3, che abbrevio in v3
            
            t_real."TeamID" AS "whichTeamIDreal"
        FROM ViewTemporanea3 v3
        LEFT JOIN teams_each_game t_real
            ON t_real."GameID" = v3."GameID"
            AND t_real."TeamID_auto" = v3."whichTeamID"
    )
    


    SELECT * FROM ViewTemporanea4;  --Alla fine, per la mia VIEW finale, RilevationsWithID, seleziono tutte le colonne della view temporanea appena creata, ViewTemporanea3
    """)




#crea_rilevationswithid()
#crea_rilevationswithid1()
crea_rilevationswithid2()
conn.commit()






print("VIEW creata con successo")

#Alla fine una VIEW è solo una query





#Trovo le righe con valori NULL nelle colonne dei ruoli
cur.execute("""
SELECT "GameID", "RilevationNumber", "Foundamental", "Pcasa_ID", "S1casa_ID", "S2casa_ID", "Ocasa_ID", "C1casa_ID", "C2casa_ID"
FROM rilevations_view
WHERE ("Pcasa_ID" IS NULL 
    OR "S1casa_ID" IS NULL 
    OR "S2casa_ID" IS NULL 
    OR "Ocasa_ID" IS NULL 
    OR "C1casa_ID" IS NULL 
    OR "C2casa_ID" IS NULL)
AND "Foundamental" NOT IN ('P', 'z', '*')
ORDER BY "GameID", "RilevationNumber"
LIMIT 5000;
""")

null_rows = cur.fetchall()
if null_rows:
    print("\nRighe con valori NULL trovate (esclusi Foundamental P/z/*):")
    for row in null_rows:
        print(f"\nGameID: {row[0]}, RilevationNumber: {row[1]}, Foundamental: {row[2]}")
        print(f"Pcasa_ID: {row[3]}")
        print(f"S1casa_ID: {row[4]}")
        print(f"S2casa_ID: {row[5]}")
        print(f"Ocasa_ID: {row[6]}")
        print(f"C1casa_ID: {row[7]}")
        print(f"C2casa_ID: {row[8]}")
else:
    print("Nessuna riga con valori NULL trovata (esclusi Foundamental P/z/*).")   #Escludo le righe con questi fondamentali perchè in queste è normale avere dei NULL in HomePlayerX e VisitorPlayerX


#Se viene stampato qualcosa, controlla che non ci siano problemi alla fonte, nel file, ad esempio che non manchi un numero(giocatore) ogni volta tra i 12 giocatori in campo
#Fai le correzioni dei giocatori mancanti/duplicati nel file c2virgola5_controllo_playereachgame_duplicati.ipynb


#Ora verifichiamo che quando NumeroMaglia è diverso da NULL o da 0, allora NumeroMaglia_ID non sia NULL
cur.execute("""
SELECT 
    "GameID", 
    "RilevationNumber", 
    "NumeroMaglia", 
    "NumeroMaglia_ID"
FROM rilevations_view
WHERE "NumeroMaglia" IS NOT NULL 
    AND "NumeroMaglia" != 0 
    AND "NumeroMaglia_ID" IS NULL
LIMIT 100;
""")

problematic_rows = cur.fetchall()
if problematic_rows:
    print("\nRighe problematiche trovate (NumeroMaglia presente ma NumeroMaglia_ID NULL):")
    for row in problematic_rows:
        print(f"GameID: {row[0]}, RilevationNumber: {row[1]}, NumeroMaglia: {row[2]}")
    
    # Verifica se il problema è dovuto a mancate corrispondenze in players_each_game
    print("\nVerifica delle corrispondenze in players_each_game per la prima riga problematica:")
    if problematic_rows:
        first_row = problematic_rows[0]
        cur.execute("""
        SELECT *
        FROM players_each_game
        WHERE "GameID" = %s AND "NumeroMaglia" = %s
        """, (first_row[0], first_row[2]))
        
        matching_players = cur.fetchall()
        if matching_players:
            print(f"Trovate {len(matching_players)} corrispondenze in players_each_game")
            for player in matching_players:
                print(player)
        else:
            print(f"Nessuna corrispondenza trovata in players_each_game per GameID: {first_row[0]}, NumeroMaglia: {first_row[2]}")
else:
    print("✅ Nessuna riga problematica trovata. Tutti i NumeroMaglia non NULL e non 0 hanno un NumeroMaglia_ID corrispondente.")











'''
print()
# Verifichiamo i dati specifici per GameID 689
cur.execute("""
WITH problematic_data AS (
    SELECT r."GameID", r."RilevationNumber", r."whichTeam",
           r."HomePlayer1", r."HomePlayer2", r."HomePlayer3", 
           r."HomePlayer4", r."HomePlayer5", r."HomePlayer6",
           g."IDHomeTeam", g."IDVisitorTeam",
           p1."TeamID" as p1_team, p1."PlayerID" as p1_player,
           p2."TeamID" as p2_team, p2."PlayerID" as p2_player,
           p3."TeamID" as p3_team, p3."PlayerID" as p3_player,
           p4."TeamID" as p4_team, p4."PlayerID" as p4_player,
           p5."TeamID" as p5_team, p5."PlayerID" as p5_player,
           p6."TeamID" as p6_team, p6."PlayerID" as p6_player
    FROM rilevations r
    LEFT JOIN games g ON r."GameID" = g."GameID"
    LEFT JOIN players_each_game p1 ON p1."GameID" = r."GameID" AND p1."NumeroMaglia" = r."HomePlayer1"
    LEFT JOIN players_each_game p2 ON p2."GameID" = r."GameID" AND p2."NumeroMaglia" = r."HomePlayer2"
    LEFT JOIN players_each_game p3 ON p3."GameID" = r."GameID" AND p3."NumeroMaglia" = r."HomePlayer3"
    LEFT JOIN players_each_game p4 ON p4."GameID" = r."GameID" AND p4."NumeroMaglia" = r."HomePlayer4"
    LEFT JOIN players_each_game p5 ON p5."GameID" = r."GameID" AND p5."NumeroMaglia" = r."HomePlayer5"
    LEFT JOIN players_each_game p6 ON p6."GameID" = r."GameID" AND p6."NumeroMaglia" = r."HomePlayer6"
    WHERE r."GameID" = 689
)
SELECT * FROM problematic_data
LIMIT 5;
""")
problem_data = cur.fetchall()
print("\nDati problematici per GameID 689:")
for row in problem_data:
    print(f"\nRilevation: GameID={row[0]}, Number={row[1]}")
    print(f"whichTeam: {row[2]}")
    print(f"HomePlayers: {row[3]}, {row[4]}, {row[5]}, {row[6]}, {row[7]}, {row[8]}")
    print(f"Teams: home={row[9]}, visitor={row[10]}")
    print(f"Player1: team={row[11]}, id={row[12]}")
    print(f"Player2: team={row[13]}, id={row[14]}")
    print(f"Player3: team={row[15]}, id={row[16]}")
    print(f"Player4: team={row[17]}, id={row[18]}")
    print(f"Player5: team={row[19]}, id={row[20]}")
    print(f"Player6: team={row[21]}, id={row[22]}")

# Verifichiamo anche i dati in players_each_game per questo GameID
cur.execute("""
SELECT "GameID", "TeamID", "NumeroMaglia", "PlayerID"
FROM players_each_game
WHERE "GameID" = 689
ORDER BY "TeamID", "NumeroMaglia";
""")
players = cur.fetchall()
print("\nGiocatori registrati per GameID 689:")
for player in players:
    print(f"Team: {player[1]}, Maglia: {player[2]}, Player: {player[3]}")
'''
    
    
    
    
'''
cur.execute("""
SELECT 
    r."GameID",
    r."RilevationNumber",
    r."HomePlayer1" as maglia1,
    r."HomePlayer1_ID" as id1,
    r."HomePlayer2" as maglia2,
    r."HomePlayer2_ID" as id2,
    r."HomePlayer3" as maglia3,
    r."HomePlayer3_ID" as id3,
    r."HomePlayer4" as maglia4,
    r."HomePlayer4_ID" as id4,
    r."HomePlayer5" as maglia5,
    r."HomePlayer5_ID" as id5,
    r."HomePlayer6" as maglia6,
    r."HomePlayer6_ID" as id6,
    r."whichTeamID" as team_id
FROM rilevationswithid r
WHERE (r."HomePlayer1_ID" IS NULL AND r."HomePlayer1" IS NOT NULL)
   OR (r."HomePlayer2_ID" IS NULL AND r."HomePlayer2" IS NOT NULL)
   OR (r."HomePlayer3_ID" IS NULL AND r."HomePlayer3" IS NOT NULL)
   OR (r."HomePlayer4_ID" IS NULL AND r."HomePlayer4" IS NOT NULL)
   OR (r."HomePlayer5_ID" IS NULL AND r."HomePlayer5" IS NOT NULL)
   OR (r."HomePlayer6_ID" IS NULL AND r."HomePlayer6" IS NOT NULL)
LIMIT 10;
""")

null_issues = cur.fetchall()
if null_issues:
    print("\nFound rows with NULL PlayerIDs despite having jersey numbers:")
    for row in null_issues:
        print(f"\nGameID: {row[0]}, RilevationNumber: {row[1]}")
        print(f"Team: {row[14]}")
        print(f"Jersey numbers and IDs:")
        print(f"Player1: {row[2]} -> {row[3]}")
        print(f"Player2: {row[4]} -> {row[5]}")
        print(f"Player3: {row[6]} -> {row[7]}")
        print(f"Player4: {row[8]} -> {row[9]}")
        print(f"Player5: {row[10]} -> {row[11]}")
        print(f"Player6: {row[12]} -> {row[13]}")

# Also check if we have the correct team_id in the JOIN
cur.execute("""
SELECT DISTINCT 
    r."GameID",
    r."whichTeam",
    r."whichTeamID",
    g."IDHomeTeam",
    g."IDVisitorTeam"
FROM rilevationswithid r
JOIN games g ON r."GameID" = g."GameID"
WHERE (r."whichTeam" = false AND r."whichTeamID" != g."IDHomeTeam")
   OR (r."whichTeam" = true AND r."whichTeamID" != g."IDVisitorTeam");
""")

team_issues = cur.fetchall()
if team_issues:
    print("\nFound mismatched team IDs:")
    for row in team_issues:
        print(f"GameID: {row[0]}, whichTeam: {row[1]}, whichTeamID: {row[2]}")
        print(f"Game HomeTeam: {row[3]}, VisitorTeam: {row[4]}")
'''
    





