@echo off
echo Setting up VolleyVision environment...

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed or not in PATH. Please install Python 3.8 or higher.
    exit /b 1
)

REM Create a virtual environment if it doesn't exist
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
)

REM Activate the virtual environment
call venv\Scripts\activate

REM Install required packages
echo Installing required packages...
pip install -r "FilePython_c\VolleyVision\Stage I - Volleyball\requirements.txt"

REM Install additional packages needed for the notebook
pip install jupyter ipykernel

REM Register the kernel
python -m ipykernel install --user --name=volleyvision --display-name="VolleyVision"

REM Create the Output directory if it doesn't exist
if not exist "FilePython_c\VolleyVision\Stage I - Volleyball\Output" (
    mkdir "FilePython_c\VolleyVision\Stage I - Volleyball\Output"
)

REM Download the DaSiamRPN model files if they don't exist
if not exist "FilePython_c\VolleyVision\Stage I - Volleyball\DaSiamRPN" (
    echo Creating DaSiamRPN directory...
    mkdir "FilePython_c\VolleyVision\Stage I - Volleyball\DaSiamRPN"

    echo Downloading DaSiamRPN model files...
    powershell -Command "& {Invoke-WebRequest -Uri 'https://github.com/opencv/opencv_zoo/raw/master/models/object_tracking_dasiamrpn/dasiamrpn_model.onnx' -OutFile 'FilePython_c\VolleyVision\Stage I - Volleyball\DaSiamRPN\dasiamrpn_model.onnx'}"
    powershell -Command "& {Invoke-WebRequest -Uri 'https://github.com/opencv/opencv_zoo/raw/master/models/object_tracking_dasiamrpn/dasiamrpn_kernel_cls1.onnx' -OutFile 'FilePython_c\VolleyVision\Stage I - Volleyball\DaSiamRPN\dasiamrpn_kernel_cls1.onnx'}"
    powershell -Command "& {Invoke-WebRequest -Uri 'https://github.com/opencv/opencv_zoo/raw/master/models/object_tracking_dasiamrpn/dasiamrpn_kernel_r1.onnx' -OutFile 'FilePython_c\VolleyVision\Stage I - Volleyball\DaSiamRPN\dasiamrpn_kernel_r1.onnx'}"
)

REM Create models directory if it doesn't exist
if not exist "FilePython_c\VolleyVision\Stage I - Volleyball\models" (
    mkdir "FilePython_c\VolleyVision\Stage I - Volleyball\models"
)


REM Download YOLOv7 weights if they don't exist
if not exist "FilePython_c\VolleyVision\Stage I - Volleyball\models\yolov7-tiny.pt" (
    echo Downloading YOLOv7 weights...
    powershell -Command "& {Invoke-WebRequest -Uri 'https://github.com/WongKinYiu/yolov7/releases/download/v0.1/yolov7-tiny.pt' -OutFile 'FilePython_c\VolleyVision\Stage I - Volleyball\models\yolov7-tiny.pt'}"
)

REM Fix the missing 're' import in my_utils.py
echo Fixing my_utils.py...
python fix_my_utils.py

REM Download a sample volleyball video
echo Downloading sample volleyball video...
python download_sample_video.py

REM Test the setup
echo Testing VolleyVision setup...
python test_volleyvision.py
if %errorlevel% neq 0 (
    echo Setup test failed. Please check the error messages above.
    pause
    exit /b 1
)

echo Setup complete!
echo.
echo Per utilizzare il notebook in VSCode:
echo 1. Apri il file "FilePython_c\VolleyVision_fixed.ipynb" in VSCode
echo 2. Seleziona il kernel "VolleyVision" dall'angolo in alto a destra
echo 3. Esegui le celle con il pulsante play o con Shift+Enter
echo.
echo Buon lavoro con VolleyVision!
