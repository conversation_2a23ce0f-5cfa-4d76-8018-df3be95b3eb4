#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Volleyball Scraper - Programma per estrarre dati dei giocatori da volleybox.net
Versione per Windows con ricerca migliorata per trovare l'URL corretto
"""

import sys
import re
import unicodedata
import time
import json
import random
import os
import requests
from bs4 import BeautifulSoup
from pathlib import Path
from playwright.sync_api import sync_playwright, TimeoutError

import numpy as np 
import pandas as pd  #Realizzato con la versione di Pandas 2.2.3
import polars as pl
import psycopg
from sqlalchemy import create_engine, text

# Directory per salvare i cookie e i dati - adattate per Windows
DATA_DIR = Path(os.path.expanduser("~")) / "Documents" / "VolleyballScraper" / "data"
COOKIES_FILE = DATA_DIR / 'cookies.json'
RESULTS_DIR = DATA_DIR / 'results'

# Assicurati che le directory esistano
DATA_DIR.mkdir(parents=True, exist_ok=True)
RESULTS_DIR.mkdir(parents=True, exist_ok=True)

def normalize_name(name):
    """
    Normalizza il nome per la costruzione dell'URL.
    Rimuove accenti, converte in minuscolo e sostituisce spazi con trattini.
    """
    # Rimuovi accenti e caratteri speciali
    name = unicodedata.normalize('NFKD', name).encode('ASCII', 'ignore').decode('utf-8')
    # Converti in minuscolo
    name = name.lower()
    # Sostituisci spazi con trattini
    name = re.sub(r'\s+', '-', name)
    # Rimuovi caratteri non alfanumerici (tranne trattini)
    name = re.sub(r'[^a-z0-9\-+]', '', name)
    return name


'''
def trova_url_volleybox_google(nome, cognome, callback=None):
    """
    Trova l'URL corretto del giocatore su volleybox.net tramite ricerca Google.
    """
    query = f"site:volleybox.net/it {nome} {cognome}"
    url = "https://www.google.com/search"
    
    headers = {
        "User-Agent": random.choice([
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.109 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.54 Safari/537.36"
        ]),
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5"
    }
    
    parameters = {'q': query}

    try:
        print(f"Searching for: {query}")
        time.sleep(random.uniform(2, 4))
        
        response = requests.get(url, headers=headers, params=parameters)
        response.raise_for_status()
        
        print(f"Response status code: {response.status_code}")
        soup = BeautifulSoup(response.text, "html.parser")
        
        # Extract all links from the page
        links = soup.find_all('a')
        print(f"Total links found: {len(links)}")
        
        for link in links:
            href = link.get('href', '')
            # Only process URLs that start with http and contain volleybox.net/it
            if href.startswith('http') and 'volleybox.net/it' in href:   #eventualmente togli /it
                # Extract the actual URL if it's in Google's redirect format
                if 'url?q=' in href:
                    actual_url = href.split('url?q=')[1].split('&')[0]
                else:
                    actual_url = href
                    
                print(f"Found potential URL: {actual_url}")
                if 'volleybox.net/it' in actual_url:
                    print(f"Found valid Volleybox URL: {actual_url}")
                    return actual_url
                  
        #Se non ha ancora ritornato niente
        print("No Volleybox URL found")
        return None
        
    except requests.RequestException as e:
        print(f"Request error: {e}")
        return None
    except Exception as e:
        print(f"An error occurred: {e}")
        print(f"Error type: {type(e)}")
        return None
'''

def trova_url_volleybox_google(nome, cognome, callback=None):
    API_KEY = "22bf2d85466323a809aec14d1ee765c259a29e68"
    #url = f"https://google.serper.dev/search?q=site%3Avolleybox.net%2Fit+{nome}+{cognome}&gl=it&hl=it&apiKey={API_KEY}"
    #url = url.replace(" ", "+")
    
    query = f"site:volleybox.net/it {nome} {cognome} club".replace(" ", "+")  # Sostituisce gli spazi con +
    url = f"https://google.serper.dev/search?q={query}&gl=it&hl=it&apiKey={API_KEY}"
    
    print(url)
    payload = {}
    headers = {}

    response = requests.request("GET", url, headers=headers, data=payload)
    try:
        dizionario = response.json()  # Parse the JSON response
        
        lista_siti = dizionario["organic"]
        print(f"Found {len(lista_siti)} results")
        
        url_giusto = ""
        siti_papabili = [
            sito["link"]
            for sito in lista_siti
            if sito["link"].startswith("https://volleybox.net/it") and
            sito["link"].endswith("/clubs")
        ]
                
        #Se trovo 0 siti papabili o più di 1 sito papabile, allora non aggiungo niente per sicurezza, e lo farò eventualmente a mano.
        if len(siti_papabili) == 0:
            print("Nessun URL papabile trovato")
            return None
        elif len(siti_papabili) == 1:
            return siti_papabili[0]
        else:
            print("Sono stati trovati più URL papabili, quindi c'era il rischio di aggiungere info di un giocatore chiamato in modo simile. Non aggiungiamo niente")
            print(siti_papabili)
            return None
            
    except (json.JSONDecodeError, KeyError) as e:
        print(f"Errore nel parsing della risposta: {e}")
        print(f"Risposta ricevuta: {response.text[:200]}...")  # Print first 200 chars of response
        return None



def get_advanced_fingerprint_script():
    """
    Genera uno script JavaScript avanzato per modificare il fingerprint del browser.
    """
    script = """
    // Nasconde WebDriver
    Object.defineProperty(navigator, 'webdriver', {
        get: () => false,
    });
    
    / Modifica il fingerprint di Canvas
    const originalGetContext = HTMLCanvasElement.prototype.getContext;
    HTMLCanvasElement.prototype.getContext = function(type, attributes) {
        const context = originalGetContext.call(this, type, attributes);
        if (type === '2d') {
            const originalFillText = context.fillText;
            context.fillText = function() {
                const args = Array.from(arguments);
                // Aggiungi un leggero rumore alle coordinate
                if (typeof args[1] === 'number' && typeof args[2] === 'number') {
                    args[1] += Math.random() * 0.2 - 0.1;
                    args[2] += Math.random() * 0.2 - 0.1;
                }
                return originalFillText.apply(this, args);
            };
        }
        
        // Modifica WebGL fingerprint
        if (type === 'webgl' || type === 'experimental-webgl' || type === 'webgl2') {
            const originalGetParameter = context.getParameter;
            context.getParameter = function(parameter) {
                // Modifica vendor e renderer
                if (parameter === 0x1F00) { // VENDOR
                    return 'Google Inc.';
                }
                if (parameter === 0x1F01) { // RENDERER
                    return 'ANGLE (Intel(R) UHD Graphics Direct3D11 vs_5_0 ps_5_0)';
                }
                // Restituisci il valore originale per altri parametri
                return originalGetParameter.call(this, parameter);
            };
        }
        
        return context;
    };
    
    // Aggiungi funzioni di Chrome
    window.chrome = {
        app: {
            isInstalled: false,
        },
        webstore: {
            onInstallStageChanged: {},
            onDownloadProgress: {},
        },
        runtime: {
            PlatformOs: {
                MAC: 'mac',
                WIN: 'win',
                ANDROID: 'android',
                CROS: 'cros',
                LINUX: 'linux',
                OPENBSD: 'openbsd',
            },
            PlatformArch: {
                ARM: 'arm',
                X86_32: 'x86-32',
                X86_64: 'x86-64',
            },
            PlatformNaclArch: {
                ARM: 'arm',
                X86_32: 'x86-32',
                X86_64: 'x86-64',
            },
            RequestUpdateCheckStatus: {
                THROTTLED: 'throttled',
                NO_UPDATE: 'no_update',
                UPDATE_AVAILABLE: 'update_available',
            },
            OnInstalledReason: {
                INSTALL: 'install',
                UPDATE: 'update',
                CHROME_UPDATE: 'chrome_update',
                SHARED_MODULE_UPDATE: 'shared_module_update',
            },
            OnRestartRequiredReason: {
                APP_UPDATE: 'app_update',
                OS_UPDATE: 'os_update',
                PERIODIC: 'periodic',
            },
        },
    };
    
    // Aggiungi i plugin
    const mockPlugins = [
        { name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer', description: 'Portable Document Format' },
        { name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai', description: '' },
        { name: 'Native Client', filename: 'internal-nacl-plugin', description: '' }
    ];
    
    Object.defineProperty(navigator, 'plugins', {
        get: function() {
            const plugins = [];
            for (const plugin of mockPlugins) {
                plugins.push(plugin);
            }
            plugins.__proto__ = Object.getPrototypeOf([]);
            return plugins;
        },
    });
    
    // Aggiungi lingue
    Object.defineProperty(navigator, 'languages', {
        get: () => ['it-IT', 'it', 'en-US', 'en'],
    });
    """
    
    return script

def setup_stealth_browser():
    """
    Configura un browser stealth con Playwright per evitare il rilevamento di Cloudflare.
    
    Returns:
        tuple: (playwright, browser, context, page)
    """
    playwright = sync_playwright().start()
    
    # Configura il browser con impostazioni avanzate per evitare il rilevamento
    browser = playwright.chromium.launch(
        headless=True,
        args=[
            '--disable-blink-features=AutomationControlled',
            '--disable-features=IsolateOrigins,site-per-process',
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--disable-gpu'
        ]
    )
    
    # Crea un contesto con impostazioni avanzate
    context = browser.new_context(
        viewport={"width": 1920, "height": 1080},
        user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        locale="it-IT",
        timezone_id="Europe/Rome"
    )
    
    # Modifica il fingerprint del browser per evitare il rilevamento
    context.add_init_script(get_advanced_fingerprint_script())
    
    # Imposta header HTTP realistici
    context.set_extra_http_headers({
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'it-IT,it;q=0.8,en-US;q=0.5,en;q=0.3',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0',
    })
    
    # Crea una nuova pagina
    page = context.new_page()
    
    return playwright, browser, context, page

def save_cookies(context, file_path=COOKIES_FILE):
    """
    Salva i cookie del contesto in un file.
    
    Args:
        context: Contesto del browser Playwright
        file_path: Percorso del file in cui salvare i cookie
    """
    cookies = context.cookies()
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(cookies, f, indent=2)
    print(f"Cookie salvati in {file_path}")

def load_cookies(context, file_path=COOKIES_FILE):
    """
    Carica i cookie da un file nel contesto.
    
    Args:
        context: Contesto del browser Playwright
        file_path: Percorso del file da cui caricare i cookie
        
    Returns:
        bool: True se i cookie sono stati caricati con successo, False altrimenti
    """
    if not os.path.exists(file_path):
        print(f"File dei cookie {file_path} non trovato")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            cookies = json.load(f)
        context.add_cookies(cookies)
        print(f"Cookie caricati da {file_path}")
        return True
    except Exception as e:
        print(f"Errore nel caricamento dei cookie: {str(e)}")
        return False

def simulate_human_behavior(page):
    """
    Simula comportamento umano sulla pagina per evitare il rilevamento.
    
    Args:
        page: Pagina Playwright
    """
    # Ritardo casuale iniziale
    time.sleep(random.uniform(1, 3))
    
    # Movimento del mouse casuale
    for _ in range(3):
        page.mouse.move(
            random.randint(100, 800),
            random.randint(100, 600),
            steps=random.randint(5, 10)
        )
        time.sleep(random.uniform(0.3, 0.7))
    
    # Scroll naturale
    for _ in range(random.randint(3, 5)):
        page.mouse.wheel(0, random.randint(100, 300))
        time.sleep(random.uniform(0.5, 1.5))
    
    # Ritardo finale
    time.sleep(random.uniform(0.5, 1.5))


def extract_player_data_with_js(page):
    """
    Estrae i dati del giocatore utilizzando JavaScript direttamente nella pagina.

    Args:
        page: Pagina Playwright

    Returns:
        dict: Dati del giocatore
    """
    try:
        return page.evaluate("""() => {
            const container = document.querySelector('div.new_box.pRelative');
            if (!container) {
                console.log('Container non trovato');
                return {};
            }

            const data = {};
            const dtElements = container.querySelectorAll('dt.info-header');
            const ddElements = container.querySelectorAll('dd.info-data');

            dtElements.forEach((dt, index) => {
                const label = dt.textContent.trim().toLowerCase();
                const valueElement = ddElements[index];
                if (!valueElement) return;

                let value = valueElement.textContent.trim();
                
                // Se il valore è un link (es. nazionalità), prendiamo solo il testo
                const link = valueElement.querySelector('a');
                if (link) {
                    value = link.textContent.trim();
                }

                if (label.includes('nazionalità')) data.nazionalita = value;
                if (label.includes('posizione')) data.posizione = value;
                if (label.includes('data di nascita')) data.data_nascita = value;
                if (label.includes('altezza')) data.altezza = value;
                if (label.includes('peso')) data.peso = value;
                if (label.includes('schiacciata')) data.schiacciata = value;
                if (label.includes('muro')) data.muro = value;
                if (label.includes('mano dominante')) data.mano_dominante = value;
            });

            return data;
        }""")
    except Exception as e:
        print(f"Errore nell'estrazione JavaScript: {str(e)}")
        return {}


def get_player_info(first_name, last_name, use_existing_cookies=True, callback=None):
    """
    Estrae le informazioni di un giocatore da volleybox.net utilizzando Playwright.
    
    Args:
        first_name (str): Nome del giocatore
        last_name (str): Cognome del giocatore
        use_existing_cookies (bool): Se utilizzare i cookie esistenti
        callback (function): Funzione di callback per aggiornare l'interfaccia
        
    Returns:
        dict: Dizionario contenente le informazioni del giocatore
    """
    # Trova l'URL corretto tramite Google
    url = trova_url_volleybox_google(first_name, last_name, callback)
    
    if not url:
        return {
            "nome": f"{first_name} {last_name}",
            "url": None,
            "nazionalità": None,
            "posizione": None,
            "data_di_nascita": None,
            "altezza": None,
            "peso": None,
            "schiacciata": None,
            "muro": None,
            "mano_dominante": None,
            "errore": "Impossibile trovare l'URL del giocatore"
        }
    
    # Inizializza il dizionario per i risultati
    player_info = {
        "nome": f"{first_name} {last_name}",
        "url": url,
        "nazionalità": None,
        "posizione": None,
        "data_di_nascita": None,
        "altezza": None,
        "peso": None,
        "schiacciata": None,
        "muro": None,
        "mano_dominante": None,
        "errore": None
    }
    
    # Funzione per aggiornare lo stato
    def update_status(message):
        print(message)
        if callback:
            callback(message)
    
    playwright = None
    browser = None
    context = None
    
    try:
        # Inizializza il browser stealth
        update_status("Inizializzando il browser...")
        playwright, browser, context, page = setup_stealth_browser()
        
        # Carica i cookie se richiesto e disponibili
        if use_existing_cookies:
            load_cookies(context)
        
        # Naviga alla pagina del giocatore
        update_status(f"Navigando a {url}...")
        page.goto(url, wait_until="networkidle", timeout=100000)
        
        # Simula comportamento umano
        update_status("Simulando comportamento umano...")
        simulate_human_behavior(page)
        
        # Verifica se siamo stati bloccati da Cloudflare
        if "Cloudflare" in page.title() and "challenge" in page.title():
            update_status("Rilevato challenge Cloudflare, attendere...")
            # Attendi che il challenge venga risolto (può richiedere fino a 50 secondi)
            page.wait_for_selector("body", state="visible", timeout=50000)
            page.wait_for_load_state("networkidle", timeout=50000)
            
            # Salva i cookie dopo aver superato il challenge
            save_cookies(context)
            
            # Ricarica la pagina
            update_status("Ricaricando la pagina...")
            page.reload(wait_until="networkidle", timeout=50000)
            simulate_human_behavior(page)
        
        # Salva i cookie per usi futuri
        save_cookies(context)
        
        # Verifica se la pagina è stata caricata correttamente
        if "404" in page.title() or "non trovato" in page.title().lower():
            player_info["errore"] = "Giocatore non trovato"
            return player_info
        
        # Estrai le informazioni dalla pagina
        update_status("Estraendo le informazioni del giocatore...")
        
        # Metodo 1: Estrazione diretta con selettori CSS
        try:
            # Nazionalità
            nationality_element = page.query_selector("div:text('Nazionalità') + div")
            if nationality_element:
                player_info["nazionalità"] = nationality_element.inner_text().strip()
            
            # Posizione
            position_element = page.query_selector("div:text('Posizione') + div")
            if position_element:
                player_info["posizione"] = position_element.inner_text().strip()
            
            # Data di nascita
            birth_date_element = page.query_selector("div:text('Data di nascita') + div")
            if birth_date_element:
                player_info["data_di_nascita"] = birth_date_element.inner_text().strip()
            
            # Altezza
            height_element = page.query_selector("div:text('Altezza') + div")
            if height_element:
                player_info["altezza"] = height_element.inner_text().strip()
            
            # Peso
            weight_element = page.query_selector("div:text('Peso') + div")
            if weight_element:
                player_info["peso"] = weight_element.inner_text().strip()
                
            # Schiacciata
            weight_element = page.query_selector("div:text('Schiacciata') + div")
            if weight_element:
                player_info["schiacciata"] = weight_element.inner_text().strip()

            # Muro
            weight_element = page.query_selector("div:text('Muro') + div")
            if weight_element:
                player_info["muro"] = weight_element.inner_text().strip()

            # Mano dominante
            weight_element = page.query_selector("div:text('Mano dominante') + div")
            if weight_element:
                player_info["mano_dominante"] = weight_element.inner_text().strip()
                
        except Exception as e:
            update_status(f"Errore nell'estrazione con selettori: {str(e)}")
        
        # Se non abbiamo trovato nessuna informazione, prova con JavaScript
        if not any([player_info["nazionalità"], player_info["posizione"], 
                   player_info["data_di_nascita"], player_info["altezza"], 
                   player_info["peso"],
                   player_info["schiacciata"],
                   player_info["muro"],
                   player_info["mano_dominante"]
                   ]):
            update_status("Tentativo di estrazione con JavaScript...")
            player_data = extract_player_data_with_js(page)
            
            if player_data:
                if player_data.get('nazionalita'):
                    player_info["nazionalità"] = player_data['nazionalita']
                if player_data.get('posizione'):
                    player_info["posizione"] = player_data['posizione']
                if player_data.get('data_nascita'):
                    player_info["data_di_nascita"] = player_data['data_nascita']
                if player_data.get('altezza'):
                    player_info["altezza"] = player_data['altezza']
                if player_data.get('peso'):
                    player_info["peso"] = player_data['peso']
                if player_data.get('schiacciata'):
                    player_info["schiacciata"] = player_data['schiacciata']
                if player_data.get('muro'):
                    player_info["muro"] = player_data['muro']
                if player_data.get('mano_dominante'):
                    player_info["mano_dominante"] = player_data['mano_dominante']
        
        # Salva uno screenshot per debug
        screenshot_path = DATA_DIR / f"{normalize_name(first_name)}_{normalize_name(last_name)}.png"
        page.screenshot(path=str(screenshot_path))
        update_status(f"Screenshot salvato in {screenshot_path}")
        
        # Se ancora non abbiamo trovato informazioni, salva l'HTML per debug
        if not any([player_info["nazionalità"], player_info["posizione"], 
                   player_info["data_di_nascita"], player_info["altezza"], 
                   player_info["peso"],
                   player_info["schiacciata"],
                   player_info["muro"],
                   player_info["mano_dominante"]
                   ]):
            html_path = DATA_DIR / f"{normalize_name(first_name)}_{normalize_name(last_name)}.html"
            with open(html_path, "w", encoding="utf-8") as f:
                f.write(page.content())
            update_status(f"HTML salvato in {html_path}")
            
            # Prova un approccio alternativo cercando nella struttura JSON-LD
            try:
                update_status("Tentativo di estrazione da JSON-LD...")
                json_ld_data = page.evaluate("""() => {
                    const jsonLdScripts = Array.from(document.querySelectorAll('script[type="application/ld+json"]'));
                    for (const script of jsonLdScripts) {
                        try {
                            const data = JSON.parse(script.textContent);
                            if (data.birthDate || data.height || data.gender) {
                                return data;
                            }
                        } catch (e) {
                            // Ignora errori di parsing
                        }
                    }
                    return null;
                }""")
                
                if json_ld_data:  
                    update_status("Dati trovati in JSON-LD")
                    if 'birthdate' in json_ld_data and not player_info["data_di_nascita"]:
                        player_info["data_di_nascita"] = json_ld_data['birthdate']
                    if 'height' in json_ld_data and not player_info["altezza"]:
                        player_info["altezza"] = json_ld_data['height']
                    if 'weight' in json_ld_data and not player_info["peso"]:
                        player_info["peso"] = json_ld_data['weight']
                    if 'position' in json_ld_data and not player_info["posizione"]:
                        player_info["posizione"] = json_ld_data['position'].replace('Volleyball player', 'Pallavolista')
            except Exception as e:
                update_status(f"Errore nell'estrazione JSON-LD: {str(e)}")
            
            # Se ancora non abbiamo trovato informazioni, segnala un errore
            if not any([player_info["nazionalità"], player_info["posizione"], 
                       player_info["data_di_nascita"], player_info["altezza"], 
                       player_info["peso"],
                       player_info["schiacciata"],
                       player_info["muro"],
                       player_info["mano_dominante"]
                       ]):
                player_info["errore"] = "Impossibile estrarre le informazioni dalla pagina"
        
    except TimeoutError:
        player_info["errore"] = "Timeout durante il caricamento della pagina"
        update_status("Timeout durante il caricamento della pagina")
    except Exception as e:
        player_info["errore"] = f"Errore durante lo scraping: {str(e)}"
        update_status(f"Errore durante lo scraping: {str(e)}")
    
    finally:
        # Chiudi il browser
        update_status("Chiudendo il browser...")
        if browser:
            browser.close()
        if playwright:
            playwright.stop()
    
    update_status("Estrazione completata")
    return player_info

def save_player_info_to_file(player_info):
    """
    Salva le informazioni del giocatore in un file di testo.
    
    Args:
        player_info (dict): Dizionario contenente le informazioni del giocatore
        
    Returns:
        str: Percorso del file salvato
    """
    # Crea un nome file basato sul nome del giocatore
    first_name, last_name = player_info["nome"].split(" ", 1)
    first_name_norm = normalize_name(first_name)
    last_name_norm = normalize_name(last_name)
    
    file_path = RESULTS_DIR / f"{first_name_norm}_{last_name_norm}.txt"
    
    with open(file_path, "w", encoding="utf-8") as f:
        f.write(f"Informazioni su {player_info['nome']}\n")
        f.write("="*50 + "\n\n")
        
        if player_info["errore"]:
            f.write(f"Errore: {player_info['errore']}\n")
        else:
            f.write(f"URL: {player_info['url']}\n")
            f.write(f"Nazionalità: {player_info['nazionalità'] or None}\n")
            f.write(f"Posizione: {player_info['posizione'] or None}\n")
            f.write(f"Data di nascita: {player_info['data_di_nascita'] or None}\n")
            f.write(f"Altezza: {player_info['altezza'] or None}\n")
            f.write(f"Peso: {player_info['peso'] or None}\n")
            f.write(f"Schiacciata: {player_info['schiacciata'] or None}\n")
            f.write(f"Muro: {player_info['muro'] or None}\n")
            f.write(f"Mano dominante: {player_info['mano_dominante'] or None}\n")
    
    print("Il tipo di player_info è:", type(player_info))
    print(player_info)
    
    return str(file_path)

def save_player_info_to_json(player_info):
    """
    Salva le informazioni del giocatore in un file JSON.
    
    Args:
        player_info (dict): Dizionario contenente le informazioni del giocatore
        
    Returns:
        str: Percorso del file salvato
    """
    # Crea un nome file basato sul nome del giocatore
    first_name, last_name = player_info["nome"].split(" ", 1)
    first_name_norm = normalize_name(first_name)
    last_name_norm = normalize_name(last_name)
    
    file_path = RESULTS_DIR / f"{first_name_norm}_{last_name_norm}.json"
    
    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(player_info, f, indent=2, ensure_ascii=False)
    
    return str(file_path)


def print_player_info(player_info):
    """
    Stampa le informazioni del giocatore in modo formattato.
    
    Args:
        player_info (dict): Dizionario contenente le informazioni del giocatore
    """
    print("\n" + "="*50)
    print(f"Informazioni su {player_info['nome']}")
    print("="*50)
    
    if player_info["errore"]:
        print(f"Errore: {player_info['errore']}")
        return
    
    print(f"URL: {player_info['url']}")
    print(f"Nazionalità: {player_info['nazionalità'] or 'Non disponibile'}")
    print(f"Posizione: {player_info['posizione'] or 'Non disponibile'}")
    print(f"Data di nascita: {player_info['data_di_nascita'] or 'Non disponibile'}")
    print(f"Altezza: {player_info['altezza'] or 'Non disponibile'}")
    print(f"Peso: {player_info['peso'] or 'Non disponibile'}")
    print(f"Schiacciata: {player_info['schiacciata'] or 'Non disponibile'}")
    print(f"Muro: {player_info['muro'] or 'Non disponibile'}")
    print(f"Mano dominante: {player_info['mano_dominante'] or 'Non disponibile'}")
    print("="*50)

def main():
    """
    Funzione principale per l'esecuzione da riga di comando.
    """
    print("Volleyball Scraper - Estrai informazioni sui giocatori da volleybox.net")
    print("-"*70)
    
    # Se vengono forniti argomenti da riga di comando
    if len(sys.argv) >= 3:
        first_name = sys.argv[1]
        last_name = sys.argv[2]
        
        #first_name = normalize_name(first_name)
        #last_name = normalize_name(last_name)
        print(first_name, last_name)
        
        print(f"\nRicerca in corso per {first_name} {last_name}, attendere prego...")
        player_info = get_player_info(first_name, last_name)
        
        print_player_info(player_info)
        
        # Salva i risultati
        if not player_info["errore"]:
            txt_path = save_player_info_to_file(player_info)
            json_path = save_player_info_to_json(player_info)
            print(f"\nRisultati salvati in:")
            print(f"- {txt_path} (formato testo)")
            print(f"- {json_path} (formato JSON)")
    else:
        # Altrimenti, modalità interattiva
        while True:
            first_name = input("\nInserisci il nome del giocatore (o 'q' per uscire): ")
            if first_name.lower() == 'q':
                break
                
            last_name = input("Inserisci il cognome del giocatore: ")
            if last_name.lower() == 'q':
                break
            
            #first_name = normalize_name(first_name)
            #last_name = normalize_name(last_name)
            print(first_name, last_name)
            
            print("\nRicerca in corso, attendere prego...")
            player_info = get_player_info(first_name, last_name)
            
            print_player_info(player_info)
            
            # Salva i risultati
            if not player_info["errore"]:
                #save = input("\nVuoi salvare i risultati? (s/n): ")
                #if save.lower() == 's':
                    txt_path = save_player_info_to_file(player_info)
                    json_path = save_player_info_to_json(player_info)
                    print(f"\nRisultati salvati in:")
                    print(f"- {txt_path} (formato testo)")
                    print(f"- {json_path} (formato JSON)")



if __name__ == "__main__":
    main()



