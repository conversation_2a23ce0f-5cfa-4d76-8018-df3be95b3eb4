# =============================================================================
# INTERFACCIA DASH INTERATTIVA PER DIAGRAMMA SANKEY
# Copia e incolla questo codice in una nuova cella del notebook
# =============================================================================

from dash import Dash, dcc, html, Input, Output
import plotly.graph_objects as go
import pandas as pd

# Caratteristiche disponibili per ogni fondamentale
fond_characteristics = {
    'S': ['Type', 'Eval', 'StartZone', 'EndZoneEsecZone', 'EndSubzoneEsecSubzone', 'Special'],
    'R': ['Type', 'Eval', 'StartZone', 'EndZoneEsecZone', 'EndSubzoneEsecSubzone', 'SkillType', 'PlayersInfo', 'Special'],
    'E': ['Type', 'Eval', 'SetterCall', 'TargAttk', 'EndZoneEsecZone', 'EndSubzoneEsecSubzone', 'SkillType', 'Special'],
    'A': ['Type', 'Eval', 'AttkCombination', 'StartZone', 'EndZoneEsecZone', 'EndSubzoneEsecSubzone', 'SkillType', 'PlayersInfo', 'Special'],
    'B': ['Type', 'Eval', 'EndZoneEsecZone', 'EndSubzoneEsecSubzone', 'SkillType', 'PlayersInfo', 'Special'],
    'D': ['Type', 'Eval', 'StartZone', 'EndZoneEsecZone', 'EndSubzoneEsecSubzone', 'SkillType', 'Special'],
    'F': ['Type', 'Eval', 'EndZoneEsecZone', 'EndSubzoneEsecSubzone', 'Special']
}

# Regole per collegare i fondamentali
regole_abs = {
    'R': {
        'S': lambda prev_abs: 2
    },
    'D': {
        'R': lambda prev_abs: 3,
        'S': lambda prev_abs: 3,
        'E': lambda prev_abs: prev_abs + 1,
        'A': lambda prev_abs: prev_abs + 1
    },
    'F': {
        'R': lambda prev_abs: 3,
        'S': lambda prev_abs: 3,
        'E': lambda prev_abs: prev_abs + 1,
        'A': lambda prev_abs: prev_abs + 1
    },
    'E': {
        'R': lambda prev_abs: prev_abs,
        'D': lambda prev_abs: prev_abs,
        'F': lambda prev_abs: prev_abs,
        'S': lambda prev_abs: prev_abs + 1,
        'A': lambda prev_abs: prev_abs + 1,
        'B': lambda prev_abs: prev_abs + 1
    },
    'A': {
        'R': lambda prev_abs: prev_abs,
        'E': lambda prev_abs: prev_abs,
        'D': lambda prev_abs: prev_abs,
        'F': lambda prev_abs: prev_abs,
        'S': lambda prev_abs: prev_abs + 1,
        'B': lambda prev_abs: prev_abs + 1
    },
    'B': {
        'R': lambda prev_abs: prev_abs + 1,
        'E': lambda prev_abs: prev_abs + 1,
        'A': lambda prev_abs: prev_abs + 1,
        'D': lambda prev_abs: prev_abs + 1,
        'F': lambda prev_abs: prev_abs + 1,
        'S': lambda prev_abs: prev_abs + 2
    },
}

# Inizializza l'app Dash
app = Dash(__name__)
app.title = "Sankey Diagram Interattivo - Volley"

# Layout dell'app
app.layout = html.Div([
    html.H1("🏐 Diagramma Sankey Interattivo - Analisi Volley", 
            style={'textAlign': 'center', 'color': '#2c3e50', 'marginBottom': 30}),
    
    # Container principale
    html.Div([
        # Colonna sinistra - Selettori Source
        html.Div([
            html.H3("🏐 Primo Tocco (Source)", style={'color': '#3498db'}),
            
            html.Label("Fondamentale:", style={'fontWeight': 'bold', 'marginTop': 10}),
            dcc.Dropdown(
                id='source-fondamentale',
                options=[
                    {'label': 'S - Battuta', 'value': 'S'},
                    {'label': 'R - Ricezione', 'value': 'R'},
                    {'label': 'E - Alzata', 'value': 'E'},
                    {'label': 'A - Attacco', 'value': 'A'},
                    {'label': 'B - Muro', 'value': 'B'},
                    {'label': 'D - Difesa', 'value': 'D'},
                    {'label': 'F - FreeBall', 'value': 'F'}
                ],
                value='S',
                style={'marginBottom': 15}
            ),
            
            html.Label("Caratteristica:", style={'fontWeight': 'bold'}),
            dcc.Dropdown(
                id='source-caratteristica',
                value='Type',
                style={'marginBottom': 15}
            )
        ], style={'width': '45%', 'display': 'inline-block', 'verticalAlign': 'top', 
                  'padding': '20px', 'backgroundColor': '#ecf0f1', 'borderRadius': '10px', 'margin': '10px'}),
        
        # Colonna destra - Selettori Target
        html.Div([
            html.H3("🎯 Secondo Tocco (Target)", style={'color': '#e74c3c'}),
            
            html.Label("Fondamentale:", style={'fontWeight': 'bold', 'marginTop': 10}),
            dcc.Dropdown(
                id='target-fondamentale',
                options=[
                    {'label': 'S - Battuta', 'value': 'S'},
                    {'label': 'R - Ricezione', 'value': 'R'},
                    {'label': 'E - Alzata', 'value': 'E'},
                    {'label': 'A - Attacco', 'value': 'A'},
                    {'label': 'B - Muro', 'value': 'B'},
                    {'label': 'D - Difesa', 'value': 'D'},
                    {'label': 'F - FreeBall', 'value': 'F'}
                ],
                value='R',
                style={'marginBottom': 15}
            ),
            
            html.Label("Caratteristica:", style={'fontWeight': 'bold'}),
            dcc.Dropdown(
                id='target-caratteristica',
                value='Type',
                style={'marginBottom': 15}
            )
        ], style={'width': '45%', 'display': 'inline-block', 'verticalAlign': 'top',
                  'padding': '20px', 'backgroundColor': '#ecf0f1', 'borderRadius': '10px', 'margin': '10px'})
    ], style={'textAlign': 'center'}),
    
    # Sezione info e debug
    html.Div([
        html.H4("📊 Informazioni Debug"),
        html.Div(id='debug-info', style={
            'margin': '20px', 'padding': '15px', 'border': '1px solid #bdc3c7',
            'borderRadius': '5px', 'backgroundColor': '#f8f9fa', 'textAlign': 'left'
        })
    ]),
    
    # Grafico Sankey
    dcc.Graph(id='sankey-graph', style={'height': '700px'})
])

# Callback per aggiornare le opzioni delle caratteristiche source
@app.callback(
    Output('source-caratteristica', 'options'),
    Output('source-caratteristica', 'value'),
    Input('source-fondamentale', 'value')
)
def update_source_caratteristiche(fondamentale):
    if fondamentale is None:
        return [], None
    
    caratteristiche = fond_characteristics.get(fondamentale, [])
    options = [{'label': c, 'value': c} for c in caratteristiche]
    value = caratteristiche[0] if caratteristiche else None
    
    return options, value

# Callback per aggiornare le opzioni delle caratteristiche target
@app.callback(
    Output('target-caratteristica', 'options'),
    Output('target-caratteristica', 'value'),
    Input('target-fondamentale', 'value')
)
def update_target_caratteristiche(fondamentale):
    if fondamentale is None:
        return [], None
    
    caratteristiche = fond_characteristics.get(fondamentale, [])
    options = [{'label': c, 'value': c} for c in caratteristiche]
    value = caratteristiche[0] if caratteristiche else None
    
    return options, value

# Callback principale per creare il diagramma Sankey
@app.callback(
    Output('sankey-graph', 'figure'),
    Output('debug-info', 'children'),
    Input('source-fondamentale', 'value'),
    Input('source-caratteristica', 'value'),
    Input('target-fondamentale', 'value'),
    Input('target-caratteristica', 'value')
)
def update_sankey_graph(source_fond, source_char, target_fond, target_char):
    debug_messages = []
    
    # Controlli di validità
    if not all([source_fond, source_char, target_fond, target_char]):
        debug_messages.append("⚠️ Seleziona tutti i parametri")
        return go.Figure(), html.Div([html.P(msg) for msg in debug_messages])
    
    debug_messages.append(f"🔍 Analisi: {source_fond}({source_char}) → {target_fond}({target_char})")
    
    # Verifica che df esista
    if 'df' not in globals() or df.empty:
        debug_messages.append("❌ Dati non disponibili")
        return go.Figure(), html.Div([html.P(msg) for msg in debug_messages])
    
    debug_messages.append(f"✅ Dataset: {len(df)} righe totali")
    
    # Verifica se esiste una regola
    if target_fond not in regole_abs or source_fond not in regole_abs[target_fond]:
        debug_messages.append(f"❌ Nessuna regola definita da {source_fond} a {target_fond}")
        regole_disponibili = []
        for target, sources in regole_abs.items():
            for source in sources.keys():
                regole_disponibili.append(f"{source} → {target}")
        debug_messages.append(f"Regole disponibili: {', '.join(regole_disponibili)}")
        return go.Figure(), html.Div([html.P(msg) for msg in debug_messages])
    
    # Filtra i dataframe
    df1 = df[df['Foundamental'] == source_fond].copy()
    df2 = df[df['Foundamental'] == target_fond].copy()
    
    debug_messages.append(f"📊 {source_fond}: {len(df1)} righe, {target_fond}: {len(df2)} righe")
    
    if len(df1) == 0 or len(df2) == 0:
        debug_messages.append("❌ Uno dei fondamentali non ha dati")
        return go.Figure(), html.Div([html.P(msg) for msg in debug_messages])
    
    # Verifica che le caratteristiche esistano
    if source_char not in df1.columns:
        debug_messages.append(f"❌ Caratteristica '{source_char}' non trovata in {source_fond}")
        debug_messages.append(f"Colonne disponibili: {', '.join(df1.columns)}")
        return go.Figure(), html.Div([html.P(msg) for msg in debug_messages])
    
    if target_char not in df2.columns:
        debug_messages.append(f"❌ Caratteristica '{target_char}' non trovata in {target_fond}")
        debug_messages.append(f"Colonne disponibili: {', '.join(df2.columns)}")
        return go.Figure(), html.Div([html.P(msg) for msg in debug_messages])
    
    # Crea i collegamenti
    join_pairs = []
    matches_found = 0
    
    # Prendi un campione per velocità (primi 2000 record)
    df1_sample = df1.head(2000)
    debug_messages.append(f"🔍 Analizzando {len(df1_sample)} record di {source_fond}")
    
    for idx, row in df1_sample.iterrows():
        prev_abs = row['AbsNumeroPossesso']
        game_id = row['GameID']
        team_id = row['whichTeamID']
        
        try:
            expected_abs = regole_abs[target_fond][source_fond](prev_abs)
        except Exception as e:
            continue
        
        # Trova i match
        matches = df2[
            (df2['GameID'] == game_id) &
            (df2['AbsNumeroPossesso'] == expected_abs) &
            (df2['whichTeamID'] == team_id)
        ]
        
        if not matches.empty:
            matches_found += len(matches)
            match = matches.iloc[0]
            val1 = row.get(source_char)
            val2 = match.get(target_char)
            
            if pd.notna(val1) and pd.notna(val2) and str(val1) != '' and str(val2) != '':
                join_pairs.append((str(val1), str(val2)))
    
    debug_messages.append(f"🔗 Trovati {matches_found} match, creati {len(join_pairs)} collegamenti")
    
    if not join_pairs:
        debug_messages.append("❌ Nessun collegamento valido trovato")
        return go.Figure(), html.Div([html.P(msg) for msg in debug_messages])
    
    # Crea il dataframe dei collegamenti
    df_pairs = pd.DataFrame(join_pairs, columns=['source', 'target'])
    df_links = df_pairs.groupby(['source', 'target']).size().reset_index(name='value')
    
    debug_messages.append(f"📊 Collegamenti unici: {len(df_links)}")
    debug_messages.append(f"📊 Valori source: {', '.join(df_links['source'].unique()[:10])}")
    debug_messages.append(f"📊 Valori target: {', '.join(df_links['target'].unique()[:10])}")
    
    # Crea le etichette
    source_labels = [f"{source_fond}: {label}" for label in df_links['source'].unique()]
    target_labels = [f"{target_fond}: {label}" for label in df_links['target'].unique()]
    all_labels = source_labels + target_labels
    
    # Mappa i valori agli indici
    source_map = {val: idx for idx, val in enumerate(df_links['source'].unique())}
    target_map = {val: idx + len(source_labels) for idx, val in enumerate(df_links['target'].unique())}
    
    # Crea il diagramma Sankey
    fig = go.Figure(data=[go.Sankey(
        node=dict(
            label=all_labels,
            pad=15,
            thickness=20,
            line=dict(color="black", width=0.5),
            color=["lightblue"] * len(source_labels) + ["lightcoral"] * len(target_labels)
        ),
        link=dict(
            source=[source_map[s] for s in df_links['source']],
            target=[target_map[t] for t in df_links['target']],
            value=df_links['value'].tolist()
        )
    )])
    
    fig.update_layout(
        title_text=f"{source_fond} ({source_char}) → {target_fond} ({target_char})",
        font_size=12,
        height=650
    )
    
    debug_messages.append("✅ Diagramma creato con successo!")
    
    return fig, html.Div([html.P(msg) for msg in debug_messages])

# Avvia l'app (solo se eseguito come script)
if __name__ == '__main__':
    app.run_server(debug=True, port=8052)
else:
    # Per il notebook, mostra l'URL
    print("🚀 App Dash creata!")
    print("Per avviare l'app, esegui in una nuova cella:")
    print("app.run_server(debug=True, port=8052)")
    print("Poi vai su: http://localhost:8052")
