#In questo file creo una VIEW rilevations_libero_battute_view, che è come rilevations_libero_view, ma contiene solo le azioni con le battute



import numpy as np 
import pandas as pd  #Realizzato con la versione di Pandas 2.2.3
import os
import psycopg
from sqlalchemy import create_engine, text


conn = psycopg.connect(
    dbname="db_modena",           # database creato in pgAdmin4
    user="postgres",              # Il tuo nome utente PostgreSQL
    password="AcquaLevissima1",   # La password che hai scelto per 'postgres'
    host="localhost",             # 'localhost' se è sul tuo PC
    port=5432                     # La porta predefinita è 5432
)

cur = conn.cursor() 

cur.execute("DROP VIEW IF EXISTS rilevations_libero_battute_view")
conn.commit()

cur.execute("""
CREATE OR REPLACE VIEW rilevations_libero_battute_view AS
SELECT * FROM rilevations_libero_view
WHERE "Foundamental" = 'S'
""")

conn.commit()
print("✅ VIEW rilevations_libero_battute_view creata con successo")




