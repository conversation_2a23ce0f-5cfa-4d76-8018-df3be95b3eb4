{"cells": [{"cell_type": "markdown", "metadata": {"id": "vpxmQ-xNB1VY"}, "source": ["<p align=\"center\">\n", "<img src=\"https://raw.githubusercontent.com/shukkkur/VolleyVision/main/assets/vv_logo.png\">\n", "</p>\n", "<h1 align=\"center\">Welcome to VolleyVision<br><code>Local</code> - Quick & Easy</h1>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Configurazione dell'ambiente\n", "\n", "<PERSON>rima di tutto, install<PERSON><PERSON> le dipendenze necessarie:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["^C\n"]}], "source": ["# Installa le dipendenze necessarie\n", "!pip install torch opencv-python pillow numpy matplotlib tqdm roboflow"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Importa le librerie necessarie\n", "import os\n", "import cv2\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from IPython.display import HTML\n", "from base64 import b64encode\n", "import requests\n", "from tqdm import tqdm"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Navigazione alle cartelle corrette\n", "\n", "Ora navighiamo alla cartella VolleyVision e poi alla sottocartella Stage I - Volleyball:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# O<PERSON><PERSON> il percorso corrente\n", "current_path = os.getcwd()\n", "print(f\"Percorso corrente: {current_path}\")\n", "\n", "# Verifica se siamo già nella cartella FilePython_c\n", "if \"FilePython_c\" in current_path:\n", "    # Naviga alla cartella VolleyVision\n", "    volleyball_path = os.path.join(current_path, \"VolleyVision\", \"Stage I - Volleyball\")\n", "else:\n", "    # Assumiamo che siamo nella cartella principale del progetto\n", "    volleyball_path = os.path.join(current_path, \"FilePython_c\", \"VolleyVision\", \"Stage I - Volleyball\")\n", "\n", "# Cambia directory\n", "os.ch<PERSON>(volleyball_path)\n", "print(f\"Nuova directory: {os.getcwd()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Preparazione dei file necessari\n", "\n", "Ora verifichiamo e creiamo le cartelle necessarie, e scarichiamo i file del modello se non esistono:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Funzione per scaricare file con barra di progresso\n", "def download_file(url, destination):\n", "    \"\"\"Download a file from a URL with a progress bar\"\"\"\n", "    response = requests.get(url, stream=True)\n", "    total_size = int(response.headers.get('content-length', 0))\n", "    block_size = 1024  # 1 Kibibyte\n", "    \n", "    # Crea la directory se non esiste\n", "    os.makedirs(os.path.dirname(destination), exist_ok=True)\n", "    \n", "    print(f\"Downloading {url} to {destination}\")\n", "    \n", "    with open(destination, 'wb') as file, tqdm(\n", "            desc=os.path.basename(destination),\n", "            total=total_size,\n", "            unit='iB',\n", "            unit_scale=True,\n", "            unit_divisor=1024,\n", "        ) as bar:\n", "        for data in response.iter_content(block_size):\n", "            size = file.write(data)\n", "            bar.update(size)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Crea la cartella Output se non esiste\n", "os.makedirs(\"Output\", exist_ok=True)\n", "\n", "# Crea la cartella models se non esiste\n", "os.makedirs(\"models\", exist_ok=True)\n", "\n", "# Crea la cartella DaSiamRPN se non esiste\n", "os.makedirs(\"DaSiamRPN\", exist_ok=True)\n", "\n", "# Scarica i file del modello YOLOv7 se non esistono\n", "if not os.path.exists(\"models/yolov7-tiny.pt\"):\n", "    download_file(\n", "        \"https://github.com/WongKinYiu/yolov7/releases/download/v0.1/yolov7-tiny.pt\",\n", "        \"models/yolov7-tiny.pt\"\n", "    )\n", "\n", "# Scarica i file del modello DaSiamRPN se non esistono\n", "if not os.path.exists(\"DaSiamRPN/dasiamrpn_model.onnx\"):\n", "    download_file(\n", "        \"https://github.com/opencv/opencv_zoo/raw/master/models/object_tracking_dasiamrpn/dasiamrpn_model.onnx\",\n", "        \"DaSiamRPN/dasiamrpn_model.onnx\"\n", "    )\n", "\n", "if not os.path.exists(\"DaSiamRPN/dasiamrpn_kernel_cls1.onnx\"):\n", "    download_file(\n", "        \"https://github.com/opencv/opencv_zoo/raw/master/models/object_tracking_dasiamrpn/dasiamrpn_kernel_cls1.onnx\",\n", "        \"DaSiamRPN/dasiamrpn_kernel_cls1.onnx\"\n", "    )\n", "\n", "if not os.path.exists(\"DaSiamRPN/dasiamrpn_kernel_r1.onnx\"):\n", "    download_file(\n", "        \"https://github.com/opencv/opencv_zoo/raw/master/models/object_tracking_dasiamrpn/dasiamrpn_kernel_r1.onnx\",\n", "        \"DaSiamRPN/dasiamrpn_kernel_r1.onnx\"\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Scarica un video di esempio\n", "\n", "Scarichiamo un video di pallavolo di esempio per testare il sistema:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Scarica un video di esempio se non esiste\n", "if not os.path.exists(\"sample_volleyball.mp4\"):\n", "    download_file(\n", "        \"https://github.com/shukkkur/VolleyVision/raw/main/assets/back_view.mp4\",\n", "        \"sample_volleyball.mp4\"\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Verifica che il file my_utils.py contenga l'import di re\n", "\n", "Verifichiamo che il file my_utils.py contenga l'import di re, altrimenti lo aggiungiamo:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Verifica che il file my_utils.py contenga l'import di re\n", "with open(\"my_utils.py\", \"r\") as f:\n", "    content = f.read()\n", "\n", "if \"import re\" not in content:\n", "    # Aggiungi l'import di re dopo gli altri import\n", "    content = content.replace(\"from typing import Generator, List, Tuple\", \"from typing import Generator, List, Tuple\\nimport re\")\n", "    \n", "    # Scrivi il contenuto modificato nel file\n", "    with open(\"my_utils.py\", \"w\") as f:\n", "        f.write(content)\n", "    \n", "    print(\"Aggiunto l'import di re al file my_utils.py\")\n", "else:\n", "    print(\"Il file my_utils.py contiene già l'import di re\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Funzione per visualizzare i video\n", "\n", "Definiamo una funzione per visualizzare i video nel notebook:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Funzione per visualizzare i video nel notebook\n", "def show_video(video_path, width=640):\n", "    video_file = open(video_path, \"r+b\").read()\n", "    video_url = f\"data:video/mp4;base64,{b64encode(video_file).decode()}\"\n", "    return HTML(f\"\"\"\n", "    <video width={width} controls>\n", "        <source src=\"{video_url}\" type=\"video/mp4\">\n", "    </video>\"\"\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Esegui il rilevamento e il tracciamento della palla da pallavolo\n", "\n", "Ora eseguiamo il rilevamento e il tracciamento della palla da pallavolo:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Esegui il rilevamento e il tracciamento della palla da pallavolo\n", "!python volley_track.py --input_video_path sample_volleyball.mp4 --model yolov7 --confidence 0.25 --marker circle --color yellow"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualizza il video di output\n", "\n", "Visualizziamo il video di output:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualizza il video di output\n", "output_path = \"VideoOutput/yolov7Track_sample_volleyball.mp4\"\n", "if os.path.exists(output_path):\n", "    show_video(output_path)\n", "else:\n", "    print(f\"Video di output non trovato: {output_path}\")\n", "    print(\"Controlla se l'elaborazione è stata completata con successo.\")\n", "    \n", "    # Elenca i file nella cartella VideoOutput\n", "    if os.path.exists(\"VideoOutput\"):\n", "        print(\"\\nFile nella cartella VideoOutput:\")\n", "        for file in os.listdir(\"VideoOutput\"):\n", "            print(f\" - {file}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 4}