#Creo la view players_each_season, che partendo da players_each_game_view, fa un GROUP BY("<PERSON><PERSON>", "PlayerID")

#Come TeamID mette quello più frequente
#Come TeamName mette quello più frequente
#Come Nome mette quello più frequente
#Come cognome mette qello più frequente
#Come NumeroMaglia mette quello più frequente
#Come NumAzioniDaRuolo5, mette la somma di tutta l'Annata
#Come NumAzioniDaRuolo4, mette la somma di tutta l'Annata
#Come NumAzioniDaRuolo3, mette la somma di tutta l'Annata
#Come NumAzioniDaRuolo2, mette la somma di tutta l'Annata
#Come RuoloCalc mette quello più frequente
#Come RuoloStagione, quello che mi interessa, se il RuoloCalc più frequente è 1 metti 1, altrimenti metti quello che ha il NumeroAzioniDaRuoloX più alto


import os
import numpy as np
import pandas as pd
import psycopg


# Connessione al database
conn = psycopg.connect(
    dbname="db_modena",           # database creato in pgAdmin4
    user="postgres",              # Il tuo nome utente PostgreSQL
    password="AcquaLevissima1",   # La password che hai scelto per 'postgres'
    host="localhost",             # 'localhost' se è sul tuo PC
    port=5432                     # La porta predefinita è 5432
)

cur = conn.cursor()


cur.execute("""
DROP VIEW IF EXISTS players_each_season_view;
""")
cur.execute("""
CREATE OR REPLACE VIEW players_each_season_view AS
WITH base AS (
    SELECT 
        "Annata",
        "PlayerID",
        
        -- Valori più frequenti
        MODE() WITHIN GROUP (ORDER BY "TeamID") AS "TeamID",
        MODE() WITHIN GROUP (ORDER BY "TeamName") AS "TeamName",
        MODE() WITHIN GROUP (ORDER BY "Nome") AS "Nome",
        MODE() WITHIN GROUP (ORDER BY "Cognome") AS "Cognome",
        MODE() WITHIN GROUP (ORDER BY "NumeroMaglia") AS "NumeroMaglia",
        MODE() WITHIN GROUP (ORDER BY "RuoloCalc") AS "RuoloCalc",
        
        -- Somme delle azioni per ruolo
        SUM("NumAzioniDaRuolo5") AS "NumAzioniDaRuolo5",
        SUM("NumAzioniDaRuolo4") AS "NumAzioniDaRuolo4",
        SUM("NumAzioniDaRuolo3") AS "NumAzioniDaRuolo3",
        SUM("NumAzioniDaRuolo2") AS "NumAzioniDaRuolo2"
        
    FROM players_each_game_view
    GROUP BY "Annata", "PlayerID"
),
ruolo_finale AS (
    SELECT *,
        -- Determina il ruolo stagionale. Se il RuoloCalc più frequente è il libero (1), allora è quello. Altrimenti metti il ruolo con il maggior numero di azioni. Altrimenti metti il RuoloCalc più frequente.
        CASE 
            WHEN "RuoloCalc" = 1 THEN 1
            WHEN GREATEST("NumAzioniDaRuolo5", "NumAzioniDaRuolo4", "NumAzioniDaRuolo3", "NumAzioniDaRuolo2") = "NumAzioniDaRuolo5" THEN 5
            WHEN GREATEST("NumAzioniDaRuolo5", "NumAzioniDaRuolo4", "NumAzioniDaRuolo3", "NumAzioniDaRuolo2") = "NumAzioniDaRuolo4" THEN 4
            WHEN GREATEST("NumAzioniDaRuolo5", "NumAzioniDaRuolo4", "NumAzioniDaRuolo3", "NumAzioniDaRuolo2") = "NumAzioniDaRuolo3" THEN 3
            WHEN GREATEST("NumAzioniDaRuolo5", "NumAzioniDaRuolo4", "NumAzioniDaRuolo3", "NumAzioniDaRuolo2") = "NumAzioniDaRuolo2" THEN 2
            ELSE "RuoloCalc"
        END AS "RuoloStagione"
    FROM base
)

SELECT * FROM ruolo_finale;

""")
conn.commit()
print("✅ View players_each_season creata con successo")


#Attenzione: al momento sia players_each_game_view che players_each_season contengono i giocatori SENZA considerare il libero in campo, ovvero i NumeroAzioniDaRuoloX non considerano il libero in campo.
#Quindi i NumeroAzioniDaRuoloX sono giuste per schiacciatori, opposti, palleggiatore, ma per il centrale ne sto contando di più, e per il libero non li sto contando.








