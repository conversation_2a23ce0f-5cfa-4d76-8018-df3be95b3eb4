#In questo file creo una nuova VIEW players_each_game_view1, sperando sia la view definitiva, in cui metto come colonne tutte le metriche box score che posso, usando le CTE
#Le metriche (colonne) le calcolo in base alla view rilevations_libero_view, siccome voglio considerare il libero in campo (oppure in base a rilevations_libero_battute_view, che contiene solo le azioni con le battute)


import numpy as np 
import pandas as pd  #Realizzato con la versione di Pandas 2.2.3
import os
import psycopg
from sqlalchemy import create_engine, text


conn = psycopg.connect(
    dbname="db_modena",           # database creato in pgAdmin4
    user="postgres",              # Il tuo nome utente PostgreSQL
    password="AcquaLevissima1",   # La password che hai scelto per 'postgres'
    host="localhost",             # 'localhost' se è sul tuo PC
    port=5432                     # La porta predefinita è 5432
)

cur = conn.cursor() 

conn.rollback()

cur.execute("""SET max_parallel_workers_per_gather = 4;
""")



# Index 1
cur.execute("""
    CREATE INDEX IF NOT EXISTS idx_rilevations_game_player
    ON rilevations_libero_view("GameID", "NumeroMaglia_ID")
""")
print("Ensured index idx_rilevations_game_player")

# Index 2
cur.execute("""
    CREATE INDEX IF NOT EXISTS idx_rilevations_game_team
    ON rilevations_libero_view("GameID", "whichTeamID")
""")
print("Ensured index idx_rilevations_game_team")

# Index 3
cur.execute("""
    CREATE INDEX IF NOT EXISTS idx_rilevations_fundamental
    ON rilevations_libero_view("Foundamental", "Eval")
""")
print("Ensured index idx_rilevations_fundamental")

# Index 4
cur.execute("""
    CREATE INDEX IF NOT EXISTS idx_base_game_player
    ON players_each_game_view("GameID", "PlayerID")
""")
print("Ensured index idx_base_game_player")





cur.execute("DROP TABLE IF EXISTS players_each_game_a")
conn.commit()


cur.execute("""
CREATE TABLE players_each_game_a AS
WITH base AS (
  SELECT * FROM players_each_game_view
),

-- OTTIMIZZAZIONE 1: Singola scansione con tutti i calcoli dei giocatori
all_players_positions AS (
  SELECT 
    "GameID",
    -- Players in campo (tutte le posizioni)
    "probHomePlayer1_ID" AS "PlayerID", 
    1 AS "Position", 
    'Home' AS "Team", 
    2 AS "Linea"
  FROM rilevations_libero_battute_view WHERE "probHomePlayer1_ID" IS NOT NULL
  UNION ALL
  SELECT "GameID", "probHomePlayer2_ID", 2, 'Home', 1 FROM rilevations_libero_battute_view WHERE "probHomePlayer2_ID" IS NOT NULL
  UNION ALL
  SELECT "GameID", "probHomePlayer3_ID", 3, 'Home', 1 FROM rilevations_libero_battute_view WHERE "probHomePlayer3_ID" IS NOT NULL
  UNION ALL
  SELECT "GameID", "probHomePlayer4_ID", 4, 'Home', 1 FROM rilevations_libero_battute_view WHERE "probHomePlayer4_ID" IS NOT NULL
  UNION ALL
  SELECT "GameID", "probHomePlayer5_ID", 5, 'Home', 2 FROM rilevations_libero_battute_view WHERE "probHomePlayer5_ID" IS NOT NULL
  UNION ALL
  SELECT "GameID", "probHomePlayer6_ID", 6, 'Home', 2 FROM rilevations_libero_battute_view WHERE "probHomePlayer6_ID" IS NOT NULL
  UNION ALL
  SELECT "GameID", "probVisitorPlayer1_ID", 1, 'Visitor', 2 FROM rilevations_libero_battute_view WHERE "probVisitorPlayer1_ID" IS NOT NULL
  UNION ALL
  SELECT "GameID", "probVisitorPlayer2_ID", 2, 'Visitor', 1 FROM rilevations_libero_battute_view WHERE "probVisitorPlayer2_ID" IS NOT NULL
  UNION ALL
  SELECT "GameID", "probVisitorPlayer3_ID", 3, 'Visitor', 1 FROM rilevations_libero_battute_view WHERE "probVisitorPlayer3_ID" IS NOT NULL
  UNION ALL
  SELECT "GameID", "probVisitorPlayer4_ID", 4, 'Visitor', 1 FROM rilevations_libero_battute_view WHERE "probVisitorPlayer4_ID" IS NOT NULL
  UNION ALL
  SELECT "GameID", "probVisitorPlayer5_ID", 5, 'Visitor', 2 FROM rilevations_libero_battute_view WHERE "probVisitorPlayer5_ID" IS NOT NULL
  UNION ALL
  SELECT "GameID", "probVisitorPlayer6_ID", 6, 'Visitor', 2 FROM rilevations_libero_battute_view WHERE "probVisitorPlayer6_ID" IS NOT NULL
),

-- OTTIMIZZAZIONE 2: Calcoli consolidati delle azioni
player_actions_summary AS (
  SELECT 
    "GameID",
    "PlayerID",
    COUNT(*) AS "NumAzioniGiocate",
    COUNT(CASE WHEN "Linea" = 1 THEN 1 END) AS "NumAzioniGiocate1Linea",
    COUNT(CASE WHEN "Linea" = 2 THEN 1 END) AS "NumAzioniGiocate2Linea"
  FROM all_players_positions
  GROUP BY "GameID", "PlayerID"
),

-- OTTIMIZZAZIONE 3: Singola scansione di rilevations_libero_view per tutti i calcoli
player_stats_consolidated AS (
  SELECT 
    r."GameID",
    r."NumeroMaglia_ID" AS "PlayerID",
    -- Conteggi totali
    COUNT(*) AS "TotalTouch",
    COUNT(CASE WHEN r."Foundamental" IN ('S', 'A', 'B') AND r."Eval" = '#' THEN 1 END) AS "PuntiFatti",
    COUNT(CASE WHEN r."Eval" = '=' THEN 1 END) AS "TotalError",
    COUNT(CASE WHEN r."Eval" IN ('-', '=') THEN 1 END) AS "TotalNegative",
    COUNT(CASE WHEN r."Eval" = '#' THEN 1 END) AS "TotalPerfect",
    COUNT(CASE WHEN r."Eval" IN ('#', '+') THEN 1 END) AS "TotalPositive",
    COUNT(CASE WHEN r."Foundamental" = 'A' THEN 1 END) AS "Attacks",
    
    -- Set distinti giocati
    COUNT(DISTINCT r."SetNumber") AS "NumSetGiocati",
    
    -- Punti per linea (usando join con posizioni)
    COUNT(CASE WHEN r."Foundamental" IN ('S', 'A', 'B') AND r."Eval" = '#' AND pos."Linea" = 1 THEN 1 END) AS "PuntiFatti1Linea",
    COUNT(CASE WHEN r."Foundamental" IN ('S', 'A', 'B') AND r."Eval" = '#' AND pos."Linea" = 2 THEN 1 END) AS "PuntiFatti2Linea"
    
  FROM rilevations_libero_view r
  LEFT JOIN (
    SELECT DISTINCT "GameID", "PlayerID", "Linea"
    FROM all_players_positions
  ) pos ON r."GameID" = pos."GameID" AND r."NumeroMaglia_ID" = pos."PlayerID"
  WHERE r."NumeroMaglia_ID" IS NOT NULL
  GROUP BY r."GameID", r."NumeroMaglia_ID"
),

-- OTTIMIZZAZIONE 4: Calcoli di squadra consolidati
team_stats AS (
  SELECT 
    r."GameID",
    r."whichTeamID" AS "TeamID_auto",
    COUNT(CASE WHEN r."Foundamental" = 'A' THEN 1 END) AS "TeamAttacks"
  FROM rilevations_libero_view r
  GROUP BY r."GameID", r."whichTeamID"
),

-- OTTIMIZZAZIONE 5: Attacchi di squadra mentre in campo (semplificato)
team_attacks_on_field AS (
  SELECT 
    pos."GameID",
    pos."PlayerID",
    COUNT(*) AS "TeamAttacksWhileOnField"
  FROM all_players_positions pos
  JOIN rilevations_libero_view r ON r."GameID" = pos."GameID" AND r."Foundamental" = 'A'
  JOIN base b ON b."GameID" = pos."GameID" AND b."PlayerID" = pos."PlayerID"
  WHERE r."whichTeamID" = b."TeamID_auto"
  GROUP BY pos."GameID", pos."PlayerID"
),

-- OTTIMIZZAZIONE 6: Calcoli ruolo-team semplificati
role_team_stats AS (
  SELECT 
    b1."GameID",
    b1."PlayerID",
    COUNT(*) AS "RoleTeamAttacks",
    COUNT(CASE WHEN pos."PlayerID" IS NOT NULL THEN 1 END) AS "RoleTeamAttacksWhileOnField"
  FROM base b1
  JOIN rilevations_libero_view r ON r."GameID" = b1."GameID" AND r."whichTeamID" = b1."TeamID_auto" AND r."Foundamental" = 'A'
  JOIN base b2 ON b2."GameID" = r."GameID" AND b2."PlayerID" = r."NumeroMaglia_ID" 
                 AND b2."TeamID_auto" = b1."TeamID_auto" AND b2."RuoloCalc" = b1."RuoloCalc"
  LEFT JOIN all_players_positions pos ON pos."GameID" = r."GameID" AND pos."PlayerID" = b1."PlayerID"
  GROUP BY b1."GameID", b1."PlayerID"
),

-- OTTIMIZZAZIONE 7: Calcoli finali con formule
final_calculations AS (
  SELECT 
    psc."GameID",
    psc."PlayerID",
    psc."TotalTouch",
    psc."PuntiFatti",
    psc."NumSetGiocati",
    psc."TotalError",
    psc."TotalNegative",
    psc."TotalPerfect",
    psc."TotalPositive",
    psc."Attacks",
    psc."PuntiFatti1Linea",
    psc."PuntiFatti2Linea",
    pas."NumAzioniGiocate",
    pas."NumAzioniGiocate1Linea", 
    pas."NumAzioniGiocate2Linea",
    
    -- Calcoli percentuali e rapporti
    CASE WHEN psc."NumSetGiocati" > 0 THEN psc."PuntiFatti"::FLOAT / psc."NumSetGiocati" ELSE NULL END AS "PuntiPerSet",
    CASE WHEN pas."NumAzioniGiocate" > 0 THEN psc."PuntiFatti"::FLOAT / pas."NumAzioniGiocate" ELSE NULL END AS "PuntiPerAzione",
    CASE WHEN pas."NumAzioniGiocate" > 0 THEN (psc."PuntiFatti"::FLOAT / pas."NumAzioniGiocate") * 100 ELSE NULL END AS "PuntiPer100Azioni",
    
    CASE WHEN pas."NumAzioniGiocate1Linea" > 0 THEN psc."PuntiFatti1Linea"::FLOAT / pas."NumAzioniGiocate1Linea" ELSE NULL END AS "PuntiPerAzione1Linea",
    CASE WHEN pas."NumAzioniGiocate2Linea" > 0 THEN psc."PuntiFatti2Linea"::FLOAT / pas."NumAzioniGiocate2Linea" ELSE NULL END AS "PuntiPerAzione2Linea",
    
    CASE WHEN psc."TotalTouch" > 0 THEN psc."TotalError"::FLOAT / psc."TotalTouch" ELSE NULL END AS "TotalErrorPercentage",
    CASE WHEN psc."TotalTouch" > 0 THEN psc."TotalNegative"::FLOAT / psc."TotalTouch" ELSE NULL END AS "TotalNegativePercentage",
    CASE WHEN psc."TotalTouch" > 0 THEN psc."TotalPerfect"::FLOAT / psc."TotalTouch" ELSE NULL END AS "TotalPerfectPercentage",
    CASE WHEN psc."TotalTouch" > 0 THEN psc."TotalPositive"::FLOAT / psc."TotalTouch" ELSE NULL END AS "TotalPositivePercentage"
    
  FROM player_stats_consolidated psc
  LEFT JOIN player_actions_summary pas ON psc."GameID" = pas."GameID" AND psc."PlayerID" = pas."PlayerID"
)

-- SELECT FINALE con tutte le metriche
SELECT 
  base.*,
  COALESCE(fc."NumAzioniGiocate", 0) AS "NumAzioniGiocate",
  COALESCE(fc."NumAzioniGiocate1Linea", 0) AS "NumAzioniGiocate1Linea",
  COALESCE(fc."NumAzioniGiocate2Linea", 0) AS "NumAzioniGiocate2Linea",
  COALESCE(fc."TotalTouch", 0) AS "TotalTouch",
  COALESCE(fc."PuntiFatti", 0) AS "PuntiFatti",
  COALESCE(fc."NumSetGiocati", 0) AS "NumSetGiocati",
  fc."PuntiPerSet",
  fc."PuntiPerAzione",
  fc."PuntiPer100Azioni",
  COALESCE(fc."PuntiFatti1Linea", 0) AS "PuntiFatti1Linea",
  COALESCE(fc."PuntiFatti2Linea", 0) AS "PuntiFatti2Linea",
  fc."PuntiPerAzione1Linea",
  fc."PuntiPerAzione2Linea",
  COALESCE(fc."TotalError", 0) AS "TotalError",
  fc."TotalErrorPercentage",
  COALESCE(fc."TotalNegative", 0) AS "TotalNegative",
  fc."TotalNegativePercentage",
  COALESCE(fc."TotalPerfect", 0) AS "TotalPerfect",
  fc."TotalPerfectPercentage",
  COALESCE(fc."TotalPositive", 0) AS "TotalPositive",
  fc."TotalPositivePercentage",
  COALESCE(fc."Attacks", 0) AS "Attacks",
  COALESCE(rts."RoleTeamAttacks", 0) AS "RoleTeamAttacks",
  COALESCE(rts."RoleTeamAttacksWhileOnField", 0) AS "RoleTeamAttacksWhileOnField",
  COALESCE(ts."TeamAttacks", 0) AS "TeamAttacks",
  COALESCE(taof."TeamAttacksWhileOnField", 0) AS "TeamAttacksWhileOnField",
  CASE WHEN taof."TeamAttacksWhileOnField" > 0 THEN fc."Attacks"::FLOAT / taof."TeamAttacksWhileOnField" ELSE NULL END AS "AttkParticipation"

FROM base
LEFT JOIN final_calculations fc ON base."GameID" = fc."GameID" AND base."PlayerID" = fc."PlayerID"
LEFT JOIN team_stats ts ON base."GameID" = ts."GameID" AND base."TeamID_auto" = ts."TeamID_auto"
LEFT JOIN team_attacks_on_field taof ON base."GameID" = taof."GameID" AND base."PlayerID" = taof."PlayerID"
LEFT JOIN role_team_stats rts ON base."GameID" = rts."GameID" AND base."PlayerID" = rts."PlayerID";
 
 
""")

conn.commit()
print("✅ TABLE players_each_game_A creata con successo")


#Invece di lasciare valori NULL metti 0






