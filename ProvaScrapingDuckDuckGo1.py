from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import random
import time

def create_undetected_chrome():
    options = Options()
    options.add_argument('--headless=new')
    options.add_argument('--disable-gpu')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-blink-features=AutomationControlled')
    options.add_argument('--disable-extensions')
    options.add_argument('--enable-unsafe-swiftshader')  # Add this to handle WebGL deprecation
    options.add_argument('--disable-software-rasterizer')  # Add this to reduce WebGL warnings
    options.add_argument(f'--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    # Add these arguments for better stability
    options.add_argument('--disable-web-security')
    options.add_argument('--allow-running-insecure-content')
    options.add_argument('--disable-site-isolation-trials')
    options.add_argument('--ignore-certificate-errors')  # Add this to handle potential SSL issues
    
    try:
        driver = webdriver.Chrome(options=options)
        
        # Mask webdriver presence
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        return driver
    except Exception as e:
        print(f"Error creating Chrome driver: {str(e)}")
        # You might want to add a retry mechanism here
        raise

def wait_for_cloudflare(driver, timeout=30):
    try:
        WebDriverWait(driver, timeout).until(
            lambda x: not any(text in x.page_source.lower() 
                            for text in ["checking if the site connection is secure",
                                       "checking your browser",
                                       "cloudflare",
                                       "challenge"])
        )
    except TimeoutException:
        pass

def find_volleybox_url(name, surname, max_retries=3):
    for attempt in range(max_retries):
        driver = None
        try:
            driver = create_undetected_chrome()
            base_urls = [
                f"https://volleybox.net/it/{name.lower()}-{surname.lower()}",
                f"https://volleybox.net/{name.lower()}-{surname.lower()}",
                f"https://volleybox.net/it/{surname.lower()}-{name.lower()}",
                f"https://volleybox.net/{surname.lower()}-{name.lower()}"
            ]

            for url in base_urls:
                try:
                    print(f"Attempt {attempt + 1}, trying URL: {url}")
                    
                    # Prima visita la homepage
                    driver.get("https://volleybox.net")
                    time.sleep(random.uniform(3, 5))
                    
                    # Poi vai all'URL del giocatore
                    driver.get(url)
                    
                    # Gestisci eventuali challenge Cloudflare
                    wait_for_cloudflare(driver)
                    
                    # Aggiungi un delay casuale per sembrare più umano
                    time.sleep(random.uniform(2, 4))
                    
                    # Verifica il contenuto della pagina
                    if name.lower() in driver.page_source.lower() and \
                       surname.lower() in driver.page_source.lower() and \
                       "player profile" in driver.page_source.lower():
                        return url
                    
                except Exception as e:
                    print(f"Error with URL {url}: {str(e)}")
                    time.sleep(random.uniform(3, 5))
                    continue
            
        except Exception as e:
            print(f"Attempt {attempt + 1} failed: {str(e)}")
            time.sleep(random.uniform(5, 10))
            
        finally:
            if driver:
                driver.quit()
                
    return None

def main():
    name = "Jacopo"
    surname = "Massari"
    print(f"\nSearching for player: {name} {surname}")
    
    url = find_volleybox_url(name, surname)
    
    if url:
        print(f"\nFinal result - Found URL: {url}")
    else:
        print("\nFinal result: No URL found")

if __name__ == "__main__":
    main()