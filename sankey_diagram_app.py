import pandas as pd
import plotly.graph_objects as go
from dash import Dash, dcc, html, Input, Output
import numpy as np
import duckdb

# Connessione a DuckDB
try:
    con = duckdb.connect('db_modena_1.duckdb')
    print("✅ Connessione al database riuscita")
except Exception as e:
    print(f"❌ ERRORE nella connessione al database: {e}")
    exit(1)

# Le regole per collegare i fondamentali
regole_abs = {
    #Nelle ricezioni, se prima avevo la battuta, prendo quelle con AbsNumeroPossesso=2
    'R': {
        'S': lambda prev_abs: 2                 #La ricezione può solo avere NumAbsPossesso=2 se la battuta è avvenuta nello stesso possesso
    },
    #Nelle difese, se prima avevo R o S prendo quelle con AbsNumeroPossesso= 3. Se prima avevo A, E (tocchi dell'altra squadra) prendo quelle nel possesso dopo.
    'D': {
        'R': lambda prev_abs: 3,                #Una difesa dopo ricezione, siccome la ricezione ha NumAbsPossesso=2, la difesa ha NumAbsPossesso=3
        'S': lambda prev_abs: 3,                #Una difesa dopo battuta, siccome va di là e ce la rice, poi se torna di qua ha NumAbsPossesso=3
        'E': lambda prev_abs: prev_abs + 1,    
        'A': lambda prev_abs: prev_abs + 1     
        #'B': qui siccome non sappiamo se dopo il muro la prende in difesa la squadra che attacca o che mura, prendiamo la difesa subito dopo il muro
    },
    #Nelle FreeBall è come nelle difese
    'F': {
        'R': lambda prev_abs: 3,                #Una FreeBall dopo ricezione, siccome la ricezione ha NumAbsPossesso=2, la difesa ha NumAbsPossesso=3
        'S': lambda prev_abs: 3,                #Una FreeBall dopo battuta, siccome va di là e ce la rice, poi se torna di qua ha NumAbsPossesso=3
        'E': lambda prev_abs: prev_abs + 1,    
        'A': lambda prev_abs: prev_abs + 1     
        #'B': qui siccome non sappiamo se dopo il muro la prende in difesa la squadra che attacca o che mura, prendiamo la difesa subito dopo il muro
    },
    #Nelle alzate, se prima avevo la R, D, F, prendo quelle nello stesso possesso. Se invece prima c'è S, A, B (tocchi dell'altra squadra), prendo quelle con AbsNumeroPossesso+1
    'E': {
        'R': lambda prev_abs: prev_abs,
        'D': lambda prev_abs: prev_abs,
        'F': lambda prev_abs: prev_abs,
        'S': lambda prev_abs: prev_abs + 1,
        'A': lambda prev_abs: prev_abs + 1,
        'B': lambda prev_abs: prev_abs + 1
    },
    #Nell'attacco, se prima avevo la R, E, D, F prendo quelli nello stesso possesso. Se invece prima c'è S o B (tocchi dell'altra squadra) prendo quelle con AbsNumeroPossesso+1
    'A': {
        'R': lambda prev_abs: prev_abs,
        'E': lambda prev_abs: prev_abs,
        'D': lambda prev_abs: prev_abs,
        'F': lambda prev_abs: prev_abs,
        'S': lambda prev_abs: prev_abs + 1,
        'B': lambda prev_abs: prev_abs + 1
    },
    #Nel muro, se prima avevo la R, E, A, D, F (tocchi dell'altra squadra) prendo quelle con AbsNumeroPossesso+1. Se invece prima c'è S (tocchi della stessa squadra due possessi fa) prendo quelle con AbsNumeroPossesso+2
    'B': {
        'R': lambda prev_abs: prev_abs + 1,
        'E': lambda prev_abs: prev_abs + 1,
        'A': lambda prev_abs: prev_abs + 1,
        'D': lambda prev_abs: prev_abs + 1,
        'F': lambda prev_abs: prev_abs + 1,
        'S': lambda prev_abs: prev_abs + 2
    },
}

# Caricamento dei dati
try:
    df = con.execute("""
    SELECT 
        "GameID",
        "Foundamental",
        "AbsNumeroPossesso",
        "RilevationNumber",
        "whichTeamID",
        "NumeroMaglia_ID",
        "Type",
        "Eval",
        "SetterCall",
        "TargAttk",
        "StartZone",
        "AttkCombination",
        'End_' || "EndZoneEsecZone" AS "EndZoneEsecZone",
        "EndSubzoneEsecSubzone",
        'Skill_' || "SkillType" AS "SkillType",
        "PlayersInfo",
        "Special",
        "CustomChar"
    FROM rilevations_libero_view_duckdb
    WHERE "Foundamental" IN ('S', 'R', 'E', 'A', 'B', 'D', 'F')  
    """).df()
    print(f"✅ Dataframe caricato: {len(df)} righe")
except Exception as e:
    print(f"❌ ERRORE nel caricare i dati: {e}")
    # Crea un dataframe vuoto per evitare errori
    df = pd.DataFrame()

# Caratteristiche disponibili per ogni fondamentale
fond_characteristics = {
    'S': ['Type', 'Eval', 'StartZone', 'EndZoneEsecZone', 'EndSubzoneEsecSubzone', 'Special'],
    'R': ['Type', 'Eval', 'StartZone', 'EndZoneEsecZone', 'EndSubzoneEsecSubzone', 'SkillType', 'PlayersInfo', 'Special'],
    'E': ['Type', 'Eval', 'SetterCall', 'TargAttk', 'EndZoneEsecZone', 'EndSubzoneEsecSubzone', 'SkillType', 'Special'],
    'A': ['Type', 'Eval', 'AttkCombination', 'StartZone', 'EndZoneEsecZone', 'EndSubzoneEsecSubzone', 'SkillType', 'PlayersInfo', 'Special'],
    'B': ['Type', 'Eval', 'EndZoneEsecZone', 'EndSubzoneEsecSubzone', 'SkillType', 'PlayersInfo', 'Special'],
    'D': ['Type', 'Eval', 'StartZone', 'EndZoneEsecZone', 'EndSubzoneEsecSubzone', 'SkillType', 'Special'],
    'F': ['Type', 'Eval', 'EndZoneEsecZone', 'EndSubzoneEsecSubzone', 'Special']
}

# Inizializzazione dell'app Dash
app = Dash(__name__)
app.title = "Sankey 2-colonne dinamico"

app.layout = html.Div([
    html.H3("Sankey Diagram dinamico a 2 colonne"),
    html.Div([
        html.H5("Primo tocco"),
        dcc.Dropdown(id='fond1', options=[{'label': f, 'value': f} for f in fond_characteristics.keys()], value='S'),
        dcc.Dropdown(id='car1')
    ]),
    html.Div([
        html.H5("Secondo tocco"),
        dcc.Dropdown(id='fond2', options=[{'label': f, 'value': f} for f in fond_characteristics.keys()], value='R'),
        dcc.Dropdown(id='car2')
    ]),
    html.Div(id='debug-info', style={'margin': '20px', 'padding': '10px', 'border': '1px solid #ccc'}),
    dcc.Graph(id='sankey-graph')
])

# Callback per aggiornare le opzioni del primo dropdown caratteristiche
@app.callback(
    Output('car1', 'options'),
    Output('car1', 'value'),
    Input('fond1', 'value')
)
def update_car1(fond1):
    if fond1 is None:
        return [], None
    opts = fond_characteristics.get(fond1, [])
    return [{'label': c, 'value': c} for c in opts], opts[0] if opts else None

# Callback per aggiornare le opzioni del secondo dropdown caratteristiche
@app.callback(
    Output('car2', 'options'),
    Output('car2', 'value'),
    Input('fond2', 'value')
)
def update_car2(fond2):
    if fond2 is None:
        return [], None
    opts = fond_characteristics.get(fond2, [])
    return [{'label': c, 'value': c} for c in opts], opts[0] if opts else None

# Callback principale per creare il diagramma Sankey
@app.callback(
    Output('sankey-graph', 'figure'),
    Output('debug-info', 'children'),
    Input('fond1', 'value'),
    Input('car1', 'value'),
    Input('fond2', 'value'),
    Input('car2', 'value')
)
def update_graph(f1, c1, f2, c2):
    debug_messages = []

    # Controllo se il dataframe esiste
    if 'df' not in globals() or df.empty:
        debug_messages.append("❌ ERRORE: Dataframe non caricato o vuoto!")
        debug_messages.append("Assicurati di aver eseguito correttamente la query SQL")
        return go.Figure(), html.Div([
            html.H4("Debug Info:", style={'color': 'red'}),
            html.Ul([html.Li(msg) for msg in debug_messages])
        ])

    debug_messages.append(f"✅ Dataframe caricato con {len(df)} righe")

    # Controlli di validità degli input
    if not all([f1, c1, f2, c2]):
        debug_messages.append("⚠️ Alcuni parametri non sono selezionati")
        return go.Figure(), html.Div([
            html.H4("Debug Info:"),
            html.Ul([html.Li(msg) for msg in debug_messages])
        ])

    # Verifica se esiste una regola
    if f2 not in regole_abs or f1 not in regole_abs[f2]:
        debug_messages.append(f"❌ Nessuna regola definita da {f1} a {f2}")
        return go.Figure(), html.Div(debug_messages)

    try:
        # Filtra i dataframe
        df1 = df[df['Foundamental'] == f1].copy()
        df2 = df[df['Foundamental'] == f2].copy()

        debug_messages.append(f"📊 Trovati {len(df1)} record per {f1} e {len(df2)} record per {f2}")

        # Verifica che le caratteristiche esistano nei dati
        if c1 not in df1.columns:
            debug_messages.append(f"❌ Caratteristica '{c1}' non trovata nei dati per {f1}")
            return go.Figure(), html.Div(debug_messages)

        if c2 not in df2.columns:
            debug_messages.append(f"❌ Caratteristica '{c2}' non trovata nei dati per {f2}")
            return go.Figure(), html.Div(debug_messages)

        join_pairs = []
        matches_found = 0
        games_processed = set()
        debug_sample_size = 10  # Per non stampare troppo
        debug_counter = 0

        # Info sui dati prima del loop
        debug_messages.append(f"🎯 Unique values in {c1} for {f1}: {df1[c1].nunique()} (sample: {df1[c1].dropna().unique()[:5].tolist()})")
        debug_messages.append(f"🎯 Unique values in {c2} for {f2}: {df2[c2].nunique()} (sample: {df2[c2].dropna().unique()[:5].tolist()})")

        # Usa tutto il dataframe - nessun limite per i dati reali
        df1_to_process = df1

        for idx, row in df1_to_process.iterrows():
            prev_abs = row['AbsNumeroPossesso']
            game_id = row['GameID']
            team_id = row['whichTeamID']

            try:
                expected_abs = regole_abs[f2][f1](prev_abs)
            except Exception as e:
                debug_messages.append(f"❌ Errore nell'applicazione della regola: {e}")
                continue

            # Debug dettagliato per i primi casi
            if debug_counter < debug_sample_size:
                debug_messages.append(f"🔍 Sample {debug_counter}: GameID={game_id}, Team={team_id}, {f1} AbsPos={prev_abs} → {f2} expected={expected_abs}")

            # Trova i match - PROVA PRIMA SENZA IL FILTRO SQUADRA
            matches_without_team = df2[
                (df2['GameID'] == game_id) &
                (df2['AbsNumeroPossesso'] == expected_abs)
            ]

            matches = df2[
                (df2['GameID'] == game_id) &
                (df2['AbsNumeroPossesso'] == expected_abs) &
                (df2['whichTeamID'] == team_id)
            ]

            if debug_counter < debug_sample_size:
                debug_messages.append(f"   → Matches without team filter: {len(matches_without_team)}")
                debug_messages.append(f"   → Matches with team filter: {len(matches)}")
                if not matches_without_team.empty:
                    unique_teams = matches_without_team['whichTeamID'].unique()
                    debug_messages.append(f"   → Teams in matches: {unique_teams.tolist()}")

            debug_counter += 1

            if not matches.empty:
                matches_found += len(matches)
                games_processed.add(game_id)

                # Prendi il primo match
                match = matches.iloc[0]  # Solo il primo per semplicità
                val1 = row.get(c1)
                val2 = match.get(c2)

                if debug_counter <= debug_sample_size:
                    debug_messages.append(f"   → Found pair: '{val1}' → '{val2}' (types: {type(val1)}, {type(val2)})")

                # Controlla valori validi
                if pd.notna(val1) and pd.notna(val2) and str(val1) != '' and str(val2) != '' and str(val1) != 'nan' and str(val2) != 'nan':
                    join_pairs.append((str(val1), str(val2)))
                    if debug_counter <= debug_sample_size:
                        debug_messages.append(f"   → ✅ Pair added to join_pairs")
                elif debug_counter <= debug_sample_size:
                    debug_messages.append(f"   → ❌ Skipped pair (invalid values): val1_valid={pd.notna(val1)}, val2_valid={pd.notna(val2)}")

        debug_messages.append(f"🔗 Trovati {matches_found} match totali in {len(games_processed)} partite")
        debug_messages.append(f"📈 Creati {len(join_pairs)} collegamenti validi")

        if not join_pairs:
            debug_messages.append("❌ Nessun collegamento valido trovato")
            return go.Figure(), html.Div(debug_messages)

        # Crea il dataframe dei collegamenti
        df_pairs = pd.DataFrame(join_pairs, columns=['source', 'target'])
        df_links = df_pairs.groupby(['source', 'target']).size().reset_index(name='value')

        debug_messages.append(f"📊 Collegamenti unici: {len(df_links)}")

        # Crea le etichette SEPARATE per source e target con conteggi
        source_counts = df_links.groupby('source')['value'].sum()
        target_counts = df_links.groupby('target')['value'].sum()

        source_labels = [f"{f1}: {label}" for label in df_links['source'].unique()]
        target_labels = [f"{f2}: {label}" for label in df_links['target'].unique()]
        all_labels = source_labels + target_labels

        # Crea custom hover text per i nodi
        source_hovertexts = [f"{f1}: {label}<br>Total connections: {source_counts[label]}"
                           for label in df_links['source'].unique()]
        target_hovertexts = [f"{f2}: {label}<br>Total connections: {target_counts[label]}"
                           for label in df_links['target'].unique()]
        all_hovertexts = source_hovertexts + target_hovertexts

        # Mappa i valori originali agli indici delle etichette
        source_map = {val: idx for idx, val in enumerate(df_links['source'].unique())}
        target_map = {val: idx + len(source_labels) for idx, val in enumerate(df_links['target'].unique())}

        debug_messages.append(f"📊 Source nodes: {len(source_labels)}, Target nodes: {len(target_labels)}")

        # Crea custom hover text per i link
        link_hovertexts = [f"{row['source']} → {row['target']}<br>Count: {row['value']}"
                          for _, row in df_links.iterrows()]

        # Crea il Sankey con indici corretti e hover personalizzato
        fig = go.Figure(data=[go.Sankey(
            node=dict(
                label=all_labels,
                pad=15,
                thickness=20,
                line=dict(color="black", width=0.5),
                color=["lightblue"] * len(source_labels) + ["lightcoral"] * len(target_labels),
                hovertemplate='%{customdata}<extra></extra>',
                customdata=all_hovertexts
            ),
            link=dict(
                source=[source_map[s] for s in df_links['source']],
                target=[target_map[t] for t in df_links['target']],
                value=df_links['value'].tolist(),
                hovertemplate='%{customdata}<extra></extra>',
                customdata=link_hovertexts
            )
        )])

        fig.update_layout(
            title_text=f"{f1} ({c1}) → {f2} ({c2})",
            font_size=12,
            height=600
        )

        debug_messages.append("✅ Sankey diagram creato con successo!")

        return fig, html.Div([
            html.H4("Debug Info:"),
            html.Ul([html.Li(msg) for msg in debug_messages])
        ])

    except Exception as e:
        debug_messages.append(f"❌ Errore generale: {str(e)}")
        return go.Figure(), html.Div([
            html.H4("Debug Info:"),
            html.Ul([html.Li(msg) for msg in debug_messages])
        ])

# Avvia l'app
if __name__ == '__main__':
    app.run(debug=True, port=8052)
