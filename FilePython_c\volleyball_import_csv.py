#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Volleyball Import CSV - Programma per importare dati dei giocatori da un file CSV nel database
"""

import os
import pandas as pd
from pathlib import Path
from sqlalchemy import create_engine

# Directory per i dati
DATA_DIR = Path(os.path.expanduser("~")) / "Documents" / "ModenaVolley" / "VolleyballScraper" / "data"
DATA_DIR.mkdir(parents=True, exist_ok=True)

def import_player_data_from_csv(csv_path):
    """
    Importa i dati dei giocatori da un file CSV nel database.
    
    Args:
        csv_path (str): Percorso del file CSV
        
    Returns:
        bool: True se l'importazione è riuscita, False altrimenti
    """
    try:
        # Crea un engine per poter usare pandas
        engine = create_engine('postgresql+psycopg://postgres:AcquaLevissima1@localhost:5432/db_modena')
        
        # Leggi il file CSV
        print(f"Leggendo il file CSV: {csv_path}")
        df_csv = pd.read_csv(csv_path)
        
        # Mostra le prime righe del CSV
        print("\nPrime righe del CSV:")
        print(df_csv.head())
        
        # Mostra le colonne del CSV
        print("\nColonne del CSV:")
        print(df_csv.columns.tolist())
        
        # Leggi la tabella players_latest dal database
        print("\nLeggendo la tabella players_latest dal database...")
        df_db = pd.read_sql_table('players_latest', engine)
        
        # Mostra le colonne del database
        print("\nColonne della tabella players_latest:")
        print(df_db.columns.tolist())
        
        # Verifica che le colonne necessarie siano presenti nel CSV
        required_columns = ["Nome", "Cognome"]
        for col in required_columns:
            if col not in df_csv.columns:
                print(f"ERRORE: La colonna '{col}' non è presente nel CSV")
                return False
        
        # Contatore per le righe aggiornate
        updated_rows = 0
        
        # Itera sulle righe del CSV
        for index, row in df_csv.iterrows():
            nome = row["Nome"]
            cognome = row["Cognome"]
            
            # Cerca il giocatore nel database
            player_mask = (df_db["Nome"] == nome) & (df_db["Cognome"] == cognome)
            
            if player_mask.any():
                # Giocatore trovato, aggiorna i dati
                player_index = df_db[player_mask].index[0]
                
                # Colonne da aggiornare
                update_columns = [
                    "Nazionalità", "DataNascita", "Altezza", "Peso", 
                    "Schiacciata", "Muro", "ManoDominante"
                ]
                
                # Aggiorna solo le colonne presenti nel CSV
                for col in update_columns:
                    if col in df_csv.columns and not pd.isna(row[col]):
                        df_db.at[player_index, col] = row[col]
                
                updated_rows += 1
                print(f"Aggiornato giocatore: {nome} {cognome}")
            else:
                print(f"Giocatore non trovato nel database: {nome} {cognome}")
        
        # Salva le modifiche nel database
        print(f"\nAggiornate {updated_rows} righe. Salvando le modifiche nel database...")
        df_db.to_sql('players_latest', engine, if_exists='replace', index=False)
        
        # Verifica che i dati siano stati salvati correttamente
        df_check = pd.read_sql_query("SELECT * FROM players_latest", engine)
        print(f"Numero di righe nel database: {len(df_check)}")
        print(f"Numero di valori non nulli in Nazionalità: {df_check['Nazionalità'].count()}")
        print(f"Numero di valori non nulli in Altezza: {df_check['Altezza'].count()}")
        
        print("\nImportazione completata con successo!")
        return True
        
    except Exception as e:
        print(f"Errore durante l'importazione: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def create_template_csv():
    """
    Crea un file CSV template con le colonne necessarie.
    
    Returns:
        str: Percorso del file CSV creato
    """
    try:
        # Crea un engine per poter usare pandas
        engine = create_engine('postgresql+psycopg://postgres:AcquaLevissima1@localhost:5432/db_modena')
        
        # Leggi la tabella players_latest dal database
        print("Leggendo la tabella players_latest dal database...")
        df_db = pd.read_sql_query("""
        SELECT * FROM players_latest 
        WHERE "Nazionalità" IS NULL 
        AND "DataNascita" IS NULL 
        AND "Altezza" IS NULL 
        AND "Peso" IS NULL 
        AND "Schiacciata" IS NULL 
        AND "Muro" IS NULL 
        AND "ManoDominante" IS NULL
        """, engine)
        
        # Crea un template con solo le colonne necessarie
        template_columns = [
            "Nome", "Cognome", "Nazionalità", "DataNascita", "Altezza", "Peso", 
            "Schiacciata", "Muro", "ManoDominante"
        ]
        
        # Crea un nuovo dataframe con solo le colonne necessarie
        df_template = df_db[template_columns].copy()
        
        # Salva il template come CSV
        template_path = DATA_DIR / "players_template.csv"
        df_template.to_csv(template_path, index=False)
        
        print(f"Template CSV creato con successo: {template_path}")
        print(f"Numero di giocatori nel template: {len(df_template)}")
        print("\nCompila il file CSV con i dati dei giocatori e poi importalo con:")
        print(f"python {__file__} --import {template_path}")
        
        return str(template_path)
        
    except Exception as e:
        print(f"Errore durante la creazione del template: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """
    Funzione principale per l'esecuzione da riga di comando.
    """
    import argparse
    
    # Crea un parser per gli argomenti da riga di comando
    parser = argparse.ArgumentParser(description="Volleyball Import CSV - Importa dati dei giocatori da un file CSV nel database")
    parser.add_argument("--template", action="store_true", help="Crea un file CSV template")
    parser.add_argument("--import", dest="import_file", help="Importa dati da un file CSV")
    args = parser.parse_args()
    
    print("Volleyball Import CSV - Importa dati dei giocatori da un file CSV nel database")
    print("-"*80)
    
    if args.template:
        create_template_csv()
    elif args.import_file:
        import_player_data_from_csv(args.import_file)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
