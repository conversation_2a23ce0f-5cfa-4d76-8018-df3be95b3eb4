<p align="center">
  <img src="https://github.com/shukkkur/VolleyVision/blob/280fed79d290c1cf6d53c869fa60355eeb04d148/assets/vv_logo.png" width=200>
</p>

<h1 align="center">
  👁️VolleyVision👁️
</h1>


<p align='center'>
  <a href="https://github.com/shukkkur/VolleyVision/forks?include=active%2Carchived%2Cinactive%2Cnetwork&page=1&period=2y&sort_by=stargazer_counts"><img src="https://img.shields.io/github/forks/shukkkur/VolleyVision.svg"></a>
  <a href="https://github.com/shukkkur/VolleyVision/stargazers"><img src="https://img.shields.io/github/stars/shukkkur/VolleyVision.svg"></a>
  <a href="https://github.com/shukkkur/VolleyVision/watchers"><img src="https://img.shields.io/github/watchers/shukkkur/VolleyVision.svg"></a>
 
  <br>
  <a href=""><img src="https://img.shields.io/github/last-commit/shukkkur/VolleyVision.svg"></a>
  <a href="https://creativecommons.org/licenses/by-nc-nd/4.0/"><img src="https://img.shields.io/badge/License-CC%20BY--NC--ND%204.0-lightgrey.svg"></a>
  <img src="https://hits.sh/github.com/shukkkur/VolleyVision.svg"/>
  
  <br>
  <a href="https://docs.google.com/presentation/d/1lcpJRam7ZxT-mF9wQBrxsambamZbcPTc/edit?usp=sharing&ouid=112155718847046590465&rtpof=true&sd=true"><img src="https://img.shields.io/badge/Presentation-Slides-B762C1"></a>
  <a href="https://colab.research.google.com/drive/1X16GNjksEfwVL1090bj3CYHGo772fG6H?usp=sharing"><img src="https://colab.research.google.com/assets/colab-badge.svg"></a>
  

  <br>
  <a href="https://ucentralasia.org/home"><code>University of Central Asia</a>⛰️</code>
  
</p>


<h3>🧪 Example usage</h3>

Volleyball  | Detection
:-------------------------:|:-------------------------:
<a href="https://github.com/shukkkur/VolleyVision/tree/main/Stage%20I%20-%20Volleyball/yV7-tiny"><img src="https://github.com/shukkkur/VolleyVision/blob/88474342fa4330ce268668986d9f5061d7ee8f6a/assets/y7Detect_volleyball15.gif" width="385" height="250"></a> | <a href="https://universe.roboflow.com/shukur-sabzaliev-bh7pq/volleyball-tracking/model/"><img src="https://github.com/shukkkur/VolleyVision/blob/eb639742363fb5564d6de4c3b1bf3da808162aa9/assets/rf_backview.gif" width="385" height="250"></a>
<strong>Action Recognition</strong> | <strong>Event Detection</strong>
<a href="https://github.com/shukkkur/VolleyVision/tree/main/Stage%20II%20-%20Players%20%26%20Actions/actions/yV8_medium"><img src="https://github.com/shukkkur/VolleyVision/blob/1c6c180c445a8be413defac520899e411c07f043/assets/actions.gif" width="385" height="250"></a> | <a href="https://github.com/shukkkur/VolleyVision/tree/main/Stage%20II%20-%20Players%20%26%20Actions#action-recognition--temporal-filtering"><img src="https://github.com/shukkkur/VolleyVision/blob/d1dabf0d17ff72c40ee2c1f30f80bd1857e661fd/assets/sliding_window.gif" width="385" height="250"></a>
<strong>Player Detection</strong> | <strong>Court Detection</strong>
<a href="https://github.com/shukkkur/VolleyVision/tree/main/Stage%20II%20-%20Players%20%26%20Actions/players/yV8_medium"><img src="https://github.com/shukkkur/VolleyVision/blob/f6981cd75cb8131c85b9fb9ec99a12d311f49239/assets/players_men.gif" width="385" height="250"></a> | <a href="https://universe.roboflow.com/shukur-sabzaliev-bh7pq/court-segmented/model/1"><img src="https://github.com/shukkkur/VolleyVision/blob/c0cab9585a9eb195d96d836f1243c97b20c80025/Stage%20III%20-%20Court%20Detection/assets/court.gif" width="385" height="250"></a>

<h3>🎯 Objectives</h3>
<p>
  ✅ Learn and apply popular CV techniques to volleyball data   <br>
  ✅ Popularize volleyball in the field of AI                   <br>
  ✅ Create volleyball datasets                                 <br>
  ✅ Contribute to open-source community                        <br>
  ✅ Receive a "thank you" message from anyone who benefited from the project. <a href="https://github.com/volleyfreak">@volleyfreak ❤️</a>
</p>


<h3>📝 About</h3>


<p><strong>November 7, 2022</strong> | The result of my project should be a web application, that takes a  volleyball video  and is able to detect and track the ball, players, the court and provides game statistics.</p>
<p><strong>May 15, 2023</strong> | hahahaha, today is the Diploma Project submission day. Well, there is a simple <a href="https://github.com/shukkkur/volleyvision_app">web app</a>, I couldn't deploy it though, and now I am too burned out and busy to get back to this project. hahah I also planned to provide game statistics from the footage, that also did not go as planned. But, overall I am happy)))</p>


<details><summary><h3>🏐 Volleyball Detection & Tracking</h3></summary>

<a href="https://universe.roboflow.com/shukur-sabzaliev1/volleyball_v2/model/2"><img src="https://app.roboflow.com/images/try-model-badge.svg"></img></a>
<a href="https://universe.roboflow.com/shukur-sabzaliev1/volleyball_v2"><img src="https://app.roboflow.com/images/download-dataset-badge.svg"></img></a>
<a href="https://github.com/shukkkur/VolleyVision/tree/main/Stage%20I%20-%20Volleyball/yV7-tiny/weights"><img src="https://img.shields.io/badge/Download-YOLOV7--TINY%20Weights-red" alt="yV8 Weights"></a>
<a href="https://wandb.ai/volleyvision/YOLOR/runs/2u30vyzp/overview?workspace=user-shukkkur"><img src="https://raw.githubusercontent.com/wandb/assets/main/wandb-github-badge-gradient.svg" alt="WandB Badge"></a>

<!--   <strong>February 10, 2023 </strong> -->
<!--    <i>Closing the first stage moderetly satisfied</i>.  -->
<!--   <br> -->

<p>Two trained models: <a href="https://blog.roboflow.com/new-and-improved-roboflow-train/">RoboFlow</a> (<a href="https://docs.roboflow.com/train">AutoML training</a>) and <a href="https://github.com/WongKinYiu/yolov7">yoloV7-tiny</a> (local training). Both were trained on my newly created dataset comprised of <strong>25k images</strong>.  As for the tracker, <a href="https://github.com/foolwood/DaSiamRPN">DaSiamRPN</a> (<a href="https://docs.opencv.org/4.x/de/d93/classcv_1_1TrackerDaSiamRPN.html">cv2</a>) was used.

|              |   **mAP(50)**   | **precision** |  **recall**  |
|:------------:|:-----------:|:-------------:|:------------:|
| yoloV7-tiny  |    74.1%    |     86.4%     |    65.8%     |
|  RoboFlow    |    92.3%    |     94.7%     |    86.1%     |

  <strong>RoboFlow</strong> model is more accurate and works better on official matches, rather than yolov7 model. However, it requires longer time for inference. <strong>YoloV7-tiny</strong> is capable of real-time inference, even though it is less accurate than RoboFlow model, it is still a good and fast choice for larger volleyballs.</p>

<h3>🏃‍♂️ How to Run</h3>
<ul>
  <li>
  <p>Check out the <a href="https://github.com/shukkkur/VolleyVision/tree/main/Stage%20I%20-%20Volleyball#readme">Stage I - Volleyball/README.md</a></p>
  </li>
</ul>
</details>

<details><summary><h3>🏃 Player Detection & 🤾 Action Recognition</h3></summary>
    <h4>Action Recognition</h4>
    <a href="https://universe.roboflow.com/shukur-sabzaliev-42xvj/volleyball-actions"><img src="https://app.roboflow.com/images/download-dataset-badge.svg"></img></a>
    <a href="https://universe.roboflow.com/shukur-sabzaliev-42xvj/volleyball-actions/model/3"><img src="https://app.roboflow.com/images/try-model-badge.svg"></img></a>
    <a href="https://github.com/shukkkur/VolleyVision/tree/main/Stage%20II%20-%20Players%20%26%20Actions/actions/yV8_medium/"><img src="https://img.shields.io/badge/Download-YOLOV8M%20Weights-red" alt="yV8 Weights"></a>
    <a href="https://wandb.ai/volleyvision/YOLOv8/runs/28bs84bi/overview?workspace=user-shukkkur"><img src="https://raw.githubusercontent.com/wandb/assets/main/wandb-github-badge-gradient.svg" alt="WandB Badge"></a>
  <p>
    In this stage, I focused on recognizing volleyball actions from the images. A comprehensive volleyball actions dataset was created, comprising <strong>14k images</strong>. I used <a href="https://github.com/ultralytics/ultralytics/tree/0cb87f7dd340a2611148fbf2a0af59b544bd7b1b#models">YOLOv8m</a> to train the action recognition model on this dataset.
  </p>  

|            | **mAP50(B)**   | **precision**   | **recall**      |
|:----------:|:----------:|:----------------:|:----------------:|
| yolov8m    | 92.31%     | 92.38%         | 89.4%         |
| RoboFlow   |  83.7%     | 78.5%      | 82.3%         |

<p>The results were highly promising, as shown by the performance metrics.</p>

<h4>Players Detection</h4>
    <a href="https://universe.roboflow.com/shukur-sabzaliev-42xvj/players-dataset"><img src="https://app.roboflow.com/images/download-dataset-badge.svg"></img></a>
    <a href="https://universe.roboflow.com/shukur-sabzaliev-42xvj/players-dataset/model/"><img src="https://app.roboflow.com/images/try-model-badge.svg"></img></a>
    <a href="https://github.com/shukkkur/VolleyVision/tree/main/Stage%20II%20-%20Players%20%26%20Actions/actions/yV8_medium"><img src="https://img.shields.io/badge/Download-YOLOV8M%20Weights-red" alt="yV8 Weights"></a>
    <p>
    In addition, I have also trained a YOLOv8m model on a dataset of volleyball players, achieving a high level of accuracy in detecting players in each frame:
    </p>  
    
|            | **mAP50(B)**   | **precision**   | **recall**      |
|:----------:|:----------:|:----------------:|:----------------:|
| yolov8m    | 97.2%    | 94.2%        | 94%         |
| RoboFlow   |  97.2%     | 96.7%      | 91.7%         |

<p>However, it's worth noting that the yolov8 model, being able to perform in real-time, may occasionally misidentify coaches as players, whereas the RoboFlow model will have a higher rate of <strong>false negatives</strong>, resulting in missed player detections.</p>

<h3>🏃‍♂️ How to Run</h3>
<ul>
  <li>
  <p>Check out the <a href="https://github.com/shukkkur/VolleyVision/tree/main/Stage%20II%20-%20Players%20%26%20Actions#readme">Stage II - Players & Actions/README.md</a></p>
  </li>
</ul>
</details>


<details><summary><h3>🏟️ Court Tracking</h3></summary>
<a href="https://universe.roboflow.com/shukur-sabzaliev-bh7pq/court-segmented"><img src="https://app.roboflow.com/images/download-dataset-badge.svg"></img></a>
<a href="https://universe.roboflow.com/shukur-sabzaliev-bh7pq/court-segmented/model/"><img src="https://app.roboflow.com/images/try-model-badge.svg"></img></a>

<p>In this stage, I trained a simple <strong>Semantic Segmentation</strong> model on RoboFlow to find and outline the court. </p>

|      | mIoU  |
|------|-------|
| RoboFlow     | 97.2% |


<p>Keep in mind, this was a quick and fun experiment due to an upcoming thesis deadline, and it may not work perfectly every time. </p>

<img src="https://github.com/shukkkur/VolleyVision/blob/86a79be02caea17bfbbbaee44366a9cfc5f31f42/Stage%20III%20-%20Court%20Detection/assets/collage_court.png" width="600">

<p>The collage image above should give you the idea about the steps I took to detect the court: 
  <ol>
    <li>Get the segmentation mask from the model</li>
    <li>Find Contours - <code>cv2.findContours</code></li>
    <li>Approximate a polygonal curve - <code>cv2.approxPolyDP</code></li>
  </ol>
</p>

<h3>🏃‍♂️ How to Run</h3>
<ul>
  <li>
  <p>Check out the <a href="https://github.com/shukkkur/VolleyVision/tree/main/Stage%20III%20-%20Court%20Detection#readme">Stage III - Court Detection/README.md</a></p>
  </li>
</ul>
</details>

<details><summary><h3>💾 Datasets</h3></summary>
  
| Volleyball | Actions | Players | Spatiotemporal Activity |
|------|---------|---------|-------|
| <a href="https://universe.roboflow.com/shukur-sabzaliev-bh7pq/volleyball-tracking"><img src="https://github.com/shukkkur/VolleyVision/blob/6ac8230e48de95a8edb3a1c4793657ddb06f1409/README_files/volley-collage.jpg" width="600"></a> | <a href="https://universe.roboflow.com/shukur-sabzaliev-42xvj/volleyball-actions"><img src="https://github.com/shukkkur/VolleyVision/blob/f59e9feba6946d6ce7706b8c6b27081461d0401e/assets/actions_collage.png" width="600"></a> | <a href="https://universe.roboflow.com/shukur-sabzaliev-42xvj/players-dataset"><img src="https://github.com/shukkkur/VolleyVision/blob/f59e9feba6946d6ce7706b8c6b27081461d0401e/assets/players_collage.png" width="600"></a> | <img src="https://github.com/shukkkur/VolleyVision/blob/10da824026eafd787f85c0a4d9e88d6259c31a72/assets/3d_activity.png" width="465"> |

  
<ol>
  <li>
    Volleyball (1 class: volleyball)
    <ul>
      <li><s>Source Images - <a href="https://universe.roboflow.com/volleyvision/volleyball-tracking/dataset/9">25k_version</a></s></li>
      <li>Duplicates Removed - <a href="https://universe.roboflow.com/shukur-sabzaliev1/volleyball_v2">19k_version</a></li>
    </ul>
  </li>
  
  <li>
    <a href="https://universe.roboflow.com/shukur-sabzaliev-42xvj/volleyball-actions">Actions</a> (5 classes: block, defense, serve, set, spike)
    <ul>
      <li>Source Images - <a href="https://universe.roboflow.com/shukur-sabzaliev-42xvj/volleyball-actions/dataset/5">original</a></li>
      <li>Resized (640x640) - <a href="https://universe.roboflow.com/shukur-sabzaliev-42xvj/volleyball-actions/dataset/3">resized_640</a></li>
      <li>Resized (1024x1024) - <a href="https://universe.roboflow.com/shukur-sabzaliev-42xvj/volleyball-actions/dataset/4">stretched_dataset</a></li>
    </ul>
  </li>
  
  <li>
    <a href="https://universe.roboflow.com/shukur-sabzaliev-42xvj/players-dataset">Players</a> (1 class: player)
    <ul>
      <li>Source Images - <a href="https://universe.roboflow.com/shukur-sabzaliev-42xvj/players-dataset/dataset/1">original</a></li>
      <li>Resized (640x640) - <a href="https://universe.roboflow.com/shukur-sabzaliev-42xvj/players-dataset/dataset/4">resized</a></li>
      <li>Augmented Resized (640x640) - <a href="https://universe.roboflow.com/shukur-sabzaliev-42xvj/players-dataset/dataset/2">aug_res</a></li>
    </ul>
  </li>
  
  <li>
    <a href="https://www.tugraz.at/index.php?id=17751">Spatiotemporal Volleyball Activtiy Dataset</a> (7 classes: defense-move, attack, block, reception, service, setting, stand).
    <ul>
      <li>Source Images - <a href="https://universe.roboflow.com/shukur-sabzaliev-zc3en/volleyball-activity-dataset/dataset/3">original</a></li>
      <li>Resized (640x640) - <a href="https://universe.roboflow.com/shukur-sabzaliev-zc3en/volleyball-activity-dataset/dataset/4">resized_640</a></li>
    </ul>
    <p>This dataset belongs to Institute of Computer Graphics and Vision, I downloaded the annotations and the videos from the website, did all the preprocessing and uploaded it to RoboFlow, to make it more accessible and convinient for others to download. Please note that this dataset is <strong>video dataset</strong> but RoboFlow splitted it randomly into train/test/split. Therefore, after downloading the images and annotations, they all should be combined and then split sequentially.</p>
  </li>
  
  <li>
    <a href="https://deeperaction.github.io/datasets/multisports.html">A Multi-Person Video Dataset of Spatio-Temporally Localized Sports Actions</a> (12 classes: first hit pass, defend, no offensive attack...).
    <ul>
      <li>HuggingFace - <a href="https://huggingface.co/datasets/MCG-NJU/MultiSports">dataset</a></li>
    </ul>
    <p>MultiSports is a multi-person dataset of spatio-temporal localized sports actions. Please refer to <a href="https://arxiv.org/abs/2105.07404">this paper</a> for more details and to <a href="https://github.com/MCG-NJU/MultiSports/">this repository</a> for evaluation. </p>
  </li>

  <li>
    Court Segmentation
    <ul>
      <li>Source Images - <a href="https://universe.roboflow.com/shukur-sabzaliev-bh7pq/court-segmented">original</a></li>
    </ul>
  </li>
  
</ol>
</details>
  
<details><summary><h3>📝 LICENSE</h3></summary>
<p>This project is licensed under the <strong>Creative Commons Attribution-NonCommercial-NoDerivatives (CC BY-NC-ND)</strong> license.</p>
<p>This license allows you to:</p>
<ul>
    <li><strong>Share</strong> — copy and redistribute the material in any medium or format</li>
    <li><strong>Adapt</strong> — remix, transform, and build upon the material</li>
</ul>
<p>Under the following terms:</p>
<ul>
    <li><strong>Attribution</strong> — You must give appropriate credit, provide a link to the license, and indicate if changes were made. You may do so in any reasonable manner, but not in any way that suggests the licensor endorses you or your use.</li>
    <li><strong>Non-Commercial</strong> — You may not use the material for commercial purposes.</li>
    <li><strong>No Derivatives</strong> — If you remix, transform, or build upon the material, you may not distribute the modified material.</li>
</ul>
<p>See the full license text for more details.</p>
<p>
<a href="https://creativecommons.org/licenses/by-nc-nd/4.0/legalcode">Read the full license text</a>
</p>
</details>

<details><summary><h3>💖 Donate</h3></summary>
<p>If you find my work useful or interesting, I would greatly appreciate your support on <strong><a href="https://ko-fi.com/shukkkur">Ko-fi</a></strong>. Alternatively, you can simply reach out to me and say 'thank you,' which I equally value!)</p>
</details>

<details><summary><h3>👨‍💻 Further Development</h3></summary>
<ul>
  <li>Develop and deploy a web app</li>
  <li>Unify all three stages into one coherent and easy to run script</li>
  <li>Research and apply better Court Detection methods</li>
  <li>Train YoloV8 on <a href="https://universe.roboflow.com/shukur-sabzaliev-bh7pq/volleyball-tracking">Volleyball Dataset</a></li>
  <li>Train 3D Model on <a href="https://universe.roboflow.com/shukur-sabzaliev-zc3en/volleyball-activity-dataset">Spatiotemporal Activity Dataset</a></li>
  <li>Extract and transport volleyball actions from <a href="https://huggingface.co/datasets/MCG-NJU/MultiSports">MultiSports</a> to RoboFlow</li>
  <li>Combine <strong>Players Detection</strong> with <strong>Actions Recognition</strong> to generate personalized statistics</li>
  <li>Combine <strong>Volleyball Tracking</strong> with <strong>Court Detection</strong> for <strong>smart ball in/out referee system</strong></li>
  <li>Annotate more side-view (official recordings) data for <a href="https://universe.roboflow.com/shukur-sabzaliev-42xvj/volleyball-actions">Action Recognition Dataset</a></li>
  <li>Annotate more data for <a href="https://universe.roboflow.com/shukur-sabzaliev-bh7pq/court-segmented">Court Segmentation Dataset</a></li>
  <li>Annotate more data for <a href="https://universe.roboflow.com/shukur-sabzaliev-42xvj/players-dataset">Players Detection Dataset</a></li>
</ul>
</details>

<details><summary><h3>🐛 Current Drawbacks</h3></summary>
<ul>
  <li>RoboFlow <a href="https://universe.roboflow.com/shukur-sabzaliev-bh7pq/volleyball-tracking/model/18">Volleyball Detection Model</a> is slow, due to the API being called on every single frame</li>
  <li>Court Detection is neither robust nor fast</li>
  <li>Action Recognition model is undertrained on <code>Defense</code> class</li>
  <li>Action Recogntiion model is undertrained on side-view data</li>
  <li>Players Detection occasionally detects coaches, referees and spectators as <code>player</code> class</li>
</ul>
</details>

<details><summary><h3>🫶 Contribute</h3></summary>

<p>Help me by annoating data, discussing potential improvements or writing a better documentation. Check out the <a href="https://github.com/shukkkur/VolleyVision/blob/main/CONTRIBUTING.md">CONTRIBUTING.md</a>. Contributing is easy and appreciated!</p>
</details>


<details><summary><h3>🙌 Acknowledgement</h3></summary>
  <ul>
      <p align="center">
        <img src="https://github.com/shukkkur/VolleyVision/blob/2bf4497171830d7b0c5279847da1d6364adfce75/assets/roboflow_logo.png" width="500">
      </p>
<li>
    This project wouldn't possible without amazing & free RoboFlow <a href="https://roboflow.com/annotate">annotation tools</a> , open-source <a href="https://universe.roboflow.com/">datasets</a>, quick & easy <a href="https://roboflow.com/deploy">deployement</a> and high-level <a href="https://blog.roboflow.com/">blog posts</a>. Special thanks to <a href="https://blog.roboflow.com/author/mohamed/">Mohamed Traore</a> for tirelessly approving my constant requests for data storage and model training credits</li>
    <li><a href="https://github.com/artkulak">Artyom Kulakov</a> - My supervisor. Sincerest gratitude that you foud time to guide and support me along the entire journey (starting from <a href="https://github.com/ArtLabss">ArtLabs</a>))))</li>
    <li><a href="https://ucentralasia.org/faculty-and-staff/dr-nurlan-shaidullaev">Nurlan Shaidullaev</a> - FYP course instructor. Thank you for your vigilence and constant reminders about the upcoming deadlines :D</li>
    <li><a href="https://ucentralasia.org/home">University of Central Asia</a> - my home for the past 5 years. Immense gratitude to AKDN.</li>
  </ul>
</details>


<h4>For any additional quesitons feel free to take part in <a href="https://github.com/shukkkur/VolleyVision/discussions">discussions</a> or open an <a href="https://github.com/shukkkur/VolleyVision/issues/new">issue</a>.</h4>

<a href="https://github.com/shukkkur/VolleyVision/discussions">
<img src="https://github.com/shukkkur/VolleyVision/blob/1d1836c3a7968cbcde4bcf5cfb5e8eaf4c16acfb/assets/header.png">
</a>

<!--
<table>
<tr>
<td> Status </td> <td> Response </td>
</tr>
<tr>
<td> 200 </td>
<td>

```python
from roboflow import Roboflow
rf = Roboflow(api_key="sparlyxRfGqxvrUwHldB")
project = rf.workspace().project("radardata")
model = project.version(1).model

# infer on a local image
print(model.predict("your_image.jpg", confidence=40, overlap=30).json())

# visualize your prediction
# model.predict("your_image.jpg", confidence=40, overlap=30).save("prediction.jpg")

# infer on an image hosted elsewhere
# print(model.predict("URL_OF_YOUR_IMAGE", hosted=True, confidence=40, overlap=30).json())
```
V Extra blank line below!

</td>
</tr>
<tr>
<td> 400 </td>
<td>

**Markdown** _here_. (Blank lines needed before and after!)

</td>
</tr>
</table>
-->

<!-- <img src="https://github.com/shukkkur/VolleyVision/blob/2e4ce97819f591573de99fcfe04ba0f0259dff9a/assets/rf_men_rally.gif" width="350" height="250"> | <img src="https://github.com/shukkkur/VolleyVision/blob/2e4ce97819f591573de99fcfe04ba0f0259dff9a/assets/rf_women_rally.gif" width="350" height="250"> -->

<!--   https://blog.roboflow.com/new-and-improved-roboflow-train/ -->

<!-- I was trying to train the standard <a href="https://github.com/WongKinYiu/yolov7#performance">yolov7</a>, however, with GPU memory being 4GB, I could only afford training with <code>batch_size=8 img-size=480</code>, which didn't yield best results. -->
