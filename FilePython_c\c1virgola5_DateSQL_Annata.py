import numpy as np 
import pandas as pd  #Realizzato con la versione di Pandas 2.2.3
import polars as pl
import os
import psycopg
from sqlalchemy import create_engine, text


conn = psycopg.connect(
    dbname="db_modena",           # database creato in pgAdmin4
    user="postgres",              # Il tuo nome utente PostgreSQL
    password="AcquaLevissima1",   # La password che hai scelto per 'postgres'
    host="localhost",             # 'localhost' se è sul tuo PC
    port=5432                     # La porta predefinita è 5432
)

cur = conn.cursor()


#Converto la colonna Annata in smallint. Non posso, perchè quando inserisco i dati per adesso mi arriva come stringa 2022/2023
'''
cur.execute("""
ALTER TABLE games
ALTER COLUMN "Annata" TYPE smallint
USING "Annata"::smallint;
""")
conn.commit()
'''



#Stampa tutte le righe in cui DateSQL è NULL
cur.execute("""SELECT * FROM games WHERE "DateSQL" IS NULL;""")
rows1 = cur.fetchall()
print("Righe con DateSQL NULL:")
for row in rows1:
    print(row)


#Stampa tutte le righe in cui DateSQL è prima del 2020 (o una data per cui prima non dovresti avere alcun dato, ad es prima del 2005 magari non abbiamo dati)
cur.execute("""SELECT * FROM games WHERE "DateSQL" < '2020-01-01';""")
rows2 = cur.fetchall()
print("Righe con DateSQL prima del 2020:")
for row in rows2:
    print(row)


#Stampa tutte le righe in cui DateSQL è dopo il giorno attuale
cur.execute("""SELECT * FROM games WHERE "DateSQL" > CURRENT_DATE;""")
rows3 = cur.fetchall()
print("Righe con DateSQL dopo il giorno attuale:")
for row in rows3:
    print(row)



#Se il mese è maggiore o uguale a 8, l'annata è Year, altrimenti è Year-1  (da agosto a dicembre l'annata coincide con l'anno, altrimenti è l'anno prima, ovvero a gennaio l'annata è l'anno prima)
#Quindi Annata sembra un anno, ma indica l'anno in cui è iniziata la stagione corrente (se la stagione è 2022/2023, Annata è 2022)
cur.execute("""
UPDATE games
SET "Annata" = 
    CASE 
        WHEN EXTRACT(MONTH FROM "DateSQL") >= 8 THEN EXTRACT(YEAR FROM "DateSQL")
        ELSE EXTRACT(YEAR FROM "DateSQL") - 1
    END;
""")
conn.commit()






