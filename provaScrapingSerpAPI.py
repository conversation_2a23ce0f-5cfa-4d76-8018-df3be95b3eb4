import requests
from bs4 import BeautifulSoup

def trova_url_volleybox_google(nome, cognome):
  url = f"https://google.serper.dev/search?q=site%3Avolleybox.net%2Fit+{nome}+{cognome}&gl=it&hl=it&apiKey=22bf2d85466323a809aec14d1ee765c259a29e68"

  payload = {}
  headers = {}

  response = requests.request("GET", url, headers=headers, data=payload)
  dizionario = response.text  #response.text è un JSON, quindi lo leggo come dizionario

  print(type(dizionario)) 
  
  lista_siti = dizionario["organic"]
  print(type(lista_siti))
  
  url_giusto = ""
  for sito in lista_siti:
    if "volleybox.net/it" in sito["link"] and sito["link"].endswith("/clubs"):
      url_giusto = sito["link"]
      break
  
  if url_giusto:
    return url_giusto
  else:
    print("Nessun URL trovato dalla ricerca Google")
    return None
  
  
  

            


name = "simone"
surname = "gian<PERSON><PERSON>"
result = trova_url_volleybox_google(name, surname)

if result:
    print(f"\nFinal result - Found URL: {result}")
else:
    print("\nFinal result: No URL found")
    
    
