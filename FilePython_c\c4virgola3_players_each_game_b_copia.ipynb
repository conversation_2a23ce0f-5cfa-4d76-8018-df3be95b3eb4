import numpy as np 
import pandas as pd  #Realizzato con la versione di Pandas 2.2.3
import os
import duckdb

import plotly.graph_objects as go
from dash import Dash, dcc, html, Input, Output


# Connessione a DuckDB
con = duckdb.connect('db_modena_1.duckdb')

# Installazione dell'estensione postgres se non è già installata
con.execute("INSTALL postgres")
con.execute("LOAD postgres")


# Estrazione della view players_each_game
con.execute("""
    CREATE OR REPLACE TABLE players_each_game_duckdb AS 
    SELECT * FROM postgres_scan('dbname=db_modena user=postgres password=AcquaLevissima1 host=localhost port=5432', 'public', 'players_each_game');
""")

# Conversione in DataFrame pandas
df_players = con.execute("SELECT * FROM players_each_game_duckdb").fetchdf()

# Visualizzazione delle prime righe
df_players

# Estrazione della view games_view
con.execute("""
    CREATE OR REPLACE TABLE games_view_duckdb AS 
    SELECT * FROM postgres_scan('dbname=db_modena user=postgres password=AcquaLevissima1 host=localhost port=5432', 'public', 'games_view');
""")

# Conversione in DataFrame pandas
df_games = con.execute("SELECT * FROM players_each_game_duckdb").fetchdf()

# Visualizzazione delle prime righe
df_games

# Estrazione della view teams
con.execute("""
    CREATE OR REPLACE TABLE teams_duckdb AS 
    SELECT * FROM postgres_scan('dbname=db_modena user=postgres password=AcquaLevissima1 host=localhost port=5432', 'public', 'teams');
""")

# Conversione in DataFrame pandas
df_teams = con.execute("SELECT * FROM teams_duckdb").fetchdf()

# Visualizzazione delle prime righe
df_teams

# Estrazione della view rilevations_libero_view
con.execute("""
    CREATE OR REPLACE TABLE rilevations_libero_view_duckdb AS 
    SELECT * FROM postgres_scan('dbname=db_modena user=postgres password=AcquaLevissima1 host=localhost port=5432', 'public', 'rilevations_libero_view');
""")

# Conversione in DataFrame pandas
df_rilevations = con.execute("SELECT * FROM rilevations_libero_view_duckdb").fetchdf()

# Visualizzazione delle prime righe
df_rilevations

con.execute("DROP VIEW IF EXISTS rilevations_libero_battute_view_duckdb")
con.execute("""
    CREATE OR REPLACE VIEW rilevations_libero_battute_view_duckdb AS
    SELECT * FROM postgres_scan('dbname=db_modena user=postgres password=AcquaLevissima1 host=localhost port=5432', 'public', 'rilevations_libero_battute_view')
    WHERE "Foundamental" = 'S';
""")



# Conversione in DataFrame pandas
#df_players_each_game_a = con.execute("SELECT * FROM players_each_game_a").fetchdf()

# Visualizzazione delle prime righe
#df_players_each_game_a



# Eliminiamo la tabella esistente
con.execute("DROP TABLE IF EXISTS battute_long")

# Tabella lunga
con.execute("""
CREATE TABLE battute_long AS
SELECT
  "GameID",
  "NumeroMaglia_ID" AS PlayerID,
  "Foundamental",
  "Type",
  "Eval",
  "StartZone",
  "EndZoneEsecZone",
  COUNT(*) AS "NumBattute"
FROM rilevations_libero_view_duckdb
WHERE "Foundamental" = 'S'
  AND "Type" IS NOT NULL 
  AND "Eval" IS NOT NULL
  AND "StartZone" IS NOT NULL 
  AND "EndZoneEsecZone" IS NOT NULL
GROUP BY ALL
""")

# Recupera le combinazioni più frequenti
df_battute_freq = con.execute("""
SELECT
  "Foundamental", "Type", "Eval", "StartZone", "EndZoneEsecZone",
  COUNT(*) AS freq
FROM battute_long
GROUP BY "Foundamental", "Type", "Eval", "StartZone", "EndZoneEsecZone"
ORDER BY freq DESC
LIMIT 1000
""").fetchdf()


# Approccio con pandas per il pivot
df_battute = con.execute("SELECT * FROM battute_long").fetchdf()

# Creiamo una colonna combinata
df_battute['combo'] = df_battute['Type'] + df_battute['Eval'] + df_battute['StartZone'].astype(str) + df_battute['EndZoneEsecZone'].astype(str)

# Pivot con pandas
df_battute_pivot_GameID_PlayerID = df_battute.pivot_table(
    index=['GameID', 'PlayerID'],
    columns='combo',
    values='NumBattute',
    aggfunc='sum',
    fill_value=0
).reset_index()

'''
# Se vuoi salvare il risultato del pivot in DuckDB
# Convertiamo le colonne in un formato più gestibile
df_pivot.columns = [str(col).replace(' ', '_').replace('(', '').replace(')', '').replace(',', '_').replace("'", "") 
                   for col in df_pivot.columns]

# Salviamo il risultato in DuckDB
con.register('df_pivot', df_pivot)
con.execute("""
    CREATE OR REPLACE TABLE battute_pivot AS
    SELECT * FROM df_pivot
""")

print("Tabella battute_pivot creata in DuckDB")
'''



# Stampiamo le combinazioni più frequenti
print("Top combinazioni:")
df_battute_freq

df_battute

df_battute_pivot_GameID_PlayerID



con.execute("""
-- Livello 1 → 2: Type → Eval
CREATE OR REPLACE TABLE sankey_1 AS
SELECT
  "Type" AS source,
  "Eval" AS target,
  COUNT(*) AS value
FROM rilevations_libero_view_duckdb
WHERE "Foundamental" = 'S' AND "Type" IN ('Q', 'M') AND "Eval" IS NOT NULL  --Prendo solo i Type Q e M
GROUP BY 1, 2;
""")

con.execute("""
-- Livello 2 → 3: Eval → StartZone
CREATE OR REPLACE TABLE sankey_2 AS
SELECT
  "Eval" AS source,
  'Start_' || "StartZone" AS target,
  COUNT(*) AS value
FROM rilevations_libero_view_duckdb
WHERE "Foundamental" = 'S' AND "Eval" IS NOT NULL AND "StartZone" IS NOT NULL
GROUP BY 1, 2;
""")

con.execute("""
-- Livello 3 → 4: StartZone → EndZoneEsecZone
CREATE OR REPLACE TABLE sankey_3 AS
SELECT
  'Start_' || "StartZone" AS source,
  'End_' || "EndZoneEsecZone" AS target,
  COUNT(*) AS value
FROM rilevations_libero_view_duckdb
WHERE "Foundamental" = 'S' AND "StartZone" IS NOT NULL AND "EndZoneEsecZone" IS NOT NULL
GROUP BY 1, 2;
""")

# Carica i tre livelli
df1 = con.execute("SELECT * FROM sankey_1").df()
df2 = con.execute("SELECT * FROM sankey_2").df()
df3 = con.execute("SELECT * FROM sankey_3").df()

# Unione dei tre livelli
df_links = pd.concat([df1, df2, df3], ignore_index=True)

# Mappatura dei nomi a indici
labels = pd.unique(df_links[['source', 'target']].values.ravel())
label_to_index = {label: i for i, label in enumerate(labels)}

# Costruzione degli array source/target/value
sources = df_links['source'].map(label_to_index)
targets = df_links['target'].map(label_to_index)
values = df_links['value']

# Costruzione Sankey
fig = go.Figure(data=[go.Sankey(
    #definisco i nodi e le loro caratteristiche
    node=dict(
        pad=15,                                 #distanza verticale tra i nodi
        thickness=20,                           #spessore del nodo
        line=dict(color="black", width=0.5),    #colore e spessore del bordo del nodo
        label=labels.tolist(),                  #etichetta del nodo, come si chiama
        color="blue"                            #colore del nodo
    ),
    #definisco i link (flussi) e le loro caratteristiche. Ogni link è una connessione da una source a un target. 
    #Le liste source, target e value hanno tutte n elementi. Esse mi dicono per ogni elemento i, da quale nodo partire, a quale nodo arrivare, e con quante osservazioni/valore.
    #Ad esempio, se source[0] = 3, target[0] = 5, value[0] = 10, allora il link 0 parte dal nodo 3, arriva al nodo 5, e ha un valore di 10.
    link=dict(
        source=sources,
        target=targets,
        value=values
    )
)])

fig.update_layout(title_text="Flusso Battute (Type → Eval → StartZone → EndZone)", font_size=10)
fig.show()

df1

# Carica i dati una volta sola
df_raw = con.execute("""
    SELECT 
        "GameID",
        "SetNumber",
        "ActionNumber",
        "RilevationNumber",
        "TouchNumber",
        "Foundamental",
        "AbsNumeroPossesso",
        "RilevationNumber",
        "whichTeamID",
        "TeamNameShort",
        "NumeroMaglia_ID",
        "NomeCompleto",
        "Type",
        "Eval",
        "SetterCall",
        "TargAttk",
        'Start_' || "StartZone" AS "StartZone",
        'StartC_' || "StartZoneCompact" AS "StartZoneCompact",
        "AttkCombination",
        'End_' || "EndZoneEsecZone" AS "EndZoneEsecZone",
        'Endd_' || "EndZoneEsecZone3aree" AS "EndZoneEsecZone3aree",
        'EndSub_' || "EndSubzoneEsecSubzone" AS "EndSubzoneEsecSubzone",
        'Skill_' || "SkillType" AS "SkillType",
        'cSkill_' || "correctSkillType" AS "correctSkillType",
        'Players_' || "PlayersInfo" AS PlayersInfo,
        "Special",
        'ccc_' || "correctCustomChar" AS "correctCustomChar",
        'ccca_' || "correctCustomCharAggregate" AS "correctCustomCharAggregate",
        'ThisSetType_' || "ThisSetType" AS "ThisSetType",
        'ThisSetTypeAgg_' || "ThisSetTypeAggregate" AS "ThisSetTypeAggregate",
        'ThisAttkType_' || "ThisAttkType" AS "ThisAttkType",
        'ThisAppog_' || "ThisAppoggio" AS "ThisAppoggio",
        'PrevAppog_' || "PrevAppoggio" AS "PrevAppoggio",
        'NextAttkType_' || "NextAttkType" AS "NextAttkType",
        'NextNextAttkType_' || "NextNextAttkType" AS "NextNextAttkType",
        'NextAttkEval_' || "NextAttkEval" AS "NextAttkEval",
        'NextNextAttkEval_' || "NextNextAttkEval" AS "NextNextAttkEval",
        'BattutaPropria_' || "BattutaPropria" AS "BattutaPropria",
        'Possesso_' || "AbsNumeroPossesso" AS "NumPossesso",        --Non posso chiamarlo AbsNumeroPossesso, perchè ce ne ho già uno che mi serve numerico così com'è
        'Punto_' || "EndedInPoint" AS "EndedInPoint",
        'Ruolo_' || "RuoloCalc" AS "RuoloCalc",
        'RotazionePropria_' || "RotazionePropria" AS "RotazionePropria",
        'RotazioneContro_' || "RotazioneContro" AS "RotazioneContro"
        
    FROM rilevations_libero_view_duckdb
    WHERE "Foundamental" IN ('S', 'R', 'E', 'A', 'B', 'D', 'F')  
    """).df()

#Le colonne che hanno valori numerici o booleani, li devo rendere stringhe per poterli visualizzare in questi grafici (così sembra)
df_raw["EndedInPoint"] = df_raw["EndedInPoint"].astype(str)  
df_raw["RuoloCalc"] = df_raw["RuoloCalc"].astype(str)  
df_raw["NumPossesso"] = df_raw["NumPossesso"].astype(str)



#Guardo quanti MB di memoria occupa df_raw
df_raw.info(memory_usage='deep')

df_raw.head()

# Colonne disponibili per le battute
available_levels = ["Type", "Eval", "StartZone", "StartZoneCompact", "EndZoneEsecZone", "EndZoneEsecZone3aree", "EndSubzoneEsecSubzone", "Special", "correctCustomChar", "correctCustomCharAggregate", "EndedInPoint", "RuoloCalc"]

#Filtro solo le battute
df_battute_raw = df_raw[df_raw["Foundamental"] == "S"]
df_battute_raw = df_battute_raw[available_levels]

#Rendo i valori di EndedInPoint stringa, quindi diventano 'True' e 'False'
df_battute_raw["EndedInPoint"] = df_battute_raw["EndedInPoint"].astype(str)
df_battute_raw["RuoloCalc"] = df_battute_raw["RuoloCalc"].astype(str)

# Opzioni per checkbox dei valori
options_dict = {
    "Type": ['Q', 'M', 'H', 'T', 'N', 'O'],
    "Eval": ['#', '+', '/', '!', '-', '='],
    "StartZone": ['Start_1', 'Start_5', 'Start_6', 'Start_7', 'Start_9'],
    "StartZoneCompact": ['StartC_1', 'StartC_5', 'StartC_6'],
    "EndZoneEsecZone": ['End_1','End_2','End_3','End_4','End_5','End_6','End_7','End_8','End_9'],
    "EndZoneEsecZone3aree": ['Endd_5', 'Endd_6', 'Endd_1'],  #Le etichette vanno chiamate in modo diverso da EndZoneEsecZone, altrimenti quelle che si chiamano uguali si collegano a sè stesse invece che ad altri nodi.
    "EndSubzoneEsecSubzone": ['EndSub_A', 'EndSub_B', 'EndSub_C', 'EndSub_D'],
    "Special": ['N', 'O', 'L', 'R', 'Z'],
    "correctCustomChar": ['ccc_R1', 'ccc_T1', 'ccc_C2', 'ccc_R2', 'ccc_T2', 'ccc_C1', 'ccc_R3', 'ccc_R4', 'ccc_C4', 'ccc_R5', 'ccc_R6', 'ccc_R7', 'ccc_R8', 'ccc_C3', 'ccc_T4', 'ccc_T3', 'ccc_C5', 'ccc_T6', 'ccc_T5', 'ccc_C6', 'ccc_R9', 'ccc_C7', 'ccc_T7', 'ccc_C8', 'ccc_T8', 'ccc_T9'],
    "correctCustomCharAggregate": ['ccca_R', 'ccca_T', 'ccca_C'],
    "EndedInPoint": ['Punto_true', 'Punto_false'],
    "RuoloCalc": ['Ruolo_0', 'Ruolo_2', 'Ruolo_3', 'Ruolo_4', 'Ruolo_5']  #Il libero non batte
}

app = Dash(__name__)
app.title = "Sankey Pallavolo"

app.layout = html.Div([
    html.H2("Diagramma Sankey - Battute Pallavolo"),

    html.Div([
        html.Label("Seleziona i livelli da includere:"),
        dcc.Checklist(
            id='livelli-sankey',
            options=[{"label": col, "value": col} for col in available_levels],
            value=["Type", "Eval", "StartZone", "StartZoneCompact", "EndZoneEsecZone", "EndZoneEsecZone3aree", "EndSubzoneEsecSubzone", "Special", "correctCustomChar", "correctCustomCharAggregate", "EndedInPoint", "RuoloCalc"],
            labelStyle={'display': 'inline-block', 'margin-right': '12px'}
        )
    ], style={'margin-bottom': '20px'}),

    html.Div([
        html.Div([
            html.Label(f"{col}"),
            dcc.Checklist(
                id=f'valori-{col.lower()}',
                options=[{'label': v, 'value': v} for v in options_dict[col]],
                value=options_dict[col],
                inline=True
            )
        ], style={'margin': '10px 0'}) for col in available_levels
    ]),

    dcc.Graph(id='sankey-graph')
])

@app.callback(
    Output('sankey-graph', 'figure'),
    Input('livelli-sankey', 'value'),
    Input('valori-type', 'value'),
    Input('valori-eval', 'value'),
    Input('valori-startzone', 'value'),
    Input('valori-startzonecompact', 'value'),
    Input('valori-endzoneeseczone', 'value'),
    Input('valori-endzoneeseczone3aree', 'value'),
    Input('valori-endsubzoneesecsubzone', 'value'),
    Input('valori-special', 'value'),
    Input('valori-correctcustomchar', 'value'),
    Input('valori-correctcustomcharaggregate', 'value'),
    Input('valori-endedinpoint', 'value'),
    Input('valori-ruolocalc', 'value')
)
def update_sankey(livelli, v_type, v_eval, v_start, v_start_compact, v_end, v_end_3aree, v_end_sub, v_special, v_correct_custom_char, v_correct_custom_char_aggregate, v_ended_in_point, v_ruolo_calc):
    if len(livelli) < 2:
        return go.Figure().update_layout(title="❗ Seleziona almeno 2 livelli.")

    # Filtro su dataframe
    df = df_battute_raw.copy()

    # Filtro dinamico solo sui livelli selezionati (così se una colonna ha tanti NULL e non la seleziono, non perdo quelle righe)
    if "Type" in livelli:
        df = df[df["Type"].isin(v_type)]
    if "Eval" in livelli:
        df = df[df["Eval"].isin(v_eval)]
    if "StartZone" in livelli:
        df = df[df["StartZone"].isin(v_start)]
    if "StartZoneCompact" in livelli:
        df = df[df["StartZoneCompact"].isin(v_start_compact)]
    if "EndZoneEsecZone" in livelli:
        df = df[df["EndZoneEsecZone"].isin(v_end)]
    if "EndZoneEsecZone3aree" in livelli:
        df = df[df["EndZoneEsecZone3aree"].isin(v_end_3aree)]
    if "EndSubzoneEsecSubzone" in livelli:
        df = df[df["EndSubzoneEsecSubzone"].isin(v_end_sub)]
    if "Special" in livelli:
        df = df[df["Special"].isin(v_special)]
    if "correctCustomChar" in livelli:
        df = df[df["correctCustomChar"].isin(v_correct_custom_char)]
    if "correctCustomCharAggregate" in livelli:
        df = df[df["correctCustomCharAggregate"].isin(v_correct_custom_char_aggregate)]
    if "EndedInPoint" in livelli:
        df = df[df["EndedInPoint"].isin(v_ended_in_point)]
    if "RuoloCalc" in livelli:
        df = df[df["RuoloCalc"].isin(v_ruolo_calc)]

    if df.empty:
        return go.Figure().update_layout(title="Nessun dato da visualizzare.")

    # Conta occorrenze per ogni valore in ogni livello (nodi)
    nodi_totali = []
    col_stats = []
    for col in livelli:  #per ogni colonna selezionata dall'utente
        cnt = df[col].value_counts().reset_index()                  #Con value_counts() conto quante volte appare ogni valore nella colonna. Con reset_index() trasformo il risultato in un dataframe con due colonne: la prima con i valori, la seconda con il conteggio.
        cnt.columns = ['label', 'count']                            #Rinomina le colonne in "label" (es: "Q", "#", "Start_1") e "count" (numero di volte che appaiono)
        cnt['col'] = col                                            #Aggiunge una colonna che indica da quale colonna/livello proviene ciascun valore
        cnt['percent'] = cnt['count'] / cnt['count'].sum() * 100    #Calcola la percentuale di ogni valore rispetto al totale della colonna
        nodi_totali.append(cnt)                                     #Salva il risultato per ogni colonna
        
        #Questa parte serve per capire quanto è completa la colonna, ovvero quante righe non sono null.
        non_null_count = df[~df[col].isna()].shape[0]
        col_stats.append({
            'col': col,
            'count': non_null_count,
            'total': df_battute_raw.shape[0],
            'percent': 100 * non_null_count / df_battute_raw.shape[0]
        })
        
    
    # Concatena i conteggi di tutte le colonne e ordina per label
    nodi_df = pd.concat(nodi_totali, ignore_index=True)
    nodi_df = nodi_df.sort_values(by='label')
    
    #Siccome ogni nodo deve essere indicato con un numero intero
    all_labels = nodi_df['label'].unique().tolist()                     #creo una lista con tutti i nodi, senza ripetizioni
    label_to_index = {label: i for i, label in enumerate(all_labels)}   #creo un dizionario che associa ad ogni nodo il suo numero intero
    #ESEMPIO  all_labels = ["Q", "M", "T", "#", "/", "Start_1", "Start_5"]
    #ESEMPIO  label_to_index = {"Q": 0, "M": 1, ..., "Start_1": 5, ...}

    #Per ogni nodo creo una stringa che indica quante volte compare nella colonna (come valore assoluto e come percentuale)
    node_customdata = [
        f"{row['count']} tocchi<br>{row['percent']:.1f}%"
        for _, row in nodi_df.iterrows()
    ]

    # Link tra livelli
    df['count'] = 1
    links = []
    for i in range(len(livelli) - 1):
        src = livelli[i]
        tgt = livelli[i + 1]
        df_pair = df.groupby([src, tgt]).size().reset_index(name='value')
        df_pair.columns = ['source', 'target', 'value']
        links.append(df_pair)

    df_links = pd.concat(links, ignore_index=True)

    # Calcolo percentuale relativa dei link rispetto al nodo sorgente
    df_links['percent'] = df_links.groupby('source')['value'].transform(lambda x: x / x.sum() * 100)

    sources = df_links['source'].map(label_to_index)
    targets = df_links['target'].map(label_to_index)
    values = df_links['value']

    link_customdata = [
        f"{v} tocchi<br>{p:.1f}%" for v, p in zip(df_links['value'], df_links['percent'])
    ]
    
    
    # Mappa da colonna a trasparenza (tra 0.1 e 1.0). Serve per modificare la trasparenza dei nodi in base a quante osservazioni ha quella colonna.
    opacity_map = {
        stat['col']: max(0.1, stat['percent'] / 100)
        for stat in col_stats
    }
    
    #Creiamo la lista con i colori per ogni colonna (e quindi per i suoi nodi)
    node_colors = []
    for _, row in nodi_df.iterrows():
        base_color = (2, 70, 107)  # RGB blu Modena
        alpha = opacity_map.get(row['col'], 1.0)
        rgba = f"rgba({base_color[0]}, {base_color[1]}, {base_color[2]}, {alpha:.2f})"
        node_colors.append(rgba)
        

    fig = go.Figure(data=[go.Sankey(
        arrangement="snap",
        node=dict(
            pad=15,
            thickness=20,
            label=all_labels,
            color=node_colors,
            customdata=node_customdata,
            hovertemplate="%{label}<br>%{customdata}<extra></extra>"
        ),
        link=dict(
            source=sources,
            target=targets,
            value=values,
            customdata=link_customdata,
            hovertemplate="%{customdata}<extra></extra>",
            #hovercolor=["yellow"],
            # colore standard → non impostiamo "color"
        )
    )])

    fig.update_layout(title="Sankey - Flusso Battute", font_size=10)
    
    n_colonne = len(livelli)
    x_positions = [i / (n_colonne - 1) for i in range(n_colonne)]
    
    for x, nome_colonna in zip(x_positions, livelli):
        fig.add_annotation(
            x=x,
            y=1.05,  # leggermente sopra i nodi
            text=f"<b>{nome_colonna}</b>",
            showarrow=False,
            xanchor='center',
            yanchor='bottom',
            font=dict(size=12, color='black')
        )
        
    return fig

if __name__ == '__main__':
    app.run(debug=True, port=8052)


print(df_battute_raw["RuoloCalc"].unique())


# Colonne disponibili
available_levels = ["Type", "Eval", "StartZone", "StartZoneCompact", "EndZoneEsecZone", "EndZoneEsecZone3aree", "EndSubzoneEsecSubzone", "SkillType", "PlayersInfo", "Special", "EndedInPoint", "RuoloCalc"]

#Filtro solo le ricezioni
df_ricezioni_raw = df_raw[df_raw["Foundamental"] == "R"]
df_ricezioni_raw = df_ricezioni_raw[available_levels]

#Rendo i valori di EndedInPoint stringa, quindi diventano 'True' e 'False'
df_ricezioni_raw["EndedInPoint"] = df_ricezioni_raw["EndedInPoint"].astype(str)
df_ricezioni_raw["RuoloCalc"] = df_ricezioni_raw["RuoloCalc"].astype(str)

# Opzioni per checkbox dei valori
options_dict = {
    "Type": ['Q', 'M', 'H', 'T', 'N', 'O'],
    "Eval": ['#', '+', '/', '!', '-', '='],
    "StartZone": ['Start_1', 'Start_5', 'Start_6', 'Start_7', 'Start_9'],
    "StartZoneCompact": ['StartC_1', 'StartC_5', 'StartC_6'],
    "EndZoneEsecZone": ['End_1','End_2','End_3','End_4','End_5','End_6','End_7','End_8','End_9'],
    "EndZoneEsecZone3aree": ['Endd_5', 'Endd_6', 'Endd_1'],
    "EndSubzoneEsecSubzone": ['EndSub_A', 'EndSub_B', 'EndSub_C', 'EndSub_D'],
    "SkillType": ['Skill_L', 'Skill_R', 'Skill_W', 'Skill_O', 'Skill_M'],
    "PlayersInfo": ['Players_1', 'Players_2', 'Players_3', 'Players_4', 'Players_5', 'Players_6', 'Players_7', 'Players_8', 'Players_9'],
    "Special": ['U', 'X', 'P', 'E', 'Z'],
    "EndedInPoint": ['Punto_true', 'Punto_false'],
    "RuoloCalc": ['Ruolo_0', 'Ruolo_1', 'Ruolo_2', 'Ruolo_3', 'Ruolo_4', 'Ruolo_5']
}

app = Dash(__name__)
app.title = "Sankey Pallavolo"

app.layout = html.Div([
    html.H2("Diagramma Sankey - Ricezioni Pallavolo"),

    html.Div([
        html.Label("Seleziona i livelli da includere:"),
        dcc.Checklist(
            id='livelli-sankey',
            options=[{"label": col, "value": col} for col in available_levels],
            value=available_levels,
            labelStyle={'display': 'inline-block', 'margin-right': '12px'}
        )
    ], style={'margin-bottom': '20px'}),

    html.Div([
        html.Div([
            html.Label(f"{col}"),
            dcc.Checklist(
                id=f'valori-{col.lower()}',
                options=[{'label': v, 'value': v} for v in options_dict[col]],
                value=options_dict[col],
                inline=True
            )
        ], style={'margin': '10px 0'}) for col in available_levels
    ]),

    dcc.Graph(id='sankey-graph')
])

@app.callback(
    Output('sankey-graph', 'figure'),
    Input('livelli-sankey', 'value'),
    Input('valori-type', 'value'),
    Input('valori-eval', 'value'),
    Input('valori-startzone', 'value'),
    Input('valori-startzonecompact', 'value'),
    Input('valori-endzoneeseczone', 'value'),
    Input('valori-endzoneeseczone3aree', 'value'),
    Input('valori-endsubzoneesecsubzone', 'value'),
    Input('valori-skilltype', 'value'),
    Input('valori-playersinfo', 'value'),
    Input('valori-special', 'value'),
    Input('valori-endedinpoint', 'value'),
    Input('valori-ruolocalc', 'value')
    
)
def update_sankey(livelli, v_type, v_eval, v_start, v_start_compact, v_end, v_end_3aree, v_end_sub, v_skill, v_players, v_special, v_ended_in_point, v_ruolo_calc):
    if len(livelli) < 2:
        return go.Figure().update_layout(title="❗ Seleziona almeno 2 livelli.")

    # Filtro su dataframe
    df = df_ricezioni_raw.copy()

    # Filtro dinamico solo sui livelli selezionati (così se una colonna ha tanti NULL e non la seleziono, non perdo quelle righe)
    if "Type" in livelli:
        df = df[df["Type"].isin(v_type)]
    if "Eval" in livelli:
        df = df[df["Eval"].isin(v_eval)]
    if "StartZone" in livelli:
        df = df[df["StartZone"].isin(v_start)]
    if "StartZoneCompact" in livelli:
        df = df[df["StartZoneCompact"].isin(v_start_compact)]
    if "EndZoneEsecZone" in livelli:
        df = df[df["EndZoneEsecZone"].isin(v_end)]
    if "EndZoneEsecZone3aree" in livelli:
        df = df[df["EndZoneEsecZone3aree"].isin(v_end_3aree)]
    if "EndSubzoneEsecSubzone" in livelli:
        df = df[df["EndSubzoneEsecSubzone"].isin(v_end_sub)]
    if "SkillType" in livelli:
        df = df[df["SkillType"].isin(v_skill)]
    if "PlayersInfo" in livelli:
        df = df[df["PlayersInfo"].isin(v_players)]
    if "Special" in livelli:
        df = df[df["Special"].isin(v_special)]
    if "EndedInPoint" in livelli:
        df = df[df["EndedInPoint"].isin(v_ended_in_point)]
    if "RuoloCalc" in livelli:
        df = df[df["RuoloCalc"].isin(v_ruolo_calc)]

    if df.empty:
        return go.Figure().update_layout(title="Nessun dato da visualizzare.")

    # Conta occorrenze per ogni valore in ogni livello (nodi)
    nodi_totali = []
    col_stats = []
    for col in livelli:
        cnt = df[col].value_counts().reset_index()
        cnt.columns = ['label', 'count']
        cnt['col'] = col
        cnt['percent'] = cnt['count'] / cnt['count'].sum() * 100
        nodi_totali.append(cnt)
        
        non_null_count = df[~df[col].isna()].shape[0]
        col_stats.append({
            'col': col,
            'count': non_null_count,
            'total': df_ricezioni_raw.shape[0],
            'percent': 100 * non_null_count / df_ricezioni_raw.shape[0]
        })
        

    nodi_df = pd.concat(nodi_totali, ignore_index=True)
    nodi_df = nodi_df.sort_values(by='label')
    all_labels = nodi_df['label'].unique().tolist()
    label_to_index = {label: i for i, label in enumerate(all_labels)}

    node_customdata = [
        f"{row['count']} tocchi<br>{row['percent']:.1f}%"
        for _, row in nodi_df.iterrows()
    ]

    # Link tra livelli
    df['count'] = 1
    links = []
    for i in range(len(livelli) - 1):
        src = livelli[i]
        tgt = livelli[i + 1]
        df_pair = df.groupby([src, tgt]).size().reset_index(name='value')
        df_pair.columns = ['source', 'target', 'value']
        links.append(df_pair)

    df_links = pd.concat(links, ignore_index=True)

    # Calcolo percentuale relativa dei link rispetto al nodo sorgente
    df_links['percent'] = df_links.groupby('source')['value'].transform(lambda x: x / x.sum() * 100)

    sources = df_links['source'].map(label_to_index)
    targets = df_links['target'].map(label_to_index)
    values = df_links['value']

    link_customdata = [
        f"{v} tocchi<br>{p:.1f}%" for v, p in zip(df_links['value'], df_links['percent'])
    ]
    
    
    # Mappa da colonna a trasparenza (tra 0.01 e 1.0). Serve per modificare la trasparenza dei nodi in base a quante osservazioni ha quella colonna.
    opacity_map = {
        stat['col']: max(0.01, stat['percent'] / 100)
        for stat in col_stats
    }
    
    #Creiamo la lista con i colori per ogni colonna (e quindi per i suoi nodi)
    node_colors = []
    for _, row in nodi_df.iterrows():
        base_color = (2, 70, 107)  # RGB blu Modena
        alpha = opacity_map.get(row['col'], 1.0)
        rgba = f"rgba({base_color[0]}, {base_color[1]}, {base_color[2]}, {alpha:.2f})"
        node_colors.append(rgba)
        

    fig = go.Figure(data=[go.Sankey(
        arrangement="snap",
        node=dict(
            pad=15,
            thickness=20,
            label=all_labels,
            color=node_colors,
            customdata=node_customdata,
            hovertemplate="%{label}<br>%{customdata}<extra></extra>"
        ),
        link=dict(
            source=sources,
            target=targets,
            value=values,
            customdata=link_customdata,
            hovertemplate="%{customdata}<extra></extra>",
            #hovercolor=["yellow"],
            # colore standard → non impostiamo "color"
        )
    )])

    fig.update_layout(title="Sankey - Flusso Ricezioni", font_size=10)
    
    n_colonne = len(livelli)
    x_positions = [i / (n_colonne - 1) for i in range(n_colonne)]
    
    for x, nome_colonna in zip(x_positions, livelli):
        fig.add_annotation(
            x=x,
            y=1.05,  # leggermente sopra i nodi
            text=f"<b>{nome_colonna}</b>",
            showarrow=False,
            xanchor='center',
            yanchor='bottom',
            font=dict(size=12, color='black')
        )
    
    return fig

if __name__ == '__main__':
    app.run(debug=True, port=8052)


# Colonne disponibili
available_levels = ["Type", "Eval", "SetterCall", "TargAttk", "EndZoneEsecZone", "EndZoneEsecZone3aree", "EndSubzoneEsecSubzone", "SkillType", "Special", "correctCustomChar", "correctCustomCharAggregate", "ThisAppoggio", "EndedInPoint", "RuoloCalc"]   #Invece di correctCustomChar e correctCustomCharAggregate, possiamo mettere i loro ThisCorrect...

#Filtro solo le ricezioni
df_alzate_raw = df_raw[df_raw["Foundamental"] == "E"]
df_alzate_raw = df_alzate_raw[available_levels]

#Rendo i valori di EndedInPoint stringa, quindi diventano 'True' e 'False'
df_alzate_raw["EndedInPoint"] = df_alzate_raw["EndedInPoint"].astype(str)
df_alzate_raw["RuoloCalc"] = df_alzate_raw["RuoloCalc"].astype(str)

# Opzioni per checkbox dei valori
options_dict = {
    "Type": ['Q', 'M', 'H', 'T', 'U', 'N', 'O'],
    "Eval": ['#', '+', '/', '!', '-', '='],
    "SetterCall": ['K2', 'KA', 'KE', 'KF', 'K7', 'K1', 'KB', 'KN', 'KX', '~~', 'KD', 'K0', 'KM', 'KO'],
    "TargAttk": ['F', 'C', 'B', 'P', 'S'],
    "EndZoneEsecZone": ['End_1','End_2','End_3','End_4','End_5','End_6','End_7','End_8','End_9'],
    "EndZoneEsecZone3aree": ['Endd_5', 'Endd_6', 'Endd_1'],
    "EndSubzoneEsecSubzone": ['EndSub_A', 'EndSub_B', 'EndSub_C', 'EndSub_D'],
    "SkillType": ['Skill_1', 'Skill_2', 'Skill_3', 'Skill_4', 'Skill_5'],
    "Special": ['U', 'I', 'Z'],
    "correctCustomChar": ['ccc_C!', 'ccc_C-', 'ccc_D!', 'ccc_C+', 'ccc_F-', 'ccc_F+', 'ccc_D-', 'ccc_R-', 'ccc_D+', 'ccc_R!', 'ccc_R+', 'ccc_F!'],   #aggiungi None (?)
    "correctCustomCharAggregate": ['ccca_+', 'ccca_!', 'ccca_-'],
    "ThisAppoggio": ['ThisAppog_+', 'ThisAppog_!', 'ThisAppog_-'],
    "EndedInPoint": ['Punto_true', 'Punto_false'],
    "RuoloCalc": ['Ruolo_0', 'Ruolo_1', 'Ruolo_2', 'Ruolo_3', 'Ruolo_4', 'Ruolo_5']
}

app = Dash(__name__)
app.title = "Sankey Pallavolo"

app.layout = html.Div([
    html.H2("Diagramma Sankey - Alzate Pallavolo"),

    html.Div([
        html.Label("Seleziona i livelli da includere:"),
        dcc.Checklist(
            id='livelli-sankey',
            options=[{"label": col, "value": col} for col in available_levels],
            value=available_levels,
            labelStyle={'display': 'inline-block', 'margin-right': '12px'}
        )
    ], style={'margin-bottom': '20px'}),

    html.Div([
        html.Div([
            html.Label(f"{col}"),
            dcc.Checklist(
                id=f'valori-{col.lower()}',
                options=[{'label': v, 'value': v} for v in options_dict[col]],
                value=options_dict[col],
                inline=True
            )
        ], style={'margin': '10px 0'}) for col in available_levels
    ]),

    dcc.Graph(id='sankey-graph')
])

@app.callback(
    Output('sankey-graph', 'figure'),
    Input('livelli-sankey', 'value'),
    Input('valori-type', 'value'),
    Input('valori-eval', 'value'),
    Input('valori-settercall', 'value'),
    Input('valori-targattk', 'value'),
    Input('valori-endzoneeseczone', 'value'),
    Input('valori-endzoneeseczone3aree', 'value'),
    Input('valori-endsubzoneesecsubzone', 'value'),
    Input('valori-skilltype', 'value'),
    Input('valori-special', 'value'),
    Input('valori-correctcustomchar', 'value'),
    Input('valori-correctcustomcharaggregate', 'value'),
    Input('valori-thisappoggio', 'value'),
    Input('valori-endedinpoint', 'value'),
    Input('valori-ruolocalc', 'value')
    
)
def update_sankey(livelli, v_type, v_eval, v_setter, v_targ, v_end, v_end_3aree, v_end_sub, v_skill, v_special, v_correct_custom_char, v_correct_custom_char_aggregate, v_this_appoggio, v_ended_in_point, v_ruolo_calc):
    if len(livelli) < 2:
        return go.Figure().update_layout(title="❗ Seleziona almeno 2 livelli.")

    # Filtro su dataframe
    df = df_alzate_raw.copy()

    # Filtro dinamico solo sui livelli selezionati (così se una colonna ha tanti NULL e non la seleziono, non perdo quelle righe)
    if "Type" in livelli:
        df = df[df["Type"].isin(v_type)]
    if "Eval" in livelli:
        df = df[df["Eval"].isin(v_eval)]
    if "SetterCall" in livelli:
        df = df[df["SetterCall"].isin(v_setter)]
    if "TargAttk" in livelli:
        df = df[df["TargAttk"].isin(v_targ)]
    if "EndZoneEsecZone" in livelli:
        df = df[df["EndZoneEsecZone"].isin(v_end)]
    if "EndZoneEsecZone3aree" in livelli:
        df = df[df["EndZoneEsecZone3aree"].isin(v_end_3aree)]
    if "EndSubzoneEsecSubzone" in livelli:
        df = df[df["EndSubzoneEsecSubzone"].isin(v_end_sub)]
    if "SkillType" in livelli:
        df = df[df["SkillType"].isin(v_skill)]
    if "Special" in livelli:
        df = df[df["Special"].isin(v_special)]
    if "correctCustomChar" in livelli:
        df = df[df["correctCustomChar"].isin(v_correct_custom_char)]
    if "correctCustomCharAggregate" in livelli:
        df = df[df["correctCustomCharAggregate"].isin(v_correct_custom_char_aggregate)]
    if "ThisAppoggio" in livelli:
        df = df[df["ThisAppoggio"].isin(v_this_appoggio)]
    if "EndedInPoint" in livelli:
        df = df[df["EndedInPoint"].isin(v_ended_in_point)]
    if "RuoloCalc" in livelli:
        df = df[df["RuoloCalc"].isin(v_ruolo_calc)]

    if df.empty:
        return go.Figure().update_layout(title="Nessun dato da visualizzare.")

    # Conta occorrenze per ogni valore in ogni livello (nodi)
    nodi_totali = []
    col_stats = []
    for col in livelli:
        cnt = df[col].value_counts().reset_index()
        cnt.columns = ['label', 'count']
        cnt['col'] = col
        cnt['percent'] = cnt['count'] / cnt['count'].sum() * 100
        nodi_totali.append(cnt)
        
        non_null_count = df[~df[col].isna()].shape[0]
        col_stats.append({
            'col': col,
            'count': non_null_count,
            'total': df_alzate_raw.shape[0],
            'percent': 100 * non_null_count / df_alzate_raw.shape[0]
        })
        

    nodi_df = pd.concat(nodi_totali, ignore_index=True)
    nodi_df = nodi_df.sort_values(by='label')
    all_labels = nodi_df['label'].unique().tolist()
    label_to_index = {label: i for i, label in enumerate(all_labels)}

    node_customdata = [
        f"{row['count']} tocchi<br>{row['percent']:.1f}%"
        for _, row in nodi_df.iterrows()
    ]

    # Link tra livelli
    df['count'] = 1
    links = []
    for i in range(len(livelli) - 1):
        src = livelli[i]
        tgt = livelli[i + 1]
        df_pair = df.groupby([src, tgt]).size().reset_index(name='value')
        df_pair.columns = ['source', 'target', 'value']
        links.append(df_pair)

    df_links = pd.concat(links, ignore_index=True)

    # Calcolo percentuale relativa dei link rispetto al nodo sorgente
    df_links['percent'] = df_links.groupby('source')['value'].transform(lambda x: x / x.sum() * 100)

    sources = df_links['source'].map(label_to_index)
    targets = df_links['target'].map(label_to_index)
    values = df_links['value']

    link_customdata = [
        f"{v} tocchi<br>{p:.1f}%" for v, p in zip(df_links['value'], df_links['percent'])
    ]
    
    
    # Mappa da colonna a trasparenza (tra 0.01 e 1.0). Serve per modificare la trasparenza dei nodi in base a quante osservazioni ha quella colonna.
    opacity_map = {
        stat['col']: max(0.01, stat['percent'] / 100)
        for stat in col_stats
    }
    
    #Creiamo la lista con i colori per ogni colonna (e quindi per i suoi nodi)
    node_colors = []
    for _, row in nodi_df.iterrows():
        base_color = (2, 70, 107)  # RGB blu Modena
        alpha = opacity_map.get(row['col'], 1.0)
        rgba = f"rgba({base_color[0]}, {base_color[1]}, {base_color[2]}, {alpha:.2f})"
        node_colors.append(rgba)
        

    fig = go.Figure(data=[go.Sankey(
        arrangement="snap",
        node=dict(
            pad=15,
            thickness=20,
            label=all_labels,
            color=node_colors,
            customdata=node_customdata,
            hovertemplate="%{label}<br>%{customdata}<extra></extra>"
        ),
        link=dict(
            source=sources,
            target=targets,
            value=values,
            customdata=link_customdata,
            hovertemplate="%{customdata}<extra></extra>",
            #hovercolor=["yellow"],
            # colore standard → non impostiamo "color"
        )
    )])

    fig.update_layout(title="Sankey - Flusso Alzate", font_size=10)
    
    #Aggiungiamo i nomi alle colonne
    n_colonne = len(livelli)
    x_positions = [i / (n_colonne - 1) for i in range(n_colonne)]

    for x, nome_colonna in zip(x_positions, livelli):
        fig.add_annotation(
            x=x,
            y=1.05,  # leggermente sopra i nodi
            text=f"<b>{nome_colonna}</b>",
            showarrow=False,
            xanchor='center',
            yanchor='bottom',
            font=dict(size=12, color='black')
        )
        
    #print(f"Righe dopo filtro {col}: {len(df)}")

    return fig

if __name__ == '__main__':
    app.run(debug=True, port=8054)  #Se sono in un notebook, invece di mettere port=X, metto use_reloader=False


print(df_alzate_raw["correctCustomChar"].unique())

#Guardiamo in quante alzate non c'è scritto da dove partono (EndZoneEsecZone), ma nel loro attacco c'è scritto da dove parte l'attacco (StartZone).

# Separiamo attacchi e alzate
df_attacchi = df_raw[df_raw["Foundamental"] == "A"].copy()
df_alzate = df_raw[df_raw["Foundamental"] == "E"].copy()

# Filtra le alzate con EndZoneEsecZone NULL
df_alzate_nullzone = df_alzate[df_alzate["EndZoneEsecZone"].isna()].copy()
merge_keys = ["GameID", "SetNumber", "ActionNumber", "AbsNumeroPossesso"]

df_merged = df_alzate_nullzone.merge(
    df_attacchi[merge_keys + ["StartZone"]],
    on=merge_keys,
    how="left",
    suffixes=('_alzata', '_attacco')
)

df_final = df_merged[df_merged["StartZone_attacco"].notna()]
df_final.shape


# Colonne disponibili
available_levels = ["Type", "Eval", "AttkCombination", "StartZone", "StartZoneCompact", "EndZoneEsecZone", "EndZoneEsecZone3aree", "EndSubzoneEsecSubzone", "SkillType", "PlayersInfo", "Special", "ThisAppoggio", "EndedInPoint", "RuoloCalc"]

#Filtro solo gli attacchi
df_attacchi_raw = df_raw[df_raw["Foundamental"] == "A"]
df_attacchi_raw = df_attacchi_raw[available_levels]

#Rendo i valori di EndedInPoint stringa, quindi diventano 'True' e 'False'
df_attacchi_raw["EndedInPoint"] = df_attacchi_raw["EndedInPoint"].astype(str)
df_attacchi_raw["RuoloCalc"] = df_attacchi_raw["RuoloCalc"].astype(str)

# Opzioni per checkbox dei valori. Non devono esserci valori uguali, neanche tra chiavi diverse.
options_dict = {
    "Type": ['Q', 'M', 'H', 'T', 'U', 'N', 'O'],
    "Eval": ['#', '+', '/', '!', '-', '='],
    "AttkCombination": ['X5', 'V5', 'X8', 'V8', 'P2', 'XB', 'X6', 'PR', 'X1', 'X7', 'VP', 'XP', 'PP', 'PK', 'V6', 'X2', 'XD', 'V0', 'V3', 'XC', 'XR', 'X9', '~~', 'X4', 'XM', 'X0', 'X3', 'VB', 'VR', 'W5', 'W6'],
    "StartZone": ['Start_1', 'Start_2', 'Start_3', 'Start_4', 'Start_5', 'Start_6', 'Start_7', 'Start_8', 'Start_9'],
    "StartZoneCompact": ['StartC_1', 'StartC_2', 'StartC_3', 'StartC_4', 'StartC_6'],
    "EndZoneEsecZone": ['End_1','End_2','End_3','End_4','End_5','End_6','End_7','End_8','End_9'],
    "EndZoneEsecZone3aree": ['Endd_5', 'Endd_6', 'Endd_1'],
    "EndSubzoneEsecSubzone": ['EndSub_A', 'EndSub_B', 'EndSub_C', 'EndSub_D'],
    "SkillType": ['Skill_H', 'Skill_P', 'Skill_T'],
    "PlayersInfo": ['Players_0', 'Players_1', 'Players_2', 'Players_3', 'Players_4'],
    "Special": ['S', 'O', 'F', 'X', 'N', 'C', 'I', 'A', 'Z'],
    "ThisAppoggio": ['ThisAppog_+', 'ThisAppog_!', 'ThisAppog_-'],
    "EndedInPoint": ['True', 'False'],
    "RuoloCalc": ['0', '1', '2', '3', '4', '5']
}

app = Dash(__name__)
app.title = "Sankey Pallavolo"

app.layout = html.Div([
    html.H2("Diagramma Sankey - Attacchi Pallavolo"),

    html.Div([
        html.Label("Seleziona i livelli da includere:"),
        dcc.Checklist(
            id='livelli-sankey',
            options=[{"label": col, "value": col} for col in available_levels],
            value=available_levels,
            labelStyle={'display': 'inline-block', 'margin-right': '12px'}
        )
    ], style={'margin-bottom': '20px'}),

    html.Div([
        html.Div([
            html.Label(f"{col}"),
            dcc.Checklist(
                id=f'valori-{col.lower()}',
                options=[{'label': v, 'value': v} for v in options_dict[col]],
                value=options_dict[col],
                inline=True
            )
        ], style={'margin': '10px 0'}) for col in available_levels
    ]),

    dcc.Graph(id='sankey-graph')
])

@app.callback(
    Output('sankey-graph', 'figure'),
    Input('livelli-sankey', 'value'),
    Input('valori-type', 'value'),
    Input('valori-eval', 'value'),
    Input('valori-attkcombination', 'value'),
    Input('valori-startzone', 'value'),
    Input('valori-startzonecompact', 'value'),
    Input('valori-endzoneeseczone', 'value'),
    Input('valori-endzoneeseczone3aree', 'value'),
    Input('valori-endsubzoneesecsubzone', 'value'),
    Input('valori-skilltype', 'value'),
    Input('valori-playersinfo', 'value'),
    Input('valori-special', 'value'),
    Input('valori-thisappoggio', 'value'),
    Input('valori-endedinpoint', 'value'),
    Input('valori-ruolocalc', 'value')
    
)
def update_sankey(livelli, v_type, v_eval, v_attk_combination, v_start, v_start_compact, v_end, v_end_3aree, v_end_sub, v_skill, v_players, v_special, v_this_appoggio, v_ended_in_point, v_ruolo_calc):
    if len(livelli) < 2:
        return go.Figure().update_layout(title="❗ Seleziona almeno 2 livelli.")

    # Filtro su dataframe
    df = df_attacchi_raw.copy()

    # Filtro dinamico solo sui livelli selezionati (così se una colonna ha tanti NULL e non la seleziono, non perdo quelle righe)
    if "Type" in livelli:
        df = df[df["Type"].isin(v_type)]
    if "Eval" in livelli:
        df = df[df["Eval"].isin(v_eval)]
    if "AttkCombination" in livelli:
        df = df[df["AttkCombination"].isin(v_attk_combination)]
    if "StartZone" in livelli:
        df = df[df["StartZone"].isin(v_start)]
    if "StartZoneCompact" in livelli:
        df = df[df["StartZoneCompact"].isin(v_start_compact)]
    if "EndZoneEsecZone" in livelli:
        df = df[df["EndZoneEsecZone"].isin(v_end)]
    if "EndZoneEsecZone3aree" in livelli:
        df = df[df["EndZoneEsecZone3aree"].isin(v_end_3aree)]
    if "EndSubzoneEsecSubzone" in livelli:
        df = df[df["EndSubzoneEsecSubzone"].isin(v_end_sub)]
    if "SkillType" in livelli:
        df = df[df["SkillType"].isin(v_skill)]
    if "PlayersInfo" in livelli:
        df = df[df["PlayersInfo"].isin(v_players)]
    if "Special" in livelli:
        df = df[df["Special"].isin(v_special)]
    if "ThisAppoggio" in livelli:
        df = df[df["ThisAppoggio"].isin(v_this_appoggio)]
    if "EndedInPoint" in livelli:
        df = df[df["EndedInPoint"].isin(v_ended_in_point)]
    if "RuoloCalc" in livelli:
        df = df[df["RuoloCalc"].isin(v_ruolo_calc)]

    if df.empty:
        return go.Figure().update_layout(title="Nessun dato da visualizzare.")

    # Conta occorrenze per ogni valore in ogni livello (nodi)
    nodi_totali = []
    col_stats = []
    for col in livelli:
        cnt = df[col].value_counts().reset_index()
        cnt.columns = ['label', 'count']
        cnt['col'] = col
        cnt['percent'] = cnt['count'] / cnt['count'].sum() * 100
        nodi_totali.append(cnt)
        
        non_null_count = df[~df[col].isna()].shape[0]
        col_stats.append({
            'col': col,
            'count': non_null_count,
            'total': df_attacchi_raw.shape[0],
            'percent': 100 * non_null_count / df_attacchi_raw.shape[0]
        })
        

    nodi_df = pd.concat(nodi_totali, ignore_index=True)
    nodi_df = nodi_df.sort_values(by='label')
    all_labels = nodi_df['label'].unique().tolist()
    label_to_index = {label: i for i, label in enumerate(all_labels)}

    node_customdata = [
        f"{row['count']} tocchi<br>{row['percent']:.1f}%"
        for _, row in nodi_df.iterrows()
    ]

    # Link tra livelli
    df['count'] = 1
    links = []
    for i in range(len(livelli) - 1):
        src = livelli[i]
        tgt = livelli[i + 1]
        df_pair = df.groupby([src, tgt]).size().reset_index(name='value')
        df_pair.columns = ['source', 'target', 'value']
        links.append(df_pair)

    df_links = pd.concat(links, ignore_index=True)

    # Calcolo percentuale relativa dei link rispetto al nodo sorgente
    df_links['percent'] = df_links.groupby('source')['value'].transform(lambda x: x / x.sum() * 100)

    sources = df_links['source'].map(label_to_index)
    targets = df_links['target'].map(label_to_index)
    values = df_links['value']

    link_customdata = [
        f"{v} tocchi<br>{p:.1f}%" for v, p in zip(df_links['value'], df_links['percent'])
    ]
    
    
    # Mappa da colonna a trasparenza (tra 0.01 e 1.0). Serve per modificare la trasparenza dei nodi in base a quante osservazioni ha quella colonna.
    opacity_map = {
        stat['col']: max(0.01, stat['percent'] / 100)
        for stat in col_stats
    }
    
    #Creiamo la lista con i colori per ogni colonna (e quindi per i suoi nodi)
    node_colors = []
    for _, row in nodi_df.iterrows():
        base_color = (2, 70, 107)  # RGB blu Modena
        alpha = opacity_map.get(row['col'], 1.0)
        rgba = f"rgba({base_color[0]}, {base_color[1]}, {base_color[2]}, {alpha:.2f})"
        node_colors.append(rgba)
        

    fig = go.Figure(data=[go.Sankey(
        arrangement="snap",
        node=dict(
            pad=15,
            thickness=20,
            label=all_labels,
            color=node_colors,
            customdata=node_customdata,
            hovertemplate="%{label}<br>%{customdata}<extra></extra>"
        ),
        link=dict(
            source=sources,
            target=targets,
            value=values,
            customdata=link_customdata,
            hovertemplate="%{customdata}<extra></extra>",
            #hovercolor=["yellow"],
            # colore standard → non impostiamo "color"
        )
    )])

    fig.update_layout(title="Sankey - Flusso Attacchi", font_size=10)
    
    n_colonne = len(livelli)
    x_positions = [i / (n_colonne - 1) for i in range(n_colonne)]
    
    for x, nome_colonna in zip(x_positions, livelli):
        fig.add_annotation(
            x=x,
            y=1.05,  # leggermente sopra i nodi
            text=f"<b>{nome_colonna}</b>",
            showarrow=False,
            xanchor='center',
            yanchor='bottom',
            font=dict(size=12, color='black')
        )
    
    return fig

if __name__ == '__main__':
    app.run(debug=True, port=8055)


#Visualizzo i valori unici di AttkCombination
df_attacchi_raw["AttkCombination"].unique()

df_attacchi_raw["StartZone"].isna().sum()

df_attacchi_raw["StartZone"].count()

# Mostra i valori unici della colonna SetterCall 
print(df_alzate_raw["SetterCall"].unique())

#Ma noi li usiamo tutti questi? Probabilmente no, stessa cosa per AttkCombination

# Separiamo attacchi e alzate
df_attacchi = df_raw[df_raw["Foundamental"] == "A"].copy()
df_alzate = df_raw[df_raw["Foundamental"] == "E"].copy()

# Filtra gli attacchi con StartZone NULL
df_attacchi_nullzone = df_attacchi[df_attacchi["StartZone"].isna()].copy()

# Merge su chiavi comuni
merge_keys = ["GameID", "SetNumber", "ActionNumber", "AbsNumeroPossesso"]

df_merged = df_attacchi_nullzone.merge(
    df_alzate[merge_keys + ["EndZoneEsecZone"]],
    on=merge_keys,
    how="left",
    suffixes=("_attacco", "_alzata")
)

# Filtro finale: EndZoneEsecZone non null
df_final = df_merged[df_merged["EndZoneEsecZone_alzata"].notna()]
df_final



#Regole per collegare i fondamentali
regole_abs = {
    #Nelle battute, se prima metto una battuta, prendo sempre le battute, quindi quel possesso
    'S': {
        'S': lambda prev_abs: prev_abs
    },
    #Nelle ricezioni, se prima avevo la battuta, prendo quelle con AbsNumeroPossesso=2
    'R': {
        'R': lambda prev_abs: prev_abs,
        'S': lambda prev_abs: 2                 #La ricezione può solo avere NumAbsPossesso=2 se la battuta è avvenuta nello stesso possesso
    },
    #Nelle difese, se prima avevo R o S prendo quelle con AbsNumeroPossesso= 3. Se prima avevo A, E (tocchi dell'altra squadra) prendo quelle nel possesso dopo.
    'D': {
        'D': lambda prev_abs: prev_abs,
        'R': lambda prev_abs: 3,                #Una difesa dopo ricezione, siccome la ricezione ha NumAbsPossesso=2, la difesa ha NumAbsPossesso=3
        'S': lambda prev_abs: 3,                #Una difesa dopo battuta, siccome va di là e ce la rice, poi se torna di qua ha NumAbsPossesso=3
        'E': lambda prev_abs: prev_abs + 1,    
        'A': lambda prev_abs: prev_abs + 1     #Attenzione: stiamo contando solo le difese del possesso dopo un attacco. Quindi se una squadra attacca, viene murata, e la squadra che ha attaccato la copre, non stiamo contando quella copertura (difesa)
        #'B': qui siccome non sappiamo se dopo il muro la prende in difesa la squadra che attacca o che mura, prendiamo la difesa subito dopo il muro
    },
    #Nelle FreeBall è come nelle difese
    'F': {
        'F': lambda prev_abs: prev_abs,
        'R': lambda prev_abs: 3,                #Una FreeBall dopo ricezione, siccome la ricezione ha NumAbsPossesso=2, la difesa ha NumAbsPossesso=3
        'S': lambda prev_abs: 3,                #Una FreeBall dopo battuta, siccome va di là e ce la rice, poi se torna di qua ha NumAbsPossesso=3
        'E': lambda prev_abs: prev_abs + 1,    
        'A': lambda prev_abs: prev_abs + 1     
        #'B': qui siccome non sappiamo se dopo il muro la prende in difesa la squadra che attacca o che mura, prendiamo la difesa subito dopo il muro
    },
    #Nelle alzate, se prima avevo la R, D, F, prendo quelle nello stesso possesso. Se invece prima c'è S, A, B (tocchi dell'altra squadra), prendo quelle con AbsNumeroPossesso+1
    'E': {
        'E': lambda prev_abs: prev_abs,
        'R': lambda prev_abs: prev_abs,
        'D': lambda prev_abs: prev_abs,
        'F': lambda prev_abs: prev_abs,
        'S': lambda prev_abs: prev_abs + 1,
        'A': lambda prev_abs: prev_abs + 1,
        'B': lambda prev_abs: prev_abs + 1
    },
    #Nell'attacco, se prima avevo la R, E, D, F prendo quelli nello stesso possesso. Se invece prima c'è S o B (tocchi dell'altra squadra) prendo quelle con AbsNumeroPossesso+1
    'A': {
        'A': lambda prev_abs: prev_abs,
        'R': lambda prev_abs: prev_abs,
        'E': lambda prev_abs: prev_abs,
        'D': lambda prev_abs: prev_abs,
        'F': lambda prev_abs: prev_abs,
        'S': lambda prev_abs: prev_abs + 1,
        'B': lambda prev_abs: prev_abs + 1
    },
    #Nel muro, se prima avevo la R, E, A, D, F (tocchi dell'altra squadra) prendo quelle con AbsNumeroPossesso+1. Se invece prima c'è S (tocchi della stessa squadra due possessi fa) prendo quelle con AbsNumeroPossesso+2
    'B': {
        'B': lambda prev_abs: prev_abs,
        'R': lambda prev_abs: prev_abs + 1,
        'E': lambda prev_abs: prev_abs + 1,
        'A': lambda prev_abs: prev_abs + 1,
        'D': lambda prev_abs: prev_abs + 1,
        'F': lambda prev_abs: prev_abs + 1,
        'S': lambda prev_abs: prev_abs + 2
    },

#Per ora ho messo che se nel sankey la colonna prima è un fondamentale, e nella colonna 2 voglio rimettere quel fondamentale, metto quelli dello stesso possesso, ovvero lo stesso tocco, per vederne caratteristiche diverse. Posso anche mettere di fare quello del possesso dopo, per vedere come quelli fatti prima influenzano quelli del possesso dopo.
}


#Mostra i valori unici di CustomChar quando Foundamental = 'A'
print(df_raw[df_raw["Foundamental"] == 'S']["correctCustomChar"].unique())

df_raw["Foundamental"][0]

df_raw[df_raw['Foundamental'] == 'A'].groupby(
    ['GameID', 'SetNumber', 'ActionNumber', 'AbsNumeroPossesso']
).size().reset_index(name='n_attacchi').query('n_attacchi > 1')

#GRAVISSIMO. Ci sono delle partite in cui ci sono più attacchi nello stesso possesso! Probabilmente ci sono dei bug nella lettura del dvw al database.
#Altre volte è un problema del file, come nel caso di "&23-12-02 and08 PER-CIS 3-1_bm.dvw"
#Infatti bisognerebbe fare dei controlli che tutto vada bene, provare a rimuovere e reinserire quelle partite, e se il problema persiste, aggiustarle.


def sankey_coppia(df_raw):

    # Caratteristiche disponibili per ogni fondamentale
    fond_characteristics = {
        'S': ['Type', 'Eval', 'StartZone', 'StartZoneCompact', 'EndZoneEsecZone', 'EndSubzoneEsecSubzone', 'Special', 'correctCustomChar', 'correctCustomCharAggregate', 'EndedInPoint', 'NumPossesso', 'NomeCompleto', 'TeamNameShort', 'RuoloCalc', 'RotazionePropria', 'RotazioneContro'],
        'R': ['Type', 'Eval', 'StartZone', 'StartZoneCompact', 'EndZoneEsecZone', 'EndSubzoneEsecSubzone', 'SkillType', 'PlayersInfo', 'Special', 'ThisAppoggio', 'NextAttkType', 'NextAttkEval', 'EndedInPoint', 'NumPossesso', 'RuoloCalc', 'NomeCompleto', 'TeamNameShort', 'BattutaPropria', 'RotazionePropria', 'RotazioneContro'],
        'E': ['Type', 'Eval', 'SetterCall', 'TargAttk', 'EndZoneEsecZone', 'EndSubzoneEsecSubzone', 'SkillType', 'Special', 'correctCustomChar', 'correctCustomCharAggregate', 'ThisSetType', 'ThisSetTypeAggregate', 'ThisAppoggio', 'NextAttkType', 'NextAttkEval', 'EndedInPoint', 'NumPossesso', 'RuoloCalc', 'NomeCompleto', 'TeamNameShort', 'BattutaPropria', 'RotazionePropria', 'RotazioneContro'],
        'A': ['Type', 'Eval', 'AttkCombination', 'StartZone', 'StartZoneCompact', 'EndZoneEsecZone', 'EndSubzoneEsecSubzone', 'SkillType', 'PlayersInfo', 'Special', 'correctCustomChar', 'correctCustomCharAggregate', 'ThisAppoggio', 'NextAttkType', 'NextAttkEval', 'NextNextAttkType', 'NextNextAttkEval', 'EndedInPoint', 'NumPossesso', 'RuoloCalc', 'NomeCompleto', 'TeamNameShort', 'BattutaPropria', 'RotazionePropria', 'RotazioneContro'],
        'B': ['Type', 'Eval', 'EndZoneEsecZone', 'EndSubzoneEsecSubzone', 'SkillType', 'PlayersInfo', 'Special', 'ThisAppoggio', 'NextAttkType', 'NextAttkEval', 'PrevAppoggio', 'EndedInPoint', 'NumPossesso', 'RuoloCalc', 'NomeCompleto', 'TeamNameShort', 'BattutaPropria', 'RotazionePropria', 'RotazioneContro'],
        'D': ['Type', 'Eval', 'StartZone', 'StartZoneCompact', 'EndZoneEsecZone', 'EndSubzoneEsecSubzone', 'SkillType', 'correctSkillType', 'Special', 'ThisAppoggio', 'NextAttkType', 'NextAttkEval', 'EndedInPoint', 'NumPossesso', 'RuoloCalc', 'NomeCompleto', 'TeamNameShort', 'BattutaPropria', 'RotazionePropria', 'RotazioneContro'],
        'F': ['Type', 'Eval', 'EndZoneEsecZone', 'EndSubzoneEsecSubzone', 'Special', 'ThisAppoggio', 'NextAttkType', 'NextAttkEval', 'EndedInPoint', 'NumPossesso', 'RuoloCalc', 'NomeCompleto', 'TeamNameShort', 'BattutaPropria', 'RotazionePropria', 'RotazioneContro']
    }


    # Inizializza l'app Dash
    app = Dash(__name__)
    app.title = "Sankey Diagram Interattivo - Volley"

    # Layout dell'app
    app.layout = html.Div([
        html.H1("🏐 Diagramma Sankey Interattivo - Analisi Volley", 
                style={'textAlign': 'center', 'color': '#2c3e50', 'marginBottom': 30}),
        
        # Container principale
        html.Div([
            # Colonna sinistra - Selettori Source
            html.Div([
                html.H3("🏐 Primo Tocco (Source)", style={'color': '#3498db'}),
                
                html.Label("Fondamentale:", style={'fontWeight': 'bold', 'marginTop': 10}),
                dcc.Dropdown(
                    id='source-fondamentale',
                    options=[
                        {'label': 'S - Battuta', 'value': 'S'},
                        {'label': 'R - Ricezione', 'value': 'R'},
                        {'label': 'E - Alzata', 'value': 'E'},
                        {'label': 'A - Attacco', 'value': 'A'},
                        {'label': 'B - Muro', 'value': 'B'},
                        {'label': 'D - Difesa', 'value': 'D'},
                        {'label': 'F - FreeBall', 'value': 'F'}
                    ],
                    value='S',
                    style={'marginBottom': 15}
                ),
                
                html.Label("Caratteristica:", style={'fontWeight': 'bold'}),
                dcc.Dropdown(
                    id='source-caratteristica',
                    value='Type',
                    style={'marginBottom': 15}
                )
            ], style={'width': '45%', 'display': 'inline-block', 'verticalAlign': 'top', 
                    'padding': '20px', 'backgroundColor': '#ecf0f1', 'borderRadius': '10px', 'margin': '10px'}),
            
            # Colonna destra - Selettori Target
            html.Div([
                html.H3("🎯 Secondo Tocco (Target)", style={'color': '#e74c3c'}),
                
                html.Label("Fondamentale:", style={'fontWeight': 'bold', 'marginTop': 10}),
                dcc.Dropdown(
                    id='target-fondamentale',
                    options=[
                        {'label': 'S - Battuta', 'value': 'S'},
                        {'label': 'R - Ricezione', 'value': 'R'},
                        {'label': 'E - Alzata', 'value': 'E'},
                        {'label': 'A - Attacco', 'value': 'A'},
                        {'label': 'B - Muro', 'value': 'B'},
                        {'label': 'D - Difesa', 'value': 'D'},
                        {'label': 'F - FreeBall', 'value': 'F'}
                    ],
                    value='R',
                    style={'marginBottom': 15}
                ),
                
                html.Label("Caratteristica:", style={'fontWeight': 'bold'}),
                dcc.Dropdown(
                    id='target-caratteristica',
                    value='Type',
                    style={'marginBottom': 15}
                )
            ], style={'width': '45%', 'display': 'inline-block', 'verticalAlign': 'top',
                    'padding': '20px', 'backgroundColor': '#ecf0f1', 'borderRadius': '10px', 'margin': '10px'})
        ], style={'textAlign': 'center'}),
        
        # Sezione info e debug
        html.Div([
            html.H4("📊 Informazioni Debug"),
            html.Div(id='debug-info', style={
                'margin': '20px', 'padding': '15px', 'border': '1px solid #bdc3c7',
                'borderRadius': '5px', 'backgroundColor': '#f8f9fa', 'textAlign': 'left'
            })
        ]),
        
        # Grafico Sankey
        dcc.Graph(id='sankey-graph', style={'height': '700px'})
    ])

    # Callback per aggiornare le opzioni delle caratteristiche source
    @app.callback(
        Output('source-caratteristica', 'options'),
        Output('source-caratteristica', 'value'),
        Input('source-fondamentale', 'value')
    )
    def update_source_caratteristiche(fondamentale):
        if fondamentale is None:
            return [], None
        
        caratteristiche = fond_characteristics.get(fondamentale, [])
        options = [{'label': c, 'value': c} for c in caratteristiche]
        value = caratteristiche[0] if caratteristiche else None
        
        return options, value

    # Callback per aggiornare le opzioni delle caratteristiche target
    @app.callback(
        Output('target-caratteristica', 'options'),
        Output('target-caratteristica', 'value'),
        Input('target-fondamentale', 'value')
    )
    def update_target_caratteristiche(fondamentale):
        if fondamentale is None:
            return [], None
        
        caratteristiche = fond_characteristics.get(fondamentale, [])
        options = [{'label': c, 'value': c} for c in caratteristiche]
        value = caratteristiche[0] if caratteristiche else None
        
        return options, value

    # Callback principale per creare il diagramma Sankey
    @app.callback(
        Output('sankey-graph', 'figure'),
        Output('debug-info', 'children'),
        Input('source-fondamentale', 'value'),
        Input('source-caratteristica', 'value'),
        Input('target-fondamentale', 'value'),
        Input('target-caratteristica', 'value')
    )
    def update_sankey_graph(source_fond, source_char, target_fond, target_char):
        debug_messages = []

        if not all([source_fond, source_char, target_fond, target_char]):
            debug_messages.append("⚠️ Seleziona tutti i parametri")
            return go.Figure(), html.Div([html.P(msg) for msg in debug_messages])

        debug_messages.append(f"🔍 Analisi: {source_fond}({source_char}) → {target_fond}({target_char})")

        if 'df_raw' not in globals() or df_raw.empty:
            debug_messages.append("❌ Dati non disponibili")
            return go.Figure(), html.Div([html.P(msg) for msg in debug_messages])

        debug_messages.append(f"✅ Dataset: {len(df_raw)} righe totali")

        if target_fond not in regole_abs or source_fond not in regole_abs[target_fond]:
            debug_messages.append(f"❌ Nessuna regola definita da {source_fond} a {target_fond}")
            regole_disponibili = [f"{src} → {tgt}" for tgt, srcs in regole_abs.items() for src in srcs]
            debug_messages.append("Regole disponibili: " + ", ".join(regole_disponibili))
            return go.Figure(), html.Div([html.P(msg) for msg in debug_messages])

        df1 = df_raw[df_raw['Foundamental'] == source_fond].copy()
        df2 = df_raw[df_raw['Foundamental'] == target_fond].copy()

        debug_messages.append(f"📊 {source_fond}: {len(df1)} righe, {target_fond}: {len(df2)} righe")

        if len(df1) == 0 or len(df2) == 0:
            debug_messages.append("❌ Uno dei fondamentali non ha dati")
            return go.Figure(), html.Div([html.P(msg) for msg in debug_messages])

        if source_char not in df1.columns:
            debug_messages.append(f"❌ Caratteristica '{source_char}' non trovata in {source_fond}")
            return go.Figure(), html.Div([html.P(msg) for msg in debug_messages])

        if target_char not in df2.columns:
            debug_messages.append(f"❌ Caratteristica '{target_char}' non trovata in {target_fond}")
            return go.Figure(), html.Div([html.P(msg) for msg in debug_messages])

        # Merge basato su GameID, SetNumber, ActionNumber
        df_merged = pd.merge(
            df1,
            df2,
            on=['GameID', 'SetNumber', 'ActionNumber'],
            suffixes=('_source', '_target')
        )

        # Calcola expected AbsNumeroPossesso
        expected_abs_func = regole_abs[target_fond][source_fond]
        df_merged = df_merged[df_merged['AbsNumeroPossesso_target'] == df_merged['AbsNumeroPossesso_source'].apply(expected_abs_func)]

        debug_messages.append(f"🔗 Match validi trovati: {len(df_merged)}")

        # Estrai le coppie source → target
        df_merged = df_merged[[source_char + '_source', target_char + '_target']].dropna()
        df_merged.columns = ['source', 'target']
        df_merged = df_merged[(df_merged['source'] != '') & (df_merged['target'] != '')]

        if df_merged.empty:
            debug_messages.append("❌ Nessun collegamento valido trovato")
            return go.Figure(), html.Div([html.P(msg) for msg in debug_messages])

        # Raggruppa per conteggi
        df_links = df_merged.groupby(['source', 'target']).size().reset_index(name='value')
        debug_messages.append(f"📊 Collegamenti unici: {len(df_links)}")
        debug_messages.append(f"📊 Valori source: {', '.join(df_links['source'].unique()[:10])}")
        debug_messages.append(f"📊 Valori target: {', '.join(df_links['target'].unique()[:10])}")

        # Conta occorrenze totali per etichette
        source_counts = df_links.groupby('source')['value'].sum()
        target_counts = df_links.groupby('target')['value'].sum()
        source_labels = [f"{source_fond} {label}" for label in df_links['source'].unique()]  #Metto le etichette nei nodi source
        target_labels = [f"{target_fond} {label}" for label in df_links['target'].unique()]  #Metto le etichette nei nodi target
        all_labels = source_labels + target_labels

        # Hover per nodi
        source_hover = [
            f"<br>Totale osservazioni: {source_counts[label]}<br>Percentuale: {source_counts[label]/source_counts.sum()*100:.1f}%"
            for label in df_links['source'].unique()
        ]
        target_hover = [
            f"<br>Totale osservazioni: {target_counts[label]}<br>Percentuale: {target_counts[label]/target_counts.sum()*100:.1f}%"
            for label in df_links['target'].unique()
        ]
        all_hover = source_hover + target_hover

        # Mappa per index
        source_map = {val: idx for idx, val in enumerate(df_links['source'].unique())}
        target_map = {val: idx + len(source_map) for idx, val in enumerate(df_links['target'].unique())}

        # Presto calcoli il totale delle uscite per ogni nodo source
        total_links = df_links['value'].sum()
        outgoing_totals = df_links.groupby('source')['value'].sum().to_dict()
        link_hover = []
        #In ogni link calcolo la percentuale rispetto al totale di osservazioni del proprio nodo
        for _, row in df_links.iterrows():
            src = row['source']
            val = row['value']
            pct_from_src = val / outgoing_totals[src] * 100
            #Definisco cosa deve comparire nei link quando faccio hover
            link_hover.append(
                f"{src} → {row['target']}<br>"
                f"Osservazioni: {val}<br>"
                f"Percentuale su {src}: {pct_from_src:.1f}%"
            )

        debug_messages.append(f"📈 Totale osservazioni nei collegamenti: {total_links}")
        debug_messages.append(f"📈 Media osservazioni per collegamento: {total_links/len(df_links):.1f}")
        top_link = df_links.loc[df_links['value'].idxmax()]
        debug_messages.append(f"📈 Collegamento più frequente: {top_link['source']} → {top_link['target']} ({top_link['value']} obs)")

        # Sankey
        fig = go.Figure(data=[go.Sankey(
            arrangement="snap",
            node=dict(
                label=all_labels,
                pad=15,
                thickness=20,
                line=dict(color="black", width=0.5),
                color=["lightblue"] * len(source_labels) + ["lightcoral"] * len(target_labels),
                hovertemplate='%{customdata}<extra></extra>',
                customdata=all_hover
            ),
            link=dict(
                source=[source_map[s] for s in df_links['source']],
                target=[target_map[t] for t in df_links['target']],
                value=df_links['value'].tolist(),
                hovertemplate='%{customdata}<extra></extra>',
                customdata=link_hover
            )
        )])

        fig.update_layout(
            title_text=f"{source_fond} ({source_char}) → {target_fond} ({target_char})<br>Totale osservazioni: {total_links}",
            font_size=12,
            height=650
        )

        debug_messages.append("✅ Diagramma creato con successo!")

        return fig, html.Div([html.P(msg) for msg in debug_messages])


    # Avvia l'app (solo se eseguito come script)
    if __name__ == '__main__':
        app.run(debug=True, port=8059)


sankey_coppia(df_raw)









# 1. Query dei tocchi rilevanti
df = con.execute("""
SELECT
    "GameID",
    "AbsNumeroPossesso",
    "Foundamental",
    "Eval",
    "RilevationNumber"
FROM rilevations_libero_view_duckdb
WHERE "Foundamental" IN ('R', 'E', 'A', 'B')
  AND "Eval" IS NOT NULL
ORDER BY "GameID", "AbsNumeroPossesso", "RilevationNumber"
""").df()

# Raggruppa per GameID e AbsNumeroPossesso (cioè ogni possesso)
triplette = []

for _, group in df.groupby(['GameID', 'AbsNumeroPossesso']):
    group = group.sort_values('RilevationNumber')
    evals = group['Eval'].tolist()
    fondamentali = group['Foundamental'].tolist()

    # Cerca sequenza R, E, A nell’ordine corretto
    try:
        idx_r = fondamentali.index('R')
        idx_e = fondamentali.index('E', idx_r + 1)
        idx_a = fondamentali.index('A', idx_e + 1)

        triplette.append({
            'Eval_R': evals[idx_r],
            'Eval_E': evals[idx_e],
            'Eval_A': evals[idx_a]
        })
    except ValueError:
        # Se manca una delle tre, salta
        continue

df_triplette = pd.DataFrame(triplette)


# Contiamo R→E e E→A separatamente
df_links_RE = (
    df_triplette.groupby(['Eval_R', 'Eval_E'])
    .size().reset_index(name='value')
    .rename(columns={'Eval_R': 'source', 'Eval_E': 'target'})
)

df_links_EA = (
    df_triplette.groupby(['Eval_E', 'Eval_A'])
    .size().reset_index(name='value')
    .rename(columns={'Eval_E': 'source', 'Eval_A': 'target'})
)


eval_order = ['#', '+', '/', '!', '-', '=']

labels_R = [f'R_{e}' for e in eval_order]
labels_E = [f'E_{e}' for e in eval_order]
labels_A = [f'A_{e}' for e in eval_order]
labels = labels_R + labels_E + labels_A

label_to_index = {lab: i for i, lab in enumerate(labels)}

# R→E
sources_RE = df_links_RE['source'].apply(lambda x: label_to_index[f'R_{x}'])
targets_RE = df_links_RE['target'].apply(lambda x: label_to_index[f'E_{x}'])
values_RE = df_links_RE['value']

# E→A
sources_EA = df_links_EA['source'].apply(lambda x: label_to_index[f'E_{x}'])
targets_EA = df_links_EA['target'].apply(lambda x: label_to_index[f'A_{x}'])
values_EA = df_links_EA['value']

# Unisci tutti i link
sources = pd.concat([sources_RE, sources_EA])
targets = pd.concat([targets_RE, targets_EA])
values = pd.concat([values_RE, values_EA])


fig = go.Figure(data=[go.Sankey(
    arrangement="snap",
    node=dict(
        pad=15,
        thickness=20,
        label=labels,
        color='cornflowerblue'
    ),
    link=dict(
        source=sources,
        target=targets,
        value=values,
        color='lightgray'
    )
)])

fig.update_layout(title_text="Flusso Ricezione → Alzata → Attacco", font_size=10)
fig.show()




df_links







