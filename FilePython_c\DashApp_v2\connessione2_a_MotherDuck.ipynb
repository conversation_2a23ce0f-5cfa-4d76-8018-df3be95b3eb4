{"cells": [{"cell_type": "code", "execution_count": 3, "id": "713336f2", "metadata": {}, "outputs": [], "source": ["import duckdb\n", "\n", "token_database_motherduck = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************.Dl_50BGVlT2f1hIIIWyJoHe-aSALpFZFx911eo5kyv4\"\n", "\n", "con = duckdb.connect('md:DatabaseModena', config={\"motherduck_token\": token_database_motherduck})\n"]}, {"cell_type": "code", "execution_count": 2, "id": "8327caba", "metadata": {}, "outputs": [{"data": {"text/plain": ["[('DatabaseModena',), ('md_information_schema',)]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["con.execute(\"SHOW DATABASES\").fetchall()"]}, {"cell_type": "code", "execution_count": 8, "id": "a2be3827", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>TeamProprio</th>\n", "      <th>TeamProprioNameShort</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2022</td>\n", "      <td>1</td>\n", "      <td>Modena</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2022</td>\n", "      <td>2</td>\n", "      <td>Milano</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2022</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2022</td>\n", "      <td>4</td>\n", "      <td>Perugia</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2022</td>\n", "      <td>5</td>\n", "      <td>Piacenza</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>60</th>\n", "      <td>2024</td>\n", "      <td>10</td>\n", "      <td>Grottazzolina</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61</th>\n", "      <td>2024</td>\n", "      <td>11</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>62</th>\n", "      <td>2024</td>\n", "      <td>12</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>63</th>\n", "      <td>2024</td>\n", "      <td>14</td>\n", "      <td>Monza</td>\n", "    </tr>\n", "    <tr>\n", "      <th>64</th>\n", "      <td>2024</td>\n", "      <td>15</td>\n", "      <td>Verona</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>65 rows × 3 columns</p>\n", "</div>"], "text/plain": ["    Annata  TeamProprio TeamProprioNameShort\n", "0     2022            1               Modena\n", "1     2022            2               Milano\n", "2     2022            3              Taranto\n", "3     2022            4              Perugia\n", "4     2022            5             Piacenza\n", "..     ...          ...                  ...\n", "60    2024           10        Grottazzolina\n", "61    2024           11             Trentino\n", "62    2024           12               Padova\n", "63    2024           14                Monza\n", "64    2024           15               Verona\n", "\n", "[65 rows x 3 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df = con.execute('SELECT * FROM teams_each_season_view').fetchdf()\n", "df"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}