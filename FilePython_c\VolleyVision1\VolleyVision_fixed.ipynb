{"cells": [{"cell_type": "markdown", "metadata": {"id": "vpxmQ-xNB1VY"}, "source": ["<p align=\"center\">\n", "<img src=\"https://raw.githubusercontent.com/shukkkur/VolleyVision/main/assets/vv_logo.png\">\n", "</p>\n", "<h1 align=\"center\">Welcome to VolleyVision<br><code>Local</code> - Quick & Easy</h1>"]}, {"cell_type": "markdown", "metadata": {"id": "Qup7yoz6eBqs"}, "source": ["- The VolleyVision repository is already cloned in your system"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "xOgqrFwMBqOF"}, "outputs": [], "source": ["# Repository is already cloned, no need to clone again\n", "# !git clone https://github.com/shukkkur/VolleyVision.git"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "IHjPltCgeARn"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[WinError 3] Impossibile trovare il percorso specificato: 'FilePython_c\\\\VolleyVision'\n", "c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\FilePython_c\n"]}], "source": ["# change directory to VolleyVision\n", "%cd FilePython_c\\VolleyVision"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["python: can't open file 'c:\\\\Users\\\\<USER>\\\\Documents\\\\ModenaVolley\\\\Codice\\\\FilePython_c\\\\detect.py': [Errno 2] No such file or directory\n"]}], "source": ["!python detect.py --input_path assets/coach_donny.mp4 --input_type video --output Output/out_coach.mp4 --model roboflow --marker box --color blue --confidence 0.5 --no_trace"]}, {"cell_type": "markdown", "metadata": {"id": "QTR-KqDagxLu"}, "source": ["### 1. Volleyball Detection and Tracking"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "LpZkANPhgwdP"}, "outputs": [], "source": ["# Change to the Stage I - Volleyball directory using the correct path\n", "%cd \"Stage I - Volleyball\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "OaxcdF2w1yg4"}, "outputs": [], "source": ["# Install required packages from requirements.txt in the current directory\n", "!pip install -r requirements.txt"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Yx-Ue-Uw2Aqm"}, "outputs": [], "source": ["# Import necessary libraries\n", "import os\n", "import cv2\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from IPython.display import HTML\n", "from base64 import b64encode"]}, {"cell_type": "markdown", "metadata": {"id": "Yx-Ue-Uw2Aqm"}, "source": ["### 2. Download a volleyball video for testing\n", "\n", "You can use your own volleyball video or download one from the internet. Place it in the current directory."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Yx-Ue-Uw2Aqm"}, "outputs": [], "source": ["# Example: Download a sample volleyball video if needed\n", "# !wget -O volleyball_sample.mp4 https://example.com/path/to/volleyball_video.mp4\n", "\n", "# Or you can use a video from your local system\n", "# video_path = \"path/to/your/volleyball_video.mp4\""]}, {"cell_type": "markdown", "metadata": {"id": "Yx-Ue-Uw2Aqm"}, "source": ["### 3. Run Volleyball Detection and Tracking"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Yx-Ue-Uw2Aqm"}, "outputs": [], "source": ["# Check if models directory exists and has the required files\n", "if not os.path.exists('models'):\n", "    os.makedirs('models')\n", "\n", "# Check if the YOLOv7 weights file exists, if not download it\n", "if not os.path.exists('models/yolov7-tiny.pt'):\n", "    !wget -P models https://github.com/WongKinYiu/yolov7/releases/download/v0.1/yolov7-tiny.pt"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Yx-Ue-Uw2Aqm"}, "outputs": [], "source": ["# Function to display video in the notebook\n", "def show_video(video_path, width=640):\n", "    video_file = open(video_path, \"r+b\").read()\n", "    video_url = f\"data:video/mp4;base64,{b64encode(video_file).decode()}\"\n", "    return HTML(f\"\"\"\n", "    <video width={width} controls>\n", "        <source src=\"{video_url}\" type=\"video/mp4\">\n", "    </video>\"\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Yx-Ue-Uw2Aqm"}, "outputs": [], "source": ["# Run volleyball detection and tracking on the sample video\n", "!python volley_track.py --source sample_volleyball.mp4 --weights models/yolov7-tiny.pt --conf 0.25 --save-txt --save-conf"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Yx-Ue-Uw2Aqm"}, "outputs": [], "source": ["# Display the output video if it was created\n", "output_path = 'Output/sample_volleyball.mp4'\n", "if os.path.exists(output_path):\n", "    show_video(output_path)\n", "else:\n", "    print(\"Output video not found. Check if the processing completed successfully.\")"]}, {"cell_type": "markdown", "metadata": {"id": "Yx-Ue-Uw2Aqm"}, "source": ["### 4. Analyze the Results\n", "\n", "After running the detection and tracking, you can analyze the results."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Yx-Ue-Uw2Aqm"}, "outputs": [], "source": ["# If tracking data was saved, you can load and analyze it\n", "# Example: Plot the trajectory of the volleyball\n", "\n", "# This is a placeholder - you'll need to adapt this to your specific output format\n", "def plot_trajectory(tracking_data_path):\n", "    if os.path.exists(tracking_data_path):\n", "        # Load tracking data (format depends on your output)\n", "        # This is just an example\n", "        data = np.loadtxt(tracking_data_path, delimiter=',')\n", "        \n", "        # Plot trajectory\n", "        plt.figure(figsize=(10, 6))\n", "        plt.plot(data[:, 1], data[:, 2], 'b-', linewidth=2)\n", "        plt.title('Volleyball Trajectory')\n", "        plt.xlabel('X position')\n", "        plt.ylabel('Y position')\n", "        plt.grid(True)\n", "        plt.show()\n", "    else:\n", "        print(f\"Tracking data not found at {tracking_data_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Yx-Ue-Uw2Aqm"}, "outputs": [], "source": ["# Example usage:\n", "# plot_trajectory('Output/tracking_data.txt')"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 0}