import os
import sys
import requests
from tqdm import tqdm

def download_file(url, destination):
    """
    Download a file from a URL with a progress bar
    """
    response = requests.get(url, stream=True)
    total_size = int(response.headers.get('content-length', 0))
    block_size = 1024  # 1 Kibibyte
    
    # Create the directory if it doesn't exist
    os.makedirs(os.path.dirname(destination), exist_ok=True)
    
    print(f"Downloading {url} to {destination}")
    
    with open(destination, 'wb') as file, tqdm(
            desc=os.path.basename(destination),
            total=total_size,
            unit='iB',
            unit_scale=True,
            unit_divisor=1024,
        ) as bar:
        for data in response.iter_content(block_size):
            size = file.write(data)
            bar.update(size)

def main():
    # Sample volleyball video URL
    video_url = "https://github.com/shukkkur/VolleyVision/raw/main/assets/back_view.mp4"
    
    # Destination path
    destination = os.path.join('FilePython_c', 'VolleyVision', 'Stage I - Volleyball', 'sample_volleyball.mp4')
    
    try:
        download_file(video_url, destination)
        print(f"Successfully downloaded sample video to {destination}")
        return True
    except Exception as e:
        print(f"Error downloading sample video: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
