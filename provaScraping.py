from bs4 import BeautifulSoup
import requests
import random
import time

def find_volleybox_url(name, surname):
    query = f"site:volleybox.net/it {name} {surname}"
    url = "https://www.google.com/search"
    
    headers = {
        "User-Agent": random.choice([
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.109 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.54 Safari/537.36"
        ]),
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5"
    }
    
    parameters = {'q': query}

    try:
        print(f"Searching for: {query}")
        time.sleep(random.uniform(5, 9))
        
        response = requests.get(url, headers=headers, params=parameters)
        response.raise_for_status()
        
        print(f"Response status code: {response.status_code}")
        soup = BeautifulSoup(response.text, "html.parser")
        
        # Extract all links from the page
        links = soup.find_all('a')
        print(f"Total links found: {len(links)}")
        
        for link in links:
            print(link)
            href = link.get('href', '')
            # Only process URLs that start with http and contain volleybox.net/it
            if href.startswith('http') and 'volleybox.net/it' in href:
                # Extract the actual URL if it's in Google's redirect format
                if 'url?q=' in href:
                    actual_url = href.split('url?q=')[1].split('&')[0]
                else:
                    actual_url = href
                    
                print(f"Found potential URL: {actual_url}")
                if 'volleybox.net/it' in actual_url:
                    print(f"Found valid Volleybox URL: {actual_url}")
                    return actual_url

        # Fallback: try direct URL
        direct_url = f"https://volleybox.net/it/{name.lower()}-{surname.lower()}"
        print(f"Trying direct URL: {direct_url}")
        
        direct_response = requests.get(direct_url, headers=headers)
        if direct_response.status_code == 200:
            print(f"Direct URL exists: {direct_url}")
            return direct_url
            
        print("No Volleybox URL found")
        return None
        
    except requests.RequestException as e:
        print(f"Request error: {e}")
        return None
    except Exception as e:
        print(f"An error occurred: {e}")
        print(f"Error type: {type(e)}")
        return None


# Example usage
name = "Wilfredo"
surname = "Leon"
result = find_volleybox_url(name, surname)

if result:
    print(f"\nFinal result - Found URL: {result}")
else:
    print("\nFinal result: No URL found")
    
    