{"cells": [{"cell_type": "code", "execution_count": 20, "id": "a64af8c4", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import psycopg\n", "from sqlalchemy import create_engine, text\n", "\n", "import requests\n", "import re\n", "from bs4 import BeautifulSoup\n", "\n"]}, {"cell_type": "code", "execution_count": 21, "id": "9c850ca8", "metadata": {}, "outputs": [], "source": ["#Connessione al database\n", "conn = psycopg.connect(\n", "    dbname=\"db_modena\",           # database creato in pgAdmin4\n", "    user=\"postgres\",              # Il tuo nome utente PostgreSQL\n", "    password=\"AcquaLevissima1\",   # La password che hai scelto per 'postgres'\n", "    host=\"localhost\",             # 'localhost' se è sul tuo PC\n", "    port=5432                     # La porta predefinita è 5432\n", ")\n", "\n", "cur = conn.cursor()\n", "engine = create_engine('postgresql+psycopg://postgres:AcquaLevissima1@localhost:5432/db_modena')"]}, {"cell_type": "markdown", "id": "5b19e53a", "metadata": {}, "source": ["# Scraping calendario LegaVolley 2016\n", "In questa sezione effettuiamo lo scraping del calendario dal sito LegaVolley per la stagione 2016."]}, {"cell_type": "code", "execution_count": null, "id": "81990d60", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<!doctype html >\n", "<!--[if IE 8]>    <html class=\"ie8\" lang=\"en\"> <![endif]-->\n", "<!--[if IE 9]>    <html class=\"ie9\" lang=\"en\"> <![endif]-->\n", "<!--[if gt IE 8]><!--> <html lang=\"it-IT\"> <!--<![endif]-->\n", "<head>\n", "    <title>Lega Pallavolo Serie A</title>\n", "    <meta charset=\"UTF-8\" />\n", "\t<link rel='stylesheet' href='/css/global.css?v=1' type='text/css' media='all' />\n", "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n", "    <link rel=\"pingback\" href=\"https://www.legavolley.it/xmlrpc.php\" />\n", "    <link rel=\"icon\" type=\"image/png\" href=\"/wp-content/uploads/2017/06/favicon.ico\"><link rel='dns-prefetch' href='//fonts.googleapis.com' />\n", "<link rel='dns-prefetch' href='//s.w.org' />\n", "<link rel=\"alternate\" type=\"application/rss+xml\" title=\"Lega Pallavolo Serie A &raquo; Feed\" href=\"https://www.legavolley.it/feed/\" />\n", "<link rel=\"alternate\" type=\"application/rss+xml\" title=\"Lega Pallavolo Serie A &raquo; Feed dei commenti\" href=\"https://www.legavolley.it/comments/feed/\" />\n", "\t\t<script type=\"text/javascript\">\n", "\t\t\twindow._wpemojiSettings = {\"baseUrl\":\"https:\\/\\/s.w.org\\/images\\/core\\/emoji\\/11\\/72x72\\/\",\"ext\":\".png\",\"svgUrl\":\"https:\\/\\/s.w.org\\/images\\/core\\/emoji\\/11\\/svg\\/\",\"svgExt\":\".svg\",\"source\":{\"concatemoji\":\"https:\\/\\/www.legavolley.it\\/wp-includes\\/js\\/wp-emoji-release.min.js?ver=c2483391c2f5fe8e166b0aeee18f8eb2\"}};\n", "\t\t\t!function(e,a,t){var n,r,o,i=a.createElement(\"canvas\"),p=i.getContext&&i.getContext(\"2d\");function s(e,t){var a=String.fromCharCode;p.clearRect(0,0,i.width,i.height),p.fillText(a.apply(this,e),0,0);e=i.toDataURL();return p.clearRect(0,0,i.width,i.height),p.fillText(a.apply(this,t),0,0),e===i.toDataURL()}function c(e){var t=a.createElement(\"script\");t.src=e,t.defer=t.type=\"text/javascript\",a.getElementsByTagName(\"head\")[0].appendChild(t)}for(o=Array(\"flag\",\"emoji\"),t.supports={everything:!0,everythingExceptFlag:!0},r=0;r<o.length;r++)t.supports[o[r]]=function(e){if(!p||!p.fillText)return!1;switch(p.textBaseline=\"top\",p.font=\"600 32px Arial\",e){case\"flag\":return s([55356,56826,55356,56819],[55356,56826,8203,55356,56819])?!1:!s([55356,57332,56128,56423,56128,56418,56128,56421,56128,56430,56128,56423,56128,56447],[55356,57332,8203,56128,56423,8203,56128,56418,8203,56128,56421,8203,56128,56430,8203,56128,56423,8203,56128,56447]);case\"emoji\":return!s([55358,56760,9792,65039],[55358,56760,8203,9792,65039])}return!1}(o[r]),t.supports.everything=t.supports.everything&&t.supports[o[r]],\"flag\"!==o[r]&&(t.supports.everythingExceptFlag=t.supports.everythingExceptFlag&&t.supports[o[r]]);t.supports.everythingExceptFlag=t.supports.everythingExceptFlag&&!t.supports.flag,t.DOMReady=!1,t.readyCallback=function(){t.DOMReady=!0},t.supports.everything||(n=function(){t.readyCallback()},a.addEventListener?(a.addEventListener(\"DOMContentLoaded\",n,!1),e.addEventListener(\"load\",n,!1)):(e.attachEvent(\"onload\",n),a.attachEvent(\"onreadystatechange\",function(){\"complete\"===a.readyState&&t.readyCallback()})),(n=t.source||{}).concatemoji?c(n.concatemoji):n.wpemoji&&n.twemoji&&(c(n.twemoji),c(n.wpemoji)))}(window,document,window._wpemojiSettings);\n", "\t\t</script>\n", "\t\t<style type=\"text/css\">\n", "img.wp-smiley,\n", "img.emoji {\n", "\tdisplay: inline !important;\n", "\tborder: none !important;\n", "\tbox-shadow: none !important;\n", "\theight: 1em !important;\n", "\twidth: 1em !important;\n", "\tmargin: 0 .07em !important;\n", "\tvertical-align: -0.1em !important;\n", "\tbackground: none !important;\n", "\tpadding: 0 !important;\n", "}\n", "</style>\n", "<link rel='stylesheet' id='mvp-css'  href='https://www.legavolley.it/wp-content/plugins/apmvp/source/css/mvp.css?ver=c2483391c2f5fe8e166b0aeee18f8eb2' type='text/css' media='all' />\n", "<link rel='stylesheet' id='awsm-team-css'  href='https://www.legavolley.it/wp-content/plugins/awsm-team-pro/css/team.min.css?ver=1.1.3' type='text/css' media='all' />\n", "<link rel='stylesheet' id='contact-form-7-css'  href='https://www.legavolley.it/wp-content/plugins/contact-form-7/includes/css/styles.css?ver=5.1.6' type='text/css' media='all' />\n", "<link rel='stylesheet' id='embed-sendy-css'  href='https://www.legavolley.it/wp-content/plugins/embed-sendy/assets/embed-sendy.css?ver=1.0.0' type='text/css' media='screen' />\n", "<link rel='stylesheet' id='cip-style-css'  href='https://www.legavolley.it/wp-content/plugins/popup-gts/style.css?ver=c2483391c2f5fe8e166b0aeee18f8eb2' type='text/css' media='all' />\n", "<link rel='stylesheet' id='searchandfilter-css'  href='https://www.legavolley.it/wp-content/plugins/search-filter/style.css?ver=1' type='text/css' media='all' />\n", "<link rel='stylesheet' id='td-plugin-framework-css'  href='https://www.legavolley.it/wp-content/plugins/td-api-plugin/css/style.css?ver=c2483391c2f5fe8e166b0aeee18f8eb2' type='text/css' media='all' />\n", "<link rel='stylesheet' id='yop-public-css'  href='https://www.legavolley.it/wp-content/plugins/yop-poll/public/assets/css/yop-poll-public-6.1.6.css?ver=c2483391c2f5fe8e166b0aeee18f8eb2' type='text/css' media='all' />\n", "<link rel='stylesheet' id='wpml-legacy-horizontal-list-0-css'  href='//www.legavolley.it/wp-content/plugins/sitepress-multilingual-cms/templates/language-switchers/legacy-list-horizontal/style.css?ver=1' type='text/css' media='all' />\n", "<link rel='stylesheet' id='wpml-menu-item-0-css'  href='//www.legavolley.it/wp-content/plugins/sitepress-multilingual-cms/templates/language-switchers/menu-item/style.css?ver=1' type='text/css' media='all' />\n", "<link rel='stylesheet' id='wpml-cms-nav-css-css'  href='https://www.legavolley.it/wp-content/plugins/wpml-cms-nav/res/css/navigation.css?ver=1.4.23' type='text/css' media='all' />\n", "<link rel='stylesheet' id='cms-navigation-style-base-css'  href='https://www.legavolley.it/wp-content/plugins/wpml-cms-nav/res/css/cms-navigation-base.css?ver=1.4.23' type='text/css' media='screen' />\n", "<link rel='stylesheet' id='cms-navigation-style-css'  href='https://www.legavolley.it/wp-content/plugins/wpml-cms-nav/res/css/cms-navigation.css?ver=1.4.23' type='text/css' media='screen' />\n", "<link rel='stylesheet' id='google-fonts-style-css'  href='https://fonts.googleapis.com/css?family=Open+Sans%3A300italic%2C400%2C400italic%2C600%2C600italic%2C700%7CRoboto%3A300%2C400%2C400italic%2C500%2C500italic%2C700%2C900&#038;ver=8.7' type='text/css' media='all' />\n", "<link rel='stylesheet' id='td-theme-css'  href='https://www.legavolley.it/wp-content/themes/legavolley/style.css?ver=8.7' type='text/css' media='all' />\n", "<script type='text/javascript' src='https://www.legavolley.it/wp-includes/js/jquery/jquery.js?ver=1.12.4'></script>\n", "<script type='text/javascript' src='https://www.legavolley.it/wp-includes/js/jquery/jquery-migrate.min.js?ver=1.4.1'></script>\n", "<script type='text/javascript' src='https://www.legavolley.it/wp-content/plugins/sitepress-multilingual-cms/res/js/jquery.cookie.js?ver=4.0.4'></script>\n", "<script type='text/javascript'>\n", "/* <![CDATA[ */\n", "var wpml_cookies = {\"_icl_current_language\":{\"value\":\"it\",\"expires\":1,\"path\":\"\\/\"}};\n", "var wpml_cookies = {\"_icl_current_language\":{\"value\":\"it\",\"expires\":1,\"path\":\"\\/\"}};\n", "/* ]]> */\n", "</script>\n", "<script type='text/javascript' src='https://www.legavolley.it/wp-content/plugins/sitepress-multilingual-cms/res/js/cookies/language-cookie.js?ver=c2483391c2f5fe8e166b0aeee18f8eb2'></script>\n", "<script type='text/javascript'>\n", "/* <![CDATA[ */\n", "var mvp_data = {\"ajax_url\":\"https:\\/\\/www.legavolley.it\\/wp-admin\\/admin-ajax.php\"};\n", "/* ]]> */\n", "</script>\n", "<script type='text/javascript' src='https://www.legavolley.it/wp-content/plugins/apmvp/source/js/new.js?ver=c2483391c2f5fe8e166b0aeee18f8eb2'></script>\n", "<script type='text/javascript' src='https://www.legavolley.it/wp-content/plugins/html5-responsive-faq/js/hrf-script.js?ver=c2483391c2f5fe8e166b0aeee18f8eb2'></script>\n", "<script type='text/javascript'>\n", "/* <![CDATA[ */\n", "var objectL10n = {\"yopPollParams\":{\"urlParams\":{\"ajax\":\"https:\\/\\/www.legavolley.it\\/wp-admin\\/admin-ajax.php\",\"wpLogin\":\"https:\\/\\/www.legavolley.it\\/login\\/?redirect_to=https%3A%2F%2Fwww.legavolley.it%2Fwp-admin%2Fadmin-ajax.php%3Faction%3Dyop_poll_record_wordpress_vote\"},\"apiParams\":{\"reCaptcha\":{\"siteKey\":\"\"},\"reCaptchaV2Invisible\":{\"siteKey\":\"\"}},\"captchaParams\":{\"imgPath\":\"https:\\/\\/www.legavolley.it\\/wp-content\\/plugins\\/yop-poll\\/public\\/assets\\/img\\/\",\"url\":\"https:\\/\\/www.legavolley.it\\/wp-content\\/plugins\\/yop-poll\\/app.php\",\"accessibilityAlt\":\"Sound icon\",\"accessibilityTitle\":\"Accessibility option: listen to a question and answer it!\",\"accessibilityDescription\":\"Type below the <strong>answer<\\/strong> to what you hear. Numbers or words:\",\"explanation\":\"Click or touch the <strong>ANSWER<\\/strong>\",\"refreshAlt\":\"Refresh\\/reload icon\",\"refreshTitle\":\"Refresh\\/reload: get new images and accessibility option!\"},\"voteParams\":{\"invalidPoll\":\"Invalid Poll\",\"noAnswersSelected\":\"No answer selected\",\"minAnswersRequired\":\"At least {min_answers_allowed} answer(s) required\",\"maxAnswersRequired\":\"A max of {max_answers_allowed} answer(s) accepted\",\"noAnswerForOther\":\"No other answer entered\",\"noValueForCustomField\":\"{custom_field_name} is required\",\"consentNotChecked\":\"You must agree to our terms and conditions\",\"noCaptchaSelected\":\"Captcha is required\",\"thankYou\":\"Thank you for your vote\"},\"resultsParams\":{\"singleVote\":\"vote\",\"multipleVotes\":\"votes\",\"singleAnswer\":\"answer\",\"multipleAnswers\":\"answers\"}}};\n", "/* ]]> */\n", "</script>\n", "<script type='text/javascript' src='https://www.legavolley.it/wp-content/plugins/yop-poll/public/assets/js/yop-poll-public-6.1.6.min.js?ver=c2483391c2f5fe8e166b0aeee18f8eb2'></script>\n", "<link rel='https://api.w.org/' href='https://www.legavolley.it/wp-json/' />\n", "<link rel=\"EditURI\" type=\"application/rsd+xml\" title=\"RSD\" href=\"https://www.legavolley.it/xmlrpc.php?rsd\" />\n", "<link rel=\"wlwmanifest\" type=\"application/wlwmanifest+xml\" href=\"https://www.legavolley.it/wp-includes/wlwmanifest.xml\" /> \n", "\n", "<link rel=\"alternate\" type=\"application/json+oembed\" href=\"https://www.legavolley.it/wp-json/oembed/1.0/embed?url\" />\n", "<link rel=\"alternate\" type=\"text/xml+oembed\" href=\"https://www.legavolley.it/wp-json/oembed/1.0/embed?url&#038;format=xml\" />\n", "<meta name=\"generator\" content=\"WPML ver\n"]}], "source": ["url = \"https://www.legavolley.it/calendario/?Anno=2016&refCampionato=947\"\n", "response = requests.get(url)\n", "\n", "# Stampa il contenuto HTML della risposta\n", "print(response.text[:10000])  # Limitiamo la stampa a 10000 caratteri per non intasare la console"]}, {"cell_type": "code", "execution_count": null, "id": "d72bba46", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Numero di tabelle trovate: 15\n", "\n", "--- <PERSON><PERSON> #1 ---\n", "<table align=\"center\" bgcolor=\"#c0c0c0\" border=\"0\" cellpadding=\"1\" cellspacing=\"1\" id=\"GareGiornata\" width=\"100%\">\n", " <div class=\"title-tab\">\n", "  Regular Season SuperLega UnipolSai\n", " </div>\n", " <div style=\"clear3\">\n", " </div>\n", " <!-- <PERSON><PERSON><PERSON><PERSON>: <PERSON><PERSON><PERSON> -->\n", " <table align=\"center\" bgcolor=\"#c0c0c0\" border=\"0\" cellpadding=\"1\" cellspacing=\"1\" id=\"GareGiornata\" width=\"100%\">\n", "  <tr class=\"GiornataA\" onclick=\"return window.top.location.href=('http://www.legavolley.it/risultati/?Anno=2016&amp;IdCampionato=648&amp;IdFase=2&amp;IdGiornata=5850')\" title=\"\">\n", "   <td colspan=\"7\">\n", "    <span>\n", "     1ª Giornata Ritorno - Domenica 11 Dicembre 2016 Ore 18:00\n", "    </span>\n", "    <img border=\"0\" src=\"/img/frdxg.gif\" valign=\"middle\"/>\n", "    <!-- <PERSON><PERSON><PERSON>S<PERSON>dra 5850,50  -->\n", "   </td>\n", "  </tr>\n", "  <tr>\n", "   <th>\n", "    #\n", "   </th>\n", "   <th>\n", "    Casa\n", "   </th>\n", "   <th>\n", "    Ris.\n", "   </th>\n", "   <th>\n", "    Ospite\n", "   </th>\n", "   <th>\n", "    <PERSON>rb<PERSON><PERSON>\n", "   </th>\n", "   <th>\n", "    Impianto\n", "   </th>\n", "   <th>\n", "    Variazioni e Tv\n", "   </th>\n", "  </tr>\n", "  <tr class=\"tab-gara\"\n", "\n", "\n", "--- <PERSON><PERSON> #2 ---\n", "<table align=\"center\" bgcolor=\"#c0c0c0\" border=\"0\" cellpadding=\"1\" cellspacing=\"1\" id=\"GareGiornata\" width=\"100%\">\n", " <tr class=\"GiornataA\" onclick=\"return window.top.location.href=('http://www.legavolley.it/risultati/?Anno=2016&amp;IdCampionato=648&amp;IdFase=2&amp;IdGiornata=5850')\" title=\"\">\n", "  <td colspan=\"7\">\n", "   <span>\n", "    1ª Giornata Ritorno - Domenica 11 Dicembre 2016 Ore 18:00\n", "   </span>\n", "   <img border=\"0\" src=\"/img/frdxg.gif\" valign=\"middle\"/>\n", "   <!-- <PERSON><PERSON><PERSON>S<PERSON>dra 5850,50  -->\n", "  </td>\n", " </tr>\n", " <tr>\n", "  <th>\n", "   #\n", "  </th>\n", "  <th>\n", "   Casa\n", "  </th>\n", "  <th>\n", "   Ris.\n", "  </th>\n", "  <th>\n", "   Ospite\n", "  </th>\n", "  <th>\n", "   <PERSON>rb<PERSON><PERSON>\n", "  </th>\n", "  <th>\n", "   Impianto\n", "  </th>\n", "  <th>\n", "   Variazioni e Tv\n", "  </th>\n", " </tr>\n", " <tr class=\"tab-gara\">\n", "  <td style=\"width:25px;text-align:center\">\n", "   92\n", "  </td>\n", "  <td align=\"center\" class=\"DettaglioCal\" onclick=\"return window.top.location.href=('http://www.legavolley.it/team/5373')\" title=\"Dettaglio Squadra\" width=\"180\">\n", "   Biosì Indexa Sora\n", "  </td>\n", "  <td align=\"center\" class=\"Detta\n", "\n", "\n", "--- <PERSON><PERSON> #3 ---\n", "<table align=\"center\" bgcolor=\"#c0c0c0\" border=\"0\" cellpadding=\"1\" cellspacing=\"1\" id=\"GareGiornata\" width=\"100%\">\n", " <tr class=\"GiornataA\" onclick=\"return window.top.location.href=('http://www.legavolley.it/risultati/?Anno=2016&amp;IdCampionato=648&amp;IdFase=2&amp;IdGiornata=5851')\" title=\"\">\n", "  <td colspan=\"7\">\n", "   <span>\n", "    2ª Giornata Ritorno - Domenica 18 Dicembre 2016 Ore 18:00\n", "   </span>\n", "   <img border=\"0\" src=\"/img/frdxg.gif\" valign=\"middle\"/>\n", "   <!-- <PERSON><PERSON><PERSON>S<PERSON>dra 5851,50  -->\n", "  </td>\n", " </tr>\n", " <tr>\n", "  <th>\n", "   #\n", "  </th>\n", "  <th>\n", "   Casa\n", "  </th>\n", "  <th>\n", "   Ris.\n", "  </th>\n", "  <th>\n", "   Ospite\n", "  </th>\n", "  <th>\n", "   <PERSON>rb<PERSON><PERSON>\n", "  </th>\n", "  <th>\n", "   Impianto\n", "  </th>\n", "  <th>\n", "   Variazioni e Tv\n", "  </th>\n", " </tr>\n", " <tr class=\"tab-gara\">\n", "  <td style=\"width:25px;text-align:center\">\n", "   99\n", "  </td>\n", "  <td align=\"center\" class=\"DettaglioCal\" onclick=\"return window.top.location.href=('http://www.legavolley.it/team/5356')\" title=\"Dettaglio Squadra\" width=\"180\">\n", "   LPR Piacenza\n", "  </td>\n", "  <td align=\"center\" class=\"DettaglioR\n", "\n", "\n", "--- <PERSON><PERSON> #4 ---\n", "<table align=\"center\" bgcolor=\"#c0c0c0\" border=\"0\" cellpadding=\"1\" cellspacing=\"1\" id=\"GareGiornata\" width=\"100%\">\n", " <tr class=\"GiornataA\" onclick=\"return window.top.location.href=('http://www.legavolley.it/risultati/?Anno=2016&amp;IdCampionato=648&amp;IdFase=2&amp;IdGiornata=5852')\" title=\"\">\n", "  <td colspan=\"7\">\n", "   <span>\n", "    3ª Giornata Ritorno - Lunedí 26 Dicembre 2016 Ore 18:00\n", "   </span>\n", "   <img border=\"0\" src=\"/img/frdxg.gif\" valign=\"middle\"/>\n", "   <!-- <PERSON><PERSON><PERSON>S<PERSON><PERSON> 5852,50  -->\n", "  </td>\n", " </tr>\n", " <tr>\n", "  <th>\n", "   #\n", "  </th>\n", "  <th>\n", "   Casa\n", "  </th>\n", "  <th>\n", "   Ris.\n", "  </th>\n", "  <th>\n", "   Ospite\n", "  </th>\n", "  <th>\n", "   <PERSON>rb<PERSON><PERSON>\n", "  </th>\n", "  <th>\n", "   Impianto\n", "  </th>\n", "  <th>\n", "   Variazioni e Tv\n", "  </th>\n", " </tr>\n", " <tr class=\"tab-gara\">\n", "  <td style=\"width:25px;text-align:center\">\n", "   106\n", "  </td>\n", "  <td align=\"center\" class=\"DettaglioCal\" onclick=\"return window.top.location.href=('http://www.legavolley.it/team/5351')\" title=\"Dettaglio Squadra\" width=\"180\">\n", "   <PERSON><PERSON><PERSON>\n", "  </td>\n", "  <td align=\"center\" class=\"D\n", "\n", "\n", "--- <PERSON><PERSON> #5 ---\n", "<table align=\"center\" bgcolor=\"#c0c0c0\" border=\"0\" cellpadding=\"1\" cellspacing=\"1\" id=\"GareGiornata\" width=\"100%\">\n", " <tr class=\"GiornataA\" onclick=\"return window.top.location.href=('http://www.legavolley.it/risultati/?Anno=2016&amp;IdCampionato=648&amp;IdFase=2&amp;IdGiornata=5853')\" title=\"\">\n", "  <td colspan=\"7\">\n", "   <span>\n", "    4ª Giornata Ritorno - Giovedí 29 Dicembre 2016 Ore 20:30\n", "   </span>\n", "   <img border=\"0\" src=\"/img/frdxg.gif\" valign=\"middle\"/>\n", "   <!-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 5853,50  -->\n", "  </td>\n", " </tr>\n", " <tr>\n", "  <th>\n", "   #\n", "  </th>\n", "  <th>\n", "   Casa\n", "  </th>\n", "  <th>\n", "   Ris.\n", "  </th>\n", "  <th>\n", "   Ospite\n", "  </th>\n", "  <th>\n", "   <PERSON>rb<PERSON><PERSON>\n", "  </th>\n", "  <th>\n", "   Impianto\n", "  </th>\n", "  <th>\n", "   Variazioni e Tv\n", "  </th>\n", " </tr>\n", " <tr class=\"tab-gara\">\n", "  <td style=\"width:25px;text-align:center\">\n", "   113\n", "  </td>\n", "  <td align=\"center\" class=\"DettaglioCal\" onclick=\"return window.top.location.href=('http://www.legavolley.it/team/5360')\" title=\"Dettaglio Squadra\" width=\"180\">\n", "   <PERSON><PERSON><PERSON>\n", "  </td>\n", "  <td align=\"center\" class=\"Dettaglio\n", "\n", "\n", "--- <PERSON><PERSON> #6 ---\n", "<table align=\"center\" bgcolor=\"#c0c0c0\" border=\"0\" cellpadding=\"1\" cellspacing=\"1\" id=\"GareGiornata\" width=\"100%\">\n", " <tr class=\"GiornataA\" onclick=\"return window.top.location.href=('http://www.legavolley.it/risultati/?Anno=2016&amp;IdCampionato=648&amp;IdFase=2&amp;IdGiornata=5854')\" title=\"\">\n", "  <td colspan=\"7\">\n", "   <span>\n", "    5ª Giornata Ritorno - Domenica 8 Gennaio 2017 Ore 18:00\n", "   </span>\n", "   <img border=\"0\" src=\"/img/frdxg.gif\" valign=\"middle\"/>\n", "   <!-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 5854,50  -->\n", "  </td>\n", " </tr>\n", " <tr>\n", "  <th>\n", "   #\n", "  </th>\n", "  <th>\n", "   Casa\n", "  </th>\n", "  <th>\n", "   Ris.\n", "  </th>\n", "  <th>\n", "   Ospite\n", "  </th>\n", "  <th>\n", "   <PERSON>rb<PERSON><PERSON>\n", "  </th>\n", "  <th>\n", "   Impianto\n", "  </th>\n", "  <th>\n", "   Variazioni e Tv\n", "  </th>\n", " </tr>\n", " <tr class=\"tab-gara\">\n", "  <td style=\"width:25px;text-align:center\">\n", "   120\n", "  </td>\n", "  <td align=\"center\" class=\"DettaglioCal\" onclick=\"return window.top.location.href=('http://www.legavolley.it/team/5352')\" title=\"Dettaglio Squadra\" width=\"180\">\n", "   Azimut Modena\n", "  </td>\n", "  <td align=\"center\" class=\"DettaglioR\n", "\n", "\n", "--- <PERSON><PERSON> #7 ---\n", "<table align=\"center\" bgcolor=\"#c0c0c0\" border=\"0\" cellpadding=\"1\" cellspacing=\"1\" id=\"GareGiornata\" width=\"100%\">\n", " <tr class=\"GiornataA\" onclick=\"return window.top.location.href=('http://www.legavolley.it/risultati/?Anno=2016&amp;IdCampionato=648&amp;IdFase=2&amp;IdGiornata=5855')\" title=\"\">\n", "  <td colspan=\"7\">\n", "   <span>\n", "    6ª Giornata Ritorno - Domenica 15 Gennaio 2017 Ore 18:00\n", "   </span>\n", "   <img border=\"0\" src=\"/img/frdxg.gif\" valign=\"middle\"/>\n", "   <!-- <PERSON><PERSON><PERSON>S<PERSON>dra 5855,50  -->\n", "  </td>\n", " </tr>\n", " <tr>\n", "  <th>\n", "   #\n", "  </th>\n", "  <th>\n", "   Casa\n", "  </th>\n", "  <th>\n", "   Ris.\n", "  </th>\n", "  <th>\n", "   Ospite\n", "  </th>\n", "  <th>\n", "   <PERSON>rb<PERSON><PERSON>\n", "  </th>\n", "  <th>\n", "   Impianto\n", "  </th>\n", "  <th>\n", "   Variazioni e Tv\n", "  </th>\n", " </tr>\n", " <tr class=\"tab-gara\">\n", "  <td style=\"width:25px;text-align:center\">\n", "   127\n", "  </td>\n", "  <td align=\"center\" class=\"DettaglioCal\" onclick=\"return window.top.location.href=('http://www.legavolley.it/team/5374')\" title=\"Dettaglio Squadra\" width=\"180\">\n", "   <PERSON><PERSON><PERSON> Calabria Vibo Valentia\n", "  </td>\n", "  <td align=\"c\n", "\n", "\n", "--- <PERSON><PERSON> #8 ---\n", "<table align=\"center\" bgcolor=\"#c0c0c0\" border=\"0\" cellpadding=\"1\" cellspacing=\"1\" id=\"GareGiornata\" width=\"100%\">\n", " <tr class=\"GiornataA\" onclick=\"return window.top.location.href=('http://www.legavolley.it/risultati/?Anno=2016&amp;IdCampionato=648&amp;IdFase=2&amp;IdGiornata=5856')\" title=\"\">\n", "  <td colspan=\"7\">\n", "   <span>\n", "    7ª Giornata Ritorno - Domenica 22 Gennaio 2017 Ore 18:00\n", "   </span>\n", "   <img border=\"0\" src=\"/img/frdxg.gif\" valign=\"middle\"/>\n", "   <!-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 5856,50  -->\n", "  </td>\n", " </tr>\n", " <tr>\n", "  <th>\n", "   #\n", "  </th>\n", "  <th>\n", "   Casa\n", "  </th>\n", "  <th>\n", "   Ris.\n", "  </th>\n", "  <th>\n", "   Ospite\n", "  </th>\n", "  <th>\n", "   <PERSON>rb<PERSON><PERSON>\n", "  </th>\n", "  <th>\n", "   Impianto\n", "  </th>\n", "  <th>\n", "   Variazioni e Tv\n", "  </th>\n", " </tr>\n", " <tr class=\"tab-gara\">\n", "  <td style=\"width:25px;text-align:center\">\n", "   134\n", "  </td>\n", "  <td align=\"center\" class=\"DettaglioCal\" onclick=\"return window.top.location.href=('http://www.legavolley.it/team/5352')\" title=\"Dettaglio Squadra\" width=\"180\">\n", "   Azimut Modena\n", "  </td>\n", "  <td align=\"center\" class=\"Dettaglio\n", "\n", "\n", "--- <PERSON><PERSON> #9 ---\n", "<table align=\"center\" bgcolor=\"#c0c0c0\" border=\"0\" cellpadding=\"1\" cellspacing=\"1\" id=\"GareGiornata\" width=\"100%\">\n", " <tr class=\"GiornataA\" onclick=\"return window.top.location.href=('http://www.legavolley.it/risultati/?Anno=2016&amp;IdCampionato=648&amp;IdFase=2&amp;IdGiornata=5857')\" title=\"\">\n", "  <td colspan=\"7\">\n", "   <span>\n", "    8ª Giornata Ritorno - Domenica 5 Febbraio 2017 Ore 18:00\n", "   </span>\n", "   <img border=\"0\" src=\"/img/frdxg.gif\" valign=\"middle\"/>\n", "   <!-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 5857,50  -->\n", "  </td>\n", " </tr>\n", " <tr>\n", "  <th>\n", "   #\n", "  </th>\n", "  <th>\n", "   Casa\n", "  </th>\n", "  <th>\n", "   Ris.\n", "  </th>\n", "  <th>\n", "   Ospite\n", "  </th>\n", "  <th>\n", "   <PERSON>rb<PERSON><PERSON>\n", "  </th>\n", "  <th>\n", "   Impianto\n", "  </th>\n", "  <th>\n", "   Variazioni e Tv\n", "  </th>\n", " </tr>\n", " <tr class=\"tab-gara\">\n", "  <td style=\"width:25px;text-align:center\">\n", "   141\n", "  </td>\n", "  <td align=\"center\" class=\"DettaglioCal\" onclick=\"return window.top.location.href=('http://www.legavolley.it/team/5349')\" title=\"Dettaglio Squadra\" width=\"180\">\n", "   Calzedonia Verona\n", "  </td>\n", "  <td align=\"center\" class=\"Detta\n", "\n", "\n", "--- <PERSON><PERSON> #10 ---\n", "<table align=\"center\" bgcolor=\"#c0c0c0\" border=\"0\" cellpadding=\"1\" cellspacing=\"1\" id=\"GareGiornata\" width=\"100%\">\n", " <tr class=\"GiornataA\" onclick=\"return window.top.location.href=('http://www.legavolley.it/risultati/?Anno=2016&amp;IdCampionato=648&amp;IdFase=2&amp;IdGiornata=5858')\" title=\"\">\n", "  <td colspan=\"7\">\n", "   <span>\n", "    9ª Giornata Ritorno - Mercoledí 8 Febbraio 2017 Ore 20:30\n", "   </span>\n", "   <img border=\"0\" src=\"/img/frdxg.gif\" valign=\"middle\"/>\n", "   <!-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 5858,50  -->\n", "  </td>\n", " </tr>\n", " <tr>\n", "  <th>\n", "   #\n", "  </th>\n", "  <th>\n", "   Casa\n", "  </th>\n", "  <th>\n", "   Ris.\n", "  </th>\n", "  <th>\n", "   Ospite\n", "  </th>\n", "  <th>\n", "   <PERSON>rb<PERSON><PERSON>\n", "  </th>\n", "  <th>\n", "   Impianto\n", "  </th>\n", "  <th>\n", "   Variazioni e Tv\n", "  </th>\n", " </tr>\n", " <tr class=\"tab-gara\">\n", "  <td style=\"width:25px;text-align:center\">\n", "   148\n", "  </td>\n", "  <td align=\"center\" class=\"DettaglioCal\" onclick=\"return window.top.location.href=('http://www.legavolley.it/team/5355')\" title=\"Dettaglio Squadra\" width=\"180\">\n", "   Gi Group Monza\n", "  </td>\n", "  <td align=\"center\" class=\"Dettagl\n", "\n", "\n", "--- <PERSON><PERSON> #11 ---\n", "<table align=\"center\" bgcolor=\"#c0c0c0\" border=\"0\" cellpadding=\"1\" cellspacing=\"1\" id=\"GareGiornata\" width=\"100%\">\n", " <tr class=\"GiornataA\" onclick=\"return window.top.location.href=('http://www.legavolley.it/risultati/?Anno=2016&amp;IdCampionato=648&amp;IdFase=2&amp;IdGiornata=5859')\" title=\"\">\n", "  <td colspan=\"7\">\n", "   <span>\n", "    10ª Giornata Ritorno - Domenica 12 Febbraio 2017 Ore 18:00\n", "   </span>\n", "   <img border=\"0\" src=\"/img/frdxg.gif\" valign=\"middle\"/>\n", "   <!-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 5859,50  -->\n", "  </td>\n", " </tr>\n", " <tr>\n", "  <th>\n", "   #\n", "  </th>\n", "  <th>\n", "   Casa\n", "  </th>\n", "  <th>\n", "   Ris.\n", "  </th>\n", "  <th>\n", "   Ospite\n", "  </th>\n", "  <th>\n", "   <PERSON>rb<PERSON><PERSON>\n", "  </th>\n", "  <th>\n", "   Impianto\n", "  </th>\n", "  <th>\n", "   Variazioni e Tv\n", "  </th>\n", " </tr>\n", " <tr class=\"tab-gara\">\n", "  <td style=\"width:25px;text-align:center\">\n", "   155\n", "  </td>\n", "  <td align=\"center\" class=\"DettaglioCal\" onclick=\"return window.top.location.href=('http://www.legavolley.it/team/5351')\" title=\"Dettaglio Squadra\" width=\"180\">\n", "   <PERSON><PERSON><PERSON>\n", "  </td>\n", "  <td align=\"center\" class\n", "\n", "\n", "--- <PERSON><PERSON> #12 ---\n", "<table align=\"center\" bgcolor=\"#c0c0c0\" border=\"0\" cellpadding=\"1\" cellspacing=\"1\" id=\"GareGiornata\" width=\"100%\">\n", " <tr class=\"GiornataA\" onclick=\"return window.top.location.href=('http://www.legavolley.it/risultati/?Anno=2016&amp;IdCampionato=648&amp;IdFase=2&amp;IdGiornata=5860')\" title=\"\">\n", "  <td colspan=\"7\">\n", "   <span>\n", "    11ª Giornata Ritorno - Domenica 19 Febbraio 2017 Ore 18:00\n", "   </span>\n", "   <img border=\"0\" src=\"/img/frdxg.gif\" valign=\"middle\"/>\n", "   <!-- <PERSON><PERSON><PERSON>S<PERSON><PERSON> 5860,50  -->\n", "  </td>\n", " </tr>\n", " <tr>\n", "  <th>\n", "   #\n", "  </th>\n", "  <th>\n", "   Casa\n", "  </th>\n", "  <th>\n", "   Ris.\n", "  </th>\n", "  <th>\n", "   Ospite\n", "  </th>\n", "  <th>\n", "   <PERSON>rb<PERSON><PERSON>\n", "  </th>\n", "  <th>\n", "   Impianto\n", "  </th>\n", "  <th>\n", "   Variazioni e Tv\n", "  </th>\n", " </tr>\n", " <tr class=\"tab-gara\">\n", "  <td style=\"width:25px;text-align:center\">\n", "   162\n", "  </td>\n", "  <td align=\"center\" class=\"DettaglioCal\" onclick=\"return window.top.location.href=('http://www.legavolley.it/team/5353')\" title=\"Dettaglio Squadra\" width=\"180\">\n", "   Diatec Trentino\n", "  </td>\n", "  <td align=\"center\" class=\"Detta\n", "\n", "\n", "--- <PERSON><PERSON> #13 ---\n", "<table align=\"center\" bgcolor=\"#c0c0c0\" border=\"0\" cellpadding=\"1\" cellspacing=\"1\" id=\"GareGiornata\" width=\"100%\">\n", " <tr class=\"GiornataA\" onclick=\"return window.top.location.href=('http://www.legavolley.it/risultati/?Anno=2016&amp;IdCampionato=648&amp;IdFase=2&amp;IdGiornata=5861')\" title=\"\">\n", "  <td colspan=\"7\">\n", "   <span>\n", "    12ª Giornata Ritorno - Domenica 26 Febbraio 2017 Ore 18:00\n", "   </span>\n", "   <img border=\"0\" src=\"/img/frdxg.gif\" valign=\"middle\"/>\n", "   <!-- <PERSON><PERSON><PERSON>S<PERSON><PERSON> 5861,50  -->\n", "  </td>\n", " </tr>\n", " <tr>\n", "  <th>\n", "   #\n", "  </th>\n", "  <th>\n", "   Casa\n", "  </th>\n", "  <th>\n", "   Ris.\n", "  </th>\n", "  <th>\n", "   Ospite\n", "  </th>\n", "  <th>\n", "   <PERSON>rb<PERSON><PERSON>\n", "  </th>\n", "  <th>\n", "   Impianto\n", "  </th>\n", "  <th>\n", "   Variazioni e Tv\n", "  </th>\n", " </tr>\n", " <tr class=\"tab-gara\">\n", "  <td style=\"width:25px;text-align:center\">\n", "   169\n", "  </td>\n", "  <td align=\"center\" class=\"DettaglioCal\" onclick=\"return window.top.location.href=('http://www.legavolley.it/team/5354')\" title=\"Dettaglio Squadra\" width=\"180\">\n", "   Exprivia <PERSON>\n", "  </td>\n", "  <td align=\"center\" class=\"Det\n", "\n", "\n", "--- <PERSON><PERSON> #14 ---\n", "<table align=\"center\" bgcolor=\"#c0c0c0\" border=\"0\" cellpadding=\"1\" cellspacing=\"1\" id=\"GareGiornata\" width=\"100%\">\n", " <tr class=\"GiornataA\" onclick=\"return window.top.location.href=('http://www.legavolley.it/risultati/?Anno=2016&amp;IdCampionato=648&amp;IdFase=2&amp;IdGiornata=5862')\" title=\"\">\n", "  <td colspan=\"7\">\n", "   <span>\n", "    13ª Giornata Ritorno - Domenica 5 Marzo 2017 Ore 18:00\n", "   </span>\n", "   <img border=\"0\" src=\"/img/frdxg.gif\" valign=\"middle\"/>\n", "   <!-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 5862,50  -->\n", "  </td>\n", " </tr>\n", " <tr>\n", "  <th>\n", "   #\n", "  </th>\n", "  <th>\n", "   Casa\n", "  </th>\n", "  <th>\n", "   Ris.\n", "  </th>\n", "  <th>\n", "   Ospite\n", "  </th>\n", "  <th>\n", "   <PERSON>rb<PERSON><PERSON>\n", "  </th>\n", "  <th>\n", "   Impianto\n", "  </th>\n", "  <th>\n", "   Variazioni e Tv\n", "  </th>\n", " </tr>\n", " <tr class=\"tab-gara\">\n", "  <td style=\"width:25px;text-align:center\">\n", "   176\n", "  </td>\n", "  <td align=\"center\" class=\"DettaglioCal\" onclick=\"return window.top.location.href=('http://www.legavolley.it/team/5351')\" title=\"Dettaglio Squadra\" width=\"180\">\n", "   <PERSON><PERSON><PERSON>\n", "  </td>\n", "  <td align=\"center\" class=\"De\n", "\n", "\n", "--- <PERSON><PERSON> #15 ---\n", "<table border=\"0\" cellpadding=\"2\" cellspacing=\"0\" id=\"Formula\" width=\"100%\">\n", " <tr height=\"14\" id=\"HeadCol\">\n", "  <td>\n", "   Formula Campionato\n", "  </td>\n", " </tr>\n", " <tr>\n", "  <td align=\"left\">\n", "   <div class=\"shadow desc\">\n", "    <a href=\"http://ww2.legavolley.it/upload/2016_RSSL.pdf\" target='_blank\"'>\n", "     <strong>\n", "      vedi Indizione Regular Season SuperLega\n", "     </strong>\n", "    </a>\n", "    <PERSON><PERSON><PERSON> Call<PERSON>o Vibo Valentia si avvale di una Licenza concessa dalla Lega ai sensi dell'Art. 3.3 del\n", "    <a href=\"VediPagina.asp?ContentId=50491\">\n", "     <strong>\n", "      Regolamento Organico\n", "     </strong>\n", "    </a>\n", "   </div>\n", "  </td>\n", " </tr>\n", "</table>\n", "\n", "\n", "\n"]}], "source": ["soup = BeautifulSoup(response.text, \"html.parser\")\n", "tabelle = soup.find_all(\"table\")\n", "\n", "print(f\"Numero di tabelle trovate: {len(tabelle)}\\n\")\n", "\n", "for idx, tabella in enumerate(tabelle):\n", "    print(f\"--- <PERSON><PERSON> #{idx + 1} ---\")\n", "    print(tabella.prettify()[:1000])  # Stampiamo solo i primi 1000 caratteri per ogni tabella\n", "    print(\"\\n\")"]}, {"cell_type": "code", "execution_count": 14, "id": "df9cf9e0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Numero di partite trovate: 91\n", "{'squadra_casa': 'Biosì Indexa Sora', 'squadra_trasferta': '<PERSON><PERSON><PERSON><PERSON>', 'risultato': '0-3', 'link_match': 'https://www.legavolley.it/match/28727', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'PalaGlobo Luca <PERSON> (FR)', 'diretta': 'Diretta Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON> Callipo Calabria Vibo Valentia', 'squadra_trasferta': '<PERSON><PERSON><PERSON>', 'risultato': '3-2', 'link_match': 'https://www.legavolley.it/match/28728', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'Pala ValentiaVibo Valenti<PERSON> (VV)', 'diretta': 'Diretta RAI Sport 1'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON>', 'squadra_trasferta': '<PERSON>pri<PERSON>', 'risultato': '3-1', 'link_match': 'https://www.legavolley.it/match/28729', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'E-Work ArenaBusto Arsizio (VA)', 'diretta': '<PERSON>retta Lega Volley Channel'}\n", "{'squadra_casa': 'Calzedonia Verona', 'squadra_trasferta': 'Top Volley Latina', 'risultato': '3-1', 'link_match': 'https://www.legavolley.it/match/28730', 'arbitri': '<PERSON>', 'stadio': 'Pala Agsm AIMVerona (VR)', 'diretta': '10/12/2016 Ore 19:30 Diretta Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON>ung<PERSON>', 'squadra_trasferta': '<PERSON><PERSON><PERSON>', 'risultato': '3-1', 'link_match': 'https://www.legavolley.it/match/28731', 'arbitri': '<PERSON><PERSON>', 'stadio': '<PERSON><PERSON> (RA)', 'diretta': '<PERSON><PERSON>ley Channel'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON> Civitanova', 'squadra_trasferta': 'Gi Group Monza', 'risultato': '3-0', 'link_match': 'https://www.legavolley.it/match/28732', 'arbitri': '<PERSON><PERSON> <PERSON><PERSON>', 'stadio': 'Eurosuole ForumCivitanova Marche (MC)', 'diretta': '<PERSON><PERSON> Lega Volley Channel'}\n", "{'squadra_casa': 'Sir Safety Conad Peru<PERSON>', 'squadra_trasferta': 'LPR Piacenza', 'risultato': '3-1', 'link_match': 'https://www.legavolley.it/match/28733', 'arbitri': '<PERSON><PERSON><PERSON><PERSON>abriz<PERSON> - <PERSON>', 'stadio': 'Pala Barton EnergyPerugia (PG)', 'diretta': '<PERSON>retta Lega Volley Channel'}\n", "{'squadra_casa': 'LPR Piacenza', 'squadra_trasferta': '<PERSON><PERSON><PERSON>', 'risultato': '1-3', 'link_match': 'https://www.legavolley.it/match/28734', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'PalaBancaSportPiacenza (PC)', 'diretta': '<PERSON>retta Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON>', 'squadra_trasferta': 'Sir <PERSON>', 'risultato': '1-3', 'link_match': 'https://www.legavolley.it/match/28735', 'arbitri': '<PERSON> Micel<PERSON>', 'stadio': '<PERSON><PERSON><PERSON> (PD)', 'diretta': '<PERSON><PERSON> Volley Channel'}\n", "{'squadra_casa': 'Expri<PERSON>', 'squadra_trasferta': 'Calzedonia Verona', 'risultato': '3-0', 'link_match': 'https://www.legavolley.it/match/28736', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'Palazzetto dello Sport \"G.Poli\"<PERSON> (BA)', 'diretta': 'Diretta Lega Volley Channel'}\n", "{'squadra_casa': 'Top Volley Latina', 'squadra_trasferta': 'Bung<PERSON> Ravenna', 'risultato': '3-1', 'link_match': 'https://www.legavolley.it/match/28737', 'arbitri': 'Saltalippi Fabrizio - Zavater Marco', 'stadio': 'PalaBianchiniLatina (LT)', 'diretta': 'Diretta RAI Sport 1'}\n", "{'squadra_casa': '<PERSON>zi<PERSON><PERSON>', 'squadra_trasferta': '<PERSON><PERSON><PERSON>', 'risultato': '3-2', 'link_match': 'https://www.legavolley.it/match/28738', 'arbitri': '<PERSON><PERSON><PERSON> - <PERSON>', 'stadio': 'PalaSport G.PaniniModena (MO)', 'diretta': '<PERSON>retta Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON>', 'squadra_trasferta': 'Biosì Indexa Sora', 'risultato': '3-1', 'link_match': 'https://www.legavolley.it/match/28739', 'arbitri': '<PERSON><PERSON><PERSON><PERSON>', 'stadio': 'ilT quotidiano ArenaTrento (TN)', 'diretta': '16/12/2016 Ore 20:30 Diretta RAI Sport 1'}\n", "{'squadra_casa': 'Gi Group Monza', 'squadra_trasferta': '<PERSON>nno Callipo Calabria Vibo Valentia', 'risultato': '3-1', 'link_match': 'https://www.legavolley.it/match/28740', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'OpiquadArena<PERSON>on<PERSON> (MB)', 'diretta': '<PERSON>retta Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON>', 'squadra_trasferta': 'Top Volley Latina', 'risultato': '3-0', 'link_match': 'https://www.legavolley.it/match/28741', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'Eurosuole ForumCivitanova March<PERSON> (MC)', 'diretta': '<PERSON><PERSON> Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON>', 'squadra_trasferta': '<PERSON><PERSON><PERSON>', 'risultato': '3-1', 'link_match': 'https://www.legavolley.it/match/28742', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'ilT quotidiano ArenaTrento (TN)', 'diretta': '<PERSON>retta Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON>', 'squadra_trasferta': 'Gi Group Monza', 'risultato': '0-3', 'link_match': 'https://www.legavolley.it/match/28743', 'arbitri': '<PERSON>', 'stadio': 'E-Work ArenaBusto Arsizio (VA)', 'diretta': 'Ore 18:30 Diretta RAI Sport 1'}\n", "{'squadra_casa': '<PERSON>zi<PERSON><PERSON>', 'squadra_trasferta': '<PERSON><PERSON><PERSON>na', 'risultato': '1-3', 'link_match': 'https://www.legavolley.it/match/28744', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'PalaSport G.PaniniModena (MO)', 'diretta': '<PERSON><PERSON> Lega Volley Channel'}\n", "{'squadra_casa': 'Calzedonia Verona', 'squadra_trasferta': 'LPR Piacenza', 'risultato': '3-0', 'link_match': 'https://www.legavolley.it/match/28745', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'Pala Agsm AIMVerona (VR)', 'diretta': '<PERSON><PERSON> Lega Volley Channel'}\n", "{'squadra_casa': 'Sir Safety Conad <PERSON>', 'squadra_trasferta': 'Biosì Indexa Sora', 'risultato': '3-0', 'link_match': 'https://www.legavolley.it/match/28746', 'arbitri': '<PERSON><PERSON> <PERSON>', 'stadio': '<PERSON><PERSON> EnergyPerugia (PG)', 'diretta': '<PERSON><PERSON> Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON>pri<PERSON>', 'squadra_trasferta': '<PERSON><PERSON><PERSON> Callipo Calabria Vibo Valentia', 'risultato': '0-3', 'link_match': 'https://www.legavolley.it/match/28747', 'arbitri': '<PERSON><PERSON>', 'stadio': 'Palazzetto dello Sport \"G.Poli\"<PERSON> (BA)', 'diretta': 'Diretta Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON>', 'squadra_trasferta': '<PERSON><PERSON><PERSON><PERSON>', 'risultato': '0-3', 'link_match': 'https://www.legavolley.it/match/28748', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': '<PERSON><PERSON><PERSON> (PD)', 'diretta': 'Diretta RAI Sport 1'}\n", "{'squadra_casa': 'Biosì Indexa Sora', 'squadra_trasferta': '<PERSON><PERSON><PERSON>iv<PERSON>', 'risultato': '0-3', 'link_match': 'https://www.legavolley.it/match/28749', 'arbitri': '<PERSON><PERSON><PERSON>ag<PERSON>', 'stadio': 'PalaGlobo Luca <PERSON> (FR)', 'diretta': 'Diretta Lega Volley Channel'}\n", "{'squadra_casa': 'Top Volley Latina', 'squadra_trasferta': '<PERSON><PERSON><PERSON>', 'risultato': '0-3', 'link_match': 'https://www.legavolley.it/match/28750', 'arbitri': '<PERSON><PERSON><PERSON><PERSON>', 'stadio': 'PalaBianchiniLatina (LT)', 'diretta': '<PERSON><PERSON> Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON>ung<PERSON>', 'squadra_trasferta': 'Sir <PERSON>', 'risultato': '0-3', 'link_match': 'https://www.legavolley.it/match/28751', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': '<PERSON><PERSON> (RA)', 'diretta': '<PERSON><PERSON>ley Channel'}\n", "{'squadra_casa': 'Gi Group Monza', 'squadra_trasferta': 'Calzedonia Verona', 'risultato': '0-3', 'link_match': 'https://www.legavolley.it/match/28752', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'OpiquadArenaMonza (MB)', 'diretta': '<PERSON>retta <PERSON> Volley Channel'}\n", "{'squadra_casa': 'LPR Piacenza', 'squadra_trasferta': '<PERSON>pri<PERSON>', 'risultato': '3-1', 'link_match': 'https://www.legavolley.it/match/28753', 'arbitri': 'Saltalippi Fabrizio - Tanasi Alessandro', 'stadio': 'PalaBancaSportPiacenza (PC)', 'diretta': 'Diretta Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON> Callipo Calabria Vibo Valentia', 'squadra_trasferta': '<PERSON><PERSON><PERSON>', 'risultato': '3-0', 'link_match': 'https://www.legavolley.it/match/28754', 'arbitri': 'Piperata Gianfranco - <PERSON>i <PERSON>', 'stadio': 'Pala ValentiaVibo <PERSON> (VV)', 'diretta': 'Diretta Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON>zi<PERSON><PERSON>', 'squadra_trasferta': 'Top Volley Latina', 'risultato': '3-2', 'link_match': 'https://www.legavolley.it/match/28755', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'PalaSport G.PaniniModena (MO)', 'diretta': '<PERSON>retta Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON>pri<PERSON>', 'squadra_trasferta': '<PERSON><PERSON><PERSON>', 'risultato': '3-0', 'link_match': 'https://www.legavolley.it/match/28756', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'Palazzetto dello Sport \"G.Poli\"<PERSON> (BA)', 'diretta': 'Diretta RAI Sport 1'}\n", "{'squadra_casa': 'Calzedonia Verona', 'squadra_trasferta': '<PERSON>ung<PERSON> Ravenna', 'risultato': '3-0', 'link_match': 'https://www.legavolley.it/match/28757', 'arbitri': '<PERSON><PERSON>', 'stadio': 'Pala Agsm AIMVerona (VR)', 'diretta': '<PERSON><PERSON> Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON>iv<PERSON>', 'squadra_trasferta': '<PERSON><PERSON><PERSON>', 'risultato': '3-0', 'link_match': 'https://www.legavolley.it/match/28758', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'Eurosuole ForumCivitanova Marche (MC)', 'diretta': '6/1/2017 Ore 18:30 Diretta RAI Sport 1'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON>', 'squadra_trasferta': 'LPR Piacenza', 'risultato': '3-0', 'link_match': 'https://www.legavolley.it/match/28759', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'ilT quotidiano ArenaTrento (TN)', 'diretta': '<PERSON>retta Lega Volley Channel'}\n", "{'squadra_casa': 'Gi Group Monza', 'squadra_trasferta': 'Biosì Indexa Sora', 'risultato': '3-1', 'link_match': 'https://www.legavolley.it/match/28760', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'OpiquadArena<PERSON>za (MB)', 'diretta': '<PERSON><PERSON> Lega Volley Channel'}\n", "{'squadra_casa': 'Sir Safety Conad <PERSON>', 'squadra_trasferta': '<PERSON><PERSON><PERSON> Calabria Vibo Valentia', 'risultato': '3-1', 'link_match': 'https://www.legavolley.it/match/28761', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': '<PERSON>la <PERSON> EnergyPerugia (PG)', 'diretta': 'Diretta Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON> Callip<PERSON> Calabria Vibo Valentia', 'squadra_trasferta': '<PERSON>zi<PERSON><PERSON>', 'risultato': '0-3', 'link_match': 'https://www.legavolley.it/match/28762', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': '<PERSON><PERSON> (VV)', 'diretta': 'Ore 16:30 Diretta Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON>', 'squadra_trasferta': '<PERSON><PERSON><PERSON>', 'risultato': '1-3', 'link_match': 'https://www.legavolley.it/match/28763', 'arbitri': '<PERSON><PERSON>', 'stadio': '<PERSON><PERSON><PERSON> (PD)', 'diretta': '<PERSON><PERSON> Volley Channel'}\n", "{'squadra_casa': 'Sir Safety Conad <PERSON>', 'squadra_trasferta': '<PERSON><PERSON><PERSON>', 'risultato': '3-0', 'link_match': 'https://www.legavolley.it/match/28764', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': '<PERSON>la <PERSON> EnergyPerugia (PG)', 'diretta': 'Diretta RAI Sport 1'}\n", "{'squadra_casa': 'Biosì Indexa Sora', 'squadra_trasferta': 'Calzedonia Verona', 'risultato': '1-3', 'link_match': 'https://www.legavolley.it/match/28765', 'arbitri': '<PERSON><PERSON> - <PERSON>i Fabrizio', 'stadio': 'PalaGlobo Luca <PERSON> (FR)', 'diretta': 'Diretta Lega Volley Channel'}\n", "{'squadra_casa': 'Bung<PERSON>', 'squadra_trasferta': '<PERSON><PERSON>ri<PERSON>', 'risultato': '2-3', 'link_match': 'https://www.legavolley.it/match/28766', 'arbitri': '<PERSON>', 'stadio': '<PERSON><PERSON> (RA)', 'diretta': '13/1/2017 Ore 20:30 Diretta RAI Sport 1'}\n", "{'squadra_casa': 'Top Volley Latina', 'squadra_trasferta': 'Gi Group Monza', 'risultato': '3-0', 'link_match': 'https://www.legavolley.it/match/28767', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'PalaBianchiniLatina (LT)', 'diretta': 'Diretta Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON>', 'squadra_trasferta': 'LPR Piacenza', 'risultato': '1-3', 'link_match': 'https://www.legavolley.it/match/28768', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'E-Work ArenaBusto Arsizio (VA)', 'diretta': '<PERSON>retta Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON>zi<PERSON><PERSON>', 'squadra_trasferta': 'Sir <PERSON>', 'risultato': '3-2', 'link_match': 'https://www.legavolley.it/match/28769', 'arbitri': '<PERSON>', 'stadio': 'PalaSport G.PaniniModena (MO)', 'diretta': 'Diretta RAI Sport 1'}\n", "{'squadra_casa': 'Expri<PERSON>', 'squadra_trasferta': 'Top Volley Latina', 'risultato': '3-2', 'link_match': 'https://www.legavolley.it/match/28770', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'Palazzetto dello Sport \"G.Poli\"<PERSON> (BA)', 'diretta': 'Diretta Lega Volley Channel'}\n", "{'squadra_casa': 'Gi Group Monza', 'squadra_trasferta': '<PERSON><PERSON><PERSON>', 'risultato': '3-1', 'link_match': 'https://www.legavolley.it/match/28771', 'arbitri': '<PERSON><PERSON>', 'stadio': 'OpiquadArenaMonza (MB)', 'diretta': '20/1/2017 Ore 20:30 Diretta RAI Sport 1'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON>', 'squadra_trasferta': '<PERSON>ung<PERSON>na', 'risultato': '3-0', 'link_match': 'https://www.legavolley.it/match/28772', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'ilT quotidiano ArenaTrento (TN)', 'diretta': '21/1/2017 Ore 20:30 Diretta Lega Volley Channel'}\n", "{'squadra_casa': 'Calzedonia Verona', 'squadra_trasferta': '<PERSON><PERSON><PERSON>', 'risultato': '3-1', 'link_match': 'https://www.legavolley.it/match/28773', 'arbitri': '<PERSON><PERSON><PERSON><PERSON>', 'stadio': 'Pala Agsm AIMVerona (VR)', 'diretta': '<PERSON><PERSON> Lega Volley Channel'}\n", "{'squadra_casa': 'LPR Piacenza', 'squadra_trasferta': 'Biosì Indexa Sora', 'risultato': '3-2', 'link_match': 'https://www.legavolley.it/match/28774', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'PalaBancaSportPiacenza (PC)', 'diretta': '<PERSON>retta Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON>iv<PERSON>nova', 'squadra_trasferta': '<PERSON><PERSON><PERSON> Calabria Vibo Valentia', 'risultato': '3-1', 'link_match': 'https://www.legavolley.it/match/28775', 'arbitri': 'Saltalippi Fabrizio - Rapisarda <PERSON>', 'stadio': 'Eurosuole ForumCivitanova Marche (MC)', 'diretta': 'Diretta Lega Volley Channel'}\n", "{'squadra_casa': 'Calzedonia Verona', 'squadra_trasferta': '<PERSON>zi<PERSON>t <PERSON>', 'risultato': '3-0', 'link_match': 'https://www.legavolley.it/match/28776', 'arbitri': '<PERSON><PERSON><PERSON> - La Micela Sandro', 'stadio': 'Pala Agsm AIMVerona (VR)', 'diretta': 'Diretta RAI Sport 1'}\n", "{'squadra_casa': 'Sir Safety Conad <PERSON>', 'squadra_trasferta': '<PERSON><PERSON><PERSON>', 'risultato': '1-3', 'link_match': 'https://www.legavolley.it/match/28777', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': '<PERSON>la <PERSON> EnergyPerugia (PG)', 'diretta': '<PERSON>retta Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON>', 'squadra_trasferta': '<PERSON><PERSON><PERSON>', 'risultato': '0-3', 'link_match': 'https://www.legavolley.it/match/28778', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'E-Work ArenaBusto Arsizio (VA)', 'diretta': '<PERSON>retta Lega Volley Channel'}\n", "{'squadra_casa': 'Biosì Indexa Sora', 'squadra_trasferta': '<PERSON>pri<PERSON>', 'risultato': '3-1', 'link_match': 'https://www.legavolley.it/match/28779', 'arbitri': '<PERSON><PERSON><PERSON><PERSON>', 'stadio': 'PalaGlobo Luca <PERSON> (FR)', 'diretta': '3/2/2017 Ore 20:30 Diretta RAI Sport 1'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON> Callipo Calabria Vibo Valentia', 'squadra_trasferta': '<PERSON><PERSON><PERSON>', 'risultato': '3-2', 'link_match': 'https://www.legavolley.it/match/28780', 'arbitri': 'Saltali<PERSON><PERSON> Luca - Vagni Il<PERSON>', 'stadio': 'Pala ValentiaVibo <PERSON>ntia (VV)', 'diretta': 'Diretta Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON>ung<PERSON>na', 'squadra_trasferta': 'Gi Group Monza', 'risultato': '3-1', 'link_match': 'https://www.legavolley.it/match/28781', 'arbitri': '<PERSON>', 'stadio': '<PERSON><PERSON> (RA)', 'diretta': '<PERSON><PERSON>ley Channel'}\n", "{'squadra_casa': 'Top Volley Latina', 'squadra_trasferta': 'LPR Piacenza', 'risultato': '2-3', 'link_match': 'https://www.legavolley.it/match/28782', 'arbitri': '<PERSON><PERSON><PERSON><PERSON>', 'stadio': 'PalaBianchiniLatina (LT)', 'diretta': '<PERSON>retta Lega Volley Channel'}\n", "{'squadra_casa': 'Gi Group Monza', 'squadra_trasferta': 'Azimut Modena', 'risultato': '2-3', 'link_match': 'https://www.legavolley.it/match/28783', 'arbitri': '<PERSON><PERSON><PERSON><PERSON>', 'stadio': 'OpiquadArenaMonza (MB)', 'diretta': '<PERSON>retta Lega Volley Channel'}\n", "{'squadra_casa': 'Expri<PERSON>', 'squadra_trasferta': 'Sir <PERSON>', 'risultato': '0-3', 'link_match': 'https://www.legavolley.it/match/28784', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'Palazzetto dello Sport \"G.Poli\"<PERSON> (BA)', 'diretta': 'Diretta Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON>', 'squadra_trasferta': 'Calzedonia Verona', 'risultato': '3-0', 'link_match': 'https://www.legavolley.it/match/28785', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'ilT quotidiano ArenaTrento (TN)', 'diretta': '<PERSON>retta Lega Volley Channel'}\n", "{'squadra_casa': 'Biosì Indexa Sora', 'squadra_trasferta': 'Top Volley Latina', 'risultato': '1-3', 'link_match': 'https://www.legavolley.it/match/28786', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'PalaGlobo Luca <PERSON> (FR)', 'diretta': '<PERSON>retta Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON>', 'squadra_trasferta': '<PERSON><PERSON><PERSON>', 'risultato': '3-0', 'link_match': 'https://www.legavolley.it/match/28787', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'Eurosuole ForumCivitanova <PERSON> (MC)', 'diretta': '<PERSON><PERSON> Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON>', 'squadra_trasferta': '<PERSON><PERSON><PERSON>', 'risultato': '3-1', 'link_match': 'https://www.legavolley.it/match/28788', 'arbitri': 'Saltalippi Fabrizio - Lot <PERSON>', 'stadio': '<PERSON><PERSON><PERSON> (PD)', 'diretta': '<PERSON><PERSON>ley Channel'}\n", "{'squadra_casa': 'LPR Piacenza', 'squadra_trasferta': '<PERSON><PERSON><PERSON> Callipo Calabria Vibo Valentia', 'risultato': '3-1', 'link_match': 'https://www.legavolley.it/match/28789', 'arbitri': '<PERSON><PERSON>', 'stadio': 'PalaBanca<PERSON>P<PERSON> (PC)', 'diretta': '9/2/2017 Ore 20:40 Diretta RAI Sport 1'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON>ivitanova', 'squadra_trasferta': '<PERSON><PERSON><PERSON>', 'risultato': '3-0', 'link_match': 'https://www.legavolley.it/match/28790', 'arbitri': '<PERSON><PERSON>', 'stadio': 'Eurosuole ForumCivitanova Marche (MC)', 'diretta': 'Diretta RAI Sport 1'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON> Callipo Calabria Vibo Valentia', 'squadra_trasferta': 'Calzedonia Verona', 'risultato': '0-3', 'link_match': 'https://www.legavolley.it/match/28791', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'Pala Valenti<PERSON> (VV)', 'diretta': 'Diretta Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON>zi<PERSON><PERSON>', 'squadra_trasferta': '<PERSON>pri<PERSON>', 'risultato': '3-1', 'link_match': 'https://www.legavolley.it/match/28792', 'arbitri': 'Saltalippi Fabrizio - Zanus<PERSON>', 'stadio': 'PalaSport G.PaniniModena (MO)', 'diretta': 'Diretta Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON>', 'squadra_trasferta': 'Top Volley Latina', 'risultato': '0-3', 'link_match': 'https://www.legavolley.it/match/28793', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': '<PERSON><PERSON><PERSON> (PD)', 'diretta': '<PERSON><PERSON> Lega Volley Channel'}\n", "{'squadra_casa': 'Sir Safety Conad <PERSON>', 'squadra_trasferta': 'Gi Group Monza', 'risultato': '3-1', 'link_match': 'https://www.legavolley.it/match/28794', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': '<PERSON><PERSON> EnergyPerugia (PG)', 'diretta': '<PERSON><PERSON> Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON>ung<PERSON>', 'squadra_trasferta': 'LPR Piacenza', 'risultato': '3-0', 'link_match': 'https://www.legavolley.it/match/28795', 'arbitri': '<PERSON><PERSON>', 'stadio': '<PERSON><PERSON> (RA)', 'diretta': '<PERSON><PERSON>ley Channel'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON>', 'squadra_trasferta': 'Biosì Indexa Sora', 'risultato': '3-2', 'link_match': 'https://www.legavolley.it/match/28796', 'arbitri': '<PERSON><PERSON><PERSON> - <PERSON> Micela Sandro', 'stadio': 'E-Work ArenaBusto Arsizio (VA)', 'diretta': '<PERSON>retta Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON>', 'squadra_trasferta': '<PERSON>zi<PERSON><PERSON>', 'risultato': '3-0', 'link_match': 'https://www.legavolley.it/match/28797', 'arbitri': '<PERSON><PERSON><PERSON><PERSON>', 'stadio': 'ilT quotidiano ArenaTrento (TN)', 'diretta': 'Diretta Lega Volley Channel'}\n", "{'squadra_casa': 'Calzedonia Verona', 'squadra_trasferta': '<PERSON><PERSON><PERSON>iv<PERSON>', 'risultato': '2-3', 'link_match': 'https://www.legavolley.it/match/28798', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'Pala Agsm AIMVerona (VR)', 'diretta': '<PERSON><PERSON> Lega Volley Channel'}\n", "{'squadra_casa': 'Top Volley Latina', 'squadra_trasferta': 'Sir Safety Conad <PERSON>', 'risultato': '1-3', 'link_match': 'https://www.legavolley.it/match/28799', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'PalaBianchiniLatina (LT)', 'diretta': 'Ore 20:30 Diretta RAI Sport 1'}\n", "{'squadra_casa': 'LPR Piacenza', 'squadra_trasferta': '<PERSON><PERSON><PERSON>', 'risultato': '3-2', 'link_match': 'https://www.legavolley.it/match/28800', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'PalaBancaSportPiacenza (PC)', 'diretta': '<PERSON><PERSON> Lega Volley Channel'}\n", "{'squadra_casa': 'Exprivia <PERSON>', 'squadra_trasferta': 'Gi Group Monza', 'risultato': '3-1', 'link_match': 'https://www.legavolley.it/match/28801', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'Palazzetto dello Sport \"G.Poli\"<PERSON> (BA)', 'diretta': 'Diretta Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON>', 'squadra_trasferta': 'Bunge Ravenna', 'risultato': '0-3', 'link_match': 'https://www.legavolley.it/match/28802', 'arbitri': '<PERSON><PERSON><PERSON> - <PERSON>', 'stadio': 'E-Work ArenaBusto Arsizio (VA)', 'diretta': '18/2/2017 Ore 20:30 Diretta Lega Volley Channel'}\n", "{'squadra_casa': 'Biosì Indexa Sora', 'squadra_trasferta': '<PERSON><PERSON><PERSON> Call<PERSON> Calabria Vibo Valentia', 'risultato': '3-1', 'link_match': 'https://www.legavolley.it/match/28803', 'arbitri': '<PERSON>', 'stadio': 'PalaGlobo Luca <PERSON> (FR)', 'diretta': 'Diretta Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON>pri<PERSON>', 'squadra_trasferta': '<PERSON><PERSON><PERSON>', 'risultato': '3-2', 'link_match': 'https://www.legavolley.it/match/28804', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'Palazzetto dello Sport \"G.Poli\"<PERSON> (BA)', 'diretta': 'Diretta Lega Volley Channel'}\n", "{'squadra_casa': 'Gi Group Monza', 'squadra_trasferta': 'Diatec Trentino', 'risultato': '0-3', 'link_match': 'https://www.legavolley.it/match/28805', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'OpiquadArenaMonza (MB)', 'diretta': 'Diretta RAI Sport 1'}\n", "{'squadra_casa': 'Sir Safety Conad <PERSON>', 'squadra_trasferta': 'Calzedonia Verona', 'risultato': '3-0', 'link_match': 'https://www.legavolley.it/match/28806', 'arbitri': '<PERSON><PERSON><PERSON><PERSON>abriz<PERSON>', 'stadio': 'Pala Barton EnergyPerugia (PG)', 'diretta': '<PERSON>retta Lega Volley Channel'}\n", "{'squadra_casa': 'Top Volley Latina', 'squadra_trasferta': '<PERSON><PERSON><PERSON>', 'risultato': '0-3', 'link_match': 'https://www.legavolley.it/match/28807', 'arbitri': '<PERSON><PERSON><PERSON><PERSON>', 'stadio': 'PalaBianchiniLatina (LT)', 'diretta': '<PERSON><PERSON> Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON>zi<PERSON><PERSON>', 'squadra_trasferta': 'LPR Piacenza', 'risultato': '3-1', 'link_match': 'https://www.legavolley.it/match/28808', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'PalaSport G.PaniniModena (MO)', 'diretta': '<PERSON>retta Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON>', 'squadra_trasferta': 'Biosì Indexa Sora', 'risultato': '3-0', 'link_match': 'https://www.legavolley.it/match/28809', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': '<PERSON><PERSON><PERSON> (PD)', 'diretta': '<PERSON><PERSON> Volley Channel'}\n", "{'squadra_casa': '<PERSON>ung<PERSON>na', 'squadra_trasferta': '<PERSON><PERSON><PERSON> Callip<PERSON> Calabria Vibo Valentia', 'risultato': '1-3', 'link_match': 'https://www.legavolley.it/match/28810', 'arbitri': 'La Micela Sandro - Saltalippi Fabrizio', 'stadio': '<PERSON><PERSON> (RA)', 'diretta': '25/2/2017 Ore 18:00 Diretta RAI Sport 1'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON>iv<PERSON>', 'squadra_trasferta': '<PERSON><PERSON><PERSON><PERSON>', 'risultato': '3-1', 'link_match': 'https://www.legavolley.it/match/28811', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'Eurosuole ForumCivitanova <PERSON> (MC)', 'diretta': '23/2/2017 Ore 20:30 Diretta RAI Sport 1'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON>', 'squadra_trasferta': 'Sir Safety Conad <PERSON>', 'risultato': '0-3', 'link_match': 'https://www.legavolley.it/match/28812', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'E-Work ArenaBusto Arsizio (VA)', 'diretta': '22/2/2017 Ore 20:30 Diretta Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON>', 'squadra_trasferta': '<PERSON><PERSON>ri<PERSON>', 'risultato': '3-0', 'link_match': 'https://www.legavolley.it/match/28813', 'arbitri': '<PERSON><PERSON><PERSON><PERSON>', 'stadio': 'ilT quotidiano ArenaTrento (TN)', 'diretta': '22/2/2017 Ore 20:30 Diretta Lega Volley Channel'}\n", "{'squadra_casa': '<PERSON><PERSON><PERSON> Callipo Calabria Vibo Valentia', 'squadra_trasferta': 'Top Volley Latina', 'risultato': '3-1', 'link_match': 'https://www.legavolley.it/match/28814', 'arbitri': '<PERSON><PERSON><PERSON><PERSON>', 'stadio': 'Pala Valenti<PERSON> (VV)', 'diretta': '22/2/2017 Ore 20:30 Diretta Lega Volley Channel'}\n", "{'squadra_casa': 'Calzedonia Verona', 'squadra_trasferta': '<PERSON><PERSON><PERSON>', 'risultato': '3-0', 'link_match': 'https://www.legavolley.it/match/28815', 'arbitri': '<PERSON>', 'stadio': '<PERSON>la <PERSON>gsm AIMVerona (VR)', 'diretta': '22/2/2017 Ore 20:30 Diretta Lega Volley Channel'}\n", "{'squadra_casa': 'LPR Piacenza', 'squadra_trasferta': 'Gi Group Monza', 'risultato': '2-3', 'link_match': 'https://www.legavolley.it/match/28816', 'arbitri': '<PERSON><PERSON><PERSON><PERSON>ab<PERSON> - Go<PERSON>', 'stadio': 'PalaBancaSportPiacenza (PC)', 'diretta': '22/2/2017 Ore 20:30 Diretta Lega Volley Channel'}\n", "{'squadra_casa': 'Biosì Indexa Sora', 'squadra_trasferta': '<PERSON>ung<PERSON>na', 'risultato': '0-3', 'link_match': 'https://www.legavolley.it/match/28817', 'arbitri': '<PERSON><PERSON><PERSON>', 'stadio': 'PalaGlobo Luca <PERSON> (FR)', 'diretta': '22/2/2017 Ore 20:30 Diretta Lega Volley Channel'}\n"]}], "source": ["url = \"https://www.legavolley.it/calendario/?Anno=2016&refCampionato=947\"\n", "response = requests.get(url)\n", "soup = BeautifulSoup(response.text, \"html.parser\")\n", "\n", "partite = []\n", "\n", "for riga in soup.select(\"tr.tab-gara\"):\n", "    celle = riga.find_all(\"td\")\n", "    if len(celle) >= 7:\n", "        squadra_casa = celle[1].get_text(strip=True)\n", "        \n", "        risultato_cell = celle[2]\n", "        risultato = risultato_cell.get_text(strip=True)\n", "        link_match = risultato_cell.find(\"a\")[\"href\"] if risultato_cell.find(\"a\") else None\n", "\n", "        squadra_trasferta = celle[3].get_text(strip=True)\n", "        arbitri_html = celle[4]\n", "        arbitri = \" - \".join(a.get_text(strip=True) for a in arbitri_html.find_all(\"a\"))\n", "\n", "        stadio = celle[5].get_text(strip=True)\n", "        diretta = celle[6].get_text(strip=True)\n", "\n", "        partite.append({\n", "            \"squadra_casa\": squadra_casa,\n", "            \"squadra_trasferta\": squadra_trasferta,\n", "            \"risultato\": risultato,\n", "            \"link_match\": link_match,\n", "            \"arbitri\": arbitri,\n", "            \"stadio\": stadio,\n", "            \"diretta\": diretta\n", "        })\n", "\n", "# Stampa di controllo\n", "print(f\"Numero di partite trovate: {len(partite)}\")\n", "for partita in partite:\n", "    print(partita)\n", "\n"]}, {"cell_type": "code", "execution_count": 64, "id": "dad9c474", "metadata": {}, "outputs": [], "source": ["def estrai_dati_match(url):\n", "    response = requests.get(url)\n", "    soup = BeautifulSoup(response.text, \"html.parser\")\n", "    \n", "    # Controllo: ci sono almeno 2 <table id=\"Tabellino\">?\n", "    tabelle = soup.find_all(\"table\", {\"id\": \"Tabellino\"})\n", "    if not tabelle or len(tabelle) < 2:\n", "        print(f\"⚠️  Nessun match valido trovato per URL: {url}\")\n", "        return None\n", "\n", "    # Controllo: il primo <th> contiene la parola \"Stagione\"?\n", "    th = tabelle[0].find(\"th\")\n", "    if not th or \"Stagione\" not in th.get_text():\n", "        print(f\"⚠️  Match non valido (assenza intestazione 'Stagione') per URL: {url}\")\n", "        return None\n", "\n", "    info = {\n", "        \"stagione\": None, \"competizione\": None, \"giornata\": None, \"fase\": None,\n", "        \"squadra_casa\": None, \"squadra_ospite\": None,\n", "        \"set_vinti_casa\": None, \"set_vinti_ospite\": None,\n", "        \"data\": None, \"ora\": None,\n", "        \"spettatori\": None, \"incasso\": None,\n", "        \"impianto\": None, \"città\": None, \"provincia\": None,\n", "        \"url\": url\n", "    }\n", "\n", "    # === INTESTAZIONE STAGIONE / COMPETIZIONE / GIORNATA ===\n", "    txt = th.get_text(strip=True)\n", "    stagione_match = re.search(r\"Stagione\\s+(\\d{4}/\\d{4})\", txt)\n", "    info[\"stagione\"] = stagione_match.group(1) if stagione_match else None\n", "\n", "    comp_match = re.search(r\"-\\s+(.*?)\\s+-\", txt)\n", "    info[\"competizione\"] = comp_match.group(1).strip() if comp_match else None\n", "\n", "    giornata_match = re.search(r\"(\\d+ª)\\s+<PERSON><PERSON><PERSON><PERSON>\", txt)\n", "    info[\"giornata\"] = giornata_match.group(1) if giornata_match else None\n", "\n", "    fase_match = re.search(r\"<PERSON>iornata\\s+(\\w+)\", txt)\n", "    info[\"fase\"] = fase_match.group(1) if fase_match else None\n", "    \n", "\n", "    # === SQUADRE E PUNTEGGIO ===\n", "    squadre = tabelle[0].find_all(\"td\", class_=\"DettaglioA\")\n", "    punteggi = tabelle[0].find_all(\"td\", id=\"HighCol\")\n", "    if len(squadre) >= 2 and len(punteggi) >= 2:\n", "        info[\"squadra_casa\"] = squadre[0].get_text(strip=True)\n", "        info[\"squadra_ospite\"] = squadre[1].get_text(strip=True)\n", "        try:\n", "            info[\"set_vinti_casa\"] = int(punteggi[0].get_text(strip=True))\n", "            info[\"set_vinti_ospite\"] = int(punteggi[1].get_text(strip=True))\n", "        except ValueError:\n", "            pass\n", "        \n", "\n", "    # === DETTAGLI EVENTO (DATA, ORA, IMPIANTO...) ===\n", "    righe = tabelle[1].find_all(\"tr\")\n", "    try:\n", "        celle_data = righe[1].find_all(\"td\")\n", "        info[\"data\"] = celle_data[0].get_text(strip=True)\n", "        info[\"ora\"] = celle_data[1].get_text(strip=True)\n", "        info[\"spettatori\"] = celle_data[2].get_text(strip=True).replace(\".\", \"\")\n", "        info[\"incasso\"] = celle_data[3].get_text(strip=True).replace(\"€\", \"\").replace(\".\", \"\").strip()\n", "\n", "        info[\"impianto\"] = righe[2].find_all(\"td\")[1].get_text(strip=True)\n", "        celle_città = righe[3].find_all(\"td\")\n", "        info[\"città\"] = celle_città[1].get_text(strip=True)\n", "        info[\"provincia\"] = celle_città[2].get_text(strip=True)\n", "    except Exception as e:\n", "        print(f\"⚠️  Errore nel parsing dettagli evento per {url}: {e}\")\n", "        print(e)\n", "\n", "    return info, url\n"]}, {"cell_type": "code", "execution_count": null, "id": "fbc1e297", "metadata": {}, "outputs": [], "source": ["def abbe<PERSON><PERSON>_dati_restituiti(info, url):\n", "    try:\n", "        # In stagione, che in realtà è la nostra Annata, prendiamo solo gli anni\n", "        info[\"stagione\"] = info[\"stagione\"][:4]  \n", "        info[\"giornata\"] = int(info[\"giornata\"].replace(\"ª\", \"\"))  #tolgo la parte \"ª\" e lo convertiamo in intero\n", "        info[\"set_vinti_casa\"] = int(info[\"set_vinti_casa\"])\n", "        info[\"set_vinti_ospite\"] = int(info[\"set_vinti_ospite\"])\n", "        \n", "        #come squadra_casa e squadra_ospite metto l'ID della squadra, che è la chiave del dizionario squadre_nomi_sponsor\n", "        for chiave, valore in squadre_nomi_sponsor:\n", "            if info[\"squadra_casa\"] in valore:\n", "                info[\"squadra_casa\"] = chiave\n", "            if info[\"squadra_ospite\"] in valore:\n", "                info[\"squadra_ospite\"] = chiave\n", "        \n", "        \n", "        # In competizione, abbelliamo i nomi\n", "        if \"regular season\" in info[\"competizione\"].lower():\n", "            info[\"competizione\"] = \"Superlega\"  \n", "        # Playoff per il 5° posto\n", "        elif \"play off 5\" or \"play off finale 5\" or \"playoff 5\" or \"playoff finale 5\" in info[\"competizione\"].lower():\n", "            info[\"competizione\"] = \"Superlega\"\n", "            info[\"fase\"] = \"Playoff 5\"\n", "            info[\"sottofase\"] = info[\"fase\"]\n", "        # Playoff per il 3° posto\n", "        elif \"play off 3\" or \"play off finale 3\" or \"playoff 3\" or \"playoff finale 3\" in info[\"competizione\"].lower():\n", "            info[\"competizione\"] = \"Superlega\"\n", "            info[\"fase\"] = \"Playoff 3\"\n", "            info[\"sottofase\"] = info[\"fase\"]\n", "        # Playoff\n", "        elif \"play off\" or \"playoff\" in info[\"competizione\"].lower():\n", "            info[\"competizione\"] = \"Superlega\"\n", "            info[\"fase\"] = \"Playoff\"\n", "            info[\"sottofase\"] = info[\"fase\"]\n", "        # Supercoppa\n", "        elif \"supercoppa\" in info[\"competizione\"].lower():\n", "            info[\"competizione\"] = \"Supercoppa Italiana\"\n", "        # Coppa Italia\n", "        elif \"coppa italia\" in info[\"competizione\"].lower():\n", "            info[\"competizione\"] = \"Coppa Italia\"\n", "        #Altrimenti non so coa potrei avere, quindi stampo quello che ho\n", "        else:\n", "            print(f\"Non so come abbellire la competizione: {info['competizione']}\")\n", "\n", "    except:\n", "        print(f\"Errore nell'abbellimento dei dati dell'url: {url}\")\n", "        \n", "    return info"]}, {"cell_type": "code", "execution_count": 56, "id": "70433f9a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["({'stagione': '2016/2017', 'competizione': 'Regular Season SuperLega UnipolSai', 'giornata': '1ª', 'fase': 'Ritor<PERSON>', 'squadra_casa': 'Biosì Indexa Sora', 'squadra_ospite': '<PERSON>zimut Modena', 'set_vinti_casa': 0, 'set_vinti_ospite': 3, 'data': '11/12/2016', 'ora': '18:00', 'spettatori': '1985', 'incasso': '15300', 'impianto': 'PalaGlobo <PERSON>', 'città': 'Sora', 'provincia': 'FR', 'url': 'https://www.legavolley.it/match/28727'}, 'https://www.legavolley.it/match/28727')\n"]}], "source": ["#<PERSON>za abbellire i dati, essi sono così\n", "dati = estrai_dati_match(\"https://www.legavolley.it/match/28727\")\n", "print(dati)"]}, {"cell_type": "code", "execution_count": 57, "id": "ed784d20", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["⚠️  Nessun match valido trovato per URL: https://www.legavolley.it/match/0\n", "None\n"]}], "source": ["#<PERSON>za abbellire i dati, essi sono così\n", "dati = estrai_dati_match(\"https://www.legavolley.it/match/0\")\n", "print(dati)"]}, {"cell_type": "code", "execution_count": null, "id": "e759c6c5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["stagione: 2021/2022\n", "competizione: Play Off SuperLega Credem Banca\n", "giornata: 1ª\n", "fase: Finale\n", "squadra_casa: Sir <PERSON>\n", "squadra_ospite: <PERSON><PERSON><PERSON>\n", "set_vinti_casa: 2\n", "set_vinti_ospite: 3\n", "data: 1/5/2022\n", "ora: 18:00\n", "spettatori: 3761\n", "incasso: 56071\n", "impianto: Pala Barton Energy\n", "città: Perugia\n", "provincia: PG\n", "url: https://www.legavolley.it/match/35616\n"]}], "source": ["#<PERSON>za abbellire i dati, essi sono così\n", "dati, url = estrai_dati_match(\"https://www.legavolley.it/match/35616\")\n", "for k, v in dati.items():\n", "    print(f\"{k}: {v}\")"]}, {"cell_type": "code", "execution_count": 65, "id": "e7caa763", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["stagione: 2024/2025\n", "competizione: Play Off SuperLega Credem Banca\n", "giornata: None\n", "fase: None\n", "squadra_casa: <PERSON><PERSON>\n", "squadra_ospite: <PERSON><PERSON><PERSON>\n", "set_vinti_casa: 3\n", "set_vinti_ospite: 0\n", "data: 27/4/2025\n", "ora: 18:30\n", "spettatori: 4182\n", "incasso: 61639\n", "impianto: ilT quotidiano Arena\n", "città: <PERSON><PERSON>\n", "provincia: TN\n", "url: https://www.legavolley.it/match/38985\n"]}], "source": ["#<PERSON>za abbellire i dati, essi sono così\n", "dati, url = estrai_dati_match(\"https://www.legavolley.it/match/38985\")\n", "for k, v in dati.items():\n", "    print(f\"{k}: {v}\")\n"]}, {"cell_type": "code", "execution_count": 54, "id": "ed2db9d6", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'NoneType' object has no attribute 'group'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[54]\u001b[39m\u001b[32m, line 3\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m#<PERSON><PERSON><PERSON><PERSON> i dati sono co<PERSON>ì\u001b[39;00m\n\u001b[32m      2\u001b[39m url = \u001b[33m\"\u001b[39m\u001b[33mhttps://www.legavolley.it/match/38985\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m3\u001b[39m dati, url = \u001b[43mestrai_dati_match\u001b[49m\u001b[43m(\u001b[49m\u001b[43murl\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m      4\u001b[39m dati_abbelliti = abbellisci_dati_restituiti(dati, url)\n\u001b[32m      5\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m k, v \u001b[38;5;129;01min\u001b[39;00m dati_abbelliti.items():\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[47]\u001b[39m\u001b[32m, line 35\u001b[39m, in \u001b[36mestrai_dati_match\u001b[39m\u001b[34m(url)\u001b[39m\n\u001b[32m     33\u001b[39m info[\u001b[33m\"\u001b[39m\u001b[33mstagione\u001b[39m\u001b[33m\"\u001b[39m] = re.search(\u001b[33mr\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mStagione\u001b[39m\u001b[33m\\\u001b[39m\u001b[33ms+(\u001b[39m\u001b[33m\\\u001b[39m\u001b[33md\u001b[39m\u001b[38;5;132;01m{4}\u001b[39;00m\u001b[33m/\u001b[39m\u001b[33m\\\u001b[39m\u001b[33md\u001b[39m\u001b[38;5;132;01m{4}\u001b[39;00m\u001b[33m)\u001b[39m\u001b[33m\"\u001b[39m, txt).group(\u001b[32m1\u001b[39m)\n\u001b[32m     34\u001b[39m info[\u001b[33m\"\u001b[39m\u001b[33mcompetizione\u001b[39m\u001b[33m\"\u001b[39m] = re.search(\u001b[33mr\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m-\u001b[39m\u001b[33m\\\u001b[39m\u001b[33ms+(.*?)\u001b[39m\u001b[33m\\\u001b[39m\u001b[33ms+-\u001b[39m\u001b[33m\"\u001b[39m, txt).group(\u001b[32m1\u001b[39m).strip()\n\u001b[32m---> \u001b[39m\u001b[32m35\u001b[39m info[\u001b[33m\"\u001b[39m\u001b[33mgiornata\u001b[39m\u001b[33m\"\u001b[39m] = \u001b[43mre\u001b[49m\u001b[43m.\u001b[49m\u001b[43msearch\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43mr\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m(\u001b[39;49m\u001b[33;43m\\\u001b[39;49m\u001b[33;43md+ª)\u001b[39;49m\u001b[33;43m\\\u001b[39;49m\u001b[33;43ms+Giornata\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtxt\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mgroup\u001b[49m(\u001b[32m1\u001b[39m)\n\u001b[32m     36\u001b[39m fase_match = re.search(\u001b[33mr\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mGiornata\u001b[39m\u001b[33m\\\u001b[39m\u001b[33ms+(\u001b[39m\u001b[33m\\\u001b[39m\u001b[33mw+)\u001b[39m\u001b[33m\"\u001b[39m, txt)\n\u001b[32m     37\u001b[39m info[\u001b[33m\"\u001b[39m\u001b[33mfase\u001b[39m\u001b[33m\"\u001b[39m] = fase_match.group(\u001b[32m1\u001b[39m) \u001b[38;5;28;01mif\u001b[39;00m fase_match \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n", "\u001b[31mAttributeError\u001b[39m: 'NoneType' object has no attribute 'group'"]}], "source": ["#A<PERSON><PERSON>do i dati sono così\n", "url = \"https://www.legavolley.it/match/38985\"\n", "dati, url = estrai_dati_match(url)\n", "dati_abbelliti = abbe<PERSON><PERSON>_dati_restituiti(dati, url)\n", "for k, v in dati_abbelliti.items():\n", "    print(f\"{k}: {v}\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}