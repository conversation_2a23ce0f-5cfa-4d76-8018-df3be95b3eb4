{"cells": [{"cell_type": "markdown", "id": "4cbd938a", "metadata": {}, "source": ["Aggiorniamo la tabella players_latest con i dati del file csv players_latest_updated.csv"]}, {"cell_type": "code", "execution_count": 2, "id": "9d75eed2", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from sqlalchemy import create_engine"]}, {"cell_type": "code", "execution_count": 3, "id": "e83c0eec", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "PlayerID", "rawType": "int64", "type": "integer"}, {"name": "Nome", "rawType": "object", "type": "string"}, {"name": "Cognome", "rawType": "object", "type": "string"}, {"name": "<PERSON><PERSON><PERSON>", "rawType": "int64", "type": "integer"}, {"name": "RuoloDescr", "rawType": "object", "type": "string"}, {"name": "NumeroMaglia", "rawType": "int64", "type": "integer"}, {"name": "Nazionalità", "rawType": "object", "type": "unknown"}, {"name": "DataNascita", "rawType": "object", "type": "unknown"}, {"name": "Altezza", "rawType": "object", "type": "unknown"}, {"name": "Peso", "rawType": "object", "type": "unknown"}, {"name": "Sc<PERSON><PERSON><PERSON>", "rawType": "object", "type": "unknown"}, {"name": "<PERSON><PERSON>", "rawType": "object", "type": "unknown"}, {"name": "ManoDominante", "rawType": "float64", "type": "float"}], "conversionMethod": "pd.DataFrame", "ref": "7fa943a9-7e6c-42f6-9f65-1e7abf7e0d5b", "rows": [["0", "13798", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "1", "Libero", "8", null, null, null, null, null, null, null], ["1", "14030", "Valentino", "<PERSON><PERSON><PERSON>", "1", "Libero", "8", null, null, null, null, null, null, null], ["2", "17", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "5", "Palleggiatore", "28", null, null, null, null, null, null, null], ["3", "21", "<PERSON>", "<PERSON><PERSON>", "3", "Opposto", "23", null, null, null, null, null, null, null], ["4", "94", "<PERSON><PERSON><PERSON><PERSON> la<PERSON>o", "<PERSON><PERSON><PERSON><PERSON>", "2", "Schiacciatore", "2", null, null, null, null, null, null, null], ["5", "199", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> ghara h.", "2", "Schiacciatore", "22", "Iran", "17.10.1993", "196cm", "88kg", "360cm", "340cm", null], ["6", "239", "<PERSON>", "<PERSON> krel<PERSON>", "5", "Palleggiatore", "14", null, null, null, null, null, null, null], ["7", "326", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "4", "Centrale", "3", null, null, null, null, null, null, null], ["8", "338", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "5", "Palleggiatore", "3", null, null, null, null, null, null, null], ["9", "365", "<PERSON><PERSON>", "Kaliberda", "2", "Schiacciatore", "6", null, null, null, null, null, null, null], ["10", "373", "Pet<PERSON>", "<PERSON><PERSON><PERSON>", "3", "Opposto", "15", null, null, null, null, null, null, null], ["11", "382", "Flavio", "<PERSON><PERSON><PERSON> g<PERSON>", "4", "Centrale", "23", null, null, null, null, null, null, null], ["12", "1695", "<PERSON>", "<PERSON><PERSON><PERSON>", "2", "Schiacciatore", "15", null, null, null, null, null, null, null], ["13", "822", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "2", "Schiacciatore", "23", null, null, null, null, null, null, null], ["14", "830", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> lopes", "0", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "3", null, null, null, null, null, null, null], ["15", "836", "<PERSON>", "<PERSON><PERSON><PERSON>", "2", "Schiacciatore", "10", null, null, null, null, null, null, null], ["16", "849", "<PERSON>.", "Ventura", "0", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "77", null, null, null, null, null, null, null], ["17", "2405", "Ozkan", "<PERSON><PERSON><PERSON>", "4", "Centrale", "9", null, null, null, null, null, null, null], ["18", "1960", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "2", "Schiacciatore", "18", null, null, null, null, null, null, null], ["19", "1137", "<PERSON>", "<PERSON><PERSON><PERSON>", "1", "Libero", "19", null, null, null, null, null, null, null], ["20", "1243", "<PERSON><PERSON>", "Kiyak", "5", "Palleggiatore", "1", null, null, null, null, null, null, null], ["21", "1247", "<PERSON><PERSON><PERSON><PERSON>", "Subasi", "3", "Opposto", "8", null, null, null, null, null, null, null], ["22", "1248", "<PERSON><PERSON> e<PERSON>", "<PERSON><PERSON><PERSON>", "3", "Opposto", "9", null, null, null, null, null, null, null], ["23", "1254", "<PERSON><PERSON>", "<PERSON><PERSON>", "2", "Schiacciatore", "22", null, null, null, null, null, null, null], ["24", "1258", "<PERSON>", "<PERSON><PERSON>l", "2", "Schiacciatore", "8", null, null, null, null, null, null, null], ["25", "1261", "<PERSON><PERSON>", "Firincioglu", "0", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "11", null, null, null, null, null, null, null], ["26", "1265", "<PERSON><PERSON>", "<PERSON><PERSON>", "0", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "18", null, null, null, null, null, null, null], ["27", "1268", "Ogulcan", "<PERSON><PERSON><PERSON>", "0", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "28", null, null, null, null, null, null, null], ["28", "1481", "Nikola", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "3", "Opposto", "14", null, null, null, null, null, null, null], ["29", "1694", "<PERSON><PERSON>", "Breiling", "1", "Libero", "14", null, null, null, null, null, null, null], ["30", "1698", "Marnix", "Acar", "0", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "99", null, null, null, null, null, null, null], ["31", "1750", "Halil", "<PERSON><PERSON><PERSON>", "0", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "1", null, null, null, null, null, null, null], ["32", "1751", "<PERSON><PERSON> berat", "<PERSON><PERSON>", "3", "Opposto", "2", null, null, null, null, null, null, null], ["33", "1952", "<PERSON><PERSON>t", "<PERSON><PERSON><PERSON>", "4", "Centrale", "4", null, null, null, null, null, null, null], ["34", "1959", "<PERSON><PERSON>", "Altinkaya", "1", "Libero", "17", null, null, null, null, null, null, null], ["35", "2346", "<PERSON>", "<PERSON><PERSON><PERSON>", "5", "Palleggiatore", "1", null, null, null, null, null, null, null], ["36", "2401", "Ser<PERSON> yuksel", "Bidak", "4", "Centrale", "3", null, null, null, null, null, null, null], ["37", "2402", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "1", "Libero", "4", null, null, null, null, null, null, null], ["38", "2404", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "2", "Schiacciatore", "8", null, null, null, null, null, null, null], ["39", "2406", "<PERSON> den<PERSON>", "Yilmaz", "2", "Schiacciatore", "10", null, null, null, null, null, null, null], ["40", "2409", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "5", "Palleggiatore", "17", null, null, null, null, null, null, null], ["41", "3058", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "5", "Palleggiatore", "4", null, null, null, null, null, null, null], ["42", "3314", "Djordje", "<PERSON><PERSON>", "4", "Centrale", "1", null, null, null, null, null, null, null], ["43", "3322", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> silva", "2", "Schiacciatore", "10", null, null, null, null, null, null, null], ["44", "4056", "Aleksander", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "3", "Opposto", "14", null, null, null, null, null, null, null], ["45", "4066", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "4", "Centrale", "5", null, null, null, null, null, null, null], ["46", "4071", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "2", "Schiacciatore", "12", null, null, null, null, null, null, null], ["47", "4073", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "4", "Centrale", "14", null, null, null, null, null, null, null], ["48", "4075", "<PERSON>", "Duspiva", "4", "Centrale", "17", null, null, null, null, null, null, null], ["49", "4076", "<PERSON>", "<PERSON><PERSON><PERSON>", "1", "Libero", "21", null, null, null, null, null, null, null]], "shape": {"columns": 13, "rows": 72}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PlayerID</th>\n", "      <th>Nome</th>\n", "      <th>Cognome</th>\n", "      <th>Ruolo</th>\n", "      <th>RuoloDescr</th>\n", "      <th>NumeroMaglia</th>\n", "      <th>Nazionalità</th>\n", "      <th>DataNascita</th>\n", "      <th>Altezza</th>\n", "      <th>Peso</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>ManoDominante</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>13798</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>1</td>\n", "      <td>Libero</td>\n", "      <td>8</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>14030</td>\n", "      <td>Vale<PERSON></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>1</td>\n", "      <td>Libero</td>\n", "      <td>8</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>17</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>5</td>\n", "      <td>Palleggiatore</td>\n", "      <td>28</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>21</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>3</td>\n", "      <td>Opposto</td>\n", "      <td>23</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>94</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> la<PERSON></td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>2</td>\n", "      <td>Sc<PERSON>cci<PERSON><PERSON></td>\n", "      <td>2</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67</th>\n", "      <td>10588</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Amhed</td>\n", "      <td>3</td>\n", "      <td>Opposto</td>\n", "      <td>24</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>68</th>\n", "      <td>11087</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON>j<PERSON></td>\n", "      <td>5</td>\n", "      <td>Palleggiatore</td>\n", "      <td>4</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>69</th>\n", "      <td>11098</td>\n", "      <td><PERSON></td>\n", "      <td>Abulubaba</td>\n", "      <td>3</td>\n", "      <td>Opposto</td>\n", "      <td>24</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>70</th>\n", "      <td>11181</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>2</td>\n", "      <td>Sc<PERSON>cci<PERSON><PERSON></td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71</th>\n", "      <td>11240</td>\n", "      <td><PERSON><PERSON> efe</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>2</td>\n", "      <td>Sc<PERSON>cci<PERSON><PERSON></td>\n", "      <td>9</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>72 rows × 13 columns</p>\n", "</div>"], "text/plain": ["    PlayerID           Nome              Cognome  Ruolo     RuoloDescr  \\\n", "0      13798       <PERSON><PERSON><PERSON>      1         Libero   \n", "1      14030      Valentino             <PERSON>etti      1         Libero   \n", "2         17       <PERSON><PERSON><PERSON><PERSON><PERSON>      5  Palleggiatore   \n", "3         21         Matteo                Paul<PERSON>      3        Opposto   \n", "4         94  Osniel lazaro  Mergarejo hernandez      2  Schiacciatore   \n", "..       ...            ...                  ...    ...            ...   \n", "67     10588     Abdulubaba                Amhed      3        Opposto   \n", "68     11087      <PERSON><PERSON><PERSON>      5  Palleggiatore   \n", "69     11098          <PERSON>      3        Opposto   \n", "70     11181         <PERSON><PERSON><PERSON>      2  Schiacciatore   \n", "71     11240    <PERSON><PERSON>      2  Schiacciatore   \n", "\n", "    NumeroMaglia Nazionalità DataNascita Altezza Peso Schiacciata Muro  \\\n", "0              8         NaN         NaN     NaN  NaN         NaN  NaN   \n", "1              8         NaN         NaN     NaN  NaN         NaN  NaN   \n", "2             28         NaN         NaN     NaN  NaN         NaN  NaN   \n", "3             23         NaN         NaN     NaN  NaN         NaN  NaN   \n", "4              2         NaN         NaN     NaN  NaN         NaN  NaN   \n", "..           ...         ...         ...     ...  ...         ...  ...   \n", "67            24         NaN         NaN     NaN  NaN         NaN  NaN   \n", "68             4         NaN         NaN     NaN  NaN         NaN  NaN   \n", "69            24         NaN         NaN     NaN  NaN         NaN  NaN   \n", "70             1         NaN         NaN     NaN  NaN         NaN  NaN   \n", "71             9         NaN         NaN     NaN  NaN         NaN  NaN   \n", "\n", "    ManoDominante  \n", "0             NaN  \n", "1             NaN  \n", "2             NaN  \n", "3             NaN  \n", "4             NaN  \n", "..            ...  \n", "67            NaN  \n", "68            NaN  \n", "69            NaN  \n", "70            NaN  \n", "71            NaN  \n", "\n", "[72 rows x 13 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["#Leggo il csv come dataframe\n", "df_players_latest_updated = pd.read_csv(r\"C:\\Users\\<USER>\\Documents\\ModenaVolley\\VolleyballScraper\\data\\players_updated_2.csv\")\n", "df_players_latest_updated"]}, {"cell_type": "code", "execution_count": 4, "id": "1142e97c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "<PERSON><PERSON><PERSON> con dati da aggiornare:\n", "    Nome             Cognome Nazionalità DataNascita Altezza  Peso  \\\n", "5  <PERSON><PERSON> h.        <PERSON>  17.10.1993   196cm  88kg   \n", "\n", "  Schiacciata   Muro  ManoDominante  \n", "5       360cm  340cm            NaN  \n", "✅ Aggiornato Mi<PERSON> ghara h.\n", "\n", "✅ Database aggiornato con successo!\n", "Righe totali nel database: 743\n", "Righe con Nazionalità: 657\n", "Righe con Altezza: 653\n", "\n", "Totale righe aggiornate: 1\n"]}], "source": ["def update_players_latest_from_csv():\n", "    engine = create_engine('postgresql+psycopg://postgres:AcquaLevissima1@localhost:5432/db_modena')\n", "    \n", "    # Leggi il CSV\n", "    df_csv = df_players_latest_updated  # Usa il DataFrame già caricato\n", "    \n", "    # Mostra le righe con valori non nulli nelle colonne d'interesse\n", "    columns_to_check = ['Nazionalità', 'DataNascita', 'Altezza', 'Peso', 'Schiacciata', 'Muro', 'ManoDominante']\n", "    non_null_mask = df_csv[columns_to_check].notna().any(axis=1)\n", "    print(\"\\nRighe con dati da aggiornare:\")\n", "    print(df_csv[non_null_mask][['Nome', 'Cognome'] + columns_to_check])\n", "    \n", "    # <PERSON>gg<PERSON> la tabella players_latest\n", "    df_db = pd.read_sql_table('players_latest', engine)\n", "    \n", "    # Normalizza i nomi per il matching\n", "    def normalize_name(name):\n", "        return name.lower().strip() if isinstance(name, str) else name\n", "    \n", "    df_csv['Nome_norm'] = df_csv['Nome'].apply(normalize_name)\n", "    df_csv['Cognome_norm'] = df_csv['Cognome'].apply(normalize_name)\n", "    df_db['Nome_norm'] = df_db['Nome'].apply(normalize_name)\n", "    df_db['Cognome_norm'] = df_db['Cognome'].apply(normalize_name)\n", "    \n", "    updated_rows = 0\n", "    \n", "    for _, csv_row in df_csv[non_null_mask].iterrows():\n", "        # Trova la riga corrispondente nel database usando i nomi normalizzati\n", "        mask = (df_db['Nome_norm'] == csv_row['Nome_norm']) & (df_db['Cognome_norm'] == csv_row['Cognome_norm'])\n", "        \n", "        if mask.any():\n", "            row_updated = False\n", "            \n", "            for field in columns_to_check:\n", "                if pd.notna(csv_row[field]):\n", "                    df_db.loc[mask, field] = csv_row[field]\n", "                    row_updated = True\n", "            \n", "            if row_updated:\n", "                updated_rows += 1\n", "                print(f\"✅ Aggiornato {csv_row['Nome']} {csv_row['Cognome']}\")\n", "        else:\n", "            print(f\"❌ Non trovato nel database: {csv_row['Nome']} {csv_row['Cognome']}\")\n", "            # Mostra i nomi nel database simili per debug\n", "            print(\"Nomi simili nel database:\")\n", "            similar_names = df_db[\n", "                (df_db['Nome_norm'].str.contains(csv_row['Nome_norm'], na=False)) |\n", "                (df_db['Cognome_norm'].str.contains(csv_row['Cognome_norm'], na=False))\n", "            ][['Nome', 'Cognome']]\n", "            print(similar_names)\n", "    \n", "    # Rimuovi le colonne temporanee prima del salvataggio\n", "    df_db = df_db.drop(['Nome_norm', 'Cognome_norm'], axis=1)\n", "    \n", "    # Salva le modifiche nel database\n", "    try:\n", "        df_db.to_sql('players_latest', engine, if_exists='replace', index=False)\n", "        print(\"\\n✅ Database aggiornato con successo!\")\n", "        print(f\"Righe totali nel database: {len(df_db)}\")\n", "        print(f\"Righe con Nazionalità: {df_db['Nazionalità'].count()}\")\n", "        print(f\"<PERSON>igh<PERSON> con Altezza: {df_db['Altezza'].count()}\")\n", "        print(f\"\\nTotale righe aggiornate: {updated_rows}\")\n", "    except Exception as e:\n", "        print(f\"❌ Errore nel salvataggio: {e}\")\n", "\n", "# Esegui la funzione\n", "update_players_latest_from_csv()"]}, {"cell_type": "code", "execution_count": 5, "id": "7f9dd184", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Numero di righe nel dataframe: 743\n"]}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "PlayerID", "rawType": "int64", "type": "integer"}, {"name": "Nome", "rawType": "object", "type": "string"}, {"name": "Cognome", "rawType": "object", "type": "string"}, {"name": "<PERSON><PERSON><PERSON>", "rawType": "int64", "type": "integer"}, {"name": "RuoloDescr", "rawType": "object", "type": "string"}, {"name": "NumeroMaglia", "rawType": "int64", "type": "integer"}, {"name": "Nazionalità", "rawType": "object", "type": "string"}, {"name": "DataNascita", "rawType": "object", "type": "string"}, {"name": "Altezza", "rawType": "object", "type": "string"}, {"name": "Peso", "rawType": "object", "type": "unknown"}, {"name": "Sc<PERSON><PERSON><PERSON>", "rawType": "object", "type": "unknown"}, {"name": "<PERSON><PERSON>", "rawType": "object", "type": "unknown"}, {"name": "ManoDominante", "rawType": "object", "type": "unknown"}], "conversionMethod": "pd.DataFrame", "ref": "c48f07c0-071b-4337-a56b-0d5b655b2006", "rows": [["0", "11273", "<PERSON><PERSON>", "<PERSON><PERSON>", "5", "Palleggiatore", "13", "Portogallo", "15.07.2005", "181cm", "75kg", "310cm", "300cm", null], ["1", "11332", "Leonardo", "<PERSON><PERSON><PERSON>", "5", "Palleggiatore", "8", "Italia", "01.01.2006", "185cm", "75kg", null, null, null], ["2", "11365", "Mattia", "<PERSON><PERSON><PERSON>", "2", "Schiacciatore", "14", "Italia", "23.03.2004", "198cm", "64kg", "350cm", "342cm", null], ["3", "11602", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "5", "Palleggiatore", "3", "Polonia", "09.08.1994", "194cm", "88kg", "345cm", null, null], ["4", "11614", "<PERSON> antonio", "<PERSON><PERSON>", "1", "Libero", "30", "Italia", "10.08.2008", "181cm", null, null, null, null]], "shape": {"columns": 13, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PlayerID</th>\n", "      <th>Nome</th>\n", "      <th>Cognome</th>\n", "      <th>Ruolo</th>\n", "      <th>RuoloDescr</th>\n", "      <th>NumeroMaglia</th>\n", "      <th>Nazionalità</th>\n", "      <th>DataNascita</th>\n", "      <th>Altezza</th>\n", "      <th>Peso</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>ManoDominante</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>11273</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>5</td>\n", "      <td>Palleggiatore</td>\n", "      <td>13</td>\n", "      <td>Portogallo</td>\n", "      <td>15.07.2005</td>\n", "      <td>181cm</td>\n", "      <td>75kg</td>\n", "      <td>310cm</td>\n", "      <td>300cm</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>11332</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>5</td>\n", "      <td>Palleggiatore</td>\n", "      <td>8</td>\n", "      <td>Italia</td>\n", "      <td>01.01.2006</td>\n", "      <td>185cm</td>\n", "      <td>75kg</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>11365</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>2</td>\n", "      <td>Sc<PERSON>cci<PERSON><PERSON></td>\n", "      <td>14</td>\n", "      <td>Italia</td>\n", "      <td>23.03.2004</td>\n", "      <td>198cm</td>\n", "      <td>64kg</td>\n", "      <td>350cm</td>\n", "      <td>342cm</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>11602</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>5</td>\n", "      <td>Palleggiatore</td>\n", "      <td>3</td>\n", "      <td>Polonia</td>\n", "      <td>09.08.1994</td>\n", "      <td>194cm</td>\n", "      <td>88kg</td>\n", "      <td>345cm</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>11614</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>1</td>\n", "      <td>Libero</td>\n", "      <td>30</td>\n", "      <td>Italia</td>\n", "      <td>10.08.2008</td>\n", "      <td>181cm</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   PlayerID             Nome     Cognome  Ruolo     RuoloDescr  NumeroMaglia  \\\n", "0     11273            <PERSON><PERSON>      5  Palleggiatore            13   \n", "1     11332         <PERSON>      5  Palleggiatore             8   \n", "2     11365           Mattia      Orioli      2  Schiacciatore            14   \n", "3     11602           <PERSON><PERSON>      5  Palleggiatore             3   \n", "4     11614  Lorenzo antonio       Basso      1         Libero            30   \n", "\n", "  Nazionalità DataNascita Altezza  Peso Schiacciata   Muro ManoDominante  \n", "0  Portogallo  15.07.2005   181cm  75kg       310cm  300cm          None  \n", "1      Italia  01.01.2006   185cm  75kg        None   None          None  \n", "2      Italia  23.03.2004   198cm  64kg       350cm  342cm          None  \n", "3     Polonia  09.08.1994   194cm  88kg       345cm   None          None  \n", "4      Italia  10.08.2008   181cm  None        None   None          None  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Connessione al database\n", "engine = create_engine('postgresql+psycopg://postgres:AcquaLevissima1@localhost:5432/db_modena')\n", "\n", "# Leggi la tabella players_latest in un dataframe pandas\n", "df_players = pd.read_sql_query(\"SELECT * FROM players_latest\", engine)\n", "\n", "# Visualizza le prime righe del dataframe\n", "print(f\"Numero di righe nel dataframe: {len(df_players)}\")\n", "display(df_players.head())\n", "\n", "# Salva il dataframe come CSV\n", "df_players.to_csv(r\"C:\\Users\\<USER>\\Documents\\ModenaVolley\\VolleyballScraper\\data\\players_latest.csv\", index=False)"]}, {"cell_type": "code", "execution_count": null, "id": "938dcb09", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}