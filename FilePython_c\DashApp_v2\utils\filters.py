"""
Modulo per applicare i filtri ai dataframe
"""
import pandas as pd
from datetime import datetime

def apply_filters(raw_data, filters):
    """
    Applica i filtri ai dataframe raw e restituisce i dataframe filtrati
    
    Args:
        raw_data (dict): Dizionario con i dataframe originali
        filters (dict): Dizionario con i filtri selezionati
    
    Returns:
        dict: Dizionario con i dataframe filtrati
    """
    if not filters or not raw_data:
        return raw_data
    
    filtered_data = {}
    
    try:
        # Converti i dati raw da dizionari a DataFrame se necessario
        dfs = {}
        for key, data in raw_data.items():
            if isinstance(data, list) and data:
                dfs[key] = pd.DataFrame(data)
            elif isinstance(data, pd.DataFrame):
                dfs[key] = data.copy()
            else:
                dfs[key] = pd.DataFrame()
        
        # Estrai i filtri
        suddivisione_dati = filters.get('suddivisione_dati', 1)
        annata_range = filters.get('annata_range', [])
        start_date = filters.get('start_date')
        end_date = filters.get('end_date')
        campionati = filters.get('campionati', [])
        competitions = filters.get('competitions', [])
        team_proprio = filters.get('team_proprio', [])
        team_contro = filters.get('team_contro', [])
        partite = filters.get('partite', [])
        giocatori = filters.get('giocatori', [])
        ruoli = filters.get('ruoli', [])
        rotazione_propria = filters.get('rotazione_propria', [])
        rotazione_contro = filters.get('rotazione_contro', [])
        
        # Applica filtri ai games
        if 'df_games' in dfs and not dfs['df_games'].empty:
            df_games_filtered = filter_games(
                dfs['df_games'], 
                annata_range, 
                start_date, 
                end_date, 
                campionati, 
                competitions,
                team_proprio,
                team_contro
            )
            filtered_data['df_games'] = df_games_filtered
            
            # Lista delle partite valide dopo il filtraggio
            valid_games = df_games_filtered['GameID'].tolist() if not df_games_filtered.empty else []
            
            # Se sono state selezionate partite specifiche, usa quelle
            if partite:
                valid_games = [g for g in valid_games if g in partite]
        else:
            valid_games = partite if partite else []
        
        # Filtra i giocatori
        if 'df_players' in dfs and not dfs['df_players'].empty:
            filtered_data['df_players'] = filter_players(
                dfs['df_players'], 
                giocatori, 
                ruoli
            )
        
        # Filtra players_each_game
        if 'df_players_each_game' in dfs and not dfs['df_players_each_game'].empty:
            filtered_data['df_players_each_game'] = filter_players_each_game(
                dfs['df_players_each_game'],
                valid_games,
                giocatori,
                ruoli,
                team_proprio
            )
        
        # Filtra players_each_season
        if 'df_players_each_season' in dfs and not dfs['df_players_each_season'].empty:
            filtered_data['df_players_each_season'] = filter_players_each_season(
                dfs['df_players_each_season'],
                annata_range,
                giocatori,
                ruoli,
                team_proprio
            )
        
        # Filtra teams
        if 'df_teams' in dfs and not dfs['df_teams'].empty:
            filtered_data['df_teams'] = filter_teams(
                dfs['df_teams'],
                team_proprio
            )
        
        # Filtra teams_each_game
        if 'df_teams_each_game' in dfs and not dfs['df_teams_each_game'].empty:
            filtered_data['df_teams_each_game'] = filter_teams_each_game(
                dfs['df_teams_each_game'],
                valid_games,
                team_proprio
            )
        
        # Filtra teams_each_season
        if 'df_teams_each_season' in dfs and not dfs['df_teams_each_season'].empty:
            filtered_data['df_teams_each_season'] = filter_teams_each_season(
                dfs['df_teams_each_season'],
                annata_range,
                team_proprio
            )
        
        # Filtra teams_each_month
        if 'df_teams_each_month' in dfs and not dfs['df_teams_each_month'].empty:
            filtered_data['df_teams_each_month'] = filter_teams_each_month(
                dfs['df_teams_each_month'],
                annata_range,
                team_proprio
            )
        
        # Filtra players_each_month
        if 'df_players_each_month' in dfs and not dfs['df_players_each_month'].empty:
            filtered_data['df_players_each_month'] = filter_players_each_month(
                dfs['df_players_each_month'],
                annata_range,
                giocatori,
                ruoli,
                team_proprio
            )
        
        # Filtra rilevations
        if 'df_rilevations' in dfs and not dfs['df_rilevations'].empty:
            filtered_data['df_rilevations'] = filter_rilevations(
                dfs['df_rilevations'],
                valid_games,
                giocatori,
                ruoli,
                rotazione_propria,
                rotazione_contro
            )
        
        print(f"Filtri applicati con successo. Dataframe filtrati: {list(filtered_data.keys())}")
        
    except Exception as e:
        print(f"Errore nell'applicazione dei filtri: {e}")
        filtered_data = raw_data
    
    return filtered_data


def filter_games(df_games, annata_range, start_date, end_date, campionati, competitions, team_proprio, team_contro):
    """Filtra il dataframe dei games"""
    df_filtered = df_games.copy()
    
    # Filtra per annata
    if annata_range and len(annata_range) == 2:
        annata_min, annata_max = annata_range
        df_filtered = df_filtered[df_filtered['Annata'].between(annata_min, annata_max)]
    
    # Filtra per date
    if start_date and 'Date' in df_filtered.columns:
        start_dt = pd.to_datetime(start_date)
        # Assicurati che la colonna Date sia in formato datetime
        df_filtered['Date'] = pd.to_datetime(df_filtered['Date'])
        df_filtered = df_filtered[df_filtered['Date'] >= start_dt]
    
    if end_date and 'Date' in df_filtered.columns:
        end_dt = pd.to_datetime(end_date)
        # Assicurati che la colonna Date sia in formato datetime
        df_filtered['Date'] = pd.to_datetime(df_filtered['Date'])
        df_filtered = df_filtered[df_filtered['Date'] <= end_dt]
    
    # Filtra per campionato
    if campionati:
        df_filtered = df_filtered[df_filtered['Campionato'].isin(campionati)]
    
    # Filtra per competition
    if competitions:
        df_filtered = df_filtered[df_filtered['Competition'].isin(competitions)]
    
    # Filtra per team proprio
    if team_proprio:
        df_filtered = df_filtered[(df_filtered['TeamProprio'].isin(team_proprio))]
    
    # Filtra per team contro
    if team_contro:
        df_filtered = df_filtered[(df_filtered['TeamContro'].isin(team_contro))]
    
    return df_filtered


def filter_players(df_players, giocatori, ruoli):
    """Filtra il dataframe dei players"""
    df_filtered = df_players.copy()
    
    # Filtra per giocatori specifici
    if giocatori:
        df_filtered = df_filtered[df_filtered['PlayerID'].isin(giocatori)]
    
    # Filtra per ruoli
    if ruoli:
        df_filtered = df_filtered[df_filtered['RuoloCalc'].isin(ruoli)]
    
    return df_filtered


def filter_players_each_game(df_players_each_game, valid_games, giocatori, ruoli, team_proprio):
    """Filtra il dataframe dei players_each_game"""
    df_filtered = df_players_each_game.copy()
    
    # Filtra per partite valide
    if valid_games:
        df_filtered = df_filtered[df_filtered['GameID'].isin(valid_games)]
    
    # Filtra per giocatori specifici
    if giocatori:
        df_filtered = df_filtered[df_filtered['PlayerID'].isin(giocatori)]
    
    # Filtra per ruoli
    if ruoli:
        df_filtered = df_filtered[df_filtered['RuoloCalc'].isin(ruoli)]
    
    # Filtra per team proprio
    if team_proprio:
        df_filtered = df_filtered[df_filtered['TeamID'].isin(team_proprio)]
    
    return df_filtered


def filter_players_each_season(df_players_each_season, annata_range, giocatori, ruoli, team_proprio):
    """Filtra il dataframe dei players_each_season"""
    df_filtered = df_players_each_season.copy()
    
    # Filtra per annata
    if annata_range and len(annata_range) == 2:
        annata_min, annata_max = annata_range
        if 'Annata' in df_filtered.columns:
            df_filtered = df_filtered[df_filtered['Annata'].between(annata_min, annata_max)]
    
    # Filtra per giocatori specifici
    if giocatori:
        df_filtered = df_filtered[df_filtered['PlayerID'].isin(giocatori)]
    
    # Filtra per ruoli
    if ruoli:
        df_filtered = df_filtered[df_filtered['RuoloCalc'].isin(ruoli)]
    
    # Filtra per team proprio
    if team_proprio:
        df_filtered = df_filtered[df_filtered['TeamID'].isin(team_proprio)]
    
    return df_filtered


def filter_teams(df_teams, team_proprio):
    """Filtra il dataframe dei teams"""
    df_filtered = df_teams.copy()
    
    # Filtra per team proprio
    if team_proprio:
        df_filtered = df_filtered[df_filtered['TeamID'].isin(team_proprio)]
    
    return df_filtered


def filter_teams_each_game(df_teams_each_game, valid_games, team_proprio):
    """Filtra il dataframe dei teams_each_game"""
    df_filtered = df_teams_each_game.copy()
    
    # Filtra per partite valide
    if valid_games:
        df_filtered = df_filtered[df_filtered['GameID'].isin(valid_games)]
    
    # Filtra per team proprio
    if team_proprio:
        df_filtered = df_filtered[df_filtered['TeamID'].isin(team_proprio)]
    
    return df_filtered


def filter_rilevations(df_rilevations, valid_games, giocatori, ruoli, rotazione_propria, rotazione_contro):
    """Filtra il dataframe delle rilevations"""
    df_filtered = df_rilevations.copy()
    
    # Filtra per partite valide
    if valid_games:
        df_filtered = df_filtered[df_filtered['GameID'].isin(valid_games)]
    
    # Filtra per giocatori specifici
    if giocatori:
        df_filtered = df_filtered[df_filtered['PlayerID'].isin(giocatori)]
    
    # Filtra per ruoli
    if ruoli and 'RuoloCalc' in df_filtered.columns:
        df_filtered = df_filtered[df_filtered['RuoloCalc'].isin(ruoli)]
    
    # Filtra per rotazione propria
    if rotazione_propria and 'RotazionePropria' in df_filtered.columns:
        df_filtered = df_filtered[df_filtered['RotazionePropria'].isin(rotazione_propria)]
    
    # Filtra per rotazione contro
    if rotazione_contro and 'RotazioneContro' in df_filtered.columns:
        df_filtered = df_filtered[df_filtered['RotazioneContro'].isin(rotazione_contro)]
    
    return df_filtered


def filter_teams_each_season(df_teams_each_season, annata_range, team_proprio):
    """Filtra il dataframe dei teams_each_season"""
    df_filtered = df_teams_each_season.copy()
    
    # Filtra per annata
    if annata_range and len(annata_range) == 2:
        annata_min, annata_max = annata_range
        if 'Annata' in df_filtered.columns:
            df_filtered = df_filtered[df_filtered['Annata'].between(annata_min, annata_max)]
    
    # Filtra per team proprio
    if team_proprio:
        df_filtered = df_filtered[df_filtered['TeamID'].isin(team_proprio)]
    
    return df_filtered


def filter_teams_each_month(df_teams_each_month, annata_range, team_proprio):
    """Filtra il dataframe dei teams_each_month"""
    df_filtered = df_teams_each_month.copy()
    
    # Filtra per annata
    if annata_range and len(annata_range) == 2:
        annata_min, annata_max = annata_range
        if 'Annata' in df_filtered.columns:
            df_filtered = df_filtered[df_filtered['Annata'].between(annata_min, annata_max)]
    
    # Filtra per team proprio
    if team_proprio:
        df_filtered = df_filtered[df_filtered['TeamID'].isin(team_proprio)]
    
    return df_filtered


def filter_players_each_month(df_players_each_month, annata_range, giocatori, ruoli, team_proprio):
    """Filtra il dataframe dei players_each_month"""
    df_filtered = df_players_each_month.copy()
    
    # Filtra per annata
    if annata_range and len(annata_range) == 2:
        annata_min, annata_max = annata_range
        if 'Annata' in df_filtered.columns:
            df_filtered = df_filtered[df_filtered['Annata'].between(annata_min, annata_max)]
    
    # Filtra per giocatori specifici
    if giocatori:
        df_filtered = df_filtered[df_filtered['PlayerID'].isin(giocatori)]
    
    # Filtra per ruoli
    if ruoli:
        df_filtered = df_filtered[df_filtered['RuoloCalc'].isin(ruoli)]
    
    # Filtra per team proprio
    if team_proprio:
        df_filtered = df_filtered[df_filtered['TeamID'].isin(team_proprio)]
    
    return df_filtered


def get_filtered_data_summary(filtered_data):
    """Restituisce un riassunto dei dati filtrati"""
    summary = {}
    for key, df in filtered_data.items():
        if isinstance(df, pd.DataFrame):
            summary[key] = {
                'rows': len(df),
                'columns': len(df.columns) if hasattr(df, 'columns') else 0,
                'empty': df.empty
            }
        elif isinstance(df, list):
            summary[key] = {
                'rows': len(df),
                'empty': len(df) == 0
            }
    return summary