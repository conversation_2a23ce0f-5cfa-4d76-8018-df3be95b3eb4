{"cells": [{"cell_type": "markdown", "id": "ef1141ff", "metadata": {}, "source": ["In questo notebook creo la materialized view teams_each_game_classifica che contiene le squadre, \n", "- quanti set hanno vinto\n", "- quanti set hanno perso\n", "- punti fatti\n", "- punti subiti\n", "- BP fatti\n", "- BP subiti\n", "- Quo<PERSON>nte set\n", "- <PERSON><PERSON><PERSON><PERSON> punti\n", "- Quoziente BP\n", "- Vittoria 1set\n", "- Vittoria 2set\n", "- Vittoria 3set\n", "- Vittoria 4set (NULL se non c'è stato)\n", "- Vittoria 5set (NULL se non c'è stato)\n", "- Punti in classifica prima dell'inizio partita\n", "- Posizione in classifica prima dell'inizio partita\n", "- Punti in classifica dopo la partita\n", "- Posizione in classifica daopo la partita\n", "- Punti in classifica fatti\n", "\n", "Stesse colonne ma invece che della singola partita, cumulate di tutte le partite di Regular Season da inizio stagione.\n", "- ELO prima della partita\n", "- ELO dopo la partita\n", "\n", "\n", "\n", "\n", "Fai queste cose di default solo per le partite di Superlega, perchè metti un WHERE \"Competition\" IN dizionario, dove dizionario ha di default solo Superlega. (oppure falle anche per tutte le altre a parte le amichevoli)\n"]}, {"cell_type": "markdown", "id": "661fb4b3", "metadata": {}, "source": ["Crea grafici sull'andamento del punteggio, basandoti su *p e ap, prendendo *p e ap per ogni set. (prendi i valori distinti di ActionNumber e plotta PunteggioCasa e PunteggioOspiti)"]}, {"cell_type": "code", "execution_count": null, "id": "93bef9ba", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "dcc1070f", "metadata": {}, "source": ["Possiamo fare che a inizio stagione tutte le squadre partono da 1000 ELO (o un altro sistema di punteggio) e in base a contro chi vincono si ricalcola il punteggio.\n", "\n", "L'ELO vale per tutte le competizioni. Per le squadre estere di cui non conosciamo l'andamento, o lo troviamo dal loro campioniamo, o mettiamo ogni volta che il loro punteggio equivale alla squadra italiana che stanno sfidando."]}, {"cell_type": "markdown", "id": "8b9aa778", "metadata": {}, "source": ["A inizio stagione della prima stagione per cui abbiamo i dati, tutte le squadre partono da 1000 ELO. Quando inizia una nuova stagione, possiamo o rimettere tutte a 1000, ma questo potrebbe essere eccessivo, oppure lasciare gli ELO come sono, ma questo potrebbe essere eccessivo siccome i roster cambiano di anno in anno, oppure potremmo fare una via di mezzo:\n", "- Se la squadra ha meno di 1000 ELO, punteggio = punteggio + |1000 - punteggio|/2\n", "- Se la squadra ha più di 1000 ELO, punteggio = punteggio - |1000 - punteggio|/2\n", "\n", "In questo modo schiaccio le squadre verso i 1000 ELO. Invece di mettere 2 a denominatore, se lo aumento schiaccio di meno, se lo diminuisco schiaccio di più. (quanto schiacciare lo decido anche in base a quanto sono più o meno di solito i punteggi a fine stagione)"]}, {"cell_type": "markdown", "id": "b99dc397", "metadata": {}, "source": ["<PERSON>va vari sistemi, an<PERSON> so<PERSON>, tipo classifica in base alla differenza set cumulata, al quoziente set, alla differenza punti cumulata, al quoziente punti\n", "\n", "Poi puoi cercare correlazioni in comportamenti tra squadre che sfidano altre squadre che hanno un certo posizionamento in quella classifica rispetto a loro. (es cosa fa di solito il trento contro le squadre che hanno un quoziente punti X volte superiore, Y volte superiore, X volte inferiore ecc? Cosa fa nel senso di con che rotazioni parte, con che formazione, quali combinazioni fa di più al primo set, quali chiamate dell'alzatore...)"]}, {"cell_type": "markdown", "id": "00f1ee0e", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "b893e197", "metadata": {}, "source": ["\"Throw-away games\" are games where teams have already earned playoff slots and have secured their playoff seeding before the end of the regular season, and want to rest/protect their starting players by benching them for remaining regular season games. This usually results in unpredictable outcomes and may skew the outcome of rating systems.\n", "\n", "#### Team composition\n", "Teams often shift their composition between and within games, and players routinely get injured. Rating a team is often about rating a specific collection of players. Some systems assume parity among all members of the league, such as each team being built from an equitable pool of players via a draft or free agency system as is done in many major league sports such as the NFL, MLB, NBA, and NHL. This is certainly not the case in collegiate leagues such as Division I-A football or men's and women's basketball.\n", "\n", "#### Cold start\n", "At the beginning of a season, there have been no games from which to judge teams' relative quality. Solutions to the cold start problem often include some measure of the previous season, perhaps weighted by what percent of the team is returning for the new season. ARGH Power Ratings is an example of a system that uses multiple previous years plus a percentage weight of returning players.\n", "Figo! possiamo resettare l'ELO con il denominatore in base a quanti giocatori sono rimasti o andati via dalla squadra."]}, {"cell_type": "markdown", "id": "b0f58c45", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "dddd4d52", "metadata": {}, "source": ["Il punto è che i criteri di classifica tradizionali (3pt per 3-0 o 3-1, 2pt per 3-2, 1pt per 2-3, 0pt per 1-3 o 0-3) sono informativi solo dopo tutte le partite di andata o dopo andata e ritorno, ma a metà lo sono meno, perchè stiamo pesando allo stesso modo le partite contro avversari diversi (stiamo pesando allo stesso modo partite con Taranto che partite con Trento).\n", "\n", "Dobbiamo quindi trovare altri metodo per fare un ranking delle squadre e/o assegnandogli punteggi che ci diano un'informazione sulla loro forza in qualsiasi momento della stagione. (ad esempio dopo 2 partite la classifica classica non è molto informativa, perchè la stagione è appena iniziata). O comunque se la prima partita si sfidano Trento e Perugia, idealmente le più forti, alla fine la classifica non rispecchierà la loro forza effettiva."]}, {"cell_type": "markdown", "id": "62bd2750", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "427525a2", "metadata": {}, "source": ["Potrei anche assegnare un ELO ad ogni giocatore, che varia in base all'esito della partita, oppure in base alle performance (?)\n", "\n", "Se assegno un ELO a ogni giocatore, come trovo l'ELO della squadra?"]}, {"cell_type": "markdown", "id": "dfad8183", "metadata": {}, "source": ["Posso mettere il coefficiente K = 32 di base, ma per le prime partite, quelle di andata, siccome le squadre cambiano, posso metterlo un po' più alto, ad esempio in base a quanti giocatori sono cambiati rispetto alla stagione prima.\n", "\n", "Ad esempio K = K + X * n/14\n", "\n", "Dove X è un numero con cui controllo il peso che ha il fatto di cambiare giocatori, e n è il numero di giocatori cambiati dalla stagione precedente.\n", "\n", "<PERSON><PERSON><PERSON>, se avessi non solo i dati dei giocatori che vanno, ma anche di quelli che arrivano, allora potrei fare un coefficiente che tiene conto del numero di azioni giocate nella stagione scorsa dai giocatori che arrivano, per capire quanto sono forti/quanto giocheranno, e quindi quanto cambiano la squadra.\n", "\n"]}, {"cell_type": "markdown", "id": "246b4931", "metadata": {}, "source": ["Oppure un K che varia da solo nel tempo, come nel sistema Glicko(?)"]}, {"cell_type": "markdown", "id": "572f2aaf", "metadata": {}, "source": ["Oppure un sitema ELO in cui, ad inizio stagione, il punteggio di tutti i team di un campionato torna verso la media del campionato.\n", "\n", "Ad esempio se la media è 1600, il nuovo punteggio di ogni team diventa punteggio+(mediaCampionato-punteggio)/2\n", "\n", "(es se ho un punteggio di 1000 diventa 1000+(1600-1000)/2 = 1300, se ho 2000 diventa 1800)"]}, {"cell_type": "markdown", "id": "410688ec", "metadata": {}, "source": ["Oppure come sopra ma alzo anche K a inizio stagione"]}, {"cell_type": "markdown", "id": "dc82c87d", "metadata": {}, "source": ["Oppure faccio un ELO che continua sempre senza modificare K o ricentrare i punteggi."]}, {"cell_type": "markdown", "id": "fdab3850", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "ee1315af", "metadata": {}, "source": ["Possiamo aggiornare gli ELO di due squadre considerando i singoli set, ovvero considerando i singoli set come vittorie/sconfitte, e aggiornare i punteggi dopo aver giocato tutti i set."]}, {"cell_type": "markdown", "id": "bc418937", "metadata": {}, "source": ["Oppure possiamo calcolare Sa (il risultato effettivo di A) come proporzione tra i set vinti e quelli totali, ovvero Sa=setA/(setA+setB). In questo modo dare 1 (3/3) se vinci 3-0, 0-75 (3/4) se vinci 3-1, 0.6 (3/5) se vinci 3-2, 0.4 (2/5) se perdi 2-3, 0.25 (1/4) se perdi 1-3, 0 (0/3) se perdi 0-3.\n", "\n", "<PERSON><PERSON><PERSON><PERSON> quindi che il nuovo punteggio di A è R'a=Ra + K*(Sa - Ea)\n"]}, {"cell_type": "markdown", "id": "139ca2b8", "metadata": {}, "source": ["Oppure dai 1 per 3-0 e 3-1, 0.67 per 3-2, 0.33 per 2-3, 0 per 1-3 e 0-3."]}, {"cell_type": "markdown", "id": "1673bcc9", "metadata": {}, "source": ["Oppure R’a = Ra + K*(Sa— Ea) + L*Pa/(Pa +Pb), dove L è un nuovo parametro (non ci serve, al<PERSON>o in Superlega, perchè tutti sfidano tutti, sia in casa che in trasferta)\n", "\n", "Però potrei calcolare home_advantage come (%di vittorie in casa - 50%), e usarlo per calcolare un nuovo ELO"]}, {"cell_type": "markdown", "id": "82cc4435", "metadata": {}, "source": ["R’a = Ra + K*(<PERSON> — <PERSON>a), where R’a represents the new, updated, Elo rating of the player after the match, <PERSON> represents the Elo rating that the player had before the match, Ea denotes the expected outcome of the match and SA is the actual outcome of the match. Variable K denotes a scaling factor that determines how much influence each particular match can have on the overall Elo rating of the player. There are several ways to derive this value. In practice, the factor K has a different value in different levels of competition, for example in different leagues of the competition. A good starting value is K = 32 with which you can start to experiment.\n", "\n", "As we mentioned before, each match can have only three possible outcomes from the perspective of player <PERSON>. This is reflected in the values that the variable SA takes, in the case of victory Sa = 1, in the case of loss, Sa = 0, and in the case of a tie Sa = 0.5."]}, {"cell_type": "markdown", "id": "2283eb59", "metadata": {}, "source": ["Puoi trovare gli expected kill, o coumnque trovare le situazioni in cui si fa più punto in attacco, in base alla posizione xyz della palla, l'altezza del salto, la velocità dell'alzata, la parabola dell'alzata, le xyz della palla al momento dell'alzata, il numero di combinazioni possibili, il numero di giocatori a muro, chi è a muro, in che zona, la disposizione degli altri giocatori in campo, chi sono/che performance hanno. L'altezza del muro, la sua invadenza, la sua compattezza..."]}, {"cell_type": "markdown", "id": "73de5b25", "metadata": {}, "source": ["Parti facendo un ELO solo per le squadre di Superlega, poi se avrai anche altri dati pensi ad uno cross-league"]}, {"cell_type": "code", "execution_count": null, "id": "3c2152de", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "064d8e0b", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 1, "id": "ba616779", "metadata": {}, "outputs": [], "source": ["import numpy as np \n", "import pandas as pd  #Realizzato con la versione di Pandas 2.2.3\n", "import polars as pl\n", "import os\n", "import psycopg\n", "from sqlalchemy import create_engine, text"]}, {"cell_type": "code", "execution_count": 2, "id": "f712fe87", "metadata": {}, "outputs": [], "source": ["conn = psycopg.connect(\n", "    dbname=\"db_modena\",           # database creato in pgAdmin4\n", "    user=\"postgres\",              # Il tuo nome utente PostgreSQL\n", "    password=\"AcquaLevissima1\",   # La password che hai scelto per 'postgres'\n", "    host=\"localhost\",             # 'localhost' se è sul tuo PC\n", "    port=5432                     # La porta predefinita è 5432\n", ")\n", "\n", "cur = conn.cursor()"]}, {"cell_type": "code", "execution_count": 3, "id": "5476e7f2", "metadata": {}, "outputs": [], "source": ["cur.execute(\"\"\"\n", "DROP VIEW IF EXISTS teams_each_game_classifica;\n", "\"\"\")\n", "conn.commit()"]}, {"cell_type": "code", "execution_count": 6, "id": "cfdf9a09", "metadata": {}, "outputs": [], "source": ["conn.rollback()\n", "\n", "cur.execute(\"\"\"\n", "CREATE OR REPLACE VIEW teams_each_game_classifica AS\n", "SELECT \n", "    t.\"GameID\",  \n", "    g.\"DateSQL\",\n", "    g.\"<PERSON><PERSON>\",\n", "    g.\"Competition\",\n", "    g.\"Phase\",\n", "    g.\"<PERSON><PERSON><PERSON><PERSON>\",\n", "    t.\"TeamID\", \n", "    t.\"<PERSON><PERSON>ame\", \n", "    t.\"HomeAway\",\n", "    \n", "    \n", "    -- 1) SetVinti: numero di set vinti in quella partita\n", "    CASE\n", "        WHEN t.\"TeamID\" = g.\"IDHomeTeam\" THEN g.\"HomeTeamSetWon\"\n", "        ELSE g.\"VisitorTeamSetWon\"\n", "    END AS \"SetVinti\",\n", "\n", "    -- 2) SetVinti_season: cumulato dai match di Superlega nella stessa stagione\n", "    SUM(\n", "        CASE \n", "            WHEN g.\"Competition\" = 'Superlega' THEN\n", "                CASE\n", "                    WHEN t.\"TeamID\" = g.\"IDHomeTeam\" THEN g.\"HomeTeamSetWon\"\n", "                    WHEN t.\"TeamID\" = g.\"IDVisitorTeam\" THEN g.\"VisitorTeamSetWon\"\n", "                    ELSE NULL\n", "                END\n", "            ELSE 0\n", "        END\n", "    ) OVER (\n", "        PARTITION BY t.\"TeamID\", g.\"Anna<PERSON>\"\n", "        ORDER BY g.\"DateSQL\", g.\"GameID\"\n", "        ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW\n", "    ) AS \"SetVinti_season\"\n", "FROM teams_each_game AS t\n", "JOIN games_view AS g\n", "  ON t.\"GameID\" = g.\"GameID\";\n", "\n", "\"\"\")\n", "\n", "conn.commit()\n"]}, {"cell_type": "code", "execution_count": null, "id": "9e5835aa", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}