{"cells": [{"cell_type": "code", "execution_count": 2, "id": "004a5da8", "metadata": {}, "outputs": [], "source": ["import numpy as np \n", "import pandas as pd  #Realizzato con la versione di Pandas 2.2.3\n", "import os\n", "import duckdb\n", "import psycopg\n", "from sqlalchemy import create_engine, text\n", "\n", "import plotly.graph_objects as go\n", "from dash import Dash, dcc, html, Input, Output"]}, {"cell_type": "code", "execution_count": 3, "id": "07b7955a", "metadata": {}, "outputs": [{"data": {"text/plain": ["<duckdb.duckdb.DuckDBPyConnection at 0x133b026b6f0>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Connessione a DuckDB\n", "con = duckdb.connect('db_modena_1.duckdb')\n", "\n", "# Installazione dell'estensione postgres se non è già installata\n", "con.execute(\"INSTALL postgres\")\n", "con.execute(\"LOAD postgres\")"]}, {"cell_type": "markdown", "id": "1bb6ddf4", "metadata": {}, "source": ["## Ora creiamo i grafici con il campo"]}, {"cell_type": "markdown", "id": "ab193fb8", "metadata": {}, "source": ["Possiamo mettere che facendo hover nel quadrato della zona compare un grafico a barre/nastri con la distribuzione degli attacchi\n"]}, {"cell_type": "markdown", "id": "23da4e5d", "metadata": {}, "source": ["<PERSON>ti anche il menu a tendina per \n", "- <PERSON><PERSON>\n", "- Campionato\n", "- Competition\n", "- Phase\n", "- RuoloCalc\n", "- StartZone"]}, {"cell_type": "markdown", "id": "38ec4221", "metadata": {}, "source": ["Oppure facciamo che puoi selezionare il GameID e/o TeamAttaccante, TeamDifensore\n", "Vogliamo anche che le opzioni in un menu siano solo quelle coerenti con quelle già fatte, oppure le mostriamo sempre tutte? Però in questo caso al momento, nel menu giocatori, vengono mostrati solo quelli che hanno fatto almeno un attacco."]}, {"cell_type": "code", "execution_count": 4, "id": "d6d850d9", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "        <iframe\n", "            width=\"100%\"\n", "            height=\"650\"\n", "            src=\"http://127.0.0.1:8053/\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "            \n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.IFrame at 0x133b0217770>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# caricamento iniziale\n", "df_attacks_raw = con.execute(\"\"\"\n", "SELECT\n", "    \"GameID\",\n", "    \"whichTeamIDreal\",\n", "    \"NumeroMaglia_ID\",\n", "    'End_' || \"EndZoneEsecZone\" AS EndZone\n", "FROM rilevations_libero_view_duckdb\n", "WHERE \"Foundamental\" = 'A'  -- Azioni di attacco\n", "  AND \"EndZoneEsecZone\" IS NOT NULL\n", "\"\"\").df()\n", "\n", "# Prepara opzioni dropdown\n", "game_options = [{'label': g, 'value': g} for g in sorted(df_attacks_raw['GameID'].unique())]\n", "# Placeholder until game is selected\n", "df_filtered_game = df_attacks_raw[df_attacks_raw['GameID'] == game_options[0]['value']] if game_options else df_attacks_raw\n", "team_options = [{'label': t, 'value': t} for t in sorted(df_attacks_raw['whichTeamIDreal'].unique())]\n", "player_options = [{'label': p, 'value': p} for p in sorted(df_attacks_raw['NumeroMaglia_ID'].unique())]\n", "\n", "# App Dash\n", "app = Dash(__name__)\n", "app.title = \"Attack Heatmap\"\n", "\n", "app.layout = html.Div([\n", "    html.H2(\"Heatmap Attacchi - Campo Avversario\"),\n", "    html.Div([\n", "        dcc.Dropdown(id='game-dropdown', options=game_options, placeholder='Seleziona GameID'),\n", "        dcc.Dropdown(id='team-dropdown', options=team_options, placeholder='Seleziona TeamID'),\n", "        dcc.Dropdown(id='player-dropdown', options=player_options, placeholder='Seleziona PlayerID')\n", "    ], style={'display':'flex','gap':'10px','margin-bottom':'20px'}),\n", "    dcc.Graph(id='heatmap-court')\n", "])\n", "\n", "@app.callback(\n", "    Output('heatmap-court', 'figure'),\n", "    Input('game-dropdown', 'value'),\n", "    Input('team-dropdown', 'value'),\n", "    Input('player-dropdown', 'value')\n", ")\n", "def update_heatmap(gameid, teamid, playerid):\n", "    # Filtra\n", "    df = df_attacks_raw.copy()\n", "    if gameid:\n", "        df = df[df['GameID'] == gameid]\n", "    if teamid:\n", "        df = df[df['whichTeamIDreal'] == teamid]\n", "    if playerid:\n", "        df = df[df['NumeroMaglia_ID'] == playerid]\n", "\n", "    # Conta occorrenze per zona 1-9\n", "    counts = df['EndZone'].value_counts().to_dict()\n", "        # Mappa zone a coordinate: (col, row) secondo la disposizione richiesta\n", "    # Zona numerate 1-9 come:\n", "    # [4 3 2]\n", "    # [7 8 9]\n", "    # [5 6 1]\n", "    zone_coords = {\n", "        '4': (0, 2), '3': (1, 2), '2': (2, 2),\n", "        '7': (0, 1), '8': (1, 1), '9': (2, 1),\n", "        '5': (0, 0), '6': (1, 0), '1': (2, 0)\n", "    }\n", "\n", "    # Crea figure vuota\n", "    fig = go.Figure()\n", "    # Normalizza colori\n", "    max_count = max(counts.values()) if counts else 1\n", "\n", "    for zone, (col, row) in zone_coords.items():\n", "        cnt = counts.get('End_' + zone, 0)\n", "        # Colora da bianco a rosso (Reverse grey): più attacchi = più scuro\n", "        intensity = cnt / max_count\n", "        color = f'rgba(255,0,0,{0.1 + 0.9*intensity})'\n", "        # Calcola le coordinate rettangolo (unità arbitrarie 0-3 e 0-3)\n", "        fig.add_shape(type='rect',\n", "            x0=col, y0=row,\n", "            x1=col+1, y1=row+1,\n", "            fillcolor=color,\n", "            line=dict(width=1, color='black')\n", "        )\n", "        # Aggiunge count come testo\n", "        fig.add_annotation(x=col+0.5, y=row+0.5,\n", "                           text=str(cnt), showarrow=False,\n", "                           font=dict(color='white' if intensity>0.5 else 'black'))\n", "\n", "    # Layout campo 3x3\n", "    fig.update_xaxes(showgrid=False, zeroline=False, visible=False, range=[0,3])\n", "    fig.update_yaxes(showgrid=False, zeroline=False, visible=False, range=[0,3])\n", "    fig.update_layout(\n", "        width=500, height=500,\n", "        title=\"<PERSON><PERSON><PERSON>à Attacchi per Zona\",\n", "        shapes=dict(layer='below')\n", "    )\n", "    return fig\n", "\n", "if __name__ == '__main__':\n", "    app.run(debug=True, port=8053)\n"]}, {"cell_type": "code", "execution_count": 5, "id": "b653bf90", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "        <iframe\n", "            width=\"100%\"\n", "            height=\"650\"\n", "            src=\"http://127.0.0.1:8053/\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "            \n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.IFrame at 0x133b47fa990>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["[2025-07-26 12:07:09,384] ERROR in app: Exception on /_dash-update-component [POST]\n", "Traceback (most recent call last):\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\flask\\app.py\", line 880, in full_dispatch_request\n", "    rv = self.dispatch_request()\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\flask\\app.py\", line 865, in dispatch_request\n", "    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]\n", "           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\dash\\dash.py\", line 1484, in dispatch\n", "    response_data = ctx.run(partial_func)\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\dash\\_callback.py\", line 698, in add_context\n", "    raise err\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\dash\\_callback.py\", line 689, in add_context\n", "    output_value = _invoke_callback(func, *func_args, **func_kwargs)  # type: ignore[reportArgumentType]\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\dash\\_callback.py\", line 58, in _invoke_callback\n", "    return func(*args, **kwargs)  # %% callback invoked %%\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20068\\1770681646.py\", line 71, in update_heatmap\n", "    counts = {z: df['EndZone'].str.replace('End_', '').value_counts().to_dict().get(z, 0) for z in zone_coords.keys()}\n", "                                                                                                   ^^^^^^^^^^^\n", "NameError: name 'zone_coords' is not defined. Did you mean: 'our_zone_coords'?\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\flask\\app.py\", line 1473, in wsgi_app\n", "    response = self.full_dispatch_request()\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\flask\\app.py\", line 882, in full_dispatch_request\n", "    rv = self.handle_user_exception(e)\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\flask\\app.py\", line 772, in handle_user_exception\n", "    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]\n", "           ~~~~~~~~~~~~~~~~~~~~~~~~~^^^\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\dash\\_jupyter.py\", line 462, in _wrap_errors\n", "    ipytb = FormattedTB(\n", "        tb_offset=skip,\n", "    ...<3 lines>...\n", "        ostream=ostream,\n", "    )\n", "TypeError: FormattedTB.__init__() got an unexpected keyword argument 'color_scheme'\n", "Error on request:\n", "Traceback (most recent call last):\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\flask\\app.py\", line 880, in full_dispatch_request\n", "    rv = self.dispatch_request()\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\flask\\app.py\", line 865, in dispatch_request\n", "    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]\n", "           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\dash\\dash.py\", line 1484, in dispatch\n", "    response_data = ctx.run(partial_func)\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\dash\\_callback.py\", line 698, in add_context\n", "    raise err\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\dash\\_callback.py\", line 689, in add_context\n", "    output_value = _invoke_callback(func, *func_args, **func_kwargs)  # type: ignore[reportArgumentType]\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\dash\\_callback.py\", line 58, in _invoke_callback\n", "    return func(*args, **kwargs)  # %% callback invoked %%\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20068\\1770681646.py\", line 71, in update_heatmap\n", "    counts = {z: df['EndZone'].str.replace('End_', '').value_counts().to_dict().get(z, 0) for z in zone_coords.keys()}\n", "                                                                                                   ^^^^^^^^^^^\n", "NameError: name 'zone_coords' is not defined. Did you mean: 'our_zone_coords'?\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\flask\\app.py\", line 1473, in wsgi_app\n", "    response = self.full_dispatch_request()\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\flask\\app.py\", line 882, in full_dispatch_request\n", "    rv = self.handle_user_exception(e)\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\flask\\app.py\", line 772, in handle_user_exception\n", "    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]\n", "           ~~~~~~~~~~~~~~~~~~~~~~~~~^^^\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\dash\\_jupyter.py\", line 462, in _wrap_errors\n", "    ipytb = FormattedTB(\n", "    \n", "TypeError: FormattedTB.__init__() got an unexpected keyword argument 'color_scheme'\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\werkzeug\\serving.py\", line 370, in run_wsgi\n", "    execute(self.server.app)\n", "    ~~~~~~~^^^^^^^^^^^^^^^^^\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\werkzeug\\serving.py\", line 331, in execute\n", "    application_iter = app(environ, start_response)\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\flask\\app.py\", line 1498, in __call__\n", "    return self.wsgi_app(environ, start_response)\n", "           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\flask\\app.py\", line 1476, in wsgi_app\n", "    response = self.handle_exception(e)\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\flask\\app.py\", line 823, in handle_exception\n", "    server_error = self.ensure_sync(handler)(server_error)\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\dash\\_jupyter.py\", line 453, in _wrap_errors\n", "    skip = _get_skip(error) if dev_tools_prune_errors else 0\n", "           ~~~~~~~~~^^^^^^^\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\dash\\_jupyter.py\", line 47, in _get_skip\n", "    while tb.tb_next is not None:\n", "          ^^^^^^^^^^\n", "AttributeError: 'NoneType' object has no attribute 'tb_next'\n", "[2025-07-26 12:07:09,471] ERROR in app: Exception on /_dash-update-component [POST]\n", "Traceback (most recent call last):\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\flask\\app.py\", line 880, in full_dispatch_request\n", "    rv = self.dispatch_request()\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\flask\\app.py\", line 865, in dispatch_request\n", "    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]\n", "           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\dash\\dash.py\", line 1484, in dispatch\n", "    response_data = ctx.run(partial_func)\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\dash\\_callback.py\", line 698, in add_context\n", "    raise err\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\dash\\_callback.py\", line 689, in add_context\n", "    output_value = _invoke_callback(func, *func_args, **func_kwargs)  # type: ignore[reportArgumentType]\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\dash\\_callback.py\", line 58, in _invoke_callback\n", "    return func(*args, **kwargs)  # %% callback invoked %%\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20068\\1770681646.py\", line 71, in update_heatmap\n", "    counts = {z: df['EndZone'].str.replace('End_', '').value_counts().to_dict().get(z, 0) for z in zone_coords.keys()}\n", "                                                                                                   ^^^^^^^^^^^\n", "NameError: name 'zone_coords' is not defined. Did you mean: 'our_zone_coords'?\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\flask\\app.py\", line 1473, in wsgi_app\n", "    response = self.full_dispatch_request()\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\flask\\app.py\", line 882, in full_dispatch_request\n", "    rv = self.handle_user_exception(e)\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\flask\\app.py\", line 772, in handle_user_exception\n", "    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]\n", "           ~~~~~~~~~~~~~~~~~~~~~~~~~^^^\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\dash\\_jupyter.py\", line 462, in _wrap_errors\n", "    ipytb = FormattedTB(\n", "        tb_offset=skip,\n", "    ...<3 lines>...\n", "        ostream=ostream,\n", "    )\n", "TypeError: FormattedTB.__init__() got an unexpected keyword argument 'color_scheme'\n", "Error on request:\n", "Traceback (most recent call last):\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\flask\\app.py\", line 880, in full_dispatch_request\n", "    rv = self.dispatch_request()\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\flask\\app.py\", line 865, in dispatch_request\n", "    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]\n", "           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\dash\\dash.py\", line 1484, in dispatch\n", "    response_data = ctx.run(partial_func)\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\dash\\_callback.py\", line 698, in add_context\n", "    raise err\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\dash\\_callback.py\", line 689, in add_context\n", "    output_value = _invoke_callback(func, *func_args, **func_kwargs)  # type: ignore[reportArgumentType]\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\dash\\_callback.py\", line 58, in _invoke_callback\n", "    return func(*args, **kwargs)  # %% callback invoked %%\n", "  File \"C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20068\\1770681646.py\", line 71, in update_heatmap\n", "    counts = {z: df['EndZone'].str.replace('End_', '').value_counts().to_dict().get(z, 0) for z in zone_coords.keys()}\n", "                                                                                                   ^^^^^^^^^^^\n", "NameError: name 'zone_coords' is not defined. Did you mean: 'our_zone_coords'?\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\flask\\app.py\", line 1473, in wsgi_app\n", "    response = self.full_dispatch_request()\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\flask\\app.py\", line 882, in full_dispatch_request\n", "    rv = self.handle_user_exception(e)\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\flask\\app.py\", line 772, in handle_user_exception\n", "    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]\n", "           ~~~~~~~~~~~~~~~~~~~~~~~~~^^^\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\dash\\_jupyter.py\", line 462, in _wrap_errors\n", "    ipytb = FormattedTB(\n", "    \n", "TypeError: FormattedTB.__init__() got an unexpected keyword argument 'color_scheme'\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\werkzeug\\serving.py\", line 370, in run_wsgi\n", "    execute(self.server.app)\n", "    ~~~~~~~^^^^^^^^^^^^^^^^^\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\werkzeug\\serving.py\", line 331, in execute\n", "    application_iter = app(environ, start_response)\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\flask\\app.py\", line 1498, in __call__\n", "    return self.wsgi_app(environ, start_response)\n", "           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\flask\\app.py\", line 1476, in wsgi_app\n", "    response = self.handle_exception(e)\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\flask\\app.py\", line 823, in handle_exception\n", "    server_error = self.ensure_sync(handler)(server_error)\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\dash\\_jupyter.py\", line 453, in _wrap_errors\n", "    skip = _get_skip(error) if dev_tools_prune_errors else 0\n", "           ~~~~~~~~~^^^^^^^\n", "  File \"c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\.venv\\Lib\\site-packages\\dash\\_jupyter.py\", line 47, in _get_skip\n", "    while tb.tb_next is not None:\n", "          ^^^^^^^^^^\n", "AttributeError: 'NoneType' object has no attribute 'tb_next'\n"]}], "source": ["# Connessione DuckDB e caricamento iniziale\n", "df_attacks_raw = con.execute(\"\"\"\n", "SELECT\n", "    \"GameID\",\n", "    \"whichTeamIDreal\",\n", "    \"NumeroMaglia_ID\",\n", "    'End_' || \"EndZoneEsecZone\" AS EndZone\n", "FROM rilevations_libero_view_duckdb\n", "WHERE \"Foundamental\" = 'A'  -- Azioni di attacco\n", "  AND \"EndZoneEsecZone\" IS NOT NULL\n", "\"\"\").df()\n", "\n", "# Prepara opzioni dropdown\n", "game_options = [{'label': g, 'value': g} for g in sorted(df_attacks_raw['GameID'].unique())]\n", "team_options = [{'label': t, 'value': t} for t in sorted(df_attacks_raw['whichTeamIDreal'].unique())]\n", "player_options = [{'label': p, 'value': p} for p in sorted(df_attacks_raw['NumeroMaglia_ID'].unique())]\n", "\n", "# Nel nostro campo le zone sono:\n", "# [4 3 2]\n", "# [7 8 9]\n", "# [5 6 1]\n", "\n", "# Nel campo avversario le zone sono:\n", "# [1 6 5]\n", "# [9 8 7]\n", "# [2 3 4]\n", "\n", "our_zone_coords = {\n", "    '4': (0, 2), '3': (1, 2), '2': (2, 2),\n", "    '7': (0, 1), '8': (1, 1), '9': (2, 1),\n", "    '5': (0, 0), '6': (1, 0), '1': (2, 0)\n", "}\n", "\n", "opponent_zone_coords = {\n", "    '1': (0, 2), '6': (1, 2), '5': (2, 2),\n", "    '9': (0, 1), '8': (1, 1), '7': (2, 1),\n", "    '2': (0, 0), '3': (1, 0), '4': (2, 0)\n", "}\n", "\n", "# App Dash\n", "app = Dash(__name__)\n", "app.title = \"Attack Heatmap\"\n", "\n", "app.layout = html.Div([\n", "    html.H2(\"Heatmap Attacchi - Campo Avversario e Proprio Campo\"),\n", "    html.Div([\n", "        dcc.Dropdown(id='game-dropdown', options=game_options, placeholder='Seleziona GameID'),\n", "        dcc.Dropdown(id='team-dropdown', options=team_options, placeholder='Seleziona TeamID'),\n", "        dcc.Dropdown(id='player-dropdown', options=player_options, placeholder='Seleziona PlayerID')\n", "    ], style={'display': 'flex', 'gap': '10px', 'margin-bottom': '20px'}),\n", "    dcc.Graph(id='heatmap-court')\n", "])\n", "\n", "@app.callback(\n", "    Output('heatmap-court', 'figure'),\n", "    Input('game-dropdown', 'value'),\n", "    Input('team-dropdown', 'value'),\n", "    Input('player-dropdown', 'value')\n", ")\n", "def update_heatmap(gameid, teamid, playerid):\n", "    # Filtra dati\n", "    df = df_attacks_raw.copy()\n", "    if gameid:\n", "        df = df[df['GameID'] == gameid]\n", "    if teamid:\n", "        df = df[df['whichTeamIDreal'] == teamid]\n", "    if playerid:\n", "        df = df[df['NumeroMaglia_ID'] == playerid]\n", "\n", "    # Conta occorrenze per zona 1-9\n", "    counts = {z: df['EndZone'].str.replace('End_', '').value_counts().to_dict().get(z, 0) for z in zone_coords.keys()}\n", "    max_count = max(counts.values()) if counts else 1\n", "\n", "    # Inizializza la figura\n", "    fig = go.Figure()\n", "\n", "    # Funzione per disegnare un campo\n", "    def draw_field(x_offset, title, heatmap=True):\n", "        # Percorre ogni zona\n", "        for zone, (col, row) in opponent_zone_coords.items():\n", "            cnt = counts.get(zone, 0) if heatmap else 0\n", "            intensity = cnt / max_count if max_count else 0\n", "            # colore: rosso con trasparenza per avversario, grigio chiaro per proprio\n", "            if heatmap:\n", "                color = f'rgba(255,0,0,{0.1 + 0.9 * intensity})'\n", "            else:\n", "                color = 'rgba(200,200,200,0.1)'\n", "            # disegna rettangolo\n", "            fig.add_shape(type='rect',\n", "                x0=x_offset + col, y0=row,\n", "                x1=x_offset + col + 1, y1=row + 1,\n", "                fillcolor=color,\n", "                line=dict(width=1, color='black')\n", "            )\n", "            # testo count\n", "            text = str(cnt) if heatmap else ''\n", "            fig.add_annotation(\n", "                x=x_offset + col + 0.5, y=row + 0.5,\n", "                text=text,\n", "                showarrow=False,\n", "                font=dict(color='white' if intensity > 0.5 else 'black')\n", "            )\n", "        # titolo campo\n", "        fig.add_annotation(\n", "            x=x_offset + 1.5, y=3.1,\n", "            text=title,\n", "            showarrow=False,\n", "            xanchor='center',\n", "            font=dict(size=14, color='black')\n", "        )\n", "\n", "    # Disegna il campo avversario con heatmap\n", "    draw_field(0, '', heatmap=True)\n", "    # Disegna il proprio campo sotto, usando y_offset\n", "    def draw_own_field(y_offset):\n", "        for zone, (col, row) in opponent_zone_coords.items():\n", "            cnt = 0  # nessun heatmap\n", "            color = 'rgba(200,200,200,0.1)'\n", "            fig.add_shape(type='rect',\n", "                x0=col, y0=row - y_offset,\n", "                x1=col + 1, y1=row + 1 - y_offset,\n", "                fillcolor=color,\n", "                line=dict(width=1, color='black')\n", "            )\n", "            fig.add_annotation(\n", "                x=col + 0.5, y=row + 0.5 - y_offset,\n", "                text='', showarrow=False\n", "            )\n", "        # titolo proprio campo\n", "        fig.add_annotation(\n", "            x=1.5, y=3.1 - y_offset,\n", "            text='', \n", "            showarrow=False,\n", "            xanchor='center', font=dict(size=14, color='black')\n", "        )\n", "    # offset verticale del campo proprio di 3 unità (altezza campo)\n", "    draw_own_field(y_offset=3)\n", "\n", "    # Nasconde assi e imposta layout e imposta layout\n", "    fig.update_xaxes(showgrid=False, zeroline=False, visible=False, range=[-0.1, 3.1])  #indica da quanto a quanto mostrare il riquadro (unità di misura: colonne)\n", "    fig.update_yaxes(showgrid=False, zeroline=False, visible=False, range=[-3.1, 3.1], scaleanchor=\"x\", scaleratio=1)\n", "\n", "    fig.update_layout(\n", "        width=500,\n", "        height=800,\n", "        title=\"Heatmap Attacchi\",\n", "        margin=dict(l=20, r=20, t=50, b=20)  #spazio bianco oltre la parte azzurra\n", "    )\n", "    return fig\n", "\n", "if __name__ == '__main__':\n", "    app.run(debug=True, port=8053)\n"]}, {"cell_type": "code", "execution_count": 6, "id": "a94376e0", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "        <iframe\n", "            width=\"100%\"\n", "            height=\"650\"\n", "            src=\"http://127.0.0.1:8053/\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "            \n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.IFrame at 0x133b47fbd90>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Connessione DuckDB e caricamento iniziale\n", "df_attacks_raw = con.execute(\"\"\"\n", "SELECT\n", "    \"GameID\",\n", "    \"whichTeamIDreal\",\n", "    \"NumeroMaglia_ID\",\n", "    'End_' || \"EndZoneEsecZone\" AS EndZone,\n", "    \"Eval\"\n", "FROM rilevations_libero_view_duckdb\n", "WHERE \"Foundamental\" = 'A'  -- Azioni di attacco\n", "  AND \"EndZoneEsecZone\" IS NOT NULL\n", "\"\"\").df()\n", "\n", "\n", "# Mi preparo ad arricchire il menu a tendina GameID\n", "games_info = con.execute(\"\"\"\n", "SELECT \n", "    g.\"GameID\", \n", "    g.\"HomeTeamName\", \n", "    g.\"VisitorTeamName\", \n", "    g.\"Date\"\n", "FROM games_view_duckdb g\n", "WHERE g.\"GameID\" IN (SELECT DISTINCT \"GameID\" FROM rilevations_libero_view_duckdb)\n", "ORDER BY g.\"DateSQL\"  --<PERSON>, così saranno così anche nel menu a tendina\n", "\"\"\").df()\n", "\n", "# Mi preparo ad arricchire il menu a tendina whichTeamID\n", "teams_info = con.execute(\"\"\"\n", "SELECT \n", "    t.\"TeamID\", \n", "    t.\"<PERSON><PERSON>ame\"\n", "FROM teams_duckdb t\n", "WHERE t.\"TeamID\" IN (SELECT DISTINCT \"whichTeamIDreal\" FROM rilevations_libero_view_duckdb)\n", "ORDER BY t.\"TeamID\"  --<PERSON>, così saranno così anche nel menu a tendina\n", "\"\"\").df()\n", "\n", "# Mi preparo ad arricchire il menu a tendina NumeroMaglia_ID\n", "players_info = con.execute(\"\"\"\n", "SELECT DISTINCT\n", "    p.\"PlayerID\",\n", "    p.\"Nome\",\n", "    p.\"Cognome\"\n", "FROM players_each_game_duckdb p\n", "WHERE p.\"PlayerID\" IN (SELECT DISTINCT \"NumeroMaglia_ID\" FROM rilevations_libero_view_duckdb)\n", "ORDER BY p.\"Cognome\", p.\"Nome\"  -- Ordino per cognome e nome\n", "\"\"\").df()\n", "\n", "\n", "\n", "\n", "\n", "#opzioni del menu a tendina per GameID\n", "game_options = [\n", "    {'label': f\"{row['HomeTeamName']} - {row['VisitorTeamName']} ({row['Date']}) (GameID {g})\",   #quello che c'è scritto nel menu a tendina\n", "    'value': g}                                                                                  #il suo valore, quello che conta veramente\n", "    for g, row in games_info.set_index('GameID').iterrows() \n", "    if g in df_attacks_raw['GameID'].unique()\n", "]\n", "# Se non ci sono corrispondenze tra il GameID di rilevations e quello in games, mostra solo il GameID\n", "if not game_options:\n", "    game_options = [{'label': g, 'value': g} for g in sorted(df_attacks_raw['GameID'].unique())]\n", "    \n", "\n", "#opzioni del menu a tendina per whichTeamIDreal\n", "team_options = [\n", "    {'label': f\"{row['TeamName']} ({t})\",                  #quello che c'è scritto nel menu a tendina\n", "    'value': t}                                           #il suo valore, quello che conta veramente\n", "    for t, row in teams_info.set_index('TeamID').iterrows()   #per ogni TeamID nella tabella teams_duckdb\n", "    if t in df_attacks_raw['whichTeamIDreal'].unique()        #se quel TeamID è presente in df_attacks_raw['whichTeamIDreal']\n", "]\n", "# Se non ci sono corrispondenze tra il GameID di rilevations e quello in games, mostra solo il GameID\n", "if not team_options:\n", "    team_options = [{'label': t, 'value': t} for t in sorted(df_attacks_raw['whichTeamIDreal'].unique())]\n", "\n", "\n", "#opzioni del menu a tendina per NumeroMaglia_ID\n", "player_options = [\n", "    {'label': f\"{row['Cognome']} {row['Nome']} ({p})\",                  #quello che c'è scritto nel menu a tendina\n", "    'value': p}                                           #il suo valore, quello che conta veramente\n", "    for p, row in players_info.set_index('PlayerID').iterrows()   #per ogni TeamID nella tabella teams_duckdb\n", "    if p in df_attacks_raw['NumeroMaglia_ID'].unique()        #se quel TeamID è presente in df_attacks_raw['whichTeamIDreal']\n", "]\n", "# Se non ci sono corrispondenze tra il GameID di rilevations e quello in games, mostra solo il GameID\n", "if not player_options:\n", "    player_options = [{'label': p, 'value': p} for p in sorted(df_attacks_raw['NumeroMaglia_ID'].unique())]\n", "    \n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "# Nel nostro campo le zone sono:\n", "# [4 3 2]\n", "# [7 8 9]\n", "# [5 6 1]\n", "\n", "# Nel campo avversario le zone sono:\n", "# [1 6 5]\n", "# [9 8 7]\n", "# [2 3 4]\n", "\n", "our_zone_coords = {\n", "    '4': (0, 2), '3': (1, 2), '2': (2, 2),\n", "    '7': (0, 1), '8': (1, 1), '9': (2, 1),\n", "    '5': (0, 0), '6': (1, 0), '1': (2, 0)\n", "}\n", "\n", "opponent_zone_coords = {\n", "    '1': (0, 2), '6': (1, 2), '5': (2, 2),\n", "    '9': (0, 1), '8': (1, 1), '7': (2, 1),\n", "    '2': (0, 0), '3': (1, 0), '4': (2, 0)\n", "}\n", "\n", "# App Dash\n", "app = Dash(__name__)\n", "app.title = \"Attack Heatmap\"\n", "\n", "app.layout = html.Div([\n", "    html.H2(\"Heatmap Attacchi - Campo Avversario e Proprio Campo\"),\n", "    html.Div([\n", "        dcc.Dropdown(id='game-dropdown', options=game_options, placeholder='Seleziona GameID', multi=True),  #mettendo multi=True lo rendo a scelta multipla\n", "        dcc.Dropdown(id='team-dropdown', options=team_options, placeholder='Seleziona TeamID', multi=True),\n", "        dcc.Dropdown(id='player-dropdown', options=player_options, placeholder='Seleziona PlayerID', multi=True)\n", "    ], style={'display': 'flex', 'gap': '10px', 'margin-bottom': '20px'}),\n", "    dcc.Graph(id='heatmap-court')\n", "])\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "#Inizio a costruire il grafico\n", "@app.callback(\n", "    Output('heatmap-court', 'figure'),\n", "    Input('game-dropdown', 'value'),\n", "    Input('team-dropdown', 'value'),\n", "    Input('player-dropdown', 'value')\n", ")\n", "\n", "def update_heatmap(gameid, teamid, playerid):\n", "    # Filtra dati\n", "    df = df_attacks_raw.copy()\n", "    if gameid:\n", "        df = df[df['GameID'].isin(gameid)]  #se il menu è a scelta multipla, gameid è una lista con gli oggetti selezionati, quindi non posso mettere == gameid ma isin(gameid)\n", "    if teamid:\n", "        df = df[df['whichTeamIDreal'].isin(teamid)]\n", "    if playerid:\n", "        df = df[df['NumeroMaglia_ID'].isin(playerid)]\n", "\n", "    # Pulisce la zona (rimuove 'End_')\n", "    df['Zone'] = df['EndZone'].str.replace('End_', '')\n", "    \n", "    # Calcola statistiche per zona e valutazione\n", "    zone_stats = {}\n", "    eval_types = ['#', '+', '!', '-', '/', '=']\n", "    \n", "    for zone in opponent_zone_coords.keys():\n", "        zone_data = df[df['Zone'] == zone]\n", "        total_attacks = len(zone_data)\n", "        \n", "        eval_counts = {}\n", "        for eval_type in eval_types:\n", "            eval_counts[eval_type] = len(zone_data[zone_data['Eval'] == eval_type])\n", "        \n", "        zone_stats[zone] = {\n", "            'total': total_attacks,\n", "            'eval_counts': eval_counts\n", "        }\n", "\n", "\n", "    \n", "    # Trova il massimo per la scala dei colori\n", "    max_count = max([stats['total'] for stats in zone_stats.values()]) if zone_stats else 1\n", "\n", "    # Inizializza la figura\n", "    fig = go.Figure()\n", "\n", "    # Funzione per creare hover text\n", "    def create_hover_text(zone):\n", "        stats = zone_stats[zone]\n", "        if stats['total'] == 0:\n", "            return f\"<PERSON><PERSON> {zone}<br><PERSON><PERSON><PERSON> at<PERSON>\"\n", "        \n", "        hover_lines = [f\"Zona {zone}<br>Totale attacchi: {stats['total']}<br>\"]\n", "        for eval_type in eval_types:\n", "            count = stats['eval_counts'][eval_type]\n", "            if count > 0:\n", "                hover_lines.append(f\"{count} attacchi {eval_type}\")  #scritta quando fai hover\n", "        \n", "        return \"<br>\".join(hover_lines)\n", "\n", "    # Disegna il campo avversario con heatmap\n", "    for zone, (col, row) in opponent_zone_coords.items():\n", "        total_attacks = zone_stats[zone]['total']\n", "        intensity = total_attacks / max_count if max_count else 0\n", "        \n", "        # Colore: rosso con trasparenza basata sull'intensità\n", "        color = f'rgba(255,0,0,{0.1 + 0.9 * intensity})'\n", "        \n", "        # Crea hover text\n", "        hover_text = create_hover_text(zone)\n", "        \n", "        # Aggiunge un scatter point invisibile per l'hover\n", "        fig.add_trace(go.<PERSON>(\n", "            x=[col + 0.5],\n", "            y=[row + 0.5],\n", "            mode='markers',\n", "            marker=dict(size=50, color='rgba(0,0,0,0)'),  # Marker invisibile\n", "            hovertemplate=hover_text + '<extra></extra>',\n", "            showlegend=False,\n", "            name=f'<PERSON><PERSON> {zone}'\n", "        ))\n", "        \n", "        # Disegna rettangolo\n", "        fig.add_shape(type='rect',\n", "            x0=col, y0=row,\n", "            x1=col + 1, y1=row + 1,\n", "            fillcolor=color,\n", "            line=dict(width=1, color='black')\n", "        )\n", "        \n", "        # Testo count\n", "        fig.add_annotation(\n", "            x=col + 0.5, y=row + 0.5,\n", "            text=str(total_attacks),\n", "            showarrow=False,\n", "            font=dict(color='white' if intensity > 0.5 else 'black', size=12)\n", "        )\n", "\n", "    # Disegna il proprio campo (senza heatmap)\n", "    y_offset = 3\n", "    for zone, (col, row) in opponent_zone_coords.items():\n", "        color = 'rgba(200,200,200,0.1)'\n", "        fig.add_shape(type='rect',\n", "            x0=col, y0=row - y_offset,\n", "            x1=col + 1, y1=row + 1 - y_offset,\n", "            fillcolor=color,\n", "            line=dict(width=1, color='black')\n", "        )\n", "        \n", "        # Etichetta zona\n", "        fig.add_annotation(\n", "            x=col + 0.5, y=row + 0.5 - y_offset,\n", "            text=zone,\n", "            showarrow=False,\n", "            font=dict(color='gray', size=10)\n", "        )\n", "\n", "    # Aggiunge la rete (linea spessa tra i due campi)\n", "    fig.add_shape(type='line',\n", "        x0=-0.1, y0=0,\n", "        x1=3.1, y1=0,\n", "        line=dict(width=8, color='black')\n", "    )\n", "    \n", "    # Aggiunge titoli dei campi\n", "    fig.add_annotation(\n", "        x=-0.3, y=1.5,\n", "        text=\"Campo Difensori\",\n", "        showarrow=False,\n", "        textangle=-90,\n", "        xanchor='center',  #indica che le coordinate di x sono rispetto al punto centrale di x del grafico\n", "        yanchor='middle',  #indica che le coordinate di y sono rispetto al punto centrale di y del grafico\n", "        font=dict(size=14, color='black', weight='bold')\n", "    )\n", "    \n", "    fig.add_annotation(\n", "        x=-0.3, y=-1.5,\n", "        text=\"Campo Attaccanti\",\n", "        showarrow=False,\n", "        textangle=-90,\n", "        xanchor='center',\n", "        yanchor='middle',\n", "        font=dict(size=14, color='black', weight='bold')\n", "    )\n", "\n", "    # Configurazione layout\n", "    fig.update_xaxes(showgrid=False, zeroline=False, visible=False, range=[-0.1, 3.1])\n", "    fig.update_yaxes(showgrid=False, zeroline=False, visible=False, range=[-3.1, 3.5], scaleanchor=\"x\", scaleratio=1)\n", "\n", "    fig.update_layout(\n", "        width=500,\n", "        height=800,\n", "        title=\"Heatmap Attacchi\",\n", "        margin=dict(l=20, r=20, t=50, b=20),\n", "        hovermode='closest'\n", "    )\n", "    \n", "    return fig\n", "\n", "if __name__ == '__main__':\n", "    app.run(debug=True, port=8053)"]}, {"cell_type": "code", "execution_count": 7, "id": "dde14d02", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "        <iframe\n", "            width=\"100%\"\n", "            height=\"650\"\n", "            src=\"http://127.0.0.1:8053/\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "            \n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.IFrame at 0x133b4ff2c40>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Connessione DuckDB e caricamento iniziale\n", "df_attacks_raw = con.execute(\"\"\"\n", "SELECT\n", "    \"GameID\",\n", "    \"whichTeamIDreal\",\n", "    \"NumeroMaglia_ID\",\n", "    'End_' || \"EndZoneEsecZone\" AS EndZone\n", "FROM rilevations_libero_view_duckdb\n", "WHERE \"Foundamental\" = 'A'  -- Azioni di attacco\n", "  AND \"EndZoneEsecZone\" IS NOT NULL\n", "\"\"\").df()\n", "\n", "# Prepara opzioni dropdown\n", "game_options = [{'label': g, 'value': g} for g in sorted(df_attacks_raw['GameID'].unique())]\n", "team_options = [{'label': t, 'value': t} for t in sorted(df_attacks_raw['whichTeamIDreal'].unique())]\n", "player_options = [{'label': p, 'value': p} for p in sorted(df_attacks_raw['NumeroMaglia_ID'].unique())]\n", "\n", "# Nel nostro campo le zone sono:\n", "# [4 3 2]\n", "# [7 8 9]\n", "# [5 6 1]\n", "\n", "# Nel campo avversario le zone sono:\n", "# [1 6 5]\n", "# [9 8 7]\n", "# [2 3 4]\n", "\n", "our_zone_coords = {\n", "    '4': (0, 2), '3': (1, 2), '2': (2, 2),\n", "    '7': (0, 1), '8': (1, 1), '9': (2, 1),\n", "    '5': (0, 0), '6': (1, 0), '1': (2, 0)\n", "}\n", "\n", "opponent_zone_coords = {\n", "    '1': (0, 2), '6': (1, 2), '5': (2, 2),\n", "    '9': (0, 1), '8': (1, 1), '7': (2, 1),\n", "    '2': (0, 0), '3': (1, 0), '4': (2, 0)\n", "}\n", "\n", "# App Dash\n", "app = Dash(__name__)\n", "app.title = \"Attack Heatmap\"\n", "\n", "app.layout = html.Div([\n", "    html.H2(\"Heatmap Attacchi - Campo Avversario e Proprio Campo\"),\n", "    html.Div([\n", "        dcc.Dropdown(id='game-dropdown', options=game_options, placeholder='Seleziona GameID'),\n", "        dcc.Dropdown(id='team-dropdown', options=team_options, placeholder='Seleziona TeamID'),\n", "        dcc.Dropdown(id='player-dropdown', options=player_options, placeholder='Seleziona PlayerID')\n", "    ], style={'display': 'flex', 'gap': '10px', 'margin-bottom': '20px'}),\n", "    dcc.Graph(id='heatmap-court')\n", "])\n", "\n", "@app.callback(\n", "    Output('heatmap-court', 'figure'),\n", "    Input('game-dropdown', 'value'),\n", "    Input('team-dropdown', 'value'),\n", "    Input('player-dropdown', 'value')\n", ")\n", "def update_heatmap(gameid, teamid, playerid):\n", "    # Filtra dati\n", "    df = df_attacks_raw.copy()\n", "    if gameid:\n", "        df = df[df['GameID'] == gameid]\n", "    if teamid:\n", "        df = df[df['whichTeamIDreal'] == teamid]\n", "    if playerid:\n", "        df = df[df['NumeroMaglia_ID'] == playerid]\n", "\n", "    # Conta occorrenze per zona 1-9\n", "    counts = {z: df['EndZone'].str.replace('End_', '').value_counts().to_dict().get(z, 0) for z in zone_coords.keys()}\n", "    max_count = max(counts.values()) if counts else 1\n", "\n", "    # Inizializza la figura\n", "    fig = go.Figure()\n", "\n", "    # Funzione per disegnare un campo\n", "    def draw_field(x_offset, title, heatmap=True):\n", "        # Percorre ogni zona\n", "        for zone, (col, row) in opponent_zone_coords.items():\n", "            cnt = counts.get(zone, 0) if heatmap else 0\n", "            intensity = cnt / max_count if max_count else 0\n", "            # colore: rosso con trasparenza per avversario, grigio chiaro per proprio\n", "            if heatmap:\n", "                color = f'rgba(255,0,0,{0.1 + 0.9 * intensity})'\n", "            else:\n", "                color = 'rgba(200,200,200,0.1)'\n", "            # disegna rettangolo\n", "            fig.add_shape(type='rect',\n", "                x0=x_offset + col, y0=row,\n", "                x1=x_offset + col + 1, y1=row + 1,\n", "                fillcolor=color,\n", "                line=dict(width=1, color='black')\n", "            )\n", "            # testo count\n", "            text = str(cnt) if heatmap else ''\n", "            fig.add_annotation(\n", "                x=x_offset + col + 0.5, y=row + 0.5,\n", "                text=text,\n", "                showarrow=False,\n", "                font=dict(color='white' if intensity > 0.5 else 'black')\n", "            )\n", "        # titolo campo\n", "        fig.add_annotation(\n", "            x=x_offset + 1.5, y=3.1,\n", "            text=title,\n", "            showarrow=False,\n", "            xanchor='center',\n", "            font=dict(size=14, color='black')\n", "        )\n", "\n", "    # Disegna il campo avversario con heatmap\n", "    draw_field(0, '', heatmap=True)\n", "    # Disegna il proprio campo sotto, usando y_offset\n", "    def draw_own_field(y_offset):\n", "        for zone, (col, row) in opponent_zone_coords.items():\n", "            cnt = 0  # nessun heatmap\n", "            color = 'rgba(200,200,200,0.1)'\n", "            fig.add_shape(type='rect',\n", "                x0=col, y0=row - y_offset,\n", "                x1=col + 1, y1=row + 1 - y_offset,\n", "                fillcolor=color,\n", "                line=dict(width=1, color='black')\n", "            )\n", "            fig.add_annotation(\n", "                x=col + 0.5, y=row + 0.5 - y_offset,\n", "                text='', showarrow=False\n", "            )\n", "        # titolo proprio campo\n", "        fig.add_annotation(\n", "            x=1.5, y=3.1 - y_offset,\n", "            text='', \n", "            showarrow=False,\n", "            xanchor='center', font=dict(size=14, color='black')\n", "        )\n", "    # offset verticale del campo proprio di 3 unità (altezza campo)\n", "    draw_own_field(y_offset=3)\n", "\n", "    # Nasconde assi e imposta layout e imposta layout\n", "    fig.update_xaxes(showgrid=False, zeroline=False, visible=False, range=[-0.1, 3.1])  #indica da quanto a quanto mostrare il riquadro (unità di misura: colonne)\n", "    fig.update_yaxes(showgrid=False, zeroline=False, visible=False, range=[-3.1, 3.1], scaleanchor=\"x\", scaleratio=1)\n", "\n", "    fig.update_layout(\n", "        width=500,\n", "        height=800,\n", "        title=\"Heatmap Attacchi\",\n", "        margin=dict(l=20, r=20, t=50, b=20)  #spazio bianco oltre la parte azzurra\n", "    )\n", "    return fig\n", "\n", "if __name__ == '__main__':\n", "    app.run(debug=True, port=8053)\n"]}, {"cell_type": "code", "execution_count": 8, "id": "92516d82", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "        <iframe\n", "            width=\"100%\"\n", "            height=\"650\"\n", "            src=\"http://127.0.0.1:8053/\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "            \n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.IFrame at 0x133b4ff3bb0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Connessione DuckDB e caricamento iniziale\n", "df_attacks_raw = con.execute(\"\"\"\n", "SELECT\n", "    \"GameID\",\n", "    \"whichTeamIDreal\",\n", "    \"NumeroMaglia_ID\",\n", "    'End_' || \"EndZoneEsecZone\" AS EndZone,\n", "    \"Eval\"\n", "FROM rilevations_libero_view_duckdb\n", "WHERE \"Foundamental\" = 'A'  -- Azioni di attacco\n", "  AND \"EndZoneEsecZone\" IS NOT NULL\n", "\"\"\").df()\n", "\n", "\n", "# Mi preparo ad arricchire il menu a tendina GameID\n", "games_info = con.execute(\"\"\"\n", "SELECT \n", "    g.\"GameID\", \n", "    g.\"HomeTeamName\", \n", "    g.\"VisitorTeamName\", \n", "    g.\"Date\"\n", "FROM games_view_duckdb g\n", "WHERE g.\"GameID\" IN (SELECT DISTINCT \"GameID\" FROM rilevations_libero_view_duckdb)\n", "ORDER BY g.\"DateSQL\"  --<PERSON>, così saranno così anche nel menu a tendina\n", "\"\"\").df()\n", "\n", "# Mi preparo ad arricchire il menu a tendina whichTeamID\n", "teams_info = con.execute(\"\"\"\n", "SELECT \n", "    t.\"TeamID\", \n", "    t.\"<PERSON><PERSON>ame\"\n", "FROM teams_duckdb t\n", "WHERE t.\"TeamID\" IN (SELECT DISTINCT \"whichTeamIDreal\" FROM rilevations_libero_view_duckdb)\n", "ORDER BY t.\"TeamID\"  --<PERSON>, così saranno così anche nel menu a tendina\n", "\"\"\").df()\n", "\n", "# Mi preparo ad arricchire il menu a tendina NumeroMaglia_ID\n", "players_info = con.execute(\"\"\"\n", "SELECT DISTINCT\n", "    p.\"PlayerID\",\n", "    p.\"Nome\",\n", "    p.\"Cognome\"\n", "FROM players_each_game_duckdb p\n", "WHERE p.\"PlayerID\" IN (SELECT DISTINCT \"NumeroMaglia_ID\" FROM rilevations_libero_view_duckdb)\n", "ORDER BY p.\"Cognome\", p.\"Nome\"  -- Ordino per cognome e nome\n", "\"\"\").df()\n", "\n", "\n", "\n", "#definisco come vengono mostrati i testi in ogni menu a tendina\n", "def testi_nei_menu(df):\n", "\n", "    #opzioni del menu a tendina per GameID\n", "    game_options = [\n", "        {'label': f\"{row['HomeTeamName']} - {row['VisitorTeamName']} ({row['Date']}) (GameID {g})\",   #quello che c'è scritto nel menu a tendina\n", "        'value': g}                                                                                  #il suo valore, quello che conta veramente\n", "        for g, row in games_info.set_index('GameID').iterrows() \n", "        if g in df['GameID'].unique()\n", "    ]\n", "    # Se non ci sono corrispondenze tra il GameID di rilevations e quello in games, mostra solo il GameID\n", "    if not game_options:\n", "        game_options = [{'label': g, 'value': g} for g in sorted(df['GameID'].unique())]\n", "        \n", "\n", "    #opzioni del menu a tendina per whichTeamIDreal\n", "    team_options = [\n", "        {'label': f\"{row['TeamName']} ({t})\",                  #quello che c'è scritto nel menu a tendina\n", "        'value': t}                                           #il suo valore, quello che conta veramente\n", "        for t, row in teams_info.set_index('TeamID').iterrows()   #per ogni TeamID nella tabella teams_duckdb\n", "        if t in df['whichTeamIDreal'].unique()        #se quel TeamID è presente in df_attacks_raw['whichTeamIDreal']\n", "    ]\n", "    # Se non ci sono corrispondenze tra il GameID di rilevations e quello in games, mostra solo il GameID\n", "    if not team_options:\n", "        team_options = [{'label': t, 'value': t} for t in sorted(df['whichTeamIDreal'].unique())]\n", "\n", "\n", "    #opzioni del menu a tendina per NumeroMaglia_ID\n", "    player_options = [\n", "        {'label': f\"{row['Cognome']} {row['Nome']} ({p})\",                  #quello che c'è scritto nel menu a tendina\n", "        'value': p}                                           #il suo valore, quello che conta veramente\n", "        for p, row in players_info.set_index('PlayerID').iterrows()   #per ogni TeamID nella tabella teams_duckdb\n", "        if p in df['NumeroMaglia_ID'].unique()        #se quel TeamID è presente in df_attacks_raw['whichTeamIDreal']\n", "    ]\n", "    # Se non ci sono corrispondenze tra il GameID di rilevations e quello in games, mostra solo il GameID\n", "    if not player_options:\n", "        player_options = [{'label': p, 'value': p} for p in sorted(df['NumeroMaglia_ID'].unique())]\n", "        \n", "    return game_options, team_options, player_options\n", "\n", "\n", "game_options, team_options, player_options = testi_nei_menu(df_attacks_raw)\n", "\n", "\n", "\n", "# Nel nostro campo le zone sono:\n", "# [4 3 2]\n", "# [7 8 9]\n", "# [5 6 1]\n", "\n", "# Nel campo avversario le zone sono:\n", "# [1 6 5]\n", "# [9 8 7]\n", "# [2 3 4]\n", "\n", "our_zone_coords = {\n", "    '4': (0, 2), '3': (1, 2), '2': (2, 2),\n", "    '7': (0, 1), '8': (1, 1), '9': (2, 1),\n", "    '5': (0, 0), '6': (1, 0), '1': (2, 0)\n", "}\n", "\n", "opponent_zone_coords = {\n", "    '1': (0, 2), '6': (1, 2), '5': (2, 2),\n", "    '9': (0, 1), '8': (1, 1), '7': (2, 1),\n", "    '2': (0, 0), '3': (1, 0), '4': (2, 0)\n", "}\n", "\n", "# App Dash\n", "app = Dash(__name__)\n", "app.title = \"Attack Heatmap\"\n", "\n", "app.layout = html.Div([\n", "    html.H2(\"Heatmap Attacchi - Campo Avversario e Proprio Campo\"),\n", "    html.Div([\n", "        dcc.Dropdown(id='game-dropdown', options=game_options, placeholder='Seleziona GameID', multi=True),  #mettendo multi=True lo rendo a scelta multipla\n", "        dcc.Dropdown(id='team-dropdown', options=team_options, placeholder='Seleziona TeamID', multi=True),\n", "        dcc.Dropdown(id='player-dropdown', options=player_options, placeholder='Seleziona PlayerID', multi=True)\n", "    ], style={'display': 'flex', 'gap': '10px', 'margin-bottom': '20px'}),\n", "    dcc.Graph(id='heatmap-court')\n", "])\n", "\n", "\n", "\n", "\n", "#<PERSON><PERSON><PERSON> faccio in modo che le opzioni dei menu a tendina siano coerenti/compatibili con le selezioni già fatte. Ovvero evito di mostrare opzioni incompatibili con le selezioni già fatte\n", "@app.callback(\n", "    Output('game-dropdown', 'options'),\n", "    Output('team-dropdown', 'options'),\n", "    Output('player-dropdown', 'options'),\n", "    Input('game-dropdown', 'value'),\n", "    Input('team-dropdown', 'value'),\n", "    Input('player-dropdown', 'value')\n", ")\n", "def update_dropdowns(selected_games, selected_teams, selected_players):\n", "    df = df_attacks_raw\n", "\n", "    # Filtra SOLO sugli altri dropdown, NON sul valore corrente\n", "    #if selected_games:\n", "    #    df = df[df['GameID'].isin(selected_games)]\n", "    if selected_teams:\n", "        df = df[df['whichTeamIDreal'].isin(selected_teams)]\n", "    if selected_players:\n", "        df = df[df['NumeroMaglia_ID'].isin(selected_players)]\n", "\n", "    #Per aggiornare quali testi mostrare (delle opzioni che mostro), chiamo la funzione che ho definito prima, che mi dice come mostrare i testi\n", "    game_options, team_options, player_options = testi_nei_menu(df)\n", "    return game_options, team_options, player_options\n", "\n", "\n", "\n", "\n", "#Inizio a costruire il grafico\n", "@app.callback(\n", "    Output('heatmap-court', 'figure'),\n", "    Input('game-dropdown', 'value'),\n", "    Input('team-dropdown', 'value'),\n", "    Input('player-dropdown', 'value')\n", ")\n", "\n", "def update_heatmap(gameid, teamid, playerid):\n", "    # Filtra dati\n", "    df = df_attacks_raw.copy()\n", "    if gameid:\n", "        df = df[df['GameID'].isin(gameid)]  #se il menu è a scelta multipla, gameid è una lista con gli oggetti selezionati, quindi non posso mettere == gameid ma isin(gameid)\n", "    if teamid:\n", "        df = df[df['whichTeamIDreal'].isin(teamid)]\n", "    if playerid:\n", "        df = df[df['NumeroMaglia_ID'].isin(playerid)]\n", "\n", "    # Pulisce la zona (rimuove 'End_')\n", "    df['Zone'] = df['EndZone'].str.replace('End_', '')\n", "    \n", "    # Calcola statistiche per zona e valutazione\n", "    zone_stats = {}\n", "    eval_types = ['#', '+', '!', '-', '/', '=']\n", "    \n", "    for zone in opponent_zone_coords.keys():\n", "        zone_data = df[df['Zone'] == zone]\n", "        total_attacks = len(zone_data)\n", "        \n", "        eval_counts = {}\n", "        for eval_type in eval_types:\n", "            eval_counts[eval_type] = len(zone_data[zone_data['Eval'] == eval_type])\n", "        \n", "        zone_stats[zone] = {\n", "            'total': total_attacks,\n", "            'eval_counts': eval_counts\n", "        }\n", "\n", "\n", "    \n", "    # Trova il massimo per la scala dei colori\n", "    max_count = max([stats['total'] for stats in zone_stats.values()]) if zone_stats else 1\n", "\n", "    # Inizializza la figura\n", "    fig = go.Figure()\n", "\n", "    # Funzione per creare hover text\n", "    def create_hover_text(zone):\n", "        stats = zone_stats[zone]\n", "        if stats['total'] == 0:\n", "            return f\"<PERSON><PERSON> {zone}<br><PERSON><PERSON><PERSON> at<PERSON>\"\n", "        \n", "        hover_lines = [f\"Zona {zone}<br>Totale attacchi: {stats['total']}<br>\"]\n", "        for eval_type in eval_types:\n", "            count = stats['eval_counts'][eval_type]\n", "            if count > 0:\n", "                hover_lines.append(f\"{count} attacchi {eval_type}\")  #scritta quando fai hover\n", "        \n", "        return \"<br>\".join(hover_lines)\n", "\n", "    # Disegna il campo avversario con heatmap\n", "    for zone, (col, row) in opponent_zone_coords.items():\n", "        total_attacks = zone_stats[zone]['total']\n", "        intensity = total_attacks / max_count if max_count else 0\n", "        \n", "        # Colore: rosso con trasparenza basata sull'intensità\n", "        color = f'rgba(255,0,0,{0.1 + 0.9 * intensity})'\n", "        \n", "        # Crea hover text\n", "        hover_text = create_hover_text(zone)\n", "        \n", "        # Aggiunge un scatter point invisibile per l'hover\n", "        fig.add_trace(go.<PERSON>(\n", "            x=[col + 0.5],\n", "            y=[row + 0.5],\n", "            mode='markers',\n", "            marker=dict(size=50, color='rgba(0,0,0,0)'),  # Marker invisibile\n", "            hovertemplate=hover_text + '<extra></extra>',\n", "            showlegend=False,\n", "            name=f'<PERSON><PERSON> {zone}'\n", "        ))\n", "        \n", "        # Disegna rettangolo\n", "        fig.add_shape(type='rect',\n", "            x0=col, y0=row,\n", "            x1=col + 1, y1=row + 1,\n", "            fillcolor=color,\n", "            line=dict(width=1, color='black')\n", "        )\n", "        \n", "        # Testo count\n", "        fig.add_annotation(\n", "            x=col + 0.5, y=row + 0.5,\n", "            text=str(total_attacks),\n", "            showarrow=False,\n", "            font=dict(color='white' if intensity > 0.5 else 'black', size=12)\n", "        )\n", "\n", "    # Disegna il proprio campo (senza heatmap)\n", "    y_offset = 3\n", "    for zone, (col, row) in opponent_zone_coords.items():\n", "        color = 'rgba(200,200,200,0.1)'\n", "        fig.add_shape(type='rect',\n", "            x0=col, y0=row - y_offset,\n", "            x1=col + 1, y1=row + 1 - y_offset,\n", "            fillcolor=color,\n", "            line=dict(width=1, color='black')\n", "        )\n", "        \n", "        # Etichetta zona\n", "        fig.add_annotation(\n", "            x=col + 0.5, y=row + 0.5 - y_offset,\n", "            text=zone,\n", "            showarrow=False,\n", "            font=dict(color='gray', size=10)\n", "        )\n", "\n", "    # Aggiunge la rete (linea spessa tra i due campi)\n", "    fig.add_shape(type='line',\n", "        x0=-0.1, y0=0,\n", "        x1=3.1, y1=0,\n", "        line=dict(width=8, color='black')\n", "    )\n", "    \n", "    # Aggiunge titoli dei campi\n", "    fig.add_annotation(\n", "        x=-0.3, y=1.5,\n", "        text=\"Campo Difensori\",\n", "        showarrow=False,\n", "        textangle=-90,\n", "        xanchor='center',  #indica che le coordinate di x sono rispetto al punto centrale di x del grafico\n", "        yanchor='middle',  #indica che le coordinate di y sono rispetto al punto centrale di y del grafico\n", "        font=dict(size=14, color='black', weight='bold')\n", "    )\n", "    \n", "    fig.add_annotation(\n", "        x=-0.3, y=-1.5,\n", "        text=\"Campo Attaccanti\",\n", "        showarrow=False,\n", "        textangle=-90,\n", "        xanchor='center',\n", "        yanchor='middle',\n", "        font=dict(size=14, color='black', weight='bold')\n", "    )\n", "\n", "    # Configurazione layout\n", "    fig.update_xaxes(showgrid=False, zeroline=False, visible=False, range=[-0.1, 3.1])\n", "    fig.update_yaxes(showgrid=False, zeroline=False, visible=False, range=[-3.1, 3.5], scaleanchor=\"x\", scaleratio=1)\n", "\n", "    fig.update_layout(\n", "        width=500,\n", "        height=800,\n", "        title=\"Heatmap Attacchi\",\n", "        margin=dict(l=20, r=20, t=50, b=20),\n", "        hovermode='closest'\n", "    )\n", "    \n", "    return fig\n", "\n", "if __name__ == '__main__':\n", "    app.run(debug=True, port=8053)"]}, {"cell_type": "code", "execution_count": null, "id": "ae232c31", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "95442237", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# Carica i dati una volta sola\n", "df_touch = con.execute(\"\"\"\n", "    SELECT \n", "        \"GameID\",\n", "        \"SetNumber\",\n", "        \"ActionNumber\",\n", "        \"TouchNumber\",\n", "        \"Foundamental\",\n", "        \"AbsNumeroPossesso\",\n", "        \"RilevationNumber\",\n", "        \"whichTeamID\",\n", "        \"NumeroMaglia_ID\",\n", "        'Type_' || \"Type\" AS \"Type\",\n", "        'Eval_' || \"Eval\" AS \"Eval\",\n", "        'Call_' || \"SetterCall\" AS \"SetterCall\",\n", "        'Targ_' || \"TargAttk\" AS \"TargAttk\",\n", "        'Start_' || \"StartZone\" AS \"StartZone\",\n", "        'StartC_' || \"StartZoneCompact\" AS \"StartZoneCompact\",\n", "        'Comb_' || \"AttkCombination\" AS \"AttkCombination\",\n", "        'End_' || \"EndZoneEsecZone\" AS \"EndZoneEsecZone\",\n", "        'Endd_' || \"EndZoneEsecZone3aree\" AS \"EndZoneEsecZone3aree\",\n", "        'EndSub_' || \"EndSubzoneEsecSubzone\" AS \"EndSubzoneEsecSubzone\",\n", "        'Skill_' || \"SkillType\" AS \"SkillType\",\n", "        'Players_' || \"PlayersInfo\" AS PlayersInfo,\n", "        'Special_' || \"Special\" AS \"Special\",\n", "        'ccc_' || \"correctCustomChar\" AS \"correctCustomChar\",\n", "        'Punto_' || \"EndedInPoint\" AS \"EndedInPoint\",\n", "        'Ruolo_' || \"RuoloCalc\" AS \"RuoloCalc\"\n", "    FROM rilevations_libero_view_duckdb\n", "    WHERE \"Foundamental\" IN ('S', 'R', 'E', 'A', 'B', 'D', 'F') AND \"GameID\"=11\n", "    \"\"\").df()"]}, {"cell_type": "code", "execution_count": 51, "id": "be3cdd45", "metadata": {}, "outputs": [], "source": ["#Salvo df_touch come csv\n", "df_touch.to_csv('df_touch.csv', index=False)"]}, {"cell_type": "code", "execution_count": 15, "id": "bdaca527", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "        <iframe\n", "            width=\"100%\"\n", "            height=\"650\"\n", "            src=\"http://127.0.0.1:8053/\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "            \n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.IFrame at 0x133b8754af0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import dash\n", "from dash import dcc, html, Input, Output, callback\n", "import plotly.graph_objects as go\n", "from collections import defaultdict\n", "\n", "# Configurazione colonne per fondamentali\n", "col_per_fond = {\n", "    \"S\": \"Eval\", \n", "    \"R\": \"<PERSON><PERSON>\", \n", "    \"E\": \"Eval\", \n", "    \"A\": \"<PERSON><PERSON>\", \n", "    \"B\": \"Eval\", \n", "    \"D\": \"Eval\", \n", "    \"F\": \"Eval\"\n", "}\n", "\n", "# Dati di esempio per il volleyball\n", "sample_data = [\n", "    # Azione 1: S+ -> R- -> E# -> A#\n", "    {'ActionId': 1, 'TouchNumber': 1, 'Foundamental': 'S', 'Eval': '+', 'Player': 'P1'},\n", "    {'ActionId': 1, 'TouchNumber': 2, 'Foundamental': 'R', 'Eval': '-', 'Player': 'P2'},\n", "    {'ActionId': 1, 'TouchNumber': 3, 'Foundamental': 'E', 'Eval': '#', 'Player': 'P3'},\n", "    {'ActionId': 1, 'TouchNumber': 4, 'Foundamental': 'A', 'Eval': '#', 'Player': 'P4'},\n", "    \n", "    # Azione 2: S! -> R+ -> A-\n", "    {'ActionId': 2, 'TouchNumber': 1, 'Foundamental': 'S', 'Eval': '!', 'Player': 'P5'},\n", "    {'ActionId': 2, 'TouchNumber': 2, 'Foundamental': 'R', 'Eval': '+', 'Player': 'P6'},\n", "    {'ActionId': 2, 'TouchNumber': 3, 'Foundamental': 'A', 'Eval': '-', 'Player': 'P7'},\n", "    \n", "    # Azione 3: S! -> R+ -> E+ -> A#\n", "    {'ActionId': 3, 'TouchNumber': 1, 'Foundamental': 'S', 'Eval': '!', 'Player': 'P8'},\n", "    {'ActionId': 3, 'TouchNumber': 2, 'Foundamental': 'R', 'Eval': '+', 'Player': 'P9'},\n", "    {'ActionId': 3, 'TouchNumber': 3, 'Foundamental': 'E', 'Eval': '+', 'Player': 'P10'},\n", "    {'ActionId': 3, 'TouchNumber': 4, 'Foundamental': 'A', 'Eval': '#', 'Player': 'P11'},\n", "    \n", "    # Azione 4: S+ -> R# -> B!\n", "    {'ActionId': 4, 'TouchNumber': 1, 'Foundamental': 'S', 'Eval': '+', 'Player': 'P12'},\n", "    {'ActionId': 4, 'TouchNumber': 2, 'Foundamental': 'R', 'Eval': '#', 'Player': 'P13'},\n", "    {'ActionId': 4, 'TouchNumber': 3, 'Foundamental': 'B', 'Eval': '!', 'Player': 'P14'},\n", "    \n", "    # Azione 5: S- -> R+ -> E- -> A+ -> B#\n", "    {'ActionId': 5, 'TouchNumber': 1, 'Foundamental': 'S', 'Eval': '-', 'Player': 'P15'},\n", "    {'ActionId': 5, 'TouchNumber': 2, 'Foundamental': 'R', 'Eval': '+', 'Player': 'P16'},\n", "    {'ActionId': 5, 'TouchNumber': 3, 'Foundamental': 'E', 'Eval': '-', 'Player': 'P17'},\n", "    {'ActionId': 5, 'TouchNumber': 4, 'Foundamental': 'A', 'Eval': '+', 'Player': 'P18'},\n", "    {'ActionId': 5, 'TouchNumber': 5, 'Foundamental': 'B', 'Eval': '#', 'Player': 'P19'},\n", "    \n", "    # Azione 6: S! -> R- -> E= -> A+\n", "    {'ActionId': 6, 'TouchNumber': 1, 'Foundamental': 'S', 'Eval': '!', 'Player': 'P20'},\n", "    {'ActionId': 6, 'TouchNumber': 2, 'Foundamental': 'R', 'Eval': '-', 'Player': 'P21'},\n", "    {'ActionId': 6, 'TouchNumber': 3, 'Foundamental': 'E', 'Eval': '=', 'Player': 'P22'},\n", "    {'ActionId': 6, 'TouchNumber': 4, 'Foundamental': 'A', 'Eval': '+', 'Player': 'P23'},\n", "]\n", "\n", "def create_node_id(touch_number, foundamental, characteristic):\n", "    \"\"\"Crea un ID unico per ogni nodo\"\"\"\n", "    return f\"T{touch_number}_{foundamental}_{characteristic}\"\n", "\n", "def get_color_by_eval(eval_val):\n", "    \"\"\"Mappa le valutazioni ai colori\"\"\"\n", "    colors = {\n", "        '#': 'rgb(255, 68, 68)',    # <PERSON><PERSON><PERSON> - rosso\n", "        '!': 'rgb(255, 136, 0)',    # <PERSON> - <PERSON><PERSON><PERSON><PERSON>\n", "        '-': 'rgb(255, 221, 0)',    # Insufficiente - g<PERSON><PERSON>\n", "        '+': 'rgb(136, 255, 136)',  # <PERSON>uo<PERSON> - verde chiaro\n", "        '++': 'rgb(0, 221, 0)',     # <PERSON><PERSON><PERSON> - verde\n", "        '=': 'rgb(136, 136, 136)'   # Neutro - grigio\n", "    }\n", "    return colors.get(eval_val, 'rgb(204, 204, 204)')\n", "\n", "def process_sankey_data(data, max_touch_number=11):\n", "    \"\"\"Processa i dati per creare il diagramma Sankey\"\"\"\n", "    df = pd.DataFrame(data)\n", "    \n", "    # Filtra tocchi fino a TouchNumber specificato\n", "    df = df[df['TouchNumber'] <= max_touch_number]\n", "    \n", "    # Dizionari per tracciare nodi e collegamenti\n", "    nodes = {}\n", "    links = defaultdict(int)\n", "    \n", "    # Raggruppa per azione\n", "    for action_id, action_data in df.groupby('ActionId'):\n", "        action_touches = action_data.sort_values('TouchNumber')\n", "        \n", "        # <PERSON>rea nodi per ogni tocco dell'azione\n", "        for _, touch in action_touches.iterrows():\n", "            characteristic = touch[col_per_fond[touch['Foundamental']]]\n", "            node_id = create_node_id(touch['TouchNumber'], touch['Foundamental'], characteristic)\n", "            \n", "            if node_id not in nodes:\n", "                nodes[node_id] = {\n", "                    'id': node_id,\n", "                    'touch_number': touch['TouchNumber'],\n", "                    'foundamental': touch['Foundamental'],\n", "                    'characteristic': characteristic,\n", "                    'count': 0,\n", "                    'label': f\"{touch['Foundamental']}{characteristic}\",\n", "                    'color': get_color_by_eval(characteristic)\n", "                }\n", "            nodes[node_id]['count'] += 1\n", "        \n", "        # <PERSON>rea collegamenti tra tocchi consecutivi\n", "        touches_list = action_touches.to_dict('records')\n", "        for i in range(len(touches_list) - 1):\n", "            current_touch = touches_list[i]\n", "            next_touch = touches_list[i + 1]\n", "            \n", "            source_char = current_touch[col_per_fond[current_touch['Foundamental']]]\n", "            target_char = next_touch[col_per_fond[next_touch['Foundamental']]]\n", "            \n", "            source_id = create_node_id(current_touch['TouchNumber'], current_touch['Foundamental'], source_char)\n", "            target_id = create_node_id(next_touch['TouchNumber'], next_touch['Foundamental'], target_char)\n", "            \n", "            link_key = (source_id, target_id)\n", "            links[link_key] += 1\n", "    \n", "    return nodes, links\n", "\n", "def create_sankey_figure(nodes, links):\n", "    \"\"\"Crea il diagramma Sankey usando Plotly\"\"\"\n", "    \n", "    # Converti nodi in liste ordinate\n", "    node_list = list(nodes.values())\n", "    node_list.sort(key=lambda x: (x['touch_number'], x['foundamental'], x['characteristic']))\n", "    \n", "    # Crea mapping da node_id a indice\n", "    node_id_to_index = {node['id']: i for i, node in enumerate(node_list)}\n", "    \n", "    # Prepara dati per <PERSON><PERSON><PERSON>\n", "    node_labels = [f\"{node['label']}<br>({node['count']})\" for node in node_list]\n", "    node_colors = [node['color'] for node in node_list]\n", "    \n", "    # Calcola posizioni X basate su TouchNumber per allineamento colonne\n", "    touch_numbers = sorted(set(node['touch_number'] for node in node_list))\n", "    x_positions = []\n", "    y_positions = []\n", "    \n", "    for node in node_list:\n", "        # Posizione X basata su TouchNumber\n", "        x_pos = (node['touch_number'] - 1) / (len(touch_numbers) - 1) if len(touch_numbers) > 1 else 0.5\n", "        x_positions.append(x_pos)\n", "        \n", "        # Posizione Y distribuita uniformemente per ogni gruppo di TouchNumber\n", "        nodes_same_touch = [n for n in node_list if n['touch_number'] == node['touch_number']]\n", "        node_index_in_touch = nodes_same_touch.index(node)\n", "        y_pos = (node_index_in_touch + 0.5) / len(nodes_same_touch) if len(nodes_same_touch) > 1 else 0.5\n", "        y_positions.append(y_pos)\n", "    \n", "    # Prepara collegamenti\n", "    source_indices = []\n", "    target_indices = []\n", "    values = []\n", "    \n", "    for (source_id, target_id), value in links.items():\n", "        if source_id in node_id_to_index and target_id in node_id_to_index:\n", "            source_indices.append(node_id_to_index[source_id])\n", "            target_indices.append(node_id_to_index[target_id])\n", "            values.append(value)\n", "    \n", "    # <PERSON>rea il diagramma Sankey\n", "    fig = go.Figure(data=[go.Sankey(\n", "        node=dict(\n", "            pad=15,\n", "            thickness=20,\n", "            line=dict(color=\"black\", width=0.5),\n", "            label=node_labels,\n", "            color=node_colors,\n", "            x=x_positions,\n", "            y=y_positions\n", "        ),\n", "        link=dict(\n", "            source=source_indices,\n", "            target=target_indices,\n", "            value=values,\n", "            color='rgba(128, 128, 128, 0.4)'\n", "        )\n", "    )])\n", "    \n", "    fig.update_layout(\n", "        title={\n", "            'text': \"<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON>\",\n", "            'font': {'size': 20}\n", "        },\n", "        font_size=12,\n", "        height=700,\n", "        margin=dict(t=80, b=40, l=40, r=40)\n", "    )\n", "    \n", "    return fig\n", "\n", "# Inizializza l'app Dash\n", "app = dash.Dash(__name__)\n", "\n", "# Layout dell'app\n", "app.layout = html.Div([\n", "    html.H1(\"<PERSON><PERSON><PERSON> - Diagramma San<PERSON>\", \n", "            style={'textAlign': 'center', 'marginBottom': 30}),\n", "    \n", "    html.Div([\n", "        html.H3(\"Configurazione\"),\n", "        html.P(\"Massimo <PERSON> da visualizzare:\"),\n", "        dcc.<PERSON><PERSON><PERSON>(\n", "            id='max-touch-slider',\n", "            min=3,\n", "            max=11,\n", "            step=1,\n", "            value=11,\n", "            marks={i: str(i) for i in range(3, 12)},\n", "            tooltip={\"placement\": \"bottom\", \"always_visible\": True}\n", "        ),\n", "    ], style={'margin': '20px', 'padding': '20px', 'border': '1px solid #ddd', 'borderRadius': '5px'}),\n", "    \n", "    html.Div([\n", "        html.H3(\"Legenda Colori\"),\n", "        html.Div([\n", "            html.Span(\"# Errore\", style={'backgroundColor': 'rgb(255, 68, 68)', 'padding': '5px', 'margin': '5px', 'borderRadius': '3px'}),\n", "            html.Span(\"! Male\", style={'backgroundColor': 'rgb(255, 136, 0)', 'padding': '5px', 'margin': '5px', 'borderRadius': '3px'}),\n", "            html.Span(\"- Insufficiente\", style={'backgroundColor': 'rgb(255, 221, 0)', 'padding': '5px', 'margin': '5px', 'borderRadius': '3px'}),\n", "            html.Span(\"+ <PERSON><PERSON><PERSON>\", style={'backgroundColor': 'rgb(136, 255, 136)', 'padding': '5px', 'margin': '5px', 'borderRadius': '3px'}),\n", "            html.Span(\"= Neutro\", style={'backgroundColor': 'rgb(136, 136, 136)', 'padding': '5px', 'margin': '5px', 'borderRadius': '3px'}),\n", "        ], style={'display': 'flex', 'flexWrap': 'wrap'})\n", "    ], style={'margin': '20px', 'padding': '20px', 'border': '1px solid #ddd', 'borderRadius': '5px'}),\n", "    \n", "    dcc.Graph(id='sankey-diagram'),\n", "    \n", "    html.Div([\n", "        html.H3(\"Come interpretare il diagramma:\"),\n", "        html.Ul([\n", "            html.Li(\"Ogni colonna verticale rappresenta un TouchNumber (1, 2, 3, etc.)\"),\n", "            html.Li(\"I nodi mostrano Fondamentale + Valutazione (es. S+, R-, E#)\"),\n", "            html.Li(\"Il numero tra parentesi indica quante volte quel nodo appare nei dati\"),\n", "            html.Li(\"Lo spessore delle connessioni indica la frequenza del flusso tra i nodi\"),\n", "            html.Li(\"I tocchi sono raggruppati per azione, mantenendo i flussi separati quando necessario\"),\n", "            html.Li(\"Le azioni con lo stesso nodo iniziale possono condividere percorsi comuni\"),\n", "        ])\n", "    ], style={'margin': '20px', 'padding': '20px', 'border': '1px solid #ddd', 'borderRadius': '5px'}),\n", "    \n", "    html.Div([\n", "        html.H3(\"Dati di Esempio Utilizzati:\"),\n", "        html.P(\"• Azione 1: S+ → R- → E# → A#\"),\n", "        html.P(\"• Azione 2: S! → R+ → A-\"),\n", "        html.P(\"• Azione 3: S! → R+ → E+ → A# (nota: unita con Azione 2 per S! e R+)\"),\n", "        html.P(\"• Azione 4: S+ → R# → B! (nota: unita con Azione 1 per S+)\"),\n", "        html.P(\"• Azione 5: S- → R+ → E- → A+ → B#\"),\n", "        html.P(\"• Azione 6: S! → R- → E= → A+\"),\n", "    ], style={'margin': '20px', 'padding': '20px', 'border': '1px solid #ddd', 'borderRadius': '5px'})\n", "])\n", "\n", "@app.callback(\n", "    Output('sankey-diagram', 'figure'),\n", "    Input('max-touch-slider', 'value')\n", ")\n", "def update_sankey(max_touch):\n", "    \"\"\"Callback per aggiornare il diagramma quando cambia il slider\"\"\"\n", "    nodes, links = process_sankey_data(sample_data, max_touch)\n", "    return create_sankey_figure(nodes, links)\n", "\n", "if __name__ == '__main__':\n", "    app.run(debug=True, port=8053)"]}, {"cell_type": "code", "execution_count": 47, "id": "2ae4c6a2", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "        <iframe\n", "            width=\"100%\"\n", "            height=\"650\"\n", "            src=\"http://127.0.0.1:8050/\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "            \n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.IFrame at 0x133b9ce48a0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import dash\n", "from dash import dcc, html, Input, Output, callback, dash_table\n", "import plotly.graph_objects as go\n", "import pandas as pd\n", "import numpy as np\n", "from collections import defaultdict\n", "\n", "# CONFIGURAZIONE - Modifica qui per cambiare le caratteristiche visualizzate\n", "col_per_fond = {\n", "    \"S\": \"Eval\", \n", "    \"R\": \"<PERSON><PERSON>\", \n", "    \"E\": \"Eval\", \n", "    \"A\": \"<PERSON><PERSON>\", \n", "    \"B\": \"Eval\", \n", "    \"D\": \"Eval\", \n", "    \"F\": \"Eval\"\n", "}\n", "\n", "# Funzione per caricare i dati - INSERISCI QUI IL TUO DATAFRAME\n", "def load_volleyball_data():    \n", "    df_touch = con.execute(\"\"\"\n", "        SELECT \n", "            \"GameID\",\n", "            \"SetNumber\",\n", "            \"ActionNumber\",\n", "            \"TouchNumber\",\n", "            \"Foundamental\",\n", "            \"AbsNumeroPossesso\",\n", "            \"RilevationNumber\",\n", "            \"whichTeamID\",\n", "            \"NumeroMaglia_ID\",\n", "            'Type_' || \"Type\" AS \"Type\",\n", "            'Eval_' || \"Eval\" AS \"Eval\",\n", "            'Call_' || \"SetterCall\" AS \"SetterCall\",\n", "            'Targ_' || \"TargAttk\" AS \"TargAttk\",\n", "            'Start_' || \"StartZone\" AS \"StartZone\",\n", "            'StartC_' || \"StartZoneCompact\" AS \"StartZoneCompact\",\n", "            'Comb_' || \"AttkCombination\" AS \"AttkCombination\",\n", "            'End_' || \"EndZoneEsecZone\" AS \"EndZoneEsecZone\",\n", "            'Endd_' || \"EndZoneEsecZone3aree\" AS \"EndZoneEsecZone3aree\",\n", "            'EndSub_' || \"EndSubzoneEsecSubzone\" AS \"EndSubzoneEsecSubzone\",\n", "            'Skill_' || \"SkillType\" AS \"SkillType\",\n", "            'Players_' || \"PlayersInfo\" AS PlayersInfo,\n", "            'Special_' || \"Special\" AS \"Special\",\n", "            'ccc_' || \"correctCustomChar\" AS \"correctCustomChar\",\n", "            'Punto_' || \"EndedInPoint\" AS \"EndedInPoint\",\n", "            'Ruolo_' || \"RuoloCalc\" AS \"RuoloCalc\"\n", "        FROM rilevations_libero_view_duckdb\n", "        WHERE \"Foundamental\" IN ('S', 'R', 'E', 'A', 'B', 'D', 'F') AND \"GameID\"=21\n", "        \"\"\").df()\n", "    \n", "    return df_touch\n", "\n", "\n", "def create_node_id(touch_number, foundamental, characteristic):\n", "    \"\"\"Crea un ID unico per ogni nodo\"\"\"\n", "    return f\"T{touch_number}_{foundamental}_{characteristic}\"\n", "\n", "def extract_eval_from_string(eval_string):\n", "    \"\"\"Estrae la valutazione dalla stringa 'Eval_X' -> 'X'\"\"\"\n", "    if pd.isna(eval_string) or eval_string == '':\n", "        return '='\n", "    if isinstance(eval_string, str) and eval_string.startswith('Eval_'):\n", "        return eval_string.replace('Eval_', '')\n", "    return str(eval_string)\n", "\n", "def get_color_by_eval(eval_val):\n", "    \"\"\"Ritorna None per lasciare che Plotly gestisca i colori automaticamente\"\"\"\n", "    return None\n", "\n", "def process_sankey_data(df, max_touch_number=11, game_id=None, set_number=None):\n", "    \"\"\"Processa i dati per creare il diagramma Sankey\"\"\"\n", "    \n", "    # Filtra i dati se specificato\n", "    if game_id is not None:\n", "        df = df[df['GameID'] == game_id]\n", "    if set_number is not None:\n", "        df = df[df['SetNumber'] == set_number]\n", "    \n", "    # Filtra tocchi fino a TouchNumber specificato\n", "    df = df[df['TouchNumber'] <= max_touch_number]\n", "    \n", "    if df.empty:\n", "        return {}, {}\n", "    \n", "    # Dizionari per tracciare nodi e collegamenti\n", "    nodes = {}\n", "    links = defaultdict(int)\n", "    \n", "    # Raggruppa per azione\n", "    for action_id, action_data in df.groupby('ActionNumber'):\n", "        action_touches = action_data.sort_values('TouchNumber')\n", "\n", "        # DEBUG: Verifica TouchNumber sequenziali\n", "        prev_touch = -1\n", "        for i, t in enumerate(action_touches['TouchNumber']):\n", "            if t < prev_touch:\n", "                print(f\"⚠️ ATTENZIONE: TouchNumber fuori ordine nell’azione {action_id} (indice {i})\")\n", "            prev_touch = t\n", "        \n", "        \n", "        # <PERSON>rea nodi per ogni tocco dell'azione\n", "        for _, touch in action_touches.iterrows():\n", "            # Ottieni la caratteristica specificata per questo fondamentale\n", "            characteristic_col = col_per_fond.get(touch['Foundamental'], 'Eval')\n", "            \n", "            if characteristic_col in touch and pd.notna(touch[characteristic_col]):\n", "                characteristic = touch[characteristic_col]\n", "            else:\n", "                characteristic = '='  # <PERSON><PERSON> di default\n", "            \n", "            # Pulisce la caratteristica se è nel formato 'Prefix_Value'\n", "            if isinstance(characteristic, str) and '_' in characteristic:\n", "                characteristic = characteristic.split('_', 1)[-1]\n", "            \n", "            node_id = create_node_id(touch['TouchNumber'], touch['Foundamental'], characteristic)\n", "            \n", "            if node_id not in nodes:\n", "                nodes[node_id] = {\n", "                    'id': node_id,\n", "                    'touch_number': touch['TouchNumber'],\n", "                    'foundamental': touch['Foundamental'],\n", "                    'characteristic': characteristic,\n", "                    'count': 0,\n", "                    'label': f\"{touch['Foundamental']}{characteristic}\"\n", "                }\n", "            nodes[node_id]['count'] += 1\n", "        \n", "        # <PERSON>rea collegamenti tra tocchi consecutivi\n", "        touches_list = action_touches.to_dict('records')\n", "        for i in range(len(touches_list) - 1):\n", "            current_touch = touches_list[i]\n", "            next_touch = touches_list[i + 1]\n", "\n", "            # Salta se i due tocchi hanno lo stesso TouchNumber\n", "            if current_touch[\"TouchNumber\"] == next_touch[\"TouchNumber\"]:\n", "                continue\n", "            \n", "            # <PERSON><PERSON><PERSON>atteri<PERSON>\n", "            current_char_col = col_per_fond.get(current_touch['Foundamental'], 'Eval')\n", "            next_char_col = col_per_fond.get(next_touch['Foundamental'], 'Eval')\n", "            \n", "            current_char = current_touch.get(current_char_col, '=')\n", "            next_char = next_touch.get(next_char_col, '=')\n", "            \n", "            # <PERSON><PERSON><PERSON><PERSON> le caratteristiche\n", "            if isinstance(current_char, str) and '_' in current_char:\n", "                current_char = current_char.split('_', 1)[-1]\n", "            if isinstance(next_char, str) and '_' in next_char:\n", "                next_char = next_char.split('_', 1)[-1]\n", "            \n", "            source_id = create_node_id(current_touch['TouchNumber'], current_touch['Foundamental'], current_char)\n", "            target_id = create_node_id(next_touch['TouchNumber'], next_touch['Foundamental'], next_char)\n", "            \n", "            link_key = (source_id, target_id)\n", "            links[link_key] += 1\n", "    \n", "    return nodes, links\n", "\n", "def create_sankey_figure(nodes, links):\n", "    \"\"\"Crea il diagramma Sankey usando Plotly\"\"\"\n", "    \n", "    if not nodes or not links:\n", "        # Crea un grafico vuoto se non ci sono dati\n", "        fig = go.Figure()\n", "        fig.add_annotation(\n", "            text=\"N<PERSON>un dato disponibile per i filtri selezionati\",\n", "            xref=\"paper\", yref=\"paper\",\n", "            x=0.5, y=0.5, showarrow=False,\n", "            font=dict(size=16)\n", "        )\n", "        fig.update_layout(height=400)\n", "        return fig\n", "    \n", "    # Converti nodi in liste ordinate\n", "    node_list = list(nodes.values())\n", "    node_list.sort(key=lambda x: (x['touch_number'], x['foundamental'], x['characteristic']))\n", "    \n", "    # Crea mapping da node_id a indice\n", "    node_id_to_index = {node['id']: i for i, node in enumerate(node_list)}\n", "    \n", "    # Prepara dati per <PERSON><PERSON><PERSON>\n", "    node_labels = [f\"{node['label']}<br>({node['count']})\" for node in node_list]\n", "    \n", "    # Calcola posizioni per evitare sovrapposizioni con nodi più piccoli\n", "    touch_numbers = sorted(set(node['touch_number'] for node in node_list))\n", "    x_positions = []\n", "    y_positions = []\n", "    \n", "    # Raggruppa nodi per TouchNumber per migliore distribuzione verticale\n", "    nodes_by_touch = {}\n", "    for node in node_list:\n", "        touch_num = node['touch_number']\n", "        if touch_num not in nodes_by_touch:\n", "            nodes_by_touch[touch_num] = []\n", "        nodes_by_touch[touch_num].append(node)\n", "    \n", "    # Calcola posizioni per ogni gruppo di TouchNumber\n", "    for node in node_list:\n", "        touch_num = node['touch_number']\n", "        \n", "        # Posizione X basata su TouchNumber con più spazio\n", "        if len(touch_numbers) > 1:\n", "            x_pos = (touch_num - min(touch_numbers)) / (max(touch_numbers) - min(touch_numbers))\n", "        else:\n", "            x_pos = 0.5\n", "        x_positions.append(x_pos)\n", "        \n", "        # Posizione Y con distribuzione molto più spaziata\n", "        nodes_same_touch = nodes_by_touch[touch_num]\n", "        nodes_same_touch.sort(key=lambda x: (x['foundamental'], x['characteristic']))\n", "        \n", "        node_index_in_touch = nodes_same_touch.index(node)\n", "        num_nodes_in_touch = len(nodes_same_touch)\n", "        \n", "        # Distribuzione con molto più spazio tra i nodi\n", "        if num_nodes_in_touch == 1:\n", "            y_pos = 0.5\n", "        else:\n", "            # Usa margini più grandi e distribuzione più spaziata\n", "            margin = 0.05\n", "            available_space = 1.0 - 2 * margin\n", "            y_pos = margin + (node_index_in_touch * available_space) / (num_nodes_in_touch - 1)\n", "        \n", "        y_positions.append(y_pos)\n", "    \n", "    # Prepara collegamenti\n", "    source_indices = []\n", "    target_indices = []\n", "    values = []\n", "    \n", "    for (source_id, target_id), value in links.items():\n", "        if source_id in node_id_to_index and target_id in node_id_to_index:\n", "            source_indices.append(node_id_to_index[source_id])\n", "            target_indices.append(node_id_to_index[target_id])\n", "            values.append(value)\n", "    \n", "    # Prepara hover text personalizzato\n", "    node_hover_text = []\n", "    for node in node_list:\n", "        hover = (\n", "            f\"Nodo: {node['label']}<br>\"\n", "            f\"TouchNumber: {node['touch_number']}<br>\"\n", "            f\"Foundamental: {node['foundamental']}<br>\"\n", "            f\"Characteristic: {node['characteristic']}<br>\"\n", "            f\"Osservazioni: {node['count']}\"\n", "        )\n", "        node_hover_text.append(hover)\n", "    \n", "    link_hover_text = []\n", "    for i, ((source_id, target_id), value) in enumerate(links.items()):\n", "        if source_id in node_id_to_index and target_id in node_id_to_index:\n", "            source_node = nodes[source_id]\n", "            target_node = nodes[target_id]\n", "            hover = (\n", "                f\"Flusso: {source_node['label']} → {target_node['label']}<br>\"\n", "                f\"Da TouchNumber: {source_node['touch_number']}<br>\"\n", "                f\"A TouchNumber: {target_node['touch_number']}<br>\"\n", "                f\"Osservazioni: {value}\"\n", "            )\n", "            link_hover_text.append(hover)\n", "    \n", "    # Crea il diagramma Sankey con nodi più piccoli e hover semplificato\n", "    fig = go.Figure(data=[go.Sankey(\n", "        node=dict(\n", "            pad=30,  # <PERSON><PERSON> padding per evitare sovrapposizioni\n", "            thickness=15,  # <PERSON><PERSON> pi<PERSON> sottili\n", "            line=dict(color=\"black\", width=0.5),\n", "            label=node_labels,\n", "            x=x_positions,\n", "            y=y_positions,\n", "            customdata=node_hover_text,\n", "            hovertemplate='%{customdata}<extra></extra>'\n", "        ),\n", "        link=dict(\n", "            source=source_indices,\n", "            target=target_indices,\n", "            value=values,\n", "            customdata=link_hover_text,\n", "            hovertemplate='%{customdata}<extra></extra>'\n", "        )\n", "    )])\n", "    \n", "    fig.update_layout(\n", "        title={\n", "            'text': \"<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON>\",\n", "            'font': {'size': 20}\n", "        },\n", "        font_size=10,  # Font più piccolo per risparmiare spazio\n", "        height=900,  # <PERSON><PERSON> al<PERSON>zza per distribuire meglio i nodi\n", "        margin=dict(t=80, b=40, l=40, r=40)\n", "    )\n", "    \n", "    return fig\n", "\n", "# Carica i dati\n", "df_touch = load_volleyball_data()\n", "\n", "# Inizializza l'app Dash\n", "app = dash.Dash(__name__)\n", "\n", "# Layout dell'app\n", "app.layout = html.Div([\n", "    html.H1(\"<PERSON><PERSON><PERSON> - Diagramma San<PERSON>\", \n", "            style={'textAlign': 'center', 'marginBottom': 30}),\n", "    \n", "    html.Div([\n", "        html.H3(\"Configurazione Filtri\"),\n", "        html.Div([\n", "            html.Div([\n", "                html.Label(\"Game ID:\"),\n", "                dcc.Dropdown(\n", "                    id='game-id-dropdown',\n", "                    options=[{'label': f'Game {gid}', 'value': gid} for gid in sorted(df_touch['GameID'].unique())],\n", "                    value=df_touch['GameID'].iloc[0] if not df_touch.empty else None,\n", "                    style={'width': '150px'}\n", "                ),\n", "            ], style={'display': 'inline-block', 'marginRight': '20px'}),\n", "            \n", "            html.Div([\n", "                html.Label(\"Set Number:\"),\n", "                dcc.Dropdown(\n", "                    id='set-number-dropdown',\n", "                    style={'width': '150px'}\n", "                ),\n", "            ], style={'display': 'inline-block', 'marginRight': '20px'}),\n", "            \n", "            html.Div([\n", "                html.Label(\"Max TouchNumber:\"),\n", "                dcc.<PERSON><PERSON><PERSON>(\n", "                    id='max-touch-slider',\n", "                    min=3,\n", "                    max=11,\n", "                    step=1,\n", "                    value=11,\n", "                    marks={i: str(i) for i in range(3, 12)},\n", "                    tooltip={\"placement\": \"bottom\", \"always_visible\": True}\n", "                ),\n", "            ], style={'display': 'inline-block', 'width': '200px'}),\n", "        ], style={'display': 'flex', 'alignItems': 'center', 'gap': '20px'}),\n", "    ], style={'margin': '20px', 'padding': '20px', 'border': '1px solid #ddd', 'borderRadius': '5px'}),\n", "    \n", "    html.Div([\n", "        html.H3(\"Configurazione Caratteristiche per Fondamentale\"),\n", "        html.P(\"Attualmente configurato per mostrare 'Eval' per tutti i fondamentali. Modifica col_per_fond nel codice per personalizzare.\"),\n", "        html.Div([\n", "            html.Span(f\"{fond}: {char}\", style={'margin': '5px', 'padding': '5px', 'backgroundColor': '#f0f0f0', 'borderRadius': '3px'})\n", "            for fond, char in col_per_fond.items()\n", "        ], style={'display': 'flex', 'flexWrap': 'wrap'})\n", "    ], style={'margin': '20px', 'padding': '20px', 'border': '1px solid #ddd', 'borderRadius': '5px'}),\n", "    \n", "    dcc.Graph(id='sankey-diagram'),\n", "    \n", "    html.Div([\n", "        html.H3(\"Statistiche Dati\"),\n", "        html.Div(id='data-stats')\n", "    ], style={'margin': '20px', 'padding': '20px', 'border': '1px solid #ddd', 'borderRadius': '5px'}),\n", "    \n", "    html.Div([\n", "        html.H3(\"Come interpretare il diagramma:\"),\n", "        html.Ul([\n", "            html.Li(\"Ogni colonna verticale rappresenta un TouchNumber (1, 2, 3, etc.)\"),\n", "            html.Li(\"I nodi mostrano Fondamentale + Caratteristica configurata (es. S+, R-, E#)\"),\n", "            html.Li(\"Il numero tra parentesi indica quante volte quel nodo appare nei dati\"),\n", "            html.Li(\"Lo spessore delle connessioni indica la frequenza del flusso tra i nodi\"),\n", "            html.Li(\"I tocchi sono raggruppati per ActionNumber, mantenendo i flussi separati quando necessario\"),\n", "            html.Li(\"Le azioni con lo stesso nodo iniziale possono condividere percorsi comuni\"),\n", "        ])\n", "    ], style={'margin': '20px', 'padding': '20px', 'border': '1px solid #ddd', 'borderRadius': '5px'})\n", "])\n", "\n", "@app.callback(\n", "    Output('set-number-dropdown', 'options'),\n", "    Output('set-number-dropdown', 'value'),\n", "    Input('game-id-dropdown', 'value')\n", ")\n", "def update_set_options(game_id):\n", "    \"\"\"Aggiorna le opzioni del Set Number basate su Game ID selezionato\"\"\"\n", "    if game_id is None:\n", "        return [], None\n", "    \n", "    filtered_df = df_touch[df_touch['GameID'] == game_id]\n", "    set_options = [{'label': f'Set {sn}', 'value': sn} for sn in sorted(filtered_df['SetNumber'].unique())]\n", "    default_set = filtered_df['SetNumber'].iloc[0] if not filtered_df.empty else None\n", "    \n", "    return set_options, default_set\n", "\n", "@app.callback(\n", "    Output('sankey-diagram', 'figure'),\n", "    Output('data-stats', 'children'),\n", "    Input('game-id-dropdown', 'value'),\n", "    Input('set-number-dropdown', 'value'),\n", "    Input('max-touch-slider', 'value')\n", ")\n", "def update_sankey(game_id, set_number, max_touch):\n", "    \"\"\"Callback per aggiornare il diagramma quando cambiano i filtri\"\"\"\n", "    nodes, links = process_sankey_data(df_touch, max_touch, game_id, set_number)\n", "    fig = create_sankey_figure(nodes, links)\n", "    \n", "    # Statistiche\n", "    filtered_df = df_touch.copy()\n", "    if game_id is not None:\n", "        filtered_df = filtered_df[filtered_df['GameID'] == game_id]\n", "    if set_number is not None:\n", "        filtered_df = filtered_df[filtered_df['SetNumber'] == set_number]\n", "    filtered_df = filtered_df[filtered_df['TouchNumber'] <= max_touch]\n", "    \n", "    stats = html.Div([\n", "        html.P(f\"Totale tocchi: {len(filtered_df)}\"),\n", "        html.P(f\"Azioni uniche: {filtered_df['ActionNumber'].nunique() if not filtered_df.empty else 0}\"),\n", "        html.P(f\"Nodi creati: {len(nodes)}\"),\n", "        html.P(f\"Collegamenti: {len(links)}\"),\n", "        html.P(f\"TouchNumber range: {filtered_df['TouchNumber'].min() if not filtered_df.empty else 'N/A'} - {filtered_df['TouchNumber'].max() if not filtered_df.empty else 'N/A'}\"),\n", "    ])\n", "    \n", "    return fig, stats\n", "\n", "if __name__ == '__main__':\n", "    app.run(debug=True, port=8050)"]}, {"cell_type": "code", "execution_count": null, "id": "30f43fc4", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "        <iframe\n", "            width=\"100%\"\n", "            height=\"650\"\n", "            src=\"http://127.0.0.1:8050/\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "            \n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.IFrame at 0x133ba267b60>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["DEBUG - Assegnazione coordinate X:\n", "Nodo 1_S! - TouchNumber: 1 - X: 0.05\n", "Nodo 1_S# - TouchNumber: 1 - X: 0.05\n", "Nodo 1_S+ - TouchNumber: 1 - X: 0.05\n", "Nodo 1_S- - TouchNumber: 1 - X: 0.05\n", "Nodo 1_S/ - TouchNumber: 1 - X: 0.05\n", "Nodo 1_S= - TouchNumber: 1 - X: 0.05\n", "Nodo 2_R! - TouchNumber: 2 - X: 0.1\n", "Nodo 2_R# - TouchNumber: 2 - X: 0.1\n", "Nodo 2_R+ - TouchNumber: 2 - X: 0.1\n", "Nodo 2_R- - TouchNumber: 2 - X: 0.1\n", "Nodo 2_R/ - TouchNumber: 2 - X: 0.1\n", "Nodo 2_R= - TouchNumber: 2 - X: 0.1\n", "Nodo 3_E# - TouchNumber: 3 - X: 0.2\n", "Nodo 3_F= - TouchNumber: 3 - X: 0.2\n", "Nodo 4_A! - TouchNumber: 4 - X: 0.3\n", "Nodo 4_A# - TouchNumber: 4 - X: 0.3\n", "Nodo 4_A+ - TouchNumber: 4 - X: 0.3\n", "Nodo 4_A- - TouchNumber: 4 - X: 0.3\n", "Nodo 4_A/ - TouchNumber: 4 - X: 0.3\n", "Nodo 4_A= - TouchNumber: 4 - X: 0.3\n", "Nodo 5_B! - TouchNumber: 5 - X: 0.4\n", "Nodo 5_B# - TouchNumber: 5 - X: 0.4\n", "Nodo 5_B+ - TouchNumber: 5 - X: 0.4\n", "Nodo 5_B= - TouchNumber: 5 - X: 0.4\n", "Nodo 5_D# - TouchNumber: 5 - X: 0.4\n", "Nodo 5_D/ - TouchNumber: 5 - X: 0.4\n", "Nodo 5_D= - TouchNumber: 5 - X: 0.4\n", "Nodo 6_D! - TouchNumber: 6 - X: 0.5\n", "Nodo 6_D# - TouchNumber: 6 - X: 0.5\n", "Nodo 6_D/ - TouchNumber: 6 - X: 0.5\n", "Nodo 6_E# - TouchNumber: 6 - X: 0.5\n", "Nodo 6_F# - TouchNumber: 6 - X: 0.5\n", "Nodo 6_F+ - TouchNumber: 6 - X: 0.5\n", "Nodo 7_A! - TouchNumber: 7 - X: 0.6\n", "Nodo 7_A# - TouchNumber: 7 - X: 0.6\n", "Nodo 7_A- - TouchNumber: 7 - X: 0.6\n", "Nodo 7_A= - TouchNumber: 7 - X: 0.6\n", "Nodo 7_E# - TouchNumber: 7 - X: 0.6\n", "Nodo 8_A! - TouchNumber: 8 - X: 0.7\n", "Nodo 8_A# - TouchNumber: 8 - X: 0.7\n", "Nodo 8_A- - TouchNumber: 8 - X: 0.7\n", "Nodo 8_A/ - TouchNumber: 8 - X: 0.7\n", "Nodo 8_B! - TouchNumber: 8 - X: 0.7\n", "Nodo 8_D# - TouchNumber: 8 - X: 0.7\n", "Nodo 8_D= - TouchNumber: 8 - X: 0.7\n", "Nodo 9_B! - TouchNumber: 9 - X: 0.8\n", "Nodo 9_B# - TouchNumber: 9 - X: 0.8\n", "Nodo 9_B= - TouchNumber: 9 - X: 0.8\n", "Nodo 9_D! - TouchNumber: 9 - X: 0.8\n", "Nodo 9_D# - TouchNumber: 9 - X: 0.8\n", "Nodo 9_D= - TouchNumber: 9 - X: 0.8\n", "Nodo 9_E# - TouchNumber: 9 - X: 0.8\n", "Nodo 10_A! - TouchNumber: 10 - X: 0.9\n", "Nodo 10_D! - TouchNumber: 10 - X: 0.9\n", "Nodo 10_D= - TouchNumber: 10 - X: 0.9\n", "Nodo 10_E# - TouchNumber: 10 - X: 0.9\n", "Nodo 11_A! - TouchNumber: 11 - X: 0.95\n", "Nodo 11_A# - TouchNumber: 11 - X: 0.95\n", "Nodo 11_B! - TouchNumber: 11 - X: 0.95\n", "Nodo 11_E# - TouchNumber: 11 - X: 0.95\n", "DEBUG - Assegnazione coordinate X:\n", "Nodo 1_S! - TouchNumber: 1 - X: 0.05\n", "Nodo 1_S# - TouchNumber: 1 - X: 0.05\n", "Nodo 1_S+ - TouchNumber: 1 - X: 0.05\n", "Nodo 1_S- - TouchNumber: 1 - X: 0.05\n", "Nodo 1_S/ - TouchNumber: 1 - X: 0.05\n", "Nodo 1_S= - TouchNumber: 1 - X: 0.05\n", "Nodo 2_R! - TouchNumber: 2 - X: 0.1\n", "Nodo 2_R# - TouchNumber: 2 - X: 0.1\n", "Nodo 2_R+ - TouchNumber: 2 - X: 0.1\n", "Nodo 2_R- - TouchNumber: 2 - X: 0.1\n", "Nodo 2_R/ - TouchNumber: 2 - X: 0.1\n", "Nodo 2_R= - TouchNumber: 2 - X: 0.1\n", "Nodo 3_E# - TouchNumber: 3 - X: 0.2\n", "Nodo 3_F= - TouchNumber: 3 - X: 0.2\n", "Nodo 4_A! - TouchNumber: 4 - X: 0.3\n", "Nodo 4_A# - TouchNumber: 4 - X: 0.3\n", "Nodo 4_A+ - TouchNumber: 4 - X: 0.3\n", "Nodo 4_A- - TouchNumber: 4 - X: 0.3\n", "Nodo 4_A/ - TouchNumber: 4 - X: 0.3\n", "Nodo 4_A= - TouchNumber: 4 - X: 0.3\n", "Nodo 5_B! - TouchNumber: 5 - X: 0.4\n", "Nodo 5_B# - TouchNumber: 5 - X: 0.4\n", "Nodo 5_B+ - TouchNumber: 5 - X: 0.4\n", "Nodo 5_B= - TouchNumber: 5 - X: 0.4\n", "Nodo 5_D# - TouchNumber: 5 - X: 0.4\n", "Nodo 5_D/ - TouchNumber: 5 - X: 0.4\n", "Nodo 5_D= - TouchNumber: 5 - X: 0.4\n", "Nodo 6_D! - TouchNumber: 6 - X: 0.5\n", "Nodo 6_D# - TouchNumber: 6 - X: 0.5\n", "Nodo 6_D/ - TouchNumber: 6 - X: 0.5\n", "Nodo 6_E# - TouchNumber: 6 - X: 0.5\n", "Nodo 6_F# - TouchNumber: 6 - X: 0.5\n", "Nodo 6_F+ - TouchNumber: 6 - X: 0.5\n", "Nodo 7_A! - TouchNumber: 7 - X: 0.6\n", "Nodo 7_A# - TouchNumber: 7 - X: 0.6\n", "Nodo 7_A- - TouchNumber: 7 - X: 0.6\n", "Nodo 7_A= - TouchNumber: 7 - X: 0.6\n", "Nodo 7_E# - TouchNumber: 7 - X: 0.6\n", "Nodo 8_A! - TouchNumber: 8 - X: 0.7\n", "Nodo 8_A# - TouchNumber: 8 - X: 0.7\n", "Nodo 8_A- - TouchNumber: 8 - X: 0.7\n", "Nodo 8_A/ - TouchNumber: 8 - X: 0.7\n", "Nodo 8_B! - TouchNumber: 8 - X: 0.7\n", "Nodo 8_D# - TouchNumber: 8 - X: 0.7\n", "Nodo 8_D= - TouchNumber: 8 - X: 0.7\n", "Nodo 9_B! - TouchNumber: 9 - X: 0.8\n", "Nodo 9_B# - TouchNumber: 9 - X: 0.8\n", "Nodo 9_B= - TouchNumber: 9 - X: 0.8\n", "Nodo 9_D! - TouchNumber: 9 - X: 0.8\n", "Nodo 9_D# - TouchNumber: 9 - X: 0.8\n", "Nodo 9_D= - TouchNumber: 9 - X: 0.8\n", "Nodo 9_E# - TouchNumber: 9 - X: 0.8\n", "Nodo 10_A! - TouchNumber: 10 - X: 0.9\n", "Nodo 10_D! - TouchNumber: 10 - X: 0.9\n", "Nodo 10_D= - TouchNumber: 10 - X: 0.9\n", "Nodo 10_E# - TouchNumber: 10 - X: 0.9\n", "Nodo 11_A! - TouchNumber: 11 - X: 0.95\n", "Nodo 11_A# - TouchNumber: 11 - X: 0.95\n", "Nodo 11_B! - TouchNumber: 11 - X: 0.95\n", "Nodo 11_E# - TouchNumber: 11 - X: 0.95\n", "DEBUG - Assegnazione coordinate X:\n", "Nodo 1_S! - TouchNumber: 1 - X: 0.05\n", "Nodo 1_S# - TouchNumber: 1 - X: 0.05\n", "Nodo 1_S+ - TouchNumber: 1 - X: 0.05\n", "Nodo 1_S- - TouchNumber: 1 - X: 0.05\n", "Nodo 1_S/ - TouchNumber: 1 - X: 0.05\n", "Nodo 1_S= - TouchNumber: 1 - X: 0.05\n", "Nodo 2_R! - TouchNumber: 2 - X: 0.1\n", "Nodo 2_R# - TouchNumber: 2 - X: 0.1\n", "Nodo 2_R+ - TouchNumber: 2 - X: 0.1\n", "Nodo 2_R- - TouchNumber: 2 - X: 0.1\n", "Nodo 2_R/ - TouchNumber: 2 - X: 0.1\n", "Nodo 2_R= - TouchNumber: 2 - X: 0.1\n", "Nodo 3_E# - TouchNumber: 3 - X: 0.2\n", "Nodo 3_F= - TouchNumber: 3 - X: 0.2\n", "Nodo 4_A! - TouchNumber: 4 - X: 0.3\n", "Nodo 4_A# - TouchNumber: 4 - X: 0.3\n", "Nodo 4_A+ - TouchNumber: 4 - X: 0.3\n", "Nodo 4_A- - TouchNumber: 4 - X: 0.3\n", "Nodo 4_A/ - TouchNumber: 4 - X: 0.3\n", "Nodo 4_A= - TouchNumber: 4 - X: 0.3\n", "Nodo 5_B! - TouchNumber: 5 - X: 0.4\n", "Nodo 5_B# - TouchNumber: 5 - X: 0.4\n", "Nodo 5_B+ - TouchNumber: 5 - X: 0.4\n", "Nodo 5_B= - TouchNumber: 5 - X: 0.4\n", "Nodo 5_D# - TouchNumber: 5 - X: 0.4\n", "Nodo 5_D/ - TouchNumber: 5 - X: 0.4\n", "Nodo 5_D= - TouchNumber: 5 - X: 0.4\n", "DEBUG - Assegnazione coordinate X:\n", "Nodo 1_S! - TouchNumber: 1 - X: 0.05\n", "Nodo 1_S# - TouchNumber: 1 - X: 0.05\n", "Nodo 1_S+ - TouchNumber: 1 - X: 0.05\n", "Nodo 1_S- - TouchNumber: 1 - X: 0.05\n", "Nodo 1_S/ - TouchNumber: 1 - X: 0.05\n", "Nodo 1_S= - TouchNumber: 1 - X: 0.05\n", "Nodo 2_R! - TouchNumber: 2 - X: 0.1\n", "Nodo 2_R# - TouchNumber: 2 - X: 0.1\n", "Nodo 2_R+ - TouchNumber: 2 - X: 0.1\n", "Nodo 2_R- - TouchNumber: 2 - X: 0.1\n", "Nodo 2_R/ - TouchNumber: 2 - X: 0.1\n", "Nodo 2_R= - TouchNumber: 2 - X: 0.1\n", "Nodo 3_E# - TouchNumber: 3 - X: 0.2\n", "Nodo 3_F= - TouchNumber: 3 - X: 0.2\n", "Nodo 4_A! - TouchNumber: 4 - X: 0.3\n", "Nodo 4_A# - TouchNumber: 4 - X: 0.3\n", "Nodo 4_A+ - TouchNumber: 4 - X: 0.3\n", "Nodo 4_A- - TouchNumber: 4 - X: 0.3\n", "Nodo 4_A/ - TouchNumber: 4 - X: 0.3\n", "Nodo 4_A= - TouchNumber: 4 - X: 0.3\n", "Nodo 5_B! - TouchNumber: 5 - X: 0.4\n", "Nodo 5_B# - TouchNumber: 5 - X: 0.4\n", "Nodo 5_B+ - TouchNumber: 5 - X: 0.4\n", "Nodo 5_B= - TouchNumber: 5 - X: 0.4\n", "Nodo 5_D# - TouchNumber: 5 - X: 0.4\n", "Nodo 5_D/ - TouchNumber: 5 - X: 0.4\n", "Nodo 5_D= - TouchNumber: 5 - X: 0.4\n", "Nodo 6_D! - TouchNumber: 6 - X: 0.5\n", "Nodo 6_D# - TouchNumber: 6 - X: 0.5\n", "Nodo 6_D/ - TouchNumber: 6 - X: 0.5\n", "Nodo 6_E# - TouchNumber: 6 - X: 0.5\n", "Nodo 6_F# - TouchNumber: 6 - X: 0.5\n", "Nodo 6_F+ - TouchNumber: 6 - X: 0.5\n"]}], "source": ["import dash\n", "from dash import dcc, html, Input, Output, callback, dash_table\n", "import plotly.graph_objects as go\n", "import pandas as pd\n", "import numpy as np\n", "from collections import defaultdict\n", "\n", "# CONFIGURAZIONE - Modifica qui per cambiare le caratteristiche visualizzate\n", "col_per_fond = {\n", "    \"S\": \"Eval\", \n", "    \"R\": \"<PERSON><PERSON>\", \n", "    \"E\": \"Eval\", \n", "    \"A\": \"<PERSON><PERSON>\", \n", "    \"B\": \"Eval\", \n", "    \"D\": \"Eval\", \n", "    \"F\": \"Eval\"\n", "}\n", "\n", "\n", "def create_node_id(touch_number, foundamental, characteristic):\n", "    \"\"\"Crea un ID unico per ogni nodo\"\"\"\n", "    return f\"T{touch_number}_{foundamental}_{characteristic}\"\n", "\n", "def extract_eval_from_string(eval_string):\n", "    \"\"\"Estrae la valutazione dalla stringa 'Eval_X' -> 'X'\"\"\"\n", "    if pd.isna(eval_string) or eval_string == '':\n", "        return '='\n", "    if isinstance(eval_string, str) and eval_string.startswith('Eval_'):\n", "        return eval_string.replace('Eval_', '')\n", "    return str(eval_string)\n", "\n", "def get_color_by_eval(eval_val):\n", "    \"\"\"Ritorna None per lasciare che Plotly gestisca i colori automaticamente\"\"\"\n", "    return None\n", "\n", "def process_sankey_data(df, max_touch_number=11, game_id=None, set_number=None):\n", "    \"\"\"Processa i dati per creare il diagramma Sankey\"\"\"\n", "    \n", "    # Filtra i dati se specificato\n", "    if game_id is not None:\n", "        df = df[df['GameID'] == game_id]\n", "    if set_number is not None:\n", "        df = df[df['SetNumber'] == set_number]\n", "    \n", "    # Filtra tocchi fino a TouchNumber specificato\n", "    df = df[df['TouchNumber'] <= max_touch_number]\n", "    \n", "    if df.empty:\n", "        return {}, {}\n", "    \n", "    # Dizionari per tracciare nodi e collegamenti\n", "    nodes = {}\n", "    links = defaultdict(int)\n", "    \n", "    # Raggruppa per azione\n", "    for action_id, action_data in df.groupby('ActionNumber'):\n", "        action_touches = action_data.sort_values('TouchNumber')\n", "        \n", "        # <PERSON>rea nodi per ogni tocco dell'azione\n", "        for _, touch in action_touches.iterrows():\n", "            # Ottieni la caratteristica specificata per questo fondamentale\n", "            characteristic_col = col_per_fond.get(touch['Foundamental'], 'Eval')\n", "            \n", "            if characteristic_col in touch and pd.notna(touch[characteristic_col]):\n", "                characteristic = touch[characteristic_col]\n", "            else:\n", "                characteristic = '='  # <PERSON><PERSON> di default\n", "            \n", "            # Pulisce la caratteristica se è nel formato 'Prefix_Value'\n", "            if isinstance(characteristic, str) and '_' in characteristic:\n", "                characteristic = characteristic.split('_', 1)[-1]\n", "            \n", "            node_id = create_node_id(touch['TouchNumber'], touch['Foundamental'], characteristic)\n", "            \n", "            if node_id not in nodes:\n", "                nodes[node_id] = {\n", "                    'id': node_id,\n", "                    'touch_number': touch['TouchNumber'],\n", "                    'foundamental': touch['Foundamental'],\n", "                    'characteristic': characteristic,\n", "                    'count': 0,\n", "                    'label': f\"{touch['TouchNumber']}_{touch['Foundamental']}{characteristic}\"\n", "                }\n", "            nodes[node_id]['count'] += 1\n", "        \n", "        # <PERSON>rea collegamenti tra tocchi consecutivi\n", "        touches_list = action_touches.to_dict('records')\n", "        for i in range(len(touches_list) - 1):\n", "            current_touch = touches_list[i]\n", "            next_touch = touches_list[i + 1]\n", "            \n", "            # <PERSON><PERSON><PERSON>atteri<PERSON>\n", "            current_char_col = col_per_fond.get(current_touch['Foundamental'], 'Eval')\n", "            next_char_col = col_per_fond.get(next_touch['Foundamental'], 'Eval')\n", "            \n", "            current_char = current_touch.get(current_char_col, '=')\n", "            next_char = next_touch.get(next_char_col, '=')\n", "            \n", "            # <PERSON><PERSON><PERSON><PERSON> le caratteristiche\n", "            if isinstance(current_char, str) and '_' in current_char:\n", "                current_char = current_char.split('_', 1)[-1]\n", "            if isinstance(next_char, str) and '_' in next_char:\n", "                next_char = next_char.split('_', 1)[-1]\n", "            \n", "            source_id = create_node_id(current_touch['TouchNumber'], current_touch['Foundamental'], current_char)\n", "            target_id = create_node_id(next_touch['TouchNumber'], next_touch['Foundamental'], next_char)\n", "            \n", "            link_key = (source_id, target_id)\n", "            links[link_key] += 1\n", "    \n", "    return nodes, links\n", "\n", "def create_sankey_figure(nodes, links):\n", "    \"\"\"Crea il diagramma Sankey usando Plotly\"\"\"\n", "    \n", "    if not nodes or not links:\n", "        # Crea un grafico vuoto se non ci sono dati\n", "        fig = go.Figure()\n", "        fig.add_annotation(\n", "            text=\"N<PERSON>un dato disponibile per i filtri selezionati\",\n", "            xref=\"paper\", yref=\"paper\",\n", "            x=0.5, y=0.5, showarrow=False,\n", "            font=dict(size=16)\n", "        )\n", "        fig.update_layout(height=400)\n", "        return fig\n", "    \n", "    # Converti nodi in liste ordinate\n", "    node_list = list(nodes.values())\n", "    node_list.sort(key=lambda x: (x['touch_number'], x['foundamental'], x['characteristic']))\n", "    \n", "    # Crea mapping da node_id a indice\n", "    node_id_to_index = {node['id']: i for i, node in enumerate(node_list)}\n", "    \n", "    # Prepara dati per <PERSON><PERSON><PERSON>\n", "    node_labels = [f\"{node['label']}<br>({node['count']})\" for node in node_list]\n", "    \n", "    # Calcola posizioni per evitare sovrapposizioni con nodi più piccoli\n", "    touch_numbers = sorted(set(node['touch_number'] for node in node_list))\n", "    x_positions = []\n", "    y_positions = []\n", "    \n", "    # Raggruppa nodi per TouchNumber per migliore distribuzione verticale\n", "    nodes_by_touch = {}\n", "    for node in node_list:\n", "        touch_num = node['touch_number']\n", "        if touch_num not in nodes_by_touch:\n", "            nodes_by_touch[touch_num] = []\n", "        nodes_by_touch[touch_num].append(node)\n", "    \n", "    # Calcola posizioni per ogni gruppo di TouchNumber\n", "    # Mapping fisso delle coordinate X per ogni TouchNumber\n", "    touch_x_mapping = {\n", "        1: 0.050,\n", "        2: 0.100,\n", "        3: 0.200,\n", "        4: 0.300,\n", "        5: 0.400,\n", "        6: 0.500,\n", "        7: 0.600,\n", "        8: 0.700,\n", "        9: 0.800,\n", "        10: 0.900,\n", "        11: 0.950,\n", "        12: 0.975,\n", "        13: 0.990,\n", "        14: 0.995,\n", "        15: 0.999,\n", "        \n", "    }\n", "    \n", "    # DEBUG: Stampiamo le assegnazioni per verificare\n", "    print(\"DEBUG - Assegnazione coordinate X:\")\n", "    for node in node_list:\n", "        touch_num = node['touch_number']\n", "        x_pos = touch_x_mapping.get(touch_num, 0.500)\n", "        print(f\"Nodo {node['label']} - TouchNumber: {touch_num} - X: {x_pos}\")\n", "        x_positions.append(x_pos)\n", "        \n", "        # Posizione Y con distribuzione molto più spaziata\n", "        nodes_same_touch = nodes_by_touch[touch_num]\n", "        nodes_same_touch.sort(key=lambda x: (x['foundamental'], x['characteristic']))\n", "        \n", "        node_index_in_touch = nodes_same_touch.index(node)\n", "        num_nodes_in_touch = len(nodes_same_touch)\n", "        \n", "        # Distribuzione con molto più spazio tra i nodi\n", "        if num_nodes_in_touch == 1:\n", "            y_pos = 0.5\n", "        else:\n", "            # Usa margini più grandi e distribuzione più spaziata\n", "            margin = 0.05\n", "            available_space = 1.0 - 2 * margin\n", "            y_pos = margin + (node_index_in_touch * available_space) / (num_nodes_in_touch - 1)\n", "        \n", "        y_positions.append(y_pos)\n", "    \n", "    # Prepara collegamenti\n", "    source_indices = []\n", "    target_indices = []\n", "    values = []\n", "    \n", "    for (source_id, target_id), value in links.items():\n", "        if source_id in node_id_to_index and target_id in node_id_to_index:\n", "            source_indices.append(node_id_to_index[source_id])\n", "            target_indices.append(node_id_to_index[target_id])\n", "            values.append(value)\n", "    \n", "    # Prepara hover text personalizzato\n", "    node_hover_text = []\n", "    for node in node_list:\n", "        hover = (\n", "            f\"Nodo: {node['label']}<br>\"\n", "            f\"TouchNumber: {node['touch_number']}<br>\"\n", "            f\"Foundamental: {node['foundamental']}<br>\"\n", "            f\"Characteristic: {node['characteristic']}<br>\"\n", "            f\"Osservazioni: {node['count']}\"\n", "        )\n", "        node_hover_text.append(hover)\n", "    \n", "    link_hover_text = []\n", "    for i, ((source_id, target_id), value) in enumerate(links.items()):\n", "        if source_id in node_id_to_index and target_id in node_id_to_index:\n", "            source_node = nodes[source_id]\n", "            target_node = nodes[target_id]\n", "            hover = (\n", "                f\"Flusso: {source_node['label']} → {target_node['label']}<br>\"\n", "                f\"Da TouchNumber: {source_node['touch_number']}<br>\"\n", "                f\"A TouchNumber: {target_node['touch_number']}<br>\"\n", "                f\"Osservazioni: {value}\"\n", "            )\n", "            link_hover_text.append(hover)\n", "    \n", "    # Crea il diagramma Sankey rimuovendo arrangement='fixed' per permettere movimento\n", "    fig = go.Figure(data=[go.Sankey(\n", "        node=dict(\n", "            pad=30,  # <PERSON><PERSON> padding per evitare sovrapposizioni\n", "            thickness=15,  # <PERSON><PERSON> pi<PERSON> sottili\n", "            line=dict(color=\"black\", width=0.5),\n", "            label=node_labels,\n", "            x=x_positions,\n", "            y=y_positions,\n", "            customdata=node_hover_text,\n", "            hovertemplate='%{customdata}<extra></extra>'\n", "        ),\n", "        link=dict(\n", "            source=source_indices,\n", "            target=target_indices,\n", "            value=values,\n", "            customdata=link_hover_text,\n", "            hovertemplate='%{customdata}<extra></extra>'\n", "        )\n", "    )])\n", "    \n", "    fig.update_layout(\n", "        title={\n", "            'text': \"<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON>\",\n", "            'font': {'size': 20}\n", "        },\n", "        font_size=10,  # Font più piccolo per risparmiare spazio\n", "        height=900,  # <PERSON><PERSON> al<PERSON>zza per distribuire meglio i nodi\n", "        margin=dict(t=80, b=40, l=40, r=40)\n", "    )\n", "    \n", "    return fig\n", "\n", "# Inizializza l'app Dash\n", "app = dash.Dash(__name__)\n", "\n", "# Layout dell'app\n", "app.layout = html.Div([\n", "    html.H1(\"<PERSON><PERSON><PERSON> - Diagramma San<PERSON>\", \n", "            style={'textAlign': 'center', 'marginBottom': 30}),\n", "    \n", "    html.Div([\n", "        html.H3(\"Debug - Posizioni Nodi\"),\n", "        html.Div(id='debug-positions')\n", "    ], style={'margin': '20px', 'padding': '20px', 'border': '1px solid #ddd', 'borderRadius': '5px'}),\n", "    \n", "    html.Div([\n", "        html.H3(\"Configurazione Filtri\"),\n", "        html.Div([\n", "            html.Div([\n", "                html.Label(\"Game ID:\"),\n", "                dcc.Dropdown(\n", "                    id='game-id-dropdown',\n", "                    options=[{'label': f'Game {gid}', 'value': gid} for gid in sorted(df_touch['GameID'].unique())],\n", "                    value=df_touch['GameID'].iloc[0] if not df_touch.empty else None,\n", "                    style={'width': '150px'}\n", "                ),\n", "            ], style={'display': 'inline-block', 'marginRight': '20px'}),\n", "            \n", "            html.Div([\n", "                html.Label(\"Set Number:\"),\n", "                dcc.Dropdown(\n", "                    id='set-number-dropdown',\n", "                    style={'width': '150px'}\n", "                ),\n", "            ], style={'display': 'inline-block', 'marginRight': '20px'}),\n", "            \n", "            html.Div([\n", "                html.Label(\"Max TouchNumber:\"),\n", "                dcc.<PERSON><PERSON><PERSON>(\n", "                    id='max-touch-slider',\n", "                    min=3,\n", "                    max=11,\n", "                    step=1,\n", "                    value=11,\n", "                    marks={i: str(i) for i in range(3, 12)},\n", "                    tooltip={\"placement\": \"bottom\", \"always_visible\": True}\n", "                ),\n", "            ], style={'display': 'inline-block', 'width': '200px'}),\n", "        ], style={'display': 'flex', 'alignItems': 'center', 'gap': '20px'}),\n", "    ], style={'margin': '20px', 'padding': '20px', 'border': '1px solid #ddd', 'borderRadius': '5px'}),\n", "    \n", "    html.Div([\n", "        html.H3(\"Configurazione Caratteristiche per Fondamentale\"),\n", "        html.P(\"Attualmente configurato per mostrare 'Eval' per tutti i fondamentali. Modifica col_per_fond nel codice per personalizzare.\"),\n", "        html.Div([\n", "            html.Span(f\"{fond}: {char}\", style={'margin': '5px', 'padding': '5px', 'backgroundColor': '#f0f0f0', 'borderRadius': '3px'})\n", "            for fond, char in col_per_fond.items()\n", "        ], style={'display': 'flex', 'flexWrap': 'wrap'})\n", "    ], style={'margin': '20px', 'padding': '20px', 'border': '1px solid #ddd', 'borderRadius': '5px'}),\n", "    \n", "    dcc.Graph(id='sankey-diagram'),\n", "    \n", "    html.Div([\n", "        html.H3(\"Statistiche Dati\"),\n", "        html.Div(id='data-stats')\n", "    ], style={'margin': '20px', 'padding': '20px', 'border': '1px solid #ddd', 'borderRadius': '5px'}),\n", "    \n", "    html.Div([\n", "        html.H3(\"Come interpretare il diagramma:\"),\n", "        html.Ul([\n", "            html.Li(\"Ogni colonna verticale rappresenta un TouchNumber (1, 2, 3, etc.)\"),\n", "            html.Li(\"I nodi mostrano Fondamentale + Caratteristica configurata (es. S+, R-, E#)\"),\n", "            html.Li(\"I tocchi sono raggruppati per ActionNumber, mantenendo i flussi separati quando necessario\"),\n", "            html.Li(\"Le azioni con lo stesso nodo iniziale possono condividere percorsi comuni\"),\n", "        ])\n", "    ], style={'margin': '20px', 'padding': '20px', 'border': '1px solid #ddd', 'borderRadius': '5px'})\n", "])\n", "\n", "@app.callback(\n", "    Output('set-number-dropdown', 'options'),\n", "    Output('set-number-dropdown', 'value'),\n", "    Input('game-id-dropdown', 'value')\n", ")\n", "def update_set_options(game_id):\n", "    \"\"\"Aggiorna le opzioni del Set Number basate su Game ID selezionato\"\"\"\n", "    if game_id is None:\n", "        return [], None\n", "    \n", "    filtered_df = df_touch[df_touch['GameID'] == game_id]\n", "    set_options = [{'label': f'Set {sn}', 'value': sn} for sn in sorted(filtered_df['SetNumber'].unique())]\n", "    default_set = filtered_df['SetNumber'].iloc[0] if not filtered_df.empty else None\n", "    \n", "    return set_options, default_set\n", "\n", "@app.callback(\n", "    Output('sankey-diagram', 'figure'),\n", "    Output('data-stats', 'children'),\n", "    Output('debug-positions', 'children'),\n", "    Input('game-id-dropdown', 'value'),\n", "    Input('set-number-dropdown', 'value'),\n", "    Input('max-touch-slider', 'value')\n", ")\n", "def update_sankey(game_id, set_number, max_touch):\n", "    \"\"\"Callback per aggiornare il diagramma quando cambiano i filtri\"\"\"\n", "    nodes, links = process_sankey_data(df_touch, max_touch, game_id, set_number)\n", "    fig = create_sankey_figure(nodes, links)\n", "    \n", "    # Statistiche\n", "    filtered_df = df_touch.copy()\n", "    if game_id is not None:\n", "        filtered_df = filtered_df[filtered_df['GameID'] == game_id]\n", "    if set_number is not None:\n", "        filtered_df = filtered_df[filtered_df['SetNumber'] == set_number]\n", "    filtered_df = filtered_df[filtered_df['TouchNumber'] <= max_touch]\n", "    \n", "    stats = html.Div([\n", "        html.P(f\"Totale tocchi: {len(filtered_df)}\"),\n", "        html.P(f\"Azioni uniche: {filtered_df['ActionNumber'].nunique() if not filtered_df.empty else 0}\"),\n", "        html.P(f\"Nodi creati: {len(nodes)}\"),\n", "        html.P(f\"Collegamenti: {len(links)}\"),\n", "        html.P(f\"TouchNumber range: {filtered_df['TouchNumber'].min() if not filtered_df.empty else 'N/A'} - {filtered_df['TouchNumber'].max() if not filtered_df.empty else 'N/A'}\"),\n", "    ])\n", "    \n", "    # Debug info per vedere le posizioni assegnate\n", "    debug_info = []\n", "    if nodes:\n", "        node_list = list(nodes.values())\n", "        node_list.sort(key=lambda x: (x['touch_number'], x['foundamental'], x['characteristic']))\n", "        \n", "        touch_x_mapping = {\n", "            1: 0.050, 2: 0.100, 3: 0.200, 4: 0.300, 5: 0.400, 6: 0.500,\n", "            7: 0.600, 8: 0.700, 9: 0.800, 10: 0.900, 11: 0.950\n", "        }\n", "        \n", "        for node in node_list:\n", "            expected_x = touch_x_mapping.get(node['touch_number'], 0.500)\n", "            debug_info.append(\n", "                html.P(f\"Nodo {node['label']} | TouchNumber: {node['touch_number']} | X attesa: {expected_x}\")\n", "            )\n", "    \n", "    debug_div = html.Div(debug_info) if debug_info else html.P(\"Nessun nodo da debuggare\")\n", "    \n", "    return fig, stats, debug_div\n", "\n", "if __name__ == '__main__':\n", "    app.run(debug=True, port=8050)"]}, {"cell_type": "code", "execution_count": 24, "id": "b4292516", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>            <script src=\"https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.5/MathJax.js?config=TeX-AMS-MML_SVG\"></script><script type=\"text/javascript\">if (window.MathJax && window.MathJax.Hub && window.MathJax.Hub.Config) {window.MathJax.Hub.Config({SVG: {font: \"STIX-Web\"}});}</script>                <script type=\"text/javascript\">window.PlotlyConfig = {MathJaxConfig: 'local'};</script>\n", "        <script charset=\"utf-8\" src=\"https://cdn.plot.ly/plotly-3.0.1.min.js\"></script>                <div id=\"29f46770-3cad-48e9-aa3d-69e23fe7ae78\" class=\"plotly-graph-div\" style=\"height:525px; width:100%;\"></div>            <script type=\"text/javascript\">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById(\"29f46770-3cad-48e9-aa3d-69e23fe7ae78\")) {                    Plotly.newPlot(                        \"29f46770-3cad-48e9-aa3d-69e23fe7ae78\",                        [{\"arrangement\":\"snap\",\"link\":{\"customdata\":[\"E: Eval_# \\u2192 A: Eval_#\\u003cbr\\u003eTouchNumber: 3 \\u2192 4\\u003cbr\\u003eCount: 71\",\"S: Eval_- \\u2192 R: Eval_+\\u003cbr\\u003eTouchNumber: 1 \\u2192 2\\u003cbr\\u003eCount: 41\",\"R: Eval_+ \\u2192 E: Eval_#\\u003cbr\\u003eTouchNumber: 2 \\u2192 3\\u003cbr\\u003eCount: 39\",\"S: Eval_+ \\u2192 R: Eval_-\\u003cbr\\u003eTouchNumber: 1 \\u2192 2\\u003cbr\\u003eCount: 39\",\"R: Eval_- \\u2192 E: Eval_#\\u003cbr\\u003eTouchNumber: 2 \\u2192 3\\u003cbr\\u003eCount: 37\",\"R: Eval_! \\u2192 E: Eval_#\\u003cbr\\u003eTouchNumber: 2 \\u2192 3\\u003cbr\\u003eCount: 34\",\"S: Eval_! \\u2192 R: Eval_!\\u003cbr\\u003eTouchNumber: 1 \\u2192 2\\u003cbr\\u003eCount: 34\",\"E: Eval_# \\u2192 A: Eval_-\\u003cbr\\u003eTouchNumber: 3 \\u2192 4\\u003cbr\\u003eCount: 32\",\"R: Eval_# \\u2192 E: Eval_#\\u003cbr\\u003eTouchNumber: 2 \\u2192 3\\u003cbr\\u003eCount: 32\",\"S: Eval_- \\u2192 R: Eval_#\\u003cbr\\u003eTouchNumber: 1 \\u2192 2\\u003cbr\\u003eCount: 32\",\"A: Eval_# \\u2192 B: Eval_=\\u003cbr\\u003eTouchNumber: 4 \\u2192 5\\u003cbr\\u003eCount: 29\",\"A: Eval_- \\u2192 D: Eval_#\\u003cbr\\u003eTouchNumber: 4 \\u2192 5\\u003cbr\\u003eCount: 17\",\"A: Eval_- \\u2192 B: Eval_+\\u003cbr\\u003eTouchNumber: 4 \\u2192 5\\u003cbr\\u003eCount: 16\",\"B: Eval_+ \\u2192 D: Eval_#\\u003cbr\\u003eTouchNumber: 5 \\u2192 6\\u003cbr\\u003eCount: 16\",\"D: Eval_# \\u2192 E: Eval_#\\u003cbr\\u003eTouchNumber: 5 \\u2192 6\\u003cbr\\u003eCount: 15\",\"D: Eval_# \\u2192 E: Eval_#\\u003cbr\\u003eTouchNumber: 6 \\u2192 7\\u003cbr\\u003eCount: 14\",\"S: Eval_# \\u2192 R: Eval_=\\u003cbr\\u003eTouchNumber: 1 \\u2192 2\\u003cbr\\u003eCount: 13\",\"E: Eval_# \\u2192 A: Eval_\\u002f\\u003cbr\\u003eTouchNumber: 3 \\u2192 4\\u003cbr\\u003eCount: 12\",\"A: Eval_\\u002f \\u2192 B: Eval_#\\u003cbr\\u003eTouchNumber: 4 \\u2192 5\\u003cbr\\u003eCount: 12\",\"E: Eval_# \\u2192 A: Eval_#\\u003cbr\\u003eTouchNumber: 7 \\u2192 8\\u003cbr\\u003eCount: 11\",\"A: Eval_! \\u2192 B: Eval_!\\u003cbr\\u003eTouchNumber: 4 \\u2192 5\\u003cbr\\u003eCount: 10\",\"E: Eval_# \\u2192 A: Eval_=\\u003cbr\\u003eTouchNumber: 3 \\u2192 4\\u003cbr\\u003eCount: 10\",\"B: Eval_! \\u2192 D: Eval_!\\u003cbr\\u003eTouchNumber: 5 \\u2192 6\\u003cbr\\u003eCount: 10\",\"E: Eval_# \\u2192 A: Eval_!\\u003cbr\\u003eTouchNumber: 3 \\u2192 4\\u003cbr\\u003eCount: 10\",\"S: Eval_\\u002f \\u2192 R: Eval_\\u002f\\u003cbr\\u003eTouchNumber: 1 \\u2192 2\\u003cbr\\u003eCount: 9\",\"D: Eval_! \\u2192 E: Eval_#\\u003cbr\\u003eTouchNumber: 6 \\u2192 7\\u003cbr\\u003eCount: 9\",\"A: Eval_# \\u2192 D: Eval_=\\u003cbr\\u003eTouchNumber: 4 \\u2192 5\\u003cbr\\u003eCount: 9\",\"D: Eval_# \\u2192 E: Eval_#\\u003cbr\\u003eTouchNumber: 9 \\u2192 10\\u003cbr\\u003eCount: 8\",\"E: Eval_# \\u2192 A: Eval_-\\u003cbr\\u003eTouchNumber: 7 \\u2192 8\\u003cbr\\u003eCount: 8\",\"E: Eval_# \\u2192 A: Eval_+\\u003cbr\\u003eTouchNumber: 3 \\u2192 4\\u003cbr\\u003eCount: 7\",\"E: Eval_# \\u2192 A: Eval_#\\u003cbr\\u003eTouchNumber: 6 \\u2192 7\\u003cbr\\u003eCount: 7\",\"A: Eval_- \\u2192 D: Eval_#\\u003cbr\\u003eTouchNumber: 8 \\u2192 9\\u003cbr\\u003eCount: 6\",\"A: Eval_# \\u2192 D: Eval_=\\u003cbr\\u003eTouchNumber: 7 \\u2192 8\\u003cbr\\u003eCount: 5\",\"A: Eval_# \\u2192 B: Eval_=\\u003cbr\\u003eTouchNumber: 8 \\u2192 9\\u003cbr\\u003eCount: 4\",\"E: Eval_# \\u2192 A: Eval_#\\u003cbr\\u003eTouchNumber: 4 \\u2192 5\\u003cbr\\u003eCount: 4\",\"E: Eval_# \\u2192 A: Eval_!\\u003cbr\\u003eTouchNumber: 7 \\u2192 8\\u003cbr\\u003eCount: 4\",\"A: Eval_! \\u2192 B: Eval_!\\u003cbr\\u003eTouchNumber: 8 \\u2192 9\\u003cbr\\u003eCount: 4\",\"D: Eval_! \\u2192 E: Eval_#\\u003cbr\\u003eTouchNumber: 10 \\u2192 11\\u003cbr\\u003eCount: 4\",\"B: Eval_! \\u2192 D: Eval_!\\u003cbr\\u003eTouchNumber: 9 \\u2192 10\\u003cbr\\u003eCount: 4\",\"B: Eval_+ \\u2192 D: Eval_#\\u003cbr\\u003eTouchNumber: 8 \\u2192 9\\u003cbr\\u003eCount: 3\",\"R: Eval_\\u002f \\u2192 F: Eval_#\\u003cbr\\u003eTouchNumber: 2 \\u2192 3\\u003cbr\\u003eCount: 3\",\"D: Eval_# \\u2192 E: Eval_#\\u003cbr\\u003eTouchNumber: 10 \\u2192 11\\u003cbr\\u003eCount: 3\",\"E: Eval_# \\u2192 A: Eval_-\\u003cbr\\u003eTouchNumber: 6 \\u2192 7\\u003cbr\\u003eCount: 3\",\"E: Eval_# \\u2192 A: Eval_!\\u003cbr\\u003eTouchNumber: 10 \\u2192 11\\u003cbr\\u003eCount: 3\",\"E: Eval_# \\u2192 A: Eval_#\\u003cbr\\u003eTouchNumber: 10 \\u2192 11\\u003cbr\\u003eCount: 3\",\"E: Eval_# \\u2192 A: Eval_\\u002f\\u003cbr\\u003eTouchNumber: 7 \\u2192 8\\u003cbr\\u003eCount: 3\",\"E: Eval_# \\u2192 A: Eval_-\\u003cbr\\u003eTouchNumber: 10 \\u2192 11\\u003cbr\\u003eCount: 3\",\"R: Eval_\\u002f \\u2192 A: Eval_#\\u003cbr\\u003eTouchNumber: 2 \\u2192 3\\u003cbr\\u003eCount: 3\",\"A: Eval_\\u002f \\u2192 B: Eval_#\\u003cbr\\u003eTouchNumber: 8 \\u2192 9\\u003cbr\\u003eCount: 3\",\"F: Eval_# \\u2192 E: Eval_#\\u003cbr\\u003eTouchNumber: 3 \\u2192 4\\u003cbr\\u003eCount: 3\",\"A: Eval_- \\u2192 B: Eval_+\\u003cbr\\u003eTouchNumber: 7 \\u2192 8\\u003cbr\\u003eCount: 3\",\"B: Eval_! \\u2192 D: Eval_!\\u003cbr\\u003eTouchNumber: 8 \\u2192 9\\u003cbr\\u003eCount: 3\",\"A: Eval_+ \\u2192 D: Eval_\\u002f\\u003cbr\\u003eTouchNumber: 4 \\u2192 5\\u003cbr\\u003eCount: 3\",\"A: Eval_# \\u2192 B: Eval_=\\u003cbr\\u003eTouchNumber: 5 \\u2192 6\\u003cbr\\u003eCount: 3\",\"A: Eval_! \\u2192 B: Eval_!\\u003cbr\\u003eTouchNumber: 7 \\u2192 8\\u003cbr\\u003eCount: 2\",\"D: Eval_\\u002f \\u2192 F: Eval_+\\u003cbr\\u003eTouchNumber: 5 \\u2192 6\\u003cbr\\u003eCount: 2\",\"R: Eval_+ \\u2192 E: Eval_=\\u003cbr\\u003eTouchNumber: 2 \\u2192 3\\u003cbr\\u003eCount: 2\",\"F: Eval_+ \\u2192 E: Eval_#\\u003cbr\\u003eTouchNumber: 6 \\u2192 7\\u003cbr\\u003eCount: 2\",\"E: Eval_# \\u2192 A: Eval_!\\u003cbr\\u003eTouchNumber: 6 \\u2192 7\\u003cbr\\u003eCount: 2\",\"A: Eval_- \\u2192 B: Eval_+\\u003cbr\\u003eTouchNumber: 8 \\u2192 9\\u003cbr\\u003eCount: 2\",\"A: Eval_+ \\u2192 D: Eval_-\\u003cbr\\u003eTouchNumber: 4 \\u2192 5\\u003cbr\\u003eCount: 2\",\"E: Eval_# \\u2192 A: Eval_=\\u003cbr\\u003eTouchNumber: 6 \\u2192 7\\u003cbr\\u003eCount: 2\",\"D: Eval_# \\u2192 E: Eval_-\\u003cbr\\u003eTouchNumber: 5 \\u2192 6\\u003cbr\\u003eCount: 2\",\"A: Eval_+ \\u2192 B: Eval_-\\u003cbr\\u003eTouchNumber: 4 \\u2192 5\\u003cbr\\u003eCount: 2\",\"D: Eval_! \\u2192 E: Eval_#\\u003cbr\\u003eTouchNumber: 9 \\u2192 10\\u003cbr\\u003eCount: 2\",\"B: Eval_+ \\u2192 D: Eval_#\\u003cbr\\u003eTouchNumber: 9 \\u2192 10\\u003cbr\\u003eCount: 2\",\"A: Eval_# \\u2192 D: Eval_=\\u003cbr\\u003eTouchNumber: 8 \\u2192 9\\u003cbr\\u003eCount: 2\",\"E: Eval_# \\u2192 A: Eval_-\\u003cbr\\u003eTouchNumber: 8 \\u2192 9\\u003cbr\\u003eCount: 2\",\"D: Eval_! \\u2192 E: Eval_#\\u003cbr\\u003eTouchNumber: 5 \\u2192 6\\u003cbr\\u003eCount: 1\",\"D: Eval_! \\u2192 F: Eval_-\\u003cbr\\u003eTouchNumber: 6 \\u2192 7\\u003cbr\\u003eCount: 1\",\"B: Eval_= \\u2192 D: Eval_=\\u003cbr\\u003eTouchNumber: 5 \\u2192 6\\u003cbr\\u003eCount: 1\",\"A: Eval_# \\u2192 B: Eval_=\\u003cbr\\u003eTouchNumber: 9 \\u2192 10\\u003cbr\\u003eCount: 1\",\"A: Eval_! \\u2192 B: Eval_!\\u003cbr\\u003eTouchNumber: 3 \\u2192 4\\u003cbr\\u003eCount: 1\",\"A: Eval_# \\u2192 B: Eval_=\\u003cbr\\u003eTouchNumber: 3 \\u2192 4\\u003cbr\\u003eCount: 1\",\"A: Eval_# \\u2192 B: Eval_=\\u003cbr\\u003eTouchNumber: 7 \\u2192 8\\u003cbr\\u003eCount: 1\",\"A: Eval_+ \\u2192 D: Eval_-\\u003cbr\\u003eTouchNumber: 7 \\u2192 8\\u003cbr\\u003eCount: 1\",\"A: Eval_+ \\u2192 D: Eval_\\u002f\\u003cbr\\u003eTouchNumber: 10 \\u2192 11\\u003cbr\\u003eCount: 1\",\"A: Eval_+ \\u2192 D: Eval_\\u002f\\u003cbr\\u003eTouchNumber: 8 \\u2192 9\\u003cbr\\u003eCount: 1\",\"D: Eval_! \\u2192 F: Eval_+\\u003cbr\\u003eTouchNumber: 9 \\u2192 10\\u003cbr\\u003eCount: 1\",\"B: Eval_- \\u2192 D: Eval_\\u002f\\u003cbr\\u003eTouchNumber: 5 \\u2192 6\\u003cbr\\u003eCount: 1\",\"B: Eval_! \\u2192 D: Eval_!\\u003cbr\\u003eTouchNumber: 4 \\u2192 5\\u003cbr\\u003eCount: 1\",\"A: Eval_- \\u2192 D: Eval_#\\u003cbr\\u003eTouchNumber: 9 \\u2192 10\\u003cbr\\u003eCount: 1\",\"A: Eval_\\u002f \\u2192 B: Eval_#\\u003cbr\\u003eTouchNumber: 3 \\u2192 4\\u003cbr\\u003eCount: 1\",\"A: Eval_- \\u2192 B: Eval_+\\u003cbr\\u003eTouchNumber: 9 \\u2192 10\\u003cbr\\u003eCount: 1\",\"A: Eval_\\u002f \\u2192 B: Eval_#\\u003cbr\\u003eTouchNumber: 7 \\u2192 8\\u003cbr\\u003eCount: 1\",\"B: Eval_! \\u2192 D: Eval_!\\u003cbr\\u003eTouchNumber: 10 \\u2192 11\\u003cbr\\u003eCount: 1\",\"B: Eval_+ \\u2192 D: Eval_#\\u003cbr\\u003eTouchNumber: 10 \\u2192 11\\u003cbr\\u003eCount: 1\",\"D: Eval_# \\u2192 A: Eval_+\\u003cbr\\u003eTouchNumber: 9 \\u2192 10\\u003cbr\\u003eCount: 1\",\"D: Eval_# \\u2192 E: Eval_=\\u003cbr\\u003eTouchNumber: 6 \\u2192 7\\u003cbr\\u003eCount: 1\",\"D: Eval_# \\u2192 E: Eval_\\u002f\\u003cbr\\u003eTouchNumber: 6 \\u2192 7\\u003cbr\\u003eCount: 1\",\"B: Eval_- \\u2192 D: Eval_=\\u003cbr\\u003eTouchNumber: 5 \\u2192 6\\u003cbr\\u003eCount: 1\",\"E: Eval_# \\u2192 A: Eval_+\\u003cbr\\u003eTouchNumber: 10 \\u2192 11\\u003cbr\\u003eCount: 1\",\"E: Eval_# \\u2192 A: Eval_#\\u003cbr\\u003eTouchNumber: 8 \\u2192 9\\u003cbr\\u003eCount: 1\",\"E: Eval_# \\u2192 A: Eval_\\u002f\\u003cbr\\u003eTouchNumber: 6 \\u2192 7\\u003cbr\\u003eCount: 1\",\"D: Eval_- \\u2192 E: Eval_=\\u003cbr\\u003eTouchNumber: 8 \\u2192 9\\u003cbr\\u003eCount: 1\",\"D: Eval_- \\u2192 E: Eval_=\\u003cbr\\u003eTouchNumber: 5 \\u2192 6\\u003cbr\\u003eCount: 1\",\"D: Eval_\\u002f \\u2192 F: Eval_+\\u003cbr\\u003eTouchNumber: 6 \\u2192 7\\u003cbr\\u003eCount: 1\",\"D: Eval_\\u002f \\u2192 B: Eval_!\\u003cbr\\u003eTouchNumber: 9 \\u2192 10\\u003cbr\\u003eCount: 1\",\"E: Eval_# \\u2192 A: Eval_+\\u003cbr\\u003eTouchNumber: 7 \\u2192 8\\u003cbr\\u003eCount: 1\",\"E: Eval_# \\u2192 A: Eval_+\\u003cbr\\u003eTouchNumber: 6 \\u2192 7\\u003cbr\\u003eCount: 1\",\"D: Eval_- \\u2192 F: Eval_-\\u003cbr\\u003eTouchNumber: 5 \\u2192 6\\u003cbr\\u003eCount: 1\",\"D: Eval_\\u002f \\u2192 F: Eval_!\\u003cbr\\u003eTouchNumber: 5 \\u2192 6\\u003cbr\\u003eCount: 1\",\"E: Eval_\\u002f \\u2192 B: Eval_!\\u003cbr\\u003eTouchNumber: 7 \\u2192 8\\u003cbr\\u003eCount: 1\",\"F: Eval_- \\u2192 A: Eval_=\\u003cbr\\u003eTouchNumber: 7 \\u2192 8\\u003cbr\\u003eCount: 1\",\"F: Eval_- \\u2192 E: Eval_#\\u003cbr\\u003eTouchNumber: 7 \\u2192 8\\u003cbr\\u003eCount: 1\",\"F: Eval_+ \\u2192 E: Eval_#\\u003cbr\\u003eTouchNumber: 10 \\u2192 11\\u003cbr\\u003eCount: 1\",\"F: Eval_- \\u2192 E: Eval_#\\u003cbr\\u003eTouchNumber: 6 \\u2192 7\\u003cbr\\u003eCount: 1\",\"F: Eval_+ \\u2192 E: Eval_#\\u003cbr\\u003eTouchNumber: 7 \\u2192 8\\u003cbr\\u003eCount: 1\",\"F: Eval_! \\u2192 E: Eval_#\\u003cbr\\u003eTouchNumber: 6 \\u2192 7\\u003cbr\\u003eCount: 1\",\"F: Eval_# \\u2192 E: Eval_#\\u003cbr\\u003eTouchNumber: 7 \\u2192 8\\u003cbr\\u003eCount: 1\",\"F: Eval_+ \\u2192 E: Eval_#\\u003cbr\\u003eTouchNumber: 3 \\u2192 4\\u003cbr\\u003eCount: 1\",\"E: Eval_- \\u2192 F: Eval_#\\u003cbr\\u003eTouchNumber: 6 \\u2192 7\\u003cbr\\u003eCount: 1\",\"E: Eval_- \\u2192 F: Eval_-\\u003cbr\\u003eTouchNumber: 6 \\u2192 7\\u003cbr\\u003eCount: 1\",\"E: Eval_- \\u2192 A: Eval_-\\u003cbr\\u003eTouchNumber: 3 \\u2192 4\\u003cbr\\u003eCount: 1\",\"R: Eval_- \\u2192 A: Eval_#\\u003cbr\\u003eTouchNumber: 2 \\u2192 3\\u003cbr\\u003eCount: 1\",\"R: Eval_\\u002f \\u2192 A: Eval_\\u002f\\u003cbr\\u003eTouchNumber: 2 \\u2192 3\\u003cbr\\u003eCount: 1\",\"R: Eval_\\u002f \\u2192 A: Eval_!\\u003cbr\\u003eTouchNumber: 2 \\u2192 3\\u003cbr\\u003eCount: 1\",\"R: Eval_- \\u2192 E: Eval_-\\u003cbr\\u003eTouchNumber: 2 \\u2192 3\\u003cbr\\u003eCount: 1\",\"R: Eval_\\u002f \\u2192 F: Eval_+\\u003cbr\\u003eTouchNumber: 2 \\u2192 3\\u003cbr\\u003eCount: 1\"],\"hovertemplate\":\"%{customdata}\\u003cextra\\u003e\\u003c\\u002fextra\\u003e\",\"source\":{\"dtype\":\"i1\",\"bdata\":\"UAECAwQFBlAHAVJTUzthYQ1QOVBRUDpQEmBSYVBQUFNSUlBQUWA6Ox5hUFBQUFAeOUZTOmNSUV0CR1BTY1BhY2A7UlBgYFxSUVJSY2NjYDU6UzlTOTo7YWFhNVBQUD09XV1QUD1dQENDR0NHRUZHSUlJBB4eBB4=\"},\"target\":{\"dtype\":\"i1\",\"bdata\":\"UgJQBFBQBVNQB1xhO2FQUEo5X1I6ZmBRHlBZUFNjUmFZXFJROlBgYUZQU1FSOVNSX1A7YF1cOkdlUFE7PWZJNVBhWVNQQ1lcOlxcPV1dR11gYV87X2BhY2VAWWNSOWVlRzpjY0NFOmZQUFBQUFBQRkNTUjlRSUc=\"},\"value\":{\"dtype\":\"i1\",\"bdata\":\"RyknJyUiIiAgIB0REBAPDg0MDAsKCgoKCQkJCAgHBwYFBAQEBAQEAwMDAwMDAwMDAwMDAwMDAgICAgICAgICAgICAgIBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQE=\"}},\"node\":{\"customdata\":[\"Label: E: Eval_#\\u003cbr\\u003eTouchNumber: 3\\u003cbr\\u003eCount: 412\",\"Label: S: Eval_-\\u003cbr\\u003eTouchNumber: 1\\u003cbr\\u003eCount: 73\",\"Label: R: Eval_+\\u003cbr\\u003eTouchNumber: 2\\u003cbr\\u003eCount: 82\",\"Label: S: Eval_+\\u003cbr\\u003eTouchNumber: 1\\u003cbr\\u003eCount: 39\",\"Label: R: Eval_-\\u003cbr\\u003eTouchNumber: 2\\u003cbr\\u003eCount: 78\",\"Label: R: Eval_!\\u003cbr\\u003eTouchNumber: 2\\u003cbr\\u003eCount: 68\",\"Label: S: Eval_!\\u003cbr\\u003eTouchNumber: 1\\u003cbr\\u003eCount: 34\",\"Label: R: Eval_#\\u003cbr\\u003eTouchNumber: 2\\u003cbr\\u003eCount: 64\",\"Label: A: Eval_#\\u003cbr\\u003eTouchNumber: 4\\u003cbr\\u003eCount: 156\",\"Label: A: Eval_-\\u003cbr\\u003eTouchNumber: 4\\u003cbr\\u003eCount: 95\",\"Label: B: Eval_+\\u003cbr\\u003eTouchNumber: 5\\u003cbr\\u003eCount: 44\",\"Label: D: Eval_#\\u003cbr\\u003eTouchNumber: 5\\u003cbr\\u003eCount: 91\",\"Label: D: Eval_#\\u003cbr\\u003eTouchNumber: 6\\u003cbr\\u003eCount: 91\",\"Label: S: Eval_#\\u003cbr\\u003eTouchNumber: 1\\u003cbr\\u003eCount: 13\",\"Label: A: Eval_\\u002f\\u003cbr\\u003eTouchNumber: 4\\u003cbr\\u003eCount: 34\",\"Label: E: Eval_#\\u003cbr\\u003eTouchNumber: 7\\u003cbr\\u003eCount: 412\",\"Label: A: Eval_!\\u003cbr\\u003eTouchNumber: 4\\u003cbr\\u003eCount: 37\",\"Label: B: Eval_!\\u003cbr\\u003eTouchNumber: 5\\u003cbr\\u003eCount: 38\",\"Label: S: Eval_\\u002f\\u003cbr\\u003eTouchNumber: 1\\u003cbr\\u003eCount: 9\",\"Label: D: Eval_!\\u003cbr\\u003eTouchNumber: 6\\u003cbr\\u003eCount: 37\",\"Label: D: Eval_#\\u003cbr\\u003eTouchNumber: 9\\u003cbr\\u003eCount: 91\",\"Label: E: Eval_#\\u003cbr\\u003eTouchNumber: 6\\u003cbr\\u003eCount: 412\",\"Label: A: Eval_-\\u003cbr\\u003eTouchNumber: 8\\u003cbr\\u003eCount: 95\",\"Label: A: Eval_#\\u003cbr\\u003eTouchNumber: 7\\u003cbr\\u003eCount: 156\",\"Label: A: Eval_#\\u003cbr\\u003eTouchNumber: 8\\u003cbr\\u003eCount: 156\",\"Label: E: Eval_#\\u003cbr\\u003eTouchNumber: 4\\u003cbr\\u003eCount: 412\",\"Label: A: Eval_!\\u003cbr\\u003eTouchNumber: 8\\u003cbr\\u003eCount: 37\",\"Label: D: Eval_!\\u003cbr\\u003eTouchNumber: 10\\u003cbr\\u003eCount: 37\",\"Label: B: Eval_!\\u003cbr\\u003eTouchNumber: 9\\u003cbr\\u003eCount: 38\",\"Label: B: Eval_+\\u003cbr\\u003eTouchNumber: 8\\u003cbr\\u003eCount: 44\",\"Label: R: Eval_\\u002f\\u003cbr\\u003eTouchNumber: 2\\u003cbr\\u003eCount: 18\",\"Label: D: Eval_#\\u003cbr\\u003eTouchNumber: 10\\u003cbr\\u003eCount: 91\",\"Label: E: Eval_#\\u003cbr\\u003eTouchNumber: 10\\u003cbr\\u003eCount: 412\",\"Label: A: Eval_\\u002f\\u003cbr\\u003eTouchNumber: 8\\u003cbr\\u003eCount: 34\",\"Label: F: Eval_#\\u003cbr\\u003eTouchNumber: 3\\u003cbr\\u003eCount: 8\",\"Label: A: Eval_-\\u003cbr\\u003eTouchNumber: 7\\u003cbr\\u003eCount: 95\",\"Label: B: Eval_!\\u003cbr\\u003eTouchNumber: 8\\u003cbr\\u003eCount: 38\",\"Label: A: Eval_+\\u003cbr\\u003eTouchNumber: 4\\u003cbr\\u003eCount: 21\",\"Label: A: Eval_#\\u003cbr\\u003eTouchNumber: 5\\u003cbr\\u003eCount: 156\",\"Label: A: Eval_!\\u003cbr\\u003eTouchNumber: 7\\u003cbr\\u003eCount: 37\",\"Label: D: Eval_\\u002f\\u003cbr\\u003eTouchNumber: 5\\u003cbr\\u003eCount: 11\",\"Label: F: Eval_+\\u003cbr\\u003eTouchNumber: 6\\u003cbr\\u003eCount: 10\",\"Label: D: Eval_!\\u003cbr\\u003eTouchNumber: 9\\u003cbr\\u003eCount: 37\",\"Label: B: Eval_+\\u003cbr\\u003eTouchNumber: 9\\u003cbr\\u003eCount: 44\",\"Label: E: Eval_#\\u003cbr\\u003eTouchNumber: 8\\u003cbr\\u003eCount: 412\",\"Label: D: Eval_!\\u003cbr\\u003eTouchNumber: 5\\u003cbr\\u003eCount: 37\",\"Label: B: Eval_=\\u003cbr\\u003eTouchNumber: 5\\u003cbr\\u003eCount: 40\",\"Label: A: Eval_#\\u003cbr\\u003eTouchNumber: 9\\u003cbr\\u003eCount: 156\",\"Label: A: Eval_!\\u003cbr\\u003eTouchNumber: 3\\u003cbr\\u003eCount: 37\",\"Label: A: Eval_#\\u003cbr\\u003eTouchNumber: 3\\u003cbr\\u003eCount: 156\",\"Label: A: Eval_+\\u003cbr\\u003eTouchNumber: 7\\u003cbr\\u003eCount: 21\",\"Label: A: Eval_+\\u003cbr\\u003eTouchNumber: 10\\u003cbr\\u003eCount: 21\",\"Label: A: Eval_+\\u003cbr\\u003eTouchNumber: 8\\u003cbr\\u003eCount: 21\",\"Label: B: Eval_-\\u003cbr\\u003eTouchNumber: 5\\u003cbr\\u003eCount: 4\",\"Label: B: Eval_!\\u003cbr\\u003eTouchNumber: 4\\u003cbr\\u003eCount: 38\",\"Label: A: Eval_-\\u003cbr\\u003eTouchNumber: 9\\u003cbr\\u003eCount: 95\",\"Label: A: Eval_\\u002f\\u003cbr\\u003eTouchNumber: 3\\u003cbr\\u003eCount: 34\",\"Label: A: Eval_\\u002f\\u003cbr\\u003eTouchNumber: 7\\u003cbr\\u003eCount: 34\",\"Label: B: Eval_!\\u003cbr\\u003eTouchNumber: 10\\u003cbr\\u003eCount: 38\",\"Label: B: Eval_+\\u003cbr\\u003eTouchNumber: 10\\u003cbr\\u003eCount: 44\",\"Label: D: Eval_-\\u003cbr\\u003eTouchNumber: 8\\u003cbr\\u003eCount: 6\",\"Label: D: Eval_-\\u003cbr\\u003eTouchNumber: 5\\u003cbr\\u003eCount: 6\",\"Label: D: Eval_\\u002f\\u003cbr\\u003eTouchNumber: 6\\u003cbr\\u003eCount: 11\",\"Label: D: Eval_\\u002f\\u003cbr\\u003eTouchNumber: 9\\u003cbr\\u003eCount: 11\",\"Label: E: Eval_\\u002f\\u003cbr\\u003eTouchNumber: 7\\u003cbr\\u003eCount: 2\",\"Label: F: Eval_-\\u003cbr\\u003eTouchNumber: 7\\u003cbr\\u003eCount: 6\",\"Label: F: Eval_+\\u003cbr\\u003eTouchNumber: 10\\u003cbr\\u003eCount: 10\",\"Label: F: Eval_-\\u003cbr\\u003eTouchNumber: 6\\u003cbr\\u003eCount: 6\",\"Label: F: Eval_+\\u003cbr\\u003eTouchNumber: 7\\u003cbr\\u003eCount: 10\",\"Label: F: Eval_!\\u003cbr\\u003eTouchNumber: 6\\u003cbr\\u003eCount: 2\",\"Label: F: Eval_#\\u003cbr\\u003eTouchNumber: 7\\u003cbr\\u003eCount: 8\",\"Label: F: Eval_+\\u003cbr\\u003eTouchNumber: 3\\u003cbr\\u003eCount: 10\",\"Label: E: Eval_-\\u003cbr\\u003eTouchNumber: 6\\u003cbr\\u003eCount: 6\",\"Label: E: Eval_-\\u003cbr\\u003eTouchNumber: 3\\u003cbr\\u003eCount: 6\",\"Label: R: Eval_=\\u003cbr\\u003eTouchNumber: 2\\u003cbr\\u003eCount: 13\",\"Label: B: Eval_#\\u003cbr\\u003eTouchNumber: 5\\u003cbr\\u003eCount: 17\",\"Label: A: Eval_=\\u003cbr\\u003eTouchNumber: 4\\u003cbr\\u003eCount: 13\",\"Label: D: Eval_=\\u003cbr\\u003eTouchNumber: 5\\u003cbr\\u003eCount: 18\",\"Label: D: Eval_=\\u003cbr\\u003eTouchNumber: 8\\u003cbr\\u003eCount: 18\",\"Label: B: Eval_=\\u003cbr\\u003eTouchNumber: 9\\u003cbr\\u003eCount: 40\",\"Label: E: Eval_#\\u003cbr\\u003eTouchNumber: 11\\u003cbr\\u003eCount: 412\",\"Label: A: Eval_!\\u003cbr\\u003eTouchNumber: 11\\u003cbr\\u003eCount: 37\",\"Label: A: Eval_#\\u003cbr\\u003eTouchNumber: 11\\u003cbr\\u003eCount: 156\",\"Label: A: Eval_-\\u003cbr\\u003eTouchNumber: 11\\u003cbr\\u003eCount: 95\",\"Label: B: Eval_#\\u003cbr\\u003eTouchNumber: 9\\u003cbr\\u003eCount: 17\",\"Label: B: Eval_=\\u003cbr\\u003eTouchNumber: 6\\u003cbr\\u003eCount: 40\",\"Label: E: Eval_=\\u003cbr\\u003eTouchNumber: 3\\u003cbr\\u003eCount: 5\",\"Label: A: Eval_=\\u003cbr\\u003eTouchNumber: 7\\u003cbr\\u003eCount: 13\",\"Label: D: Eval_=\\u003cbr\\u003eTouchNumber: 9\\u003cbr\\u003eCount: 18\",\"Label: D: Eval_=\\u003cbr\\u003eTouchNumber: 6\\u003cbr\\u003eCount: 18\",\"Label: B: Eval_=\\u003cbr\\u003eTouchNumber: 10\\u003cbr\\u003eCount: 40\",\"Label: B: Eval_=\\u003cbr\\u003eTouchNumber: 4\\u003cbr\\u003eCount: 40\",\"Label: B: Eval_=\\u003cbr\\u003eTouchNumber: 8\\u003cbr\\u003eCount: 40\",\"Label: D: Eval_\\u002f\\u003cbr\\u003eTouchNumber: 11\\u003cbr\\u003eCount: 11\",\"Label: B: Eval_#\\u003cbr\\u003eTouchNumber: 4\\u003cbr\\u003eCount: 17\",\"Label: B: Eval_#\\u003cbr\\u003eTouchNumber: 8\\u003cbr\\u003eCount: 17\",\"Label: D: Eval_!\\u003cbr\\u003eTouchNumber: 11\\u003cbr\\u003eCount: 37\",\"Label: D: Eval_#\\u003cbr\\u003eTouchNumber: 11\\u003cbr\\u003eCount: 91\",\"Label: E: Eval_=\\u003cbr\\u003eTouchNumber: 7\\u003cbr\\u003eCount: 5\",\"Label: A: Eval_+\\u003cbr\\u003eTouchNumber: 11\\u003cbr\\u003eCount: 21\",\"Label: E: Eval_=\\u003cbr\\u003eTouchNumber: 9\\u003cbr\\u003eCount: 5\",\"Label: E: Eval_=\\u003cbr\\u003eTouchNumber: 6\\u003cbr\\u003eCount: 5\",\"Label: A: Eval_=\\u003cbr\\u003eTouchNumber: 8\\u003cbr\\u003eCount: 13\"],\"hovertemplate\":\"%{customdata}\\u003cextra\\u003e\\u003c\\u002fextra\\u003e\",\"label\":[\"E: Eval_#\",\"S: Eval_-\",\"R: Eval_+\",\"S: Eval_+\",\"R: Eval_-\",\"R: Eval_!\",\"S: Eval_!\",\"R: Eval_#\",\"A: Eval_#\",\"A: Eval_-\",\"B: Eval_+\",\"D: Eval_#\",\"D: Eval_#\",\"S: Eval_#\",\"A: Eval_\\u002f\",\"E: Eval_#\",\"A: Eval_!\",\"B: Eval_!\",\"S: Eval_\\u002f\",\"D: Eval_!\",\"D: Eval_#\",\"E: Eval_#\",\"A: Eval_-\",\"A: Eval_#\",\"A: Eval_#\",\"E: Eval_#\",\"A: Eval_!\",\"D: Eval_!\",\"B: Eval_!\",\"B: Eval_+\",\"R: Eval_\\u002f\",\"D: Eval_#\",\"E: Eval_#\",\"A: Eval_\\u002f\",\"F: Eval_#\",\"A: Eval_-\",\"B: Eval_!\",\"A: Eval_+\",\"A: Eval_#\",\"A: Eval_!\",\"D: Eval_\\u002f\",\"F: Eval_+\",\"D: Eval_!\",\"B: Eval_+\",\"E: Eval_#\",\"D: Eval_!\",\"B: Eval_=\",\"A: Eval_#\",\"A: Eval_!\",\"A: Eval_#\",\"A: Eval_+\",\"A: Eval_+\",\"A: Eval_+\",\"B: Eval_-\",\"B: Eval_!\",\"A: Eval_-\",\"A: Eval_\\u002f\",\"A: Eval_\\u002f\",\"B: Eval_!\",\"B: Eval_+\",\"D: Eval_-\",\"D: Eval_-\",\"D: Eval_\\u002f\",\"D: Eval_\\u002f\",\"E: Eval_\\u002f\",\"F: Eval_-\",\"F: Eval_+\",\"F: Eval_-\",\"F: Eval_+\",\"F: Eval_!\",\"F: Eval_#\",\"F: Eval_+\",\"E: Eval_-\",\"E: Eval_-\",\"R: Eval_=\",\"B: Eval_#\",\"A: Eval_=\",\"D: Eval_=\",\"D: Eval_=\",\"B: Eval_=\",\"E: Eval_#\",\"A: Eval_!\",\"A: Eval_#\",\"A: Eval_-\",\"B: Eval_#\",\"B: Eval_=\",\"E: Eval_=\",\"A: Eval_=\",\"D: Eval_=\",\"D: Eval_=\",\"B: Eval_=\",\"B: Eval_=\",\"B: Eval_=\",\"D: Eval_\\u002f\",\"B: Eval_#\",\"B: Eval_#\",\"D: Eval_!\",\"D: Eval_#\",\"E: Eval_=\",\"A: Eval_+\",\"E: Eval_=\",\"E: Eval_=\",\"A: Eval_=\"],\"line\":{\"color\":\"black\",\"width\":0.5},\"pad\":20,\"thickness\":20,\"x\":{\"dtype\":\"f8\",\"bdata\":\"dNFFF1100T9GF1100UW3P0YXXXTRRcc\\u002fRhdddNFFtz9GF1100UXHP0YXXXTRRcc\\u002fRhdddNFFtz9GF1100UXHP0YXXXTRRdc\\u002fRhdddNFF1z8XXXTRRRfdPxdddNFFF90\\u002fdNFFF1104T9GF1100UW3P0YXXXTRRdc\\u002fXXTRRRdd5D9GF1100UXXPxdddNFFF90\\u002fRhdddNFFtz900UUXXXThPy+66KKLLuo\\u002fdNFFF1104T9GF1100UXnP1100UUXXeQ\\u002fRhdddNFF5z9GF1100UXXP0YXXXTRRec\\u002fF1100UUX7T8vuuiiiy7qP0YXXXTRRec\\u002fRhdddNFFxz8XXXTRRRftPxdddNFFF+0\\u002fRhdddNFF5z900UUXXXTRP1100UUXXeQ\\u002fRhdddNFF5z9GF1100UXXPxdddNFFF90\\u002fXXTRRRdd5D8XXXTRRRfdP3TRRRdddOE\\u002fL7rooosu6j8vuuiiiy7qP0YXXXTRRec\\u002fF1100UUX3T8XXXTRRRfdPy+66KKLLuo\\u002fdNFFF1100T900UUXXXTRP1100UUXXeQ\\u002fF1100UUX7T9GF1100UXnPxdddNFFF90\\u002fRhdddNFF1z8vuuiiiy7qP3TRRRdddNE\\u002fXXTRRRdd5D8XXXTRRRftPxdddNFFF+0\\u002fRhdddNFF5z8XXXTRRRfdP3TRRRdddOE\\u002fL7rooosu6j9ddNFFF13kP1100UUXXeQ\\u002fF1100UUX7T900UUXXXThP1100UUXXeQ\\u002fdNFFF1104T9ddNFFF13kP3TRRRdddNE\\u002fdNFFF1104T900UUXXXTRP0YXXXTRRcc\\u002fF1100UUX3T9GF1100UXXPxdddNFFF90\\u002fRhdddNFF5z8vuuiiiy7qPwAAAAAAAPA\\u002fAAAAAAAA8D8AAAAAAADwPwAAAAAAAPA\\u002fL7rooosu6j900UUXXXThP3TRRRdddNE\\u002fXXTRRRdd5D8vuuiiiy7qP3TRRRdddOE\\u002fF1100UUX7T9GF1100UXXP0YXXXTRRec\\u002fAAAAAAAA8D9GF1100UXXP0YXXXTRRec\\u002fAAAAAAAA8D8AAAAAAADwP1100UUXXeQ\\u002fAAAAAAAA8D8vuuiiiy7qP3TRRRdddOE\\u002fRhdddNFF5z8=\"}},\"type\":\"sankey\"}],                        {\"template\":{\"data\":{\"histogram2dcontour\":[{\"type\":\"histogram2dcontour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"choropleth\":[{\"type\":\"choropleth\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"histogram2d\":[{\"type\":\"histogram2d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"heatmap\":[{\"type\":\"heatmap\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"contourcarpet\":[{\"type\":\"contourcarpet\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"contour\":[{\"type\":\"contour\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"surface\":[{\"type\":\"surface\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]}],\"mesh3d\":[{\"type\":\"mesh3d\",\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}],\"scatter\":[{\"fillpattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2},\"type\":\"scatter\"}],\"parcoords\":[{\"type\":\"parcoords\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolargl\":[{\"type\":\"scatterpolargl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"bar\":[{\"error_x\":{\"color\":\"#2a3f5f\"},\"error_y\":{\"color\":\"#2a3f5f\"},\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"scattergeo\":[{\"type\":\"scattergeo\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterpolar\":[{\"type\":\"scatterpolar\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"scattergl\":[{\"type\":\"scattergl\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatter3d\":[{\"type\":\"scatter3d\",\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermap\":[{\"type\":\"scattermap\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattermapbox\":[{\"type\":\"scattermapbox\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scatterternary\":[{\"type\":\"scatterternary\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"scattercarpet\":[{\"type\":\"scattercarpet\",\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}}}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"baxis\":{\"endlinecolor\":\"#2a3f5f\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"minorgridcolor\":\"white\",\"startlinecolor\":\"#2a3f5f\"},\"type\":\"carpet\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#EBF0F8\"},\"line\":{\"color\":\"white\"}},\"header\":{\"fill\":{\"color\":\"#C8D4E3\"},\"line\":{\"color\":\"white\"}},\"type\":\"table\"}],\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"#E5ECF6\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}]},\"layout\":{\"autotypenumbers\":\"strict\",\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#2a3f5f\"},\"hovermode\":\"closest\",\"hoverlabel\":{\"align\":\"left\"},\"paper_bgcolor\":\"white\",\"plot_bgcolor\":\"#E5ECF6\",\"polar\":{\"bgcolor\":\"#E5ECF6\",\"angularaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"radialaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"ternary\":{\"bgcolor\":\"#E5ECF6\",\"aaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"},\"caxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\"}},\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]]},\"xaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"yaxis\":{\"gridcolor\":\"white\",\"linecolor\":\"white\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"white\",\"automargin\":true,\"zerolinewidth\":2},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"yaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2},\"zaxis\":{\"backgroundcolor\":\"#E5ECF6\",\"gridcolor\":\"white\",\"linecolor\":\"white\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"white\",\"gridwidth\":2}},\"shapedefaults\":{\"line\":{\"color\":\"#2a3f5f\"}},\"annotationdefaults\":{\"arrowcolor\":\"#2a3f5f\",\"arrowhead\":0,\"arrowwidth\":1},\"geo\":{\"bgcolor\":\"white\",\"landcolor\":\"#E5ECF6\",\"subunitcolor\":\"white\",\"showland\":true,\"showlakes\":true,\"lakecolor\":\"white\"},\"title\":{\"x\":0.05},\"mapbox\":{\"style\":\"light\"}}},\"title\":{\"text\":\"Sankey ordinato per TouchNumber\"},\"font\":{\"size\":12}},                        {\"responsive\": true}                    ).then(function(){\n", "                            \n", "var gd = document.getElementById('29f46770-3cad-48e9-aa3d-69e23fe7ae78');\n", "var x = new MutationObserver(function (mutations, observer) {{\n", "        var display = window.getComputedStyle(gd).display;\n", "        if (!display || display === 'none') {{\n", "            console.log([gd, 'removed!']);\n", "            Plotly.purge(gd);\n", "            observer.disconnect();\n", "        }}\n", "}});\n", "\n", "// Listen for the removal of the full notebook cells\n", "var notebookContainer = gd.closest('#notebook-container');\n", "if (notebookContainer) {{\n", "    x.observe(<PERSON><PERSON><PERSON><PERSON>, {childList: true});\n", "}}\n", "\n", "// Listen for the clearing of the current output cell\n", "var outputEl = gd.closest('.output');\n", "if (outputEl) {{\n", "    x.observe(outputEl, {childList: true});\n", "}}\n", "\n", "                        })                };            </script>        </div>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import plotly.graph_objects as go\n", "\n", "# --- CONFIG ---\n", "touch_to_x = {i: i/11 for i in range(1, 12)}\n", "col_per_fond = {\"S\": \"Eval\", \"R\": \"Eva<PERSON>\", \"E\": \"Eval\", \"A\": \"Eval\", \"B\": \"Eval\", \"D\": \"Eval\", \"F\": \"Eval\"}\n", "\n", "# --- DATI BASE ---\n", "df = df_touch[\n", "    (df_touch[\"Foundamental\"].isin(col_per_fond.keys())) &\n", "    (df_touch[\"TouchNumber\"] <= 11)\n", "].copy()\n", "\n", "df[\"Label\"] = df.apply(\n", "    lambda row: f\"{row['Foundamental']}: {str(row[col_per_fond[row['Foundamental']]])[:10]}\", axis=1\n", ")\n", "\n", "# Next step\n", "df[\"next_label\"] = df.groupby([\"GameID\", \"SetNumber\", \"ActionNumber\"])[\"Label\"].shift(-1)\n", "df[\"next_touch\"] = df.groupby([\"GameID\", \"SetNumber\", \"ActionNumber\"])[\"TouchNumber\"].shift(-1)\n", "\n", "# Filtra transizioni valide\n", "df_trans = df.dropna(subset=[\"next_label\", \"next_touch\"])\n", "\n", "# Conta transizioni\n", "link_counts = df_trans.groupby([\"Label\", \"TouchNumber\", \"next_label\", \"next_touch\"]).size().reset_index(name=\"count\")\n", "link_counts = link_counts.sort_values(\"count\", ascending=False).head(150)\n", "\n", "# --- CREAZIONE NODI UNIFICATA ---\n", "labels_src = link_counts[[\"Label\", \"TouchNumber\"]].rename(columns={\"Label\": \"Label\", \"TouchNumber\": \"TouchNumber\"})\n", "labels_tgt = link_counts[[\"next_label\", \"next_touch\"]].rename(columns={\"next_label\": \"Label\", \"next_touch\": \"TouchNumber\"})\n", "all_nodes_df = pd.concat([labels_src, labels_tgt], ignore_index=True).drop_duplicates()\n", "\n", "# Conta osservazioni per ogni nodo\n", "obs_counts_src = link_counts.groupby(\"Label\")[\"count\"].sum()\n", "obs_counts_tgt = link_counts.groupby(\"next_label\")[\"count\"].sum()\n", "obs_total = obs_counts_src.add(obs_counts_tgt, fill_value=0)\n", "\n", "# Costruisci nodes_df\n", "all_nodes_df[\"Count\"] = all_nodes_df[\"Label\"].map(obs_total)\n", "all_nodes_df[\"x\"] = all_nodes_df[\"TouchNumber\"].map(touch_to_x).fillna(0.99)\n", "all_nodes_df[\"id\"] = range(len(all_nodes_df))\n", "\n", "# Mappa da Label → ID\n", "label2id = dict(zip(all_nodes_df[\"Label\"], all_nodes_df[\"id\"]))\n", "\n", "# Customdata nodi\n", "all_nodes_df[\"hover\"] = all_nodes_df.apply(\n", "    lambda r: f\"Label: {r['Label']}<br>TouchNumber: {int(r['TouchNumber'])}<br>Count: {int(r['Count'])}\", axis=1\n", ")\n", "\n", "# --- PREPARA LINK ---\n", "link_sources = link_counts[\"Label\"].map(label2id)\n", "link_targets = link_counts[\"next_label\"].map(label2id)\n", "link_values = link_counts[\"count\"]\n", "link_hover = link_counts.apply(\n", "    lambda r: f\"{r['Label']} → {r['next_label']}<br>TouchNumber: {int(r['TouchNumber'])} → {int(r['next_touch'])}<br>Count: {r['count']}\",\n", "    axis=1\n", ")\n", "\n", "# --- PLOT ---\n", "fig = go.Figure(data=[go.Sankey(\n", "    arrangement=\"snap\",\n", "    node=dict(\n", "        pad=20,\n", "        thickness=20,\n", "        line=dict(color=\"black\", width=0.5),\n", "        label=all_nodes_df[\"Label\"],\n", "        x=all_nodes_df[\"x\"],\n", "        hovertemplate=\"%{customdata}<extra></extra>\",\n", "        customdata=all_nodes_df[\"hover\"]\n", "    ),\n", "    link=dict(\n", "        source=link_sources,\n", "        target=link_targets,\n", "        value=link_values,\n", "        customdata=link_hover,\n", "        hovertemplate=\"%{customdata}<extra></extra>\"\n", "    )\n", ")])\n", "\n", "fig.update_layout(title_text=\"Sankey ordinato per TouchNumber\", font_size=12)\n", "fig.show()\n"]}, {"cell_type": "code", "execution_count": 37, "id": "925c5087", "metadata": {}, "outputs": [], "source": ["con.close()"]}], "metadata": {"kernelspec": {"display_name": ".venv (3.13.2)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}