import numpy as np 
import pandas as pd  #Realizzato con la versione di Pandas 2.2.3
import os
import duckdb
import psycopg
from sqlalchemy import create_engine, text

import plotly.graph_objects as go
from dash import Dash, dcc, html, Input, Output

# Connessione a DuckDB
#con = duckdb.connect('db_modena_1.duckdb')

# Installazione dell'estensione postgres se non è già installata
#con.execute("INSTALL postgres")
#con.execute("LOAD postgres")

conn = psycopg.connect(
    dbname="db_modena",           # database creato in pgAdmin4
    user="postgres",              # Il tuo nome utente PostgreSQL
    password="AcquaLevissima1",   # La password che hai scelto per 'postgres'
    host="localhost",             # 'localhost' se è sul tuo PC
    port=5432                     # La porta predefinita è 5432
)

cur = conn.cursor() 

engine = create_engine('postgresql+psycopg://postgres:AcquaLevissima1@localhost:5432/db_modena')

# Connessione DuckDB e caricamento iniziale
df_attacks_raw = pd.read_sql_query("""
SELECT
    "GameID",
    "whichTeamIDreal",
    "NumeroMaglia_ID",
    "Eval",
    'End_' || "EndZoneEsecZone" AS "EndZone"
FROM rilevations_libero_view
WHERE "Foundamental" = 'A'  -- Azioni di attacco
  AND "EndZoneEsecZone" IS NOT NULL
""", engine)

# Mi preparo ad arricchire il menu a tendina GameID
games_info = pd.read_sql_query("""
SELECT 
    g."GameID", 
    g."HomeTeamName", 
    g."VisitorTeamName", 
    g."Date"
FROM games_view g
WHERE g."GameID" IN (SELECT DISTINCT "GameID" FROM rilevations_libero_view)
ORDER BY g."DateSQL"  --Li ordino, così saranno così anche nel menu a tendina
""", engine)

# Mi preparo ad arricchire il menu a tendina whichTeamID
teams_info = pd.read_sql_query("""
SELECT 
    t."TeamID", 
    t."TeamName"
FROM teams t
WHERE t."TeamID" IN (SELECT DISTINCT "whichTeamIDreal" FROM rilevations_libero_view)
ORDER BY t."TeamID"  --Li ordino, così saranno così anche nel menu a tendina
""", engine)

# Mi preparo ad arricchire il menu a tendina NumeroMaglia_ID
players_info = pd.read_sql_query("""
SELECT DISTINCT
    p."PlayerID",
    p."Nome",
    p."Cognome"
FROM players_each_game p
WHERE p."PlayerID" IN (SELECT DISTINCT "NumeroMaglia_ID" FROM rilevations_libero_view)
ORDER BY p."Cognome", p."Nome"  -- Ordino per cognome e nome
""", engine)



# Nel nostro campo le zone sono:
# [4 3 2]
# [7 8 9]
# [5 6 1]

# Nel campo avversario le zone sono:
# [1 6 5]
# [9 8 7]
# [2 3 4]

our_zone_coords = {
    '4': (0, 2), '3': (1, 2), '2': (2, 2),
    '7': (0, 1), '8': (1, 1), '9': (2, 1),
    '5': (0, 0), '6': (1, 0), '1': (2, 0)
}

opponent_zone_coords = {
    '1': (0, 2), '6': (1, 2), '5': (2, 2),
    '9': (0, 1), '8': (1, 1), '7': (2, 1),
    '2': (0, 0), '3': (1, 0), '4': (2, 0)
}

#opzioni del menu a tendina per GameID
game_options = [
    {'label': f"{row['HomeTeamName']} - {row['VisitorTeamName']} ({row['Date']}) (GameID {g})",   #quello che c'è scritto nel menu a tendina
    'value': g}                                                                                  #il suo valore, quello che conta veramente
    for g, row in games_info.set_index('GameID').iterrows() 
    if g in df_attacks_raw['GameID'].unique()
]
# Se non ci sono corrispondenze tra il GameID di rilevations e quello in games, mostra solo il GameID
if not game_options:
    game_options = [{'label': g, 'value': g} for g in sorted(df_attacks_raw['GameID'].unique())]
    

#opzioni del menu a tendina per whichTeamIDreal
team_options = [
    {'label': f"{row['TeamName']} ({t})",                  #quello che c'è scritto nel menu a tendina
    'value': t}                                           #il suo valore, quello che conta veramente
    for t, row in teams_info.set_index('TeamID').iterrows()   #per ogni TeamID nella tabella teams_duckdb
    if t in df_attacks_raw['whichTeamIDreal'].unique()        #se quel TeamID è presente in df_attacks_raw['whichTeamIDreal']
]
# Se non ci sono corrispondenze tra il GameID di rilevations e quello in games, mostra solo il GameID
if not team_options:
    team_options = [{'label': t, 'value': t} for t in sorted(df_attacks_raw['whichTeamIDreal'].unique())]


#opzioni del menu a tendina per NumeroMaglia_ID
player_options = [
    {'label': f"{row['Cognome']} {row['Nome']} ({p})",                  #quello che c'è scritto nel menu a tendina
    'value': p}                                           #il suo valore, quello che conta veramente
    for p, row in players_info.set_index('PlayerID').iterrows()   #per ogni TeamID nella tabella teams_duckdb
    if p in df_attacks_raw['NumeroMaglia_ID'].unique()        #se quel TeamID è presente in df_attacks_raw['whichTeamIDreal']
]
# Se non ci sono corrispondenze tra il GameID di rilevations e quello in games, mostra solo il GameID
if not player_options:
    player_options = [{'label': p, 'value': p} for p in sorted(df_attacks_raw['NumeroMaglia_ID'].unique())]
    




# App Dash
app = Dash(__name__)
app.title = "Attack Heatmap"

app.layout = html.Div([
    html.H2("Heatmap Attacchi - Campo Avversario e Proprio Campo"),
    html.Div([
        dcc.Dropdown(id='game-dropdown', options=game_options, placeholder='Seleziona GameID', multi=True),  #mettendo multi=True lo rendo a scelta multipla
        dcc.Dropdown(id='team-dropdown', options=team_options, placeholder='Seleziona TeamID', multi=True),
        dcc.Dropdown(id='player-dropdown', options=player_options, placeholder='Seleziona PlayerID', multi=True)
    ], style={'display': 'flex', 'gap': '10px', 'margin-bottom': '20px'}),
    dcc.Graph(id='heatmap-court')
])






#Inizio a costruire il grafico
@app.callback(
    Output('heatmap-court', 'figure'),
    Input('game-dropdown', 'value'),
    Input('team-dropdown', 'value'),
    Input('player-dropdown', 'value')
)

def update_heatmap(gameid, teamid, playerid):
    # Filtra dati
    df = df_attacks_raw.copy()
    if gameid:
        df = df[df['GameID'].isin(gameid)]  #se il menu è a scelta multipla, gameid è una lista con gli oggetti selezionati, quindi non posso mettere == gameid ma isin(gameid)
    if teamid:
        df = df[df['whichTeamIDreal'].isin(teamid)]
    if playerid:
        df = df[df['NumeroMaglia_ID'].isin(playerid)]

    # Pulisce la zona (rimuove 'End_')
    df['Zone'] = df['EndZone'].str.replace('End_', '')
    
    # Calcola statistiche per zona e valutazione
    zone_stats = {}
    eval_types = ['#', '+', '!', '-', '/', '=']
    
    for zone in opponent_zone_coords.keys():
        zone_data = df[df['Zone'] == zone]
        total_attacks = len(zone_data)
        
        eval_counts = {}
        for eval_type in eval_types:
            eval_counts[eval_type] = len(zone_data[zone_data['Eval'] == eval_type])
        
        zone_stats[zone] = {
            'total': total_attacks,
            'eval_counts': eval_counts
        }


    
    # Trova il massimo per la scala dei colori
    max_count = max([stats['total'] for stats in zone_stats.values()]) if zone_stats else 1

    # Inizializza la figura
    fig = go.Figure()

    # Funzione per creare hover text
    def create_hover_text(zone):
        stats = zone_stats[zone]
        if stats['total'] == 0:
            return f"Zona {zone}<br>Nessun attacco"
        
        hover_lines = [f"Zona {zone}<br>Totale attacchi: {stats['total']}<br>"]
        for eval_type in eval_types:
            count = stats['eval_counts'][eval_type]
            if count > 0:
                hover_lines.append(f"{count} attacchi {eval_type}")  #scritta quando fai hover
        
        return "<br>".join(hover_lines)

    # Disegna il campo avversario con heatmap
    for zone, (col, row) in opponent_zone_coords.items():
        total_attacks = zone_stats[zone]['total']
        intensity = total_attacks / max_count if max_count else 0
        
        # Colore: rosso con trasparenza basata sull'intensità
        color = f'rgba(255,0,0,{0.1 + 0.9 * intensity})'
        
        # Crea hover text
        hover_text = create_hover_text(zone)
        
        # Aggiunge un scatter point invisibile per l'hover
        fig.add_trace(go.Scatter(
            x=[col + 0.5],
            y=[row + 0.5],
            mode='markers',
            marker=dict(size=50, color='rgba(0,0,0,0)'),  # Marker invisibile
            hovertemplate=hover_text + '<extra></extra>',
            showlegend=False,
            name=f'Zona {zone}'
        ))
        
        # Disegna rettangolo
        fig.add_shape(type='rect',
            x0=col, y0=row,
            x1=col + 1, y1=row + 1,
            fillcolor=color,
            line=dict(width=1, color='black')
        )
        
        # Testo count
        fig.add_annotation(
            x=col + 0.5, y=row + 0.5,
            text=str(total_attacks),
            showarrow=False,
            font=dict(color='white' if intensity > 0.5 else 'black', size=12)
        )

    # Disegna il proprio campo (senza heatmap)
    y_offset = 3
    for zone, (col, row) in our_zone_coords.items():
        color = 'rgba(200,200,200,0.1)'
        fig.add_shape(type='rect',
            x0=col, y0=row - y_offset,
            x1=col + 1, y1=row + 1 - y_offset,
            fillcolor=color,
            line=dict(width=1, color='black')
        )
        
        # Etichetta zona
        fig.add_annotation(
            x=col + 0.5, y=row + 0.5 - y_offset,
            text=zone,
            showarrow=False,
            font=dict(color='gray', size=10)
        )

    # Aggiunge la rete (linea spessa tra i due campi)
    fig.add_shape(type='line',
        x0=-0.1, y0=0,
        x1=3.1, y1=0,
        line=dict(width=8, color='black')
    )
    
    # Aggiunge titoli dei campi
    fig.add_annotation(
        x=-0.3, y=1.5,
        text="Campo Difensori",
        showarrow=False,
        textangle=-90,
        xanchor='center',  #indica che le coordinate di x sono rispetto al punto centrale di x del grafico
        yanchor='middle',  #indica che le coordinate di y sono rispetto al punto centrale di y del grafico
        font=dict(size=14, color='black', weight='bold')
    )
    
    fig.add_annotation(
        x=-0.3, y=-1.5,
        text="Campo Attaccanti",
        showarrow=False,
        textangle=-90,
        xanchor='center',
        yanchor='middle',
        font=dict(size=14, color='black', weight='bold')
    )

    # Configurazione layout
    fig.update_xaxes(showgrid=False, zeroline=False, visible=False, range=[-0.1, 3.1])
    fig.update_yaxes(showgrid=False, zeroline=False, visible=False, range=[-3.1, 3.5], scaleanchor="x", scaleratio=1)

    fig.update_layout(
        width=500,
        height=800,
        title="Heatmap Attacchi",
        margin=dict(l=20, r=20, t=50, b=20),
        hovermode='closest'
    )
    
    return fig

if __name__ == '__main__':
    app.run(debug=True, port=8053)

#definisco come vengono mostrati i testi in ogni menu a tendina
def testi_nei_menu(df):

    #opzioni del menu a tendina per GameID
    game_options = [
        {'label': f"{row['HomeTeamName']} - {row['VisitorTeamName']} ({row['Date']}) (GameID {g})",   #quello che c'è scritto nel menu a tendina
        'value': g}                                                                                  #il suo valore, quello che conta veramente
        for g, row in games_info.set_index('GameID').iterrows() 
        if g in df['GameID'].unique()
    ]
    # Se non ci sono corrispondenze tra il GameID di rilevations e quello in games, mostra solo il GameID
    if not game_options:
        game_options = [{'label': g, 'value': g} for g in sorted(df['GameID'].unique())]
        

    #opzioni del menu a tendina per whichTeamIDreal
    team_options = [
        {'label': f"{row['TeamName']} ({t})",                  #quello che c'è scritto nel menu a tendina
        'value': t}                                           #il suo valore, quello che conta veramente
        for t, row in teams_info.set_index('TeamID').iterrows()   #per ogni TeamID nella tabella teams_duckdb
        if t in df['whichTeamIDreal'].unique()        #se quel TeamID è presente in df_attacks_raw['whichTeamIDreal']
    ]
    # Se non ci sono corrispondenze tra il GameID di rilevations e quello in games, mostra solo il GameID
    if not team_options:
        team_options = [{'label': t, 'value': t} for t in sorted(df['whichTeamIDreal'].unique())]


    #opzioni del menu a tendina per NumeroMaglia_ID
    player_options = [
        {'label': f"{row['Cognome']} {row['Nome']} ({p})",                  #quello che c'è scritto nel menu a tendina
        'value': p}                                           #il suo valore, quello che conta veramente
        for p, row in players_info.set_index('PlayerID').iterrows()   #per ogni TeamID nella tabella teams_duckdb
        if p in df['NumeroMaglia_ID'].unique()        #se quel TeamID è presente in df_attacks_raw['whichTeamIDreal']
    ]
    # Se non ci sono corrispondenze tra il GameID di rilevations e quello in games, mostra solo il GameID
    if not player_options:
        player_options = [{'label': p, 'value': p} for p in sorted(df['NumeroMaglia_ID'].unique())]
        
    return game_options, team_options, player_options

game_options, team_options, player_options = testi_nei_menu(df_attacks_raw)


# App Dash
app = Dash(__name__)
app.title = "Attack Heatmap"

app.layout = html.Div([
    html.H2("Heatmap Attacchi - Campo Avversario e Proprio Campo"),
    html.Div([
        dcc.Dropdown(id='game-dropdown', options=game_options, placeholder='Seleziona GameID', multi=True),  #mettendo multi=True lo rendo a scelta multipla
        dcc.Dropdown(id='team-dropdown', options=team_options, placeholder='Seleziona TeamID', multi=True),
        dcc.Dropdown(id='player-dropdown', options=player_options, placeholder='Seleziona PlayerID', multi=True)
    ], style={'display': 'flex', 'gap': '10px', 'margin-bottom': '20px'}),
    dcc.Graph(id='heatmap-court')
])




#Adesso faccio in modo che le opzioni dei menu a tendina siano coerenti/compatibili con le selezioni già fatte. Ovvero evito di mostrare opzioni incompatibili con le selezioni già fatte
@app.callback(
    Output('game-dropdown', 'options'),
    Output('team-dropdown', 'options'),
    Output('player-dropdown', 'options'),
    Input('game-dropdown', 'value'),
    Input('team-dropdown', 'value'),
    Input('player-dropdown', 'value')
)
def update_dropdowns(selected_games, selected_teams, selected_players):
    df = df_attacks_raw

    # Filtra SOLO sugli altri dropdown, NON sul valore corrente
    #if selected_games:
    #    df = df[df['GameID'].isin(selected_games)]
    if selected_teams:
        df = df[df['whichTeamIDreal'].isin(selected_teams)]
    if selected_players:
        df = df[df['NumeroMaglia_ID'].isin(selected_players)]

    #Per aggiornare quali testi mostrare (delle opzioni che mostro), chiamo la funzione che ho definito prima, che mi dice come mostrare i testi
    game_options, team_options, player_options = testi_nei_menu(df)
    return game_options, team_options, player_options




#Inizio a costruire il grafico
@app.callback(
    Output('heatmap-court', 'figure'),
    Input('game-dropdown', 'value'),
    Input('team-dropdown', 'value'),
    Input('player-dropdown', 'value')
)

def update_heatmap(gameid, teamid, playerid):
    # Filtra dati
    df = df_attacks_raw.copy()
    if gameid:
        df = df[df['GameID'].isin(gameid)]  #se il menu è a scelta multipla, gameid è una lista con gli oggetti selezionati, quindi non posso mettere == gameid ma isin(gameid)
    if teamid:
        df = df[df['whichTeamIDreal'].isin(teamid)]
    if playerid:
        df = df[df['NumeroMaglia_ID'].isin(playerid)]

    # Pulisce la zona (rimuove 'End_')
    df['Zone'] = df['EndZone'].str.replace('End_', '')
    
    # Calcola statistiche per zona e valutazione
    zone_stats = {}
    eval_types = ['#', '+', '!', '-', '/', '=']
    
    for zone in opponent_zone_coords.keys():
        zone_data = df[df['Zone'] == zone]
        total_attacks = len(zone_data)
        
        eval_counts = {}
        for eval_type in eval_types:
            eval_counts[eval_type] = len(zone_data[zone_data['Eval'] == eval_type])
        
        zone_stats[zone] = {
            'total': total_attacks,
            'eval_counts': eval_counts
        }


    
    # Trova il massimo per la scala dei colori
    max_count = max([stats['total'] for stats in zone_stats.values()]) if zone_stats else 1

    # Inizializza la figura
    fig = go.Figure()

    # Funzione per creare hover text
    def create_hover_text(zone):
        stats = zone_stats[zone]
        if stats['total'] == 0:
            return f"Zona {zone}<br>Nessun attacco"
        
        hover_lines = [f"Zona {zone}<br>Totale attacchi: {stats['total']}<br>"]
        for eval_type in eval_types:
            count = stats['eval_counts'][eval_type]
            if count > 0:
                hover_lines.append(f"{count} attacchi {eval_type}")  #scritta quando fai hover
        
        return "<br>".join(hover_lines)

    # Disegna il campo avversario con heatmap
    for zone, (col, row) in opponent_zone_coords.items():
        total_attacks = zone_stats[zone]['total']
        intensity = total_attacks / max_count if max_count else 0
        
        # Colore: rosso con trasparenza basata sull'intensità
        color = f'rgba(255,0,0,{0.1 + 0.9 * intensity})'
        
        # Crea hover text
        hover_text = create_hover_text(zone)
        
        # Aggiunge un scatter point invisibile per l'hover
        fig.add_trace(go.Scatter(
            x=[col + 0.5],
            y=[row + 0.5],
            mode='markers',
            marker=dict(size=50, color='rgba(0,0,0,0)'),  # Marker invisibile
            hovertemplate=hover_text + '<extra></extra>',
            showlegend=False,
            name=f'Zona {zone}'
        ))
        
        # Disegna rettangolo
        fig.add_shape(type='rect',
            x0=col, y0=row,
            x1=col + 1, y1=row + 1,
            fillcolor=color,
            line=dict(width=1, color='black')
        )
        
        # Testo count
        fig.add_annotation(
            x=col + 0.5, y=row + 0.5,
            text=str(total_attacks),
            showarrow=False,
            font=dict(color='white' if intensity > 0.5 else 'black', size=12)
        )

    # Disegna il proprio campo (senza heatmap)
    y_offset = 3
    for zone, (col, row) in our_zone_coords.items():
        color = 'rgba(200,200,200,0.1)'
        fig.add_shape(type='rect',
            x0=col, y0=row - y_offset,
            x1=col + 1, y1=row + 1 - y_offset,
            fillcolor=color,
            line=dict(width=1, color='black')
        )
        
        # Etichetta zona
        fig.add_annotation(
            x=col + 0.5, y=row + 0.5 - y_offset,
            text=zone,
            showarrow=False,
            font=dict(color='gray', size=10)
        )

    # Aggiunge la rete (linea spessa tra i due campi)
    fig.add_shape(type='line',
        x0=-0.1, y0=0,
        x1=3.1, y1=0,
        line=dict(width=8, color='black')
    )
    
    # Aggiunge titoli dei campi
    fig.add_annotation(
        x=-0.3, y=1.5,
        text="Campo Difensori",
        showarrow=False,
        textangle=-90,
        xanchor='center',  #indica che le coordinate di x sono rispetto al punto centrale di x del grafico
        yanchor='middle',  #indica che le coordinate di y sono rispetto al punto centrale di y del grafico
        font=dict(size=14, color='black', weight='bold')
    )
    
    fig.add_annotation(
        x=-0.3, y=-1.5,
        text="Campo Attaccanti",
        showarrow=False,
        textangle=-90,
        xanchor='center',
        yanchor='middle',
        font=dict(size=14, color='black', weight='bold')
    )

    # Configurazione layout
    fig.update_xaxes(showgrid=False, zeroline=False, visible=False, range=[-0.1, 3.1])
    fig.update_yaxes(showgrid=False, zeroline=False, visible=False, range=[-3.1, 3.5], scaleanchor="x", scaleratio=1)

    fig.update_layout(
        width=500,
        height=800,
        title="Heatmap Attacchi",
        margin=dict(l=20, r=20, t=50, b=20),
        hovermode='closest'
    )
    
    return fig

if __name__ == "__main__":
    app.run(debug=False, port=8053)

game_options, team_options, player_options = testi_nei_menu(df_attacks_raw)


# App Dash
app = Dash(__name__)
app.title = "Attack Heatmap"

app.layout = html.Div([
    html.H2("Heatmap Attacchi - Campo Avversario e Proprio Campo"),
    html.Div([
        dcc.Dropdown(id='game-dropdown', options=game_options, placeholder='Seleziona GameID', multi=True),  #mettendo multi=True lo rendo a scelta multipla
        dcc.Dropdown(id='team-dropdown', options=team_options, placeholder='Seleziona TeamID', multi=True),
        dcc.Dropdown(id='player-dropdown', options=player_options, placeholder='Seleziona PlayerID', multi=True)
    ], style={'display': 'flex', 'gap': '10px', 'margin-bottom': '20px'}),
    dcc.Graph(id='heatmap-court')
])




#Adesso faccio in modo che le opzioni dei menu a tendina siano coerenti/compatibili con le selezioni già fatte. Ovvero evito di mostrare opzioni incompatibili con le selezioni già fatte
@app.callback(
    Output('game-dropdown', 'options'),
    Output('team-dropdown', 'options'),
    Output('player-dropdown', 'options'),
    Input('game-dropdown', 'value'),
    Input('team-dropdown', 'value'),
    Input('player-dropdown', 'value')
)
def update_dropdowns(selected_games, selected_teams, selected_players):
    df = df_attacks_raw

    # Filtra SOLO sugli altri dropdown, NON sul valore corrente
    #if selected_games:
    #    df = df[df['GameID'].isin(selected_games)]
    if selected_teams:
        df = df[df['whichTeamIDreal'].isin(selected_teams)]
    if selected_players:
        df = df[df['NumeroMaglia_ID'].isin(selected_players)]

    #Per aggiornare quali testi mostrare (delle opzioni che mostro), chiamo la funzione che ho definito prima, che mi dice come mostrare i testi
    game_options, team_options, player_options = testi_nei_menu(df)
    return game_options, team_options, player_options




#Inizio a costruire il grafico
@app.callback(
    Output('heatmap-court', 'figure'),
    Input('game-dropdown', 'value'),
    Input('team-dropdown', 'value'),
    Input('player-dropdown', 'value')
)

def update_heatmap(gameid, teamid, playerid):
    # Filtra dati
    df = df_attacks_raw.copy()
    if gameid:
        df = df[df['GameID'].isin(gameid)]
    if teamid:
        df = df[df['whichTeamIDreal'].isin(teamid)]
    if playerid:
        df = df[df['NumeroMaglia_ID'].isin(playerid)]

    # Pulisce la zona (rimuove 'End_')
    df['Zone'] = df['EndZone'].str.replace('End_', '')
    
    # Calcola statistiche per zona e valutazione
    zone_stats = {}
    eval_types = ['#', '+', '!', '-', '/', '=']
    eval_colors = {
        '#': 'darkgreen',   # Perfetto - verde scuro
        '+': 'limegreen',   # Positivo - verde chiaro
        '!': 'blue',        # Coperto - blu
        '-': 'orange',      # Scadente - arancione
        '/': 'red',         # Murato - rosso
        '=': 'darkred'      # Errore - rosso scuro
    }
    
    for zone in opponent_zone_coords.keys():
        zone_data = df[df['Zone'] == zone]
        total_attacks = len(zone_data)
        
        eval_counts = {}
        for eval_type in eval_types:
            eval_counts[eval_type] = len(zone_data[zone_data['Eval'] == eval_type])
        
        zone_stats[zone] = {
            'total': total_attacks,
            'eval_counts': eval_counts
        }

    # Trova il massimo per la scala delle barre
    max_eval_count = 0
    for stats in zone_stats.values():
        for count in stats['eval_counts'].values():
            if count > max_eval_count:
                max_eval_count = count
    
    if max_eval_count == 0:
        max_eval_count = 1

    # Inizializza la figura
    fig = go.Figure()

    # Disegna il campo avversario con barchart
    for zone, (col, row) in opponent_zone_coords.items():
        stats = zone_stats[zone]
        totale_attacchi_zona = stats['total']
        
        # Disegna il bordo della cella
        fig.add_shape(type='rect',
            x0=col, y0=row,
            x1=col + 1, y1=row + 1,
            fillcolor='rgba(240,240,240,0.3)',  #bordo nero
            line=dict(width=1.5, color='black')
        )
        
        # Se ci sono attacchi in questa zona, disegna le barre
        if stats['total'] > 0:
            # Calcola la larghezza di ogni barra (dividiamo la cella in 6 parti per le 6 valutazioni)
            bar_width = 0.9 / len(eval_types)  # Lasciamo un po' di margine
            start_x = col + 0.05  # Margine sinistro
            
            bar_index = 0
            for eval_type in eval_types:
                count = stats['eval_counts'][eval_type]
                if count > 0:
                    # Altezza della barra proporzionale al count
                    bar_height = (count / max_eval_count) * 0.9  # Massimo 70% dell'altezza della cella
                    
                    # Posizione x della barra
                    bar_x = start_x + bar_index * bar_width
                    
                    # Disegna la barra come rettangolo
                    fig.add_shape(type='rect',
                        x0=bar_x, y0=row + 0.01,  # y0 è il margine inferiore, lo spazio dal pavimento della cella
                        x1=bar_x + bar_width * 0.95, y1=row + bar_height,
                        fillcolor=eval_colors[eval_type],
                        line=dict(width=1, color='black'),
                        opacity=0.8
                    )
                    
                    # Aggiungo dei puntini invisibili dentro ogni barra. Su questi posso fare hover e mostrare quello che voglio
                    fig.add_trace(go.Scatter(
                        x=[bar_x + bar_width * 0.4],
                        y=[row + bar_height/2],
                        mode='markers',
                        marker=dict(size=1.1, color='rgba(0,0,0,0)'),
                        hovertemplate=f"Zona {zone}<br>{count} attacchi con Eval {eval_type}<extra></extra>",
                        showlegend=False,
                        name=f'Zona_{zone}_Eval_{eval_type}'
                    ))
                    
                    # Etichetta del count sopra la barra (solo se la barra non è alta al massimo e se il count è minore di 100, quindi a due cifre)
                    if bar_height < 1 and count<100:
                        fig.add_annotation(
                            x=bar_x + bar_width * 0.4,  #coordinata x
                            y=row + bar_height + 0.05,  #coordinata y
                            text=str(count),
                            showarrow=False,
                            font=dict(color='black', size=8)
                        )
                
                bar_index += 1
        
        # Etichetta della zona 
        fig.add_annotation(
            x=col + 0.5, y=row + 0.8,
            text=f"{zone}",
            showarrow=False,
            font=dict(color='lightgray', size=18)
        )

        # Aggiungo dei puntini invisibili sopra il numero della zona. Su questi posso fare hover e mostrare quello che voglio
        fig.add_trace(go.Scatter(
            x=[col + 0.5],
            y=[row + 0.8],
            mode='markers',
            marker=dict(size=1, color='rgba(0,0,0,0)'),
            hovertemplate=f"{totale_attacchi_zona} attacchi in zona {zone}",
            showlegend=False,
            name=""   #Aggiunge una scritta in più che non mi serve a niente. È come il titolo dell'etichetta hover
        ))
        
        # Totale attacchi (in alto a destra)
        '''
        fig.add_annotation(
            x=col + 0.9, y=row + 0.9,
            text=f"Tot: {stats['total']}",
            showarrow=False,
            font=dict(color='black', size=8)
        )
        '''

    # Disegna il proprio campo (senza barchart)
    y_offset = 3
    for zone, (col, row) in our_zone_coords.items():
        color = 'rgba(200,200,200,0.1)'
        fig.add_shape(type='rect',
            x0=col, y0=row - y_offset,
            x1=col + 1, y1=row + 1 - y_offset,
            fillcolor=color,
            line=dict(width=1, color='black')
        )
        
        # Etichetta zona
        fig.add_annotation(
            x=col + 0.5, y=row + 0.5 - y_offset,
            text=zone,
            showarrow=False,
            font=dict(color='lightgray', size=18)
        )

    # Aggiunge la rete (linea spessa tra i due campi)
    fig.add_shape(type='line',
        x0=-0.1, y0=-0.01,
        x1=3.1, y1=-0.01,
        line=dict(width=5, color='black')
    )
    
    # Aggiunge titoli dei campi
    fig.add_annotation(
        x=-0.3, y=1.5,
        text="Campo Difensori",
        showarrow=False,
        textangle=-90,
        xanchor='center',
        yanchor='middle',
        font=dict(size=14, color='black', weight='bold')
    )
    
    fig.add_annotation(
        x=-0.3, y=-1.5,
        text="Campo Attaccanti",
        showarrow=False,
        textangle=-90,
        xanchor='center',
        yanchor='middle',
        font=dict(size=14, color='black', weight='bold')
    )

    # Aggiungi una legenda per i colori delle valutazioni
    '''
    legend_y = 3.2
    for i, (eval_type, color) in enumerate(eval_colors.items()):
        fig.add_annotation(
            x=0.5 * i, y=legend_y,
            text=f"{eval_type}",
            showarrow=False,
            font=dict(color=color, size=12, weight='bold'),
            bgcolor='white',
            bordercolor=color,
            borderwidth=1
        )
    '''

    # Configurazione layout
    fig.update_xaxes(showgrid=False, zeroline=False, visible=False, range=[-0.5, 3.5])
    fig.update_yaxes(showgrid=False, zeroline=False, visible=False, range=[-3.5, 3.8], scaleanchor="x", scaleratio=1)

    fig.update_layout(
        width=600,
        height=900,
        title="Heatmap Attacchi con Distribuzione Valutazioni",
        margin=dict(l=20, r=20, t=50, b=20),
        hovermode='closest'
    )
    
    return fig

if __name__ == "__main__":
    app.run(debug=False, port=8053)







# Carica i dati una volta sola
df_touch = pd.read_sql_query("""
    SELECT 
        "GameID",
        "SetNumber",
        "ActionNumber",
        "TouchNumber",
        "Foundamental",
        "AbsNumeroPossesso",
        "RilevationNumber",
        "whichTeamID",
        "NumeroMaglia_ID",
        'Type_' || "Type" AS "Type",
        'Eval_' || "Eval" AS "Eval",
        'Call_' || "SetterCall" AS "SetterCall",
        'Targ_' || "TargAttk" AS "TargAttk",
        'Start_' || "StartZone" AS "StartZone",
        'StartC_' || "StartZoneCompact" AS "StartZoneCompact",
        'Comb_' || "AttkCombination" AS "AttkCombination",
        'End_' || "EndZoneEsecZone" AS "EndZoneEsecZone",
        'Endd_' || "EndZoneEsecZone3aree" AS "EndZoneEsecZone3aree",
        'EndSub_' || "EndSubzoneEsecSubzone" AS "EndSubzoneEsecSubzone",
        'Skill_' || "SkillType" AS "SkillType",
        'Players_' || "PlayersInfo" AS PlayersInfo,
        'Special_' || "Special" AS "Special",
        'ccc_' || "correctCustomChar" AS "correctCustomChar",
        'Punto_' || "EndedInPoint" AS "EndedInPoint",
        'Ruolo_' || "RuoloCalc" AS "RuoloCalc"
    FROM rilevations_libero_view
    WHERE "Foundamental" IN ('S', 'R', 'E', 'A', 'B', 'D', 'F') AND "GameID"=11
    """, engine)

#Salvo df_touch come csv
df_touch.to_csv('df_touch.csv', index=False)

import dash
from dash import dcc, html, Input, Output, callback
import plotly.graph_objects as go
from collections import defaultdict

# Configurazione colonne per fondamentali
col_per_fond = {
    "S": "Eval", 
    "R": "Eval", 
    "E": "Eval", 
    "A": "Eval", 
    "B": "Eval", 
    "D": "Eval", 
    "F": "Eval"
}

# Dati di esempio per il volleyball
sample_data = [
    # Azione 1: S+ -> R- -> E# -> A#
    {'ActionId': 1, 'TouchNumber': 1, 'Foundamental': 'S', 'Eval': '+', 'Player': 'P1'},
    {'ActionId': 1, 'TouchNumber': 2, 'Foundamental': 'R', 'Eval': '-', 'Player': 'P2'},
    {'ActionId': 1, 'TouchNumber': 3, 'Foundamental': 'E', 'Eval': '#', 'Player': 'P3'},
    {'ActionId': 1, 'TouchNumber': 4, 'Foundamental': 'A', 'Eval': '#', 'Player': 'P4'},
    
    # Azione 2: S! -> R+ -> A-
    {'ActionId': 2, 'TouchNumber': 1, 'Foundamental': 'S', 'Eval': '!', 'Player': 'P5'},
    {'ActionId': 2, 'TouchNumber': 2, 'Foundamental': 'R', 'Eval': '+', 'Player': 'P6'},
    {'ActionId': 2, 'TouchNumber': 3, 'Foundamental': 'A', 'Eval': '-', 'Player': 'P7'},
    
    # Azione 3: S! -> R+ -> E+ -> A#
    {'ActionId': 3, 'TouchNumber': 1, 'Foundamental': 'S', 'Eval': '!', 'Player': 'P8'},
    {'ActionId': 3, 'TouchNumber': 2, 'Foundamental': 'R', 'Eval': '+', 'Player': 'P9'},
    {'ActionId': 3, 'TouchNumber': 3, 'Foundamental': 'E', 'Eval': '+', 'Player': 'P10'},
    {'ActionId': 3, 'TouchNumber': 4, 'Foundamental': 'A', 'Eval': '#', 'Player': 'P11'},
    
    # Azione 4: S+ -> R# -> B!
    {'ActionId': 4, 'TouchNumber': 1, 'Foundamental': 'S', 'Eval': '+', 'Player': 'P12'},
    {'ActionId': 4, 'TouchNumber': 2, 'Foundamental': 'R', 'Eval': '#', 'Player': 'P13'},
    {'ActionId': 4, 'TouchNumber': 3, 'Foundamental': 'B', 'Eval': '!', 'Player': 'P14'},
    
    # Azione 5: S- -> R+ -> E- -> A+ -> B#
    {'ActionId': 5, 'TouchNumber': 1, 'Foundamental': 'S', 'Eval': '-', 'Player': 'P15'},
    {'ActionId': 5, 'TouchNumber': 2, 'Foundamental': 'R', 'Eval': '+', 'Player': 'P16'},
    {'ActionId': 5, 'TouchNumber': 3, 'Foundamental': 'E', 'Eval': '-', 'Player': 'P17'},
    {'ActionId': 5, 'TouchNumber': 4, 'Foundamental': 'A', 'Eval': '+', 'Player': 'P18'},
    {'ActionId': 5, 'TouchNumber': 5, 'Foundamental': 'B', 'Eval': '#', 'Player': 'P19'},
    
    # Azione 6: S! -> R- -> E= -> A+
    {'ActionId': 6, 'TouchNumber': 1, 'Foundamental': 'S', 'Eval': '!', 'Player': 'P20'},
    {'ActionId': 6, 'TouchNumber': 2, 'Foundamental': 'R', 'Eval': '-', 'Player': 'P21'},
    {'ActionId': 6, 'TouchNumber': 3, 'Foundamental': 'E', 'Eval': '=', 'Player': 'P22'},
    {'ActionId': 6, 'TouchNumber': 4, 'Foundamental': 'A', 'Eval': '+', 'Player': 'P23'},
]

def create_node_id(touch_number, foundamental, characteristic):
    """Crea un ID unico per ogni nodo"""
    return f"T{touch_number}_{foundamental}_{characteristic}"

def get_color_by_eval(eval_val):
    """Mappa le valutazioni ai colori"""
    colors = {
        '#': 'rgb(255, 68, 68)',    # Errore - rosso
        '!': 'rgb(255, 136, 0)',    # Male - arancione
        '-': 'rgb(255, 221, 0)',    # Insufficiente - giallo
        '+': 'rgb(136, 255, 136)',  # Buono - verde chiaro
        '++': 'rgb(0, 221, 0)',     # Ottimo - verde
        '=': 'rgb(136, 136, 136)'   # Neutro - grigio
    }
    return colors.get(eval_val, 'rgb(204, 204, 204)')

def process_sankey_data(data, max_touch_number=11):
    """Processa i dati per creare il diagramma Sankey"""
    df = pd.DataFrame(data)
    
    # Filtra tocchi fino a TouchNumber specificato
    df = df[df['TouchNumber'] <= max_touch_number]
    
    # Dizionari per tracciare nodi e collegamenti
    nodes = {}
    links = defaultdict(int)
    
    # Raggruppa per azione
    for action_id, action_data in df.groupby('ActionId'):
        action_touches = action_data.sort_values('TouchNumber')
        
        # Crea nodi per ogni tocco dell'azione
        for _, touch in action_touches.iterrows():
            characteristic = touch[col_per_fond[touch['Foundamental']]]
            node_id = create_node_id(touch['TouchNumber'], touch['Foundamental'], characteristic)
            
            if node_id not in nodes:
                nodes[node_id] = {
                    'id': node_id,
                    'touch_number': touch['TouchNumber'],
                    'foundamental': touch['Foundamental'],
                    'characteristic': characteristic,
                    'count': 0,
                    'label': f"{touch['Foundamental']}{characteristic}",
                    'color': get_color_by_eval(characteristic)
                }
            nodes[node_id]['count'] += 1
        
        # Crea collegamenti tra tocchi consecutivi
        touches_list = action_touches.to_dict('records')
        for i in range(len(touches_list) - 1):
            current_touch = touches_list[i]
            next_touch = touches_list[i + 1]
            
            source_char = current_touch[col_per_fond[current_touch['Foundamental']]]
            target_char = next_touch[col_per_fond[next_touch['Foundamental']]]
            
            source_id = create_node_id(current_touch['TouchNumber'], current_touch['Foundamental'], source_char)
            target_id = create_node_id(next_touch['TouchNumber'], next_touch['Foundamental'], target_char)
            
            link_key = (source_id, target_id)
            links[link_key] += 1
    
    return nodes, links

def create_sankey_figure(nodes, links):
    """Crea il diagramma Sankey usando Plotly"""
    
    # Converti nodi in liste ordinate
    node_list = list(nodes.values())
    node_list.sort(key=lambda x: (x['touch_number'], x['foundamental'], x['characteristic']))
    
    # Crea mapping da node_id a indice
    node_id_to_index = {node['id']: i for i, node in enumerate(node_list)}
    
    # Prepara dati per Plotly Sankey
    node_labels = [f"{node['label']}<br>({node['count']})" for node in node_list]
    node_colors = [node['color'] for node in node_list]
    
    # Calcola posizioni X basate su TouchNumber per allineamento colonne
    touch_numbers = sorted(set(node['touch_number'] for node in node_list))
    x_positions = []
    y_positions = []
    
    for node in node_list:
        # Posizione X basata su TouchNumber
        x_pos = (node['touch_number'] - 1) / (len(touch_numbers) - 1) if len(touch_numbers) > 1 else 0.5
        x_positions.append(x_pos)
        
        # Posizione Y distribuita uniformemente per ogni gruppo di TouchNumber
        nodes_same_touch = [n for n in node_list if n['touch_number'] == node['touch_number']]
        node_index_in_touch = nodes_same_touch.index(node)
        y_pos = (node_index_in_touch + 0.5) / len(nodes_same_touch) if len(nodes_same_touch) > 1 else 0.5
        y_positions.append(y_pos)
    
    # Prepara collegamenti
    source_indices = []
    target_indices = []
    values = []
    
    for (source_id, target_id), value in links.items():
        if source_id in node_id_to_index and target_id in node_id_to_index:
            source_indices.append(node_id_to_index[source_id])
            target_indices.append(node_id_to_index[target_id])
            values.append(value)
    
    # Crea il diagramma Sankey
    fig = go.Figure(data=[go.Sankey(
        node=dict(
            pad=15,
            thickness=20,
            line=dict(color="black", width=0.5),
            label=node_labels,
            color=node_colors,
            x=x_positions,
            y=y_positions
        ),
        link=dict(
            source=source_indices,
            target=target_indices,
            value=values,
            color='rgba(128, 128, 128, 0.4)'
        )
    )])
    
    fig.update_layout(
        title={
            'text': "Diagramma Sankey - Tocchi Pallavolo",
            'font': {'size': 20}
        },
        font_size=12,
        height=700,
        margin=dict(t=80, b=40, l=40, r=40)
    )
    
    return fig

# Inizializza l'app Dash
app = dash.Dash(__name__)

# Layout dell'app
app.layout = html.Div([
    html.H1("Analisi Tocchi Pallavolo - Diagramma Sankey", 
            style={'textAlign': 'center', 'marginBottom': 30}),
    
    html.Div([
        html.H3("Configurazione"),
        html.P("Massimo TouchNumber da visualizzare:"),
        dcc.Slider(
            id='max-touch-slider',
            min=3,
            max=11,
            step=1,
            value=11,
            marks={i: str(i) for i in range(3, 12)},
            tooltip={"placement": "bottom", "always_visible": True}
        ),
    ], style={'margin': '20px', 'padding': '20px', 'border': '1px solid #ddd', 'borderRadius': '5px'}),
    
    html.Div([
        html.H3("Legenda Colori"),
        html.Div([
            html.Span("# Errore", style={'backgroundColor': 'rgb(255, 68, 68)', 'padding': '5px', 'margin': '5px', 'borderRadius': '3px'}),
            html.Span("! Male", style={'backgroundColor': 'rgb(255, 136, 0)', 'padding': '5px', 'margin': '5px', 'borderRadius': '3px'}),
            html.Span("- Insufficiente", style={'backgroundColor': 'rgb(255, 221, 0)', 'padding': '5px', 'margin': '5px', 'borderRadius': '3px'}),
            html.Span("+ Buono", style={'backgroundColor': 'rgb(136, 255, 136)', 'padding': '5px', 'margin': '5px', 'borderRadius': '3px'}),
            html.Span("= Neutro", style={'backgroundColor': 'rgb(136, 136, 136)', 'padding': '5px', 'margin': '5px', 'borderRadius': '3px'}),
        ], style={'display': 'flex', 'flexWrap': 'wrap'})
    ], style={'margin': '20px', 'padding': '20px', 'border': '1px solid #ddd', 'borderRadius': '5px'}),
    
    dcc.Graph(id='sankey-diagram'),
    
    html.Div([
        html.H3("Come interpretare il diagramma:"),
        html.Ul([
            html.Li("Ogni colonna verticale rappresenta un TouchNumber (1, 2, 3, etc.)"),
            html.Li("I nodi mostrano Fondamentale + Valutazione (es. S+, R-, E#)"),
            html.Li("Il numero tra parentesi indica quante volte quel nodo appare nei dati"),
            html.Li("Lo spessore delle connessioni indica la frequenza del flusso tra i nodi"),
            html.Li("I tocchi sono raggruppati per azione, mantenendo i flussi separati quando necessario"),
            html.Li("Le azioni con lo stesso nodo iniziale possono condividere percorsi comuni"),
        ])
    ], style={'margin': '20px', 'padding': '20px', 'border': '1px solid #ddd', 'borderRadius': '5px'}),
    
    html.Div([
        html.H3("Dati di Esempio Utilizzati:"),
        html.P("• Azione 1: S+ → R- → E# → A#"),
        html.P("• Azione 2: S! → R+ → A-"),
        html.P("• Azione 3: S! → R+ → E+ → A# (nota: unita con Azione 2 per S! e R+)"),
        html.P("• Azione 4: S+ → R# → B! (nota: unita con Azione 1 per S+)"),
        html.P("• Azione 5: S- → R+ → E- → A+ → B#"),
        html.P("• Azione 6: S! → R- → E= → A+"),
    ], style={'margin': '20px', 'padding': '20px', 'border': '1px solid #ddd', 'borderRadius': '5px'})
])

@app.callback(
    Output('sankey-diagram', 'figure'),
    Input('max-touch-slider', 'value')
)
def update_sankey(max_touch):
    """Callback per aggiornare il diagramma quando cambia il slider"""
    nodes, links = process_sankey_data(sample_data, max_touch)
    return create_sankey_figure(nodes, links)

if __name__ == '__main__':
    app.run(debug=True, port=8053)

import dash
from dash import dcc, html, Input, Output, callback, dash_table
import plotly.graph_objects as go
import pandas as pd
import numpy as np
from collections import defaultdict

# CONFIGURAZIONE - Modifica qui per cambiare le caratteristiche visualizzate
col_per_fond = {
    "S": "Eval", 
    "R": "Eval", 
    "E": "Eval", 
    "A": "Eval", 
    "B": "Eval", 
    "D": "Eval", 
    "F": "Eval"
}

# Funzione per caricare i dati - INSERISCI QUI IL TUO DATAFRAME
def load_volleyball_data():    
    df_touch = con.execute("""
        SELECT 
            "GameID",
            "SetNumber",
            "ActionNumber",
            "TouchNumber",
            "Foundamental",
            "AbsNumeroPossesso",
            "RilevationNumber",
            "whichTeamID",
            "NumeroMaglia_ID",
            'Type_' || "Type" AS "Type",
            'Eval_' || "Eval" AS "Eval",
            'Call_' || "SetterCall" AS "SetterCall",
            'Targ_' || "TargAttk" AS "TargAttk",
            'Start_' || "StartZone" AS "StartZone",
            'StartC_' || "StartZoneCompact" AS "StartZoneCompact",
            'Comb_' || "AttkCombination" AS "AttkCombination",
            'End_' || "EndZoneEsecZone" AS "EndZoneEsecZone",
            'Endd_' || "EndZoneEsecZone3aree" AS "EndZoneEsecZone3aree",
            'EndSub_' || "EndSubzoneEsecSubzone" AS "EndSubzoneEsecSubzone",
            'Skill_' || "SkillType" AS "SkillType",
            'Players_' || "PlayersInfo" AS PlayersInfo,
            'Special_' || "Special" AS "Special",
            'ccc_' || "correctCustomChar" AS "correctCustomChar",
            'Punto_' || "EndedInPoint" AS "EndedInPoint",
            'Ruolo_' || "RuoloCalc" AS "RuoloCalc"
        FROM rilevations_libero_view
        WHERE "Foundamental" IN ('S', 'R', 'E', 'A', 'B', 'D', 'F') AND "GameID"=21
        """).df()
    
    return df_touch


def create_node_id(touch_number, foundamental, characteristic):
    """Crea un ID unico per ogni nodo"""
    return f"T{touch_number}_{foundamental}_{characteristic}"

def extract_eval_from_string(eval_string):
    """Estrae la valutazione dalla stringa 'Eval_X' -> 'X'"""
    if pd.isna(eval_string) or eval_string == '':
        return '='
    if isinstance(eval_string, str) and eval_string.startswith('Eval_'):
        return eval_string.replace('Eval_', '')
    return str(eval_string)

def get_color_by_eval(eval_val):
    """Ritorna None per lasciare che Plotly gestisca i colori automaticamente"""
    return None

def process_sankey_data(df, max_touch_number=11, game_id=None, set_number=None):
    """Processa i dati per creare il diagramma Sankey"""
    
    # Filtra i dati se specificato
    if game_id is not None:
        df = df[df['GameID'] == game_id]
    if set_number is not None:
        df = df[df['SetNumber'] == set_number]
    
    # Filtra tocchi fino a TouchNumber specificato
    df = df[df['TouchNumber'] <= max_touch_number]
    
    if df.empty:
        return {}, {}
    
    # Dizionari per tracciare nodi e collegamenti
    nodes = {}
    links = defaultdict(int)
    
    # Raggruppa per azione
    for action_id, action_data in df.groupby('ActionNumber'):
        action_touches = action_data.sort_values('TouchNumber')

        # DEBUG: Verifica TouchNumber sequenziali
        prev_touch = -1
        for i, t in enumerate(action_touches['TouchNumber']):
            if t < prev_touch:
                print(f"⚠️ ATTENZIONE: TouchNumber fuori ordine nell’azione {action_id} (indice {i})")
            prev_touch = t
        
        
        # Crea nodi per ogni tocco dell'azione
        for _, touch in action_touches.iterrows():
            # Ottieni la caratteristica specificata per questo fondamentale
            characteristic_col = col_per_fond.get(touch['Foundamental'], 'Eval')
            
            if characteristic_col in touch and pd.notna(touch[characteristic_col]):
                characteristic = touch[characteristic_col]
            else:
                characteristic = '='  # Valore di default
            
            # Pulisce la caratteristica se è nel formato 'Prefix_Value'
            if isinstance(characteristic, str) and '_' in characteristic:
                characteristic = characteristic.split('_', 1)[-1]
            
            node_id = create_node_id(touch['TouchNumber'], touch['Foundamental'], characteristic)
            
            if node_id not in nodes:
                nodes[node_id] = {
                    'id': node_id,
                    'touch_number': touch['TouchNumber'],
                    'foundamental': touch['Foundamental'],
                    'characteristic': characteristic,
                    'count': 0,
                    'label': f"{touch['Foundamental']}{characteristic}"
                }
            nodes[node_id]['count'] += 1
        
        # Crea collegamenti tra tocchi consecutivi
        touches_list = action_touches.to_dict('records')
        for i in range(len(touches_list) - 1):
            current_touch = touches_list[i]
            next_touch = touches_list[i + 1]

            # Salta se i due tocchi hanno lo stesso TouchNumber
            if current_touch["TouchNumber"] == next_touch["TouchNumber"]:
                continue
            
            # Ottieni caratteristiche
            current_char_col = col_per_fond.get(current_touch['Foundamental'], 'Eval')
            next_char_col = col_per_fond.get(next_touch['Foundamental'], 'Eval')
            
            current_char = current_touch.get(current_char_col, '=')
            next_char = next_touch.get(next_char_col, '=')
            
            # Pulisce le caratteristiche
            if isinstance(current_char, str) and '_' in current_char:
                current_char = current_char.split('_', 1)[-1]
            if isinstance(next_char, str) and '_' in next_char:
                next_char = next_char.split('_', 1)[-1]
            
            source_id = create_node_id(current_touch['TouchNumber'], current_touch['Foundamental'], current_char)
            target_id = create_node_id(next_touch['TouchNumber'], next_touch['Foundamental'], next_char)
            
            link_key = (source_id, target_id)
            links[link_key] += 1
    
    return nodes, links

def create_sankey_figure(nodes, links):
    """Crea il diagramma Sankey usando Plotly"""
    
    if not nodes or not links:
        # Crea un grafico vuoto se non ci sono dati
        fig = go.Figure()
        fig.add_annotation(
            text="Nessun dato disponibile per i filtri selezionati",
            xref="paper", yref="paper",
            x=0.5, y=0.5, showarrow=False,
            font=dict(size=16)
        )
        fig.update_layout(height=400)
        return fig
    
    # Converti nodi in liste ordinate
    node_list = list(nodes.values())
    node_list.sort(key=lambda x: (x['touch_number'], x['foundamental'], x['characteristic']))
    
    # Crea mapping da node_id a indice
    node_id_to_index = {node['id']: i for i, node in enumerate(node_list)}
    
    # Prepara dati per Plotly Sankey
    node_labels = [f"{node['label']}<br>({node['count']})" for node in node_list]
    
    # Calcola posizioni per evitare sovrapposizioni con nodi più piccoli
    touch_numbers = sorted(set(node['touch_number'] for node in node_list))
    x_positions = []
    y_positions = []
    
    # Raggruppa nodi per TouchNumber per migliore distribuzione verticale
    nodes_by_touch = {}
    for node in node_list:
        touch_num = node['touch_number']
        if touch_num not in nodes_by_touch:
            nodes_by_touch[touch_num] = []
        nodes_by_touch[touch_num].append(node)
    
    # Calcola posizioni per ogni gruppo di TouchNumber
    for node in node_list:
        touch_num = node['touch_number']
        
        # Posizione X basata su TouchNumber con più spazio
        if len(touch_numbers) > 1:
            x_pos = (touch_num - min(touch_numbers)) / (max(touch_numbers) - min(touch_numbers))
        else:
            x_pos = 0.5
        x_positions.append(x_pos)
        
        # Posizione Y con distribuzione molto più spaziata
        nodes_same_touch = nodes_by_touch[touch_num]
        nodes_same_touch.sort(key=lambda x: (x['foundamental'], x['characteristic']))
        
        node_index_in_touch = nodes_same_touch.index(node)
        num_nodes_in_touch = len(nodes_same_touch)
        
        # Distribuzione con molto più spazio tra i nodi
        if num_nodes_in_touch == 1:
            y_pos = 0.5
        else:
            # Usa margini più grandi e distribuzione più spaziata
            margin = 0.05
            available_space = 1.0 - 2 * margin
            y_pos = margin + (node_index_in_touch * available_space) / (num_nodes_in_touch - 1)
        
        y_positions.append(y_pos)
    
    # Prepara collegamenti
    source_indices = []
    target_indices = []
    values = []
    
    for (source_id, target_id), value in links.items():
        if source_id in node_id_to_index and target_id in node_id_to_index:
            source_indices.append(node_id_to_index[source_id])
            target_indices.append(node_id_to_index[target_id])
            values.append(value)
    
    # Prepara hover text personalizzato
    node_hover_text = []
    for node in node_list:
        hover = (
            f"Nodo: {node['label']}<br>"
            f"TouchNumber: {node['touch_number']}<br>"
            f"Foundamental: {node['foundamental']}<br>"
            f"Characteristic: {node['characteristic']}<br>"
            f"Osservazioni: {node['count']}"
        )
        node_hover_text.append(hover)
    
    link_hover_text = []
    for i, ((source_id, target_id), value) in enumerate(links.items()):
        if source_id in node_id_to_index and target_id in node_id_to_index:
            source_node = nodes[source_id]
            target_node = nodes[target_id]
            hover = (
                f"Flusso: {source_node['label']} → {target_node['label']}<br>"
                f"Da TouchNumber: {source_node['touch_number']}<br>"
                f"A TouchNumber: {target_node['touch_number']}<br>"
                f"Osservazioni: {value}"
            )
            link_hover_text.append(hover)
    
    # Crea il diagramma Sankey con nodi più piccoli e hover semplificato
    fig = go.Figure(data=[go.Sankey(
        node=dict(
            pad=30,  # Più padding per evitare sovrapposizioni
            thickness=15,  # Nodi più sottili
            line=dict(color="black", width=0.5),
            label=node_labels,
            x=x_positions,
            y=y_positions,
            customdata=node_hover_text,
            hovertemplate='%{customdata}<extra></extra>'
        ),
        link=dict(
            source=source_indices,
            target=target_indices,
            value=values,
            customdata=link_hover_text,
            hovertemplate='%{customdata}<extra></extra>'
        )
    )])
    
    fig.update_layout(
        title={
            'text': "Diagramma Sankey - Tocchi Pallavolo",
            'font': {'size': 20}
        },
        font_size=10,  # Font più piccolo per risparmiare spazio
        height=900,  # Più altezza per distribuire meglio i nodi
        margin=dict(t=80, b=40, l=40, r=40)
    )
    
    return fig

# Carica i dati
df_touch = load_volleyball_data()

# Inizializza l'app Dash
app = dash.Dash(__name__)

# Layout dell'app
app.layout = html.Div([
    html.H1("Analisi Tocchi Pallavolo - Diagramma Sankey", 
            style={'textAlign': 'center', 'marginBottom': 30}),
    
    html.Div([
        html.H3("Configurazione Filtri"),
        html.Div([
            html.Div([
                html.Label("Game ID:"),
                dcc.Dropdown(
                    id='game-id-dropdown',
                    options=[{'label': f'Game {gid}', 'value': gid} for gid in sorted(df_touch['GameID'].unique())],
                    value=df_touch['GameID'].iloc[0] if not df_touch.empty else None,
                    style={'width': '150px'}
                ),
            ], style={'display': 'inline-block', 'marginRight': '20px'}),
            
            html.Div([
                html.Label("Set Number:"),
                dcc.Dropdown(
                    id='set-number-dropdown',
                    style={'width': '150px'}
                ),
            ], style={'display': 'inline-block', 'marginRight': '20px'}),
            
            html.Div([
                html.Label("Max TouchNumber:"),
                dcc.Slider(
                    id='max-touch-slider',
                    min=3,
                    max=11,
                    step=1,
                    value=11,
                    marks={i: str(i) for i in range(3, 12)},
                    tooltip={"placement": "bottom", "always_visible": True}
                ),
            ], style={'display': 'inline-block', 'width': '200px'}),
        ], style={'display': 'flex', 'alignItems': 'center', 'gap': '20px'}),
    ], style={'margin': '20px', 'padding': '20px', 'border': '1px solid #ddd', 'borderRadius': '5px'}),
    
    html.Div([
        html.H3("Configurazione Caratteristiche per Fondamentale"),
        html.P("Attualmente configurato per mostrare 'Eval' per tutti i fondamentali. Modifica col_per_fond nel codice per personalizzare."),
        html.Div([
            html.Span(f"{fond}: {char}", style={'margin': '5px', 'padding': '5px', 'backgroundColor': '#f0f0f0', 'borderRadius': '3px'})
            for fond, char in col_per_fond.items()
        ], style={'display': 'flex', 'flexWrap': 'wrap'})
    ], style={'margin': '20px', 'padding': '20px', 'border': '1px solid #ddd', 'borderRadius': '5px'}),
    
    dcc.Graph(id='sankey-diagram'),
    
    html.Div([
        html.H3("Statistiche Dati"),
        html.Div(id='data-stats')
    ], style={'margin': '20px', 'padding': '20px', 'border': '1px solid #ddd', 'borderRadius': '5px'}),
    
    html.Div([
        html.H3("Come interpretare il diagramma:"),
        html.Ul([
            html.Li("Ogni colonna verticale rappresenta un TouchNumber (1, 2, 3, etc.)"),
            html.Li("I nodi mostrano Fondamentale + Caratteristica configurata (es. S+, R-, E#)"),
            html.Li("Il numero tra parentesi indica quante volte quel nodo appare nei dati"),
            html.Li("Lo spessore delle connessioni indica la frequenza del flusso tra i nodi"),
            html.Li("I tocchi sono raggruppati per ActionNumber, mantenendo i flussi separati quando necessario"),
            html.Li("Le azioni con lo stesso nodo iniziale possono condividere percorsi comuni"),
        ])
    ], style={'margin': '20px', 'padding': '20px', 'border': '1px solid #ddd', 'borderRadius': '5px'})
])

@app.callback(
    Output('set-number-dropdown', 'options'),
    Output('set-number-dropdown', 'value'),
    Input('game-id-dropdown', 'value')
)
def update_set_options(game_id):
    """Aggiorna le opzioni del Set Number basate su Game ID selezionato"""
    if game_id is None:
        return [], None
    
    filtered_df = df_touch[df_touch['GameID'] == game_id]
    set_options = [{'label': f'Set {sn}', 'value': sn} for sn in sorted(filtered_df['SetNumber'].unique())]
    default_set = filtered_df['SetNumber'].iloc[0] if not filtered_df.empty else None
    
    return set_options, default_set

@app.callback(
    Output('sankey-diagram', 'figure'),
    Output('data-stats', 'children'),
    Input('game-id-dropdown', 'value'),
    Input('set-number-dropdown', 'value'),
    Input('max-touch-slider', 'value')
)
def update_sankey(game_id, set_number, max_touch):
    """Callback per aggiornare il diagramma quando cambiano i filtri"""
    nodes, links = process_sankey_data(df_touch, max_touch, game_id, set_number)
    fig = create_sankey_figure(nodes, links)
    
    # Statistiche
    filtered_df = df_touch.copy()
    if game_id is not None:
        filtered_df = filtered_df[filtered_df['GameID'] == game_id]
    if set_number is not None:
        filtered_df = filtered_df[filtered_df['SetNumber'] == set_number]
    filtered_df = filtered_df[filtered_df['TouchNumber'] <= max_touch]
    
    stats = html.Div([
        html.P(f"Totale tocchi: {len(filtered_df)}"),
        html.P(f"Azioni uniche: {filtered_df['ActionNumber'].nunique() if not filtered_df.empty else 0}"),
        html.P(f"Nodi creati: {len(nodes)}"),
        html.P(f"Collegamenti: {len(links)}"),
        html.P(f"TouchNumber range: {filtered_df['TouchNumber'].min() if not filtered_df.empty else 'N/A'} - {filtered_df['TouchNumber'].max() if not filtered_df.empty else 'N/A'}"),
    ])
    
    return fig, stats

if __name__ == '__main__':
    app.run(debug=True, port=8050)

import dash
from dash import dcc, html, Input, Output, callback, dash_table
import plotly.graph_objects as go
import pandas as pd
import numpy as np
from collections import defaultdict

# CONFIGURAZIONE - Modifica qui per cambiare le caratteristiche visualizzate
col_per_fond = {
    "S": "Eval", 
    "R": "Eval", 
    "E": "Eval", 
    "A": "Eval", 
    "B": "Eval", 
    "D": "Eval", 
    "F": "Eval"
}


def create_node_id(touch_number, foundamental, characteristic):
    """Crea un ID unico per ogni nodo"""
    return f"T{touch_number}_{foundamental}_{characteristic}"

def extract_eval_from_string(eval_string):
    """Estrae la valutazione dalla stringa 'Eval_X' -> 'X'"""
    if pd.isna(eval_string) or eval_string == '':
        return '='
    if isinstance(eval_string, str) and eval_string.startswith('Eval_'):
        return eval_string.replace('Eval_', '')
    return str(eval_string)

def get_color_by_eval(eval_val):
    """Ritorna None per lasciare che Plotly gestisca i colori automaticamente"""
    return None

def process_sankey_data(df, max_touch_number=11, game_id=None, set_number=None):
    """Processa i dati per creare il diagramma Sankey"""
    
    # Filtra i dati se specificato
    if game_id is not None:
        df = df[df['GameID'] == game_id]
    if set_number is not None:
        df = df[df['SetNumber'] == set_number]
    
    # Filtra tocchi fino a TouchNumber specificato
    df = df[df['TouchNumber'] <= max_touch_number]
    
    if df.empty:
        return {}, {}
    
    # Dizionari per tracciare nodi e collegamenti
    nodes = {}
    links = defaultdict(int)
    
    # Raggruppa per azione
    for action_id, action_data in df.groupby('ActionNumber'):
        action_touches = action_data.sort_values('TouchNumber')
        
        # Crea nodi per ogni tocco dell'azione
        for _, touch in action_touches.iterrows():
            # Ottieni la caratteristica specificata per questo fondamentale
            characteristic_col = col_per_fond.get(touch['Foundamental'], 'Eval')
            
            if characteristic_col in touch and pd.notna(touch[characteristic_col]):
                characteristic = touch[characteristic_col]
            else:
                characteristic = '='  # Valore di default
            
            # Pulisce la caratteristica se è nel formato 'Prefix_Value'
            if isinstance(characteristic, str) and '_' in characteristic:
                characteristic = characteristic.split('_', 1)[-1]
            
            node_id = create_node_id(touch['TouchNumber'], touch['Foundamental'], characteristic)
            
            if node_id not in nodes:
                nodes[node_id] = {
                    'id': node_id,
                    'touch_number': touch['TouchNumber'],
                    'foundamental': touch['Foundamental'],
                    'characteristic': characteristic,
                    'count': 0,
                    'label': f"{touch['TouchNumber']}_{touch['Foundamental']}{characteristic}"
                }
            nodes[node_id]['count'] += 1
        
        # Crea collegamenti tra tocchi consecutivi
        touches_list = action_touches.to_dict('records')
        for i in range(len(touches_list) - 1):
            current_touch = touches_list[i]
            next_touch = touches_list[i + 1]
            
            # Ottieni caratteristiche
            current_char_col = col_per_fond.get(current_touch['Foundamental'], 'Eval')
            next_char_col = col_per_fond.get(next_touch['Foundamental'], 'Eval')
            
            current_char = current_touch.get(current_char_col, '=')
            next_char = next_touch.get(next_char_col, '=')
            
            # Pulisce le caratteristiche
            if isinstance(current_char, str) and '_' in current_char:
                current_char = current_char.split('_', 1)[-1]
            if isinstance(next_char, str) and '_' in next_char:
                next_char = next_char.split('_', 1)[-1]
            
            source_id = create_node_id(current_touch['TouchNumber'], current_touch['Foundamental'], current_char)
            target_id = create_node_id(next_touch['TouchNumber'], next_touch['Foundamental'], next_char)
            
            link_key = (source_id, target_id)
            links[link_key] += 1
    
    return nodes, links

def create_sankey_figure(nodes, links):
    """Crea il diagramma Sankey usando Plotly"""
    
    if not nodes or not links:
        # Crea un grafico vuoto se non ci sono dati
        fig = go.Figure()
        fig.add_annotation(
            text="Nessun dato disponibile per i filtri selezionati",
            xref="paper", yref="paper",
            x=0.5, y=0.5, showarrow=False,
            font=dict(size=16)
        )
        fig.update_layout(height=400)
        return fig
    
    # Converti nodi in liste ordinate
    node_list = list(nodes.values())
    node_list.sort(key=lambda x: (x['touch_number'], x['foundamental'], x['characteristic']))
    
    # Crea mapping da node_id a indice
    node_id_to_index = {node['id']: i for i, node in enumerate(node_list)}
    
    # Prepara dati per Plotly Sankey
    node_labels = [f"{node['label']}<br>({node['count']})" for node in node_list]
    
    # Calcola posizioni per evitare sovrapposizioni con nodi più piccoli
    touch_numbers = sorted(set(node['touch_number'] for node in node_list))
    x_positions = []
    y_positions = []
    
    # Raggruppa nodi per TouchNumber per migliore distribuzione verticale
    nodes_by_touch = {}
    for node in node_list:
        touch_num = node['touch_number']
        if touch_num not in nodes_by_touch:
            nodes_by_touch[touch_num] = []
        nodes_by_touch[touch_num].append(node)
    
    # Calcola posizioni per ogni gruppo di TouchNumber
    # Mapping fisso delle coordinate X per ogni TouchNumber
    touch_x_mapping = {
        1: 0.050,
        2: 0.100,
        3: 0.200,
        4: 0.300,
        5: 0.400,
        6: 0.500,
        7: 0.600,
        8: 0.700,
        9: 0.800,
        10: 0.900,
        11: 0.950,
        12: 0.975,
        13: 0.990,
        14: 0.995,
        15: 0.999,
        
    }
    
    # DEBUG: Stampiamo le assegnazioni per verificare
    print("DEBUG - Assegnazione coordinate X:")
    for node in node_list:
        touch_num = node['touch_number']
        x_pos = touch_x_mapping.get(touch_num, 0.500)
        print(f"Nodo {node['label']} - TouchNumber: {touch_num} - X: {x_pos}")
        x_positions.append(x_pos)
        
        # Posizione Y con distribuzione molto più spaziata
        nodes_same_touch = nodes_by_touch[touch_num]
        nodes_same_touch.sort(key=lambda x: (x['foundamental'], x['characteristic']))
        
        node_index_in_touch = nodes_same_touch.index(node)
        num_nodes_in_touch = len(nodes_same_touch)
        
        # Distribuzione con molto più spazio tra i nodi
        if num_nodes_in_touch == 1:
            y_pos = 0.5
        else:
            # Usa margini più grandi e distribuzione più spaziata
            margin = 0.05
            available_space = 1.0 - 2 * margin
            y_pos = margin + (node_index_in_touch * available_space) / (num_nodes_in_touch - 1)
        
        y_positions.append(y_pos)
    
    # Prepara collegamenti
    source_indices = []
    target_indices = []
    values = []
    
    for (source_id, target_id), value in links.items():
        if source_id in node_id_to_index and target_id in node_id_to_index:
            source_indices.append(node_id_to_index[source_id])
            target_indices.append(node_id_to_index[target_id])
            values.append(value)
    
    # Prepara hover text personalizzato
    node_hover_text = []
    for node in node_list:
        hover = (
            f"Nodo: {node['label']}<br>"
            f"TouchNumber: {node['touch_number']}<br>"
            f"Foundamental: {node['foundamental']}<br>"
            f"Characteristic: {node['characteristic']}<br>"
            f"Osservazioni: {node['count']}"
        )
        node_hover_text.append(hover)
    
    link_hover_text = []
    for i, ((source_id, target_id), value) in enumerate(links.items()):
        if source_id in node_id_to_index and target_id in node_id_to_index:
            source_node = nodes[source_id]
            target_node = nodes[target_id]
            hover = (
                f"Flusso: {source_node['label']} → {target_node['label']}<br>"
                f"Da TouchNumber: {source_node['touch_number']}<br>"
                f"A TouchNumber: {target_node['touch_number']}<br>"
                f"Osservazioni: {value}"
            )
            link_hover_text.append(hover)
    
    # Crea il diagramma Sankey rimuovendo arrangement='fixed' per permettere movimento
    fig = go.Figure(data=[go.Sankey(
        node=dict(
            pad=30,  # Più padding per evitare sovrapposizioni
            thickness=15,  # Nodi più sottili
            line=dict(color="black", width=0.5),
            label=node_labels,
            x=x_positions,
            y=y_positions,
            customdata=node_hover_text,
            hovertemplate='%{customdata}<extra></extra>'
        ),
        link=dict(
            source=source_indices,
            target=target_indices,
            value=values,
            customdata=link_hover_text,
            hovertemplate='%{customdata}<extra></extra>'
        )
    )])
    
    fig.update_layout(
        title={
            'text': "Diagramma Sankey - Tocchi Pallavolo",
            'font': {'size': 20}
        },
        font_size=10,  # Font più piccolo per risparmiare spazio
        height=900,  # Più altezza per distribuire meglio i nodi
        margin=dict(t=80, b=40, l=40, r=40)
    )
    
    return fig

# Inizializza l'app Dash
app = dash.Dash(__name__)

# Layout dell'app
app.layout = html.Div([
    html.H1("Analisi Tocchi Pallavolo - Diagramma Sankey", 
            style={'textAlign': 'center', 'marginBottom': 30}),
    
    html.Div([
        html.H3("Debug - Posizioni Nodi"),
        html.Div(id='debug-positions')
    ], style={'margin': '20px', 'padding': '20px', 'border': '1px solid #ddd', 'borderRadius': '5px'}),
    
    html.Div([
        html.H3("Configurazione Filtri"),
        html.Div([
            html.Div([
                html.Label("Game ID:"),
                dcc.Dropdown(
                    id='game-id-dropdown',
                    options=[{'label': f'Game {gid}', 'value': gid} for gid in sorted(df_touch['GameID'].unique())],
                    value=df_touch['GameID'].iloc[0] if not df_touch.empty else None,
                    style={'width': '150px'}
                ),
            ], style={'display': 'inline-block', 'marginRight': '20px'}),
            
            html.Div([
                html.Label("Set Number:"),
                dcc.Dropdown(
                    id='set-number-dropdown',
                    style={'width': '150px'}
                ),
            ], style={'display': 'inline-block', 'marginRight': '20px'}),
            
            html.Div([
                html.Label("Max TouchNumber:"),
                dcc.Slider(
                    id='max-touch-slider',
                    min=3,
                    max=11,
                    step=1,
                    value=11,
                    marks={i: str(i) for i in range(3, 12)},
                    tooltip={"placement": "bottom", "always_visible": True}
                ),
            ], style={'display': 'inline-block', 'width': '200px'}),
        ], style={'display': 'flex', 'alignItems': 'center', 'gap': '20px'}),
    ], style={'margin': '20px', 'padding': '20px', 'border': '1px solid #ddd', 'borderRadius': '5px'}),
    
    html.Div([
        html.H3("Configurazione Caratteristiche per Fondamentale"),
        html.P("Attualmente configurato per mostrare 'Eval' per tutti i fondamentali. Modifica col_per_fond nel codice per personalizzare."),
        html.Div([
            html.Span(f"{fond}: {char}", style={'margin': '5px', 'padding': '5px', 'backgroundColor': '#f0f0f0', 'borderRadius': '3px'})
            for fond, char in col_per_fond.items()
        ], style={'display': 'flex', 'flexWrap': 'wrap'})
    ], style={'margin': '20px', 'padding': '20px', 'border': '1px solid #ddd', 'borderRadius': '5px'}),
    
    dcc.Graph(id='sankey-diagram'),
    
    html.Div([
        html.H3("Statistiche Dati"),
        html.Div(id='data-stats')
    ], style={'margin': '20px', 'padding': '20px', 'border': '1px solid #ddd', 'borderRadius': '5px'}),
    
    html.Div([
        html.H3("Come interpretare il diagramma:"),
        html.Ul([
            html.Li("Ogni colonna verticale rappresenta un TouchNumber (1, 2, 3, etc.)"),
            html.Li("I nodi mostrano Fondamentale + Caratteristica configurata (es. S+, R-, E#)"),
            html.Li("I tocchi sono raggruppati per ActionNumber, mantenendo i flussi separati quando necessario"),
            html.Li("Le azioni con lo stesso nodo iniziale possono condividere percorsi comuni"),
        ])
    ], style={'margin': '20px', 'padding': '20px', 'border': '1px solid #ddd', 'borderRadius': '5px'})
])

@app.callback(
    Output('set-number-dropdown', 'options'),
    Output('set-number-dropdown', 'value'),
    Input('game-id-dropdown', 'value')
)
def update_set_options(game_id):
    """Aggiorna le opzioni del Set Number basate su Game ID selezionato"""
    if game_id is None:
        return [], None
    
    filtered_df = df_touch[df_touch['GameID'] == game_id]
    set_options = [{'label': f'Set {sn}', 'value': sn} for sn in sorted(filtered_df['SetNumber'].unique())]
    default_set = filtered_df['SetNumber'].iloc[0] if not filtered_df.empty else None
    
    return set_options, default_set

@app.callback(
    Output('sankey-diagram', 'figure'),
    Output('data-stats', 'children'),
    Output('debug-positions', 'children'),
    Input('game-id-dropdown', 'value'),
    Input('set-number-dropdown', 'value'),
    Input('max-touch-slider', 'value')
)
def update_sankey(game_id, set_number, max_touch):
    """Callback per aggiornare il diagramma quando cambiano i filtri"""
    nodes, links = process_sankey_data(df_touch, max_touch, game_id, set_number)
    fig = create_sankey_figure(nodes, links)
    
    # Statistiche
    filtered_df = df_touch.copy()
    if game_id is not None:
        filtered_df = filtered_df[filtered_df['GameID'] == game_id]
    if set_number is not None:
        filtered_df = filtered_df[filtered_df['SetNumber'] == set_number]
    filtered_df = filtered_df[filtered_df['TouchNumber'] <= max_touch]
    
    stats = html.Div([
        html.P(f"Totale tocchi: {len(filtered_df)}"),
        html.P(f"Azioni uniche: {filtered_df['ActionNumber'].nunique() if not filtered_df.empty else 0}"),
        html.P(f"Nodi creati: {len(nodes)}"),
        html.P(f"Collegamenti: {len(links)}"),
        html.P(f"TouchNumber range: {filtered_df['TouchNumber'].min() if not filtered_df.empty else 'N/A'} - {filtered_df['TouchNumber'].max() if not filtered_df.empty else 'N/A'}"),
    ])
    
    # Debug info per vedere le posizioni assegnate
    debug_info = []
    if nodes:
        node_list = list(nodes.values())
        node_list.sort(key=lambda x: (x['touch_number'], x['foundamental'], x['characteristic']))
        
        touch_x_mapping = {
            1: 0.050, 2: 0.100, 3: 0.200, 4: 0.300, 5: 0.400, 6: 0.500,
            7: 0.600, 8: 0.700, 9: 0.800, 10: 0.900, 11: 0.950
        }
        
        for node in node_list:
            expected_x = touch_x_mapping.get(node['touch_number'], 0.500)
            debug_info.append(
                html.P(f"Nodo {node['label']} | TouchNumber: {node['touch_number']} | X attesa: {expected_x}")
            )
    
    debug_div = html.Div(debug_info) if debug_info else html.P("Nessun nodo da debuggare")
    
    return fig, stats, debug_div

if __name__ == '__main__':
    app.run(debug=True, port=8050)

import pandas as pd
import plotly.graph_objects as go

# --- CONFIG ---
touch_to_x = {i: i/11 for i in range(1, 12)}
col_per_fond = {"S": "Eval", "R": "Eval", "E": "Eval", "A": "Eval", "B": "Eval", "D": "Eval", "F": "Eval"}

# --- DATI BASE ---
df = df_touch[
    (df_touch["Foundamental"].isin(col_per_fond.keys())) &
    (df_touch["TouchNumber"] <= 11)
].copy()

df["Label"] = df.apply(
    lambda row: f"{row['Foundamental']}: {str(row[col_per_fond[row['Foundamental']]])[:10]}", axis=1
)

# Next step
df["next_label"] = df.groupby(["GameID", "SetNumber", "ActionNumber"])["Label"].shift(-1)
df["next_touch"] = df.groupby(["GameID", "SetNumber", "ActionNumber"])["TouchNumber"].shift(-1)

# Filtra transizioni valide
df_trans = df.dropna(subset=["next_label", "next_touch"])

# Conta transizioni
link_counts = df_trans.groupby(["Label", "TouchNumber", "next_label", "next_touch"]).size().reset_index(name="count")
link_counts = link_counts.sort_values("count", ascending=False).head(150)

# --- CREAZIONE NODI UNIFICATA ---
labels_src = link_counts[["Label", "TouchNumber"]].rename(columns={"Label": "Label", "TouchNumber": "TouchNumber"})
labels_tgt = link_counts[["next_label", "next_touch"]].rename(columns={"next_label": "Label", "next_touch": "TouchNumber"})
all_nodes_df = pd.concat([labels_src, labels_tgt], ignore_index=True).drop_duplicates()

# Conta osservazioni per ogni nodo
obs_counts_src = link_counts.groupby("Label")["count"].sum()
obs_counts_tgt = link_counts.groupby("next_label")["count"].sum()
obs_total = obs_counts_src.add(obs_counts_tgt, fill_value=0)

# Costruisci nodes_df
all_nodes_df["Count"] = all_nodes_df["Label"].map(obs_total)
all_nodes_df["x"] = all_nodes_df["TouchNumber"].map(touch_to_x).fillna(0.99)
all_nodes_df["id"] = range(len(all_nodes_df))

# Mappa da Label → ID
label2id = dict(zip(all_nodes_df["Label"], all_nodes_df["id"]))

# Customdata nodi
all_nodes_df["hover"] = all_nodes_df.apply(
    lambda r: f"Label: {r['Label']}<br>TouchNumber: {int(r['TouchNumber'])}<br>Count: {int(r['Count'])}", axis=1
)

# --- PREPARA LINK ---
link_sources = link_counts["Label"].map(label2id)
link_targets = link_counts["next_label"].map(label2id)
link_values = link_counts["count"]
link_hover = link_counts.apply(
    lambda r: f"{r['Label']} → {r['next_label']}<br>TouchNumber: {int(r['TouchNumber'])} → {int(r['next_touch'])}<br>Count: {r['count']}",
    axis=1
)

# --- PLOT ---
fig = go.Figure(data=[go.Sankey(
    arrangement="snap",
    node=dict(
        pad=20,
        thickness=20,
        line=dict(color="black", width=0.5),
        label=all_nodes_df["Label"],
        x=all_nodes_df["x"],
        hovertemplate="%{customdata}<extra></extra>",
        customdata=all_nodes_df["hover"]
    ),
    link=dict(
        source=link_sources,
        target=link_targets,
        value=link_values,
        customdata=link_hover,
        hovertemplate="%{customdata}<extra></extra>"
    )
)])

fig.update_layout(title_text="Sankey ordinato per TouchNumber", font_size=12)
fig.show()


con.close()