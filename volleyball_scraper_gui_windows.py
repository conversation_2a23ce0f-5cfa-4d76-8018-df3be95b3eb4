#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Volleyball Scraper - Interfaccia utente per estrarre dati dei giocatori da volleybox.net
Versione per Windows con interfaccia grafica e ricerca Google per trovare l'URL corretto
"""

import sys
import re
import unicodedata
import time
import json
import random
import os
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from pathlib import Path
from threading import Thread
import requests
from bs4 import BeautifulSoup
from playwright.sync_api import sync_playwright, TimeoutError

# Directory per salvare i cookie e i dati - adattate per Windows
DATA_DIR = Path(os.path.expanduser("~")) / "Documents" / "VolleyballScraper" / "data"
COOKIES_FILE = DATA_DIR / 'cookies.json'
RESULTS_DIR = DATA_DIR / 'results'

# Assicurati che le directory esistano
DATA_DIR.mkdir(parents=True, exist_ok=True)
RESULTS_DIR.mkdir(parents=True, exist_ok=True)

def normalize_name(name):
    """
    Normalizza il nome per la costruzione dell'URL.
    Rimuove accenti, converte in minuscolo e sostituisce spazi con trattini.
    """
    # Rimuovi accenti e caratteri speciali
    name = unicodedata.normalize('NFKD', name).encode('ASCII', 'ignore').decode('utf-8')
    # Converti in minuscolo
    name = name.lower()
    # Sostituisci spazi con trattini
    name = re.sub(r'\s+', '-', name)
    # Rimuovi caratteri non alfanumerici (tranne trattini)
    name = re.sub(r'[^a-z0-9-]', '', name)
    return name


def trova_url_volleybox_google(nome, cognome, callback=None):
    """
    Trova l'URL corretto del giocatore su volleybox.net tramite ricerca Google.
    
    Args:
        nome (str): Nome del giocatore
        cognome (str): Cognome del giocatore
        callback (function): Funzione di callback per aggiornare l'interfaccia
        
    Returns:
        str: URL del giocatore o None se non trovato
    """
    # Funzione per aggiornare lo stato
    def update_status(message):
        print(message)
        if callback:
            callback(message)
    
    update_status(f"Cercando l'URL per {nome} {cognome} tramite Google...")
    
    # Metodo 1: Ricerca diretta su volleybox.net
    try:
        update_status("Tentativo di ricerca diretta su volleybox.net...")
        url_ricerca = f"https://volleybox.net/it/search?query={nome}+{cognome}"
        
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)  AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
            "Accept-Language": "it-IT,it;q=0.9,en-US;q=0.8,en;q=0.7",
            "Referer": "https://volleybox.net/",
            "DNT": "1"
        }
        
        response = requests.get(url_ricerca, headers=headers) 
        update_status(f"Codice di stato: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, "html.parser")
            
            # Cerca link ai profili dei giocatori
            player_links = []
            for a in soup.find_all('a', href=True):
                href = a['href']
                if '/it/' in href and not '/search' in href and not '/login' in href:
                    # Verifica se il link contiene il nome o cognome del giocatore
                    if nome.lower() in href.lower() or cognome.lower() in href.lower():
                        player_links.append("https://volleybox.net" + href if not href.startswith('http')  else href)
            
            if player_links:
                url = player_links[0]
                update_status(f"URL trovato: {url}")
                
                # Modifica l'URL per puntare alla pagina "clubs" se necessario
                if "/clubs" not in url:
                    url = url.rstrip("/") + "/clubs"
                    update_status(f"URL modificato per la pagina clubs: {url}")
                
                return url
    except Exception as e:
        update_status(f"Errore durante la ricerca diretta: {str(e)}")
    
    # Metodo 2: Ricerca Google con selettori alternativi
    try:
        update_status("Tentativo di ricerca tramite Google...")
        query = f"site:volleybox.net/it {nome} {cognome}"
        url_ricerca = f"https://www.google.com/search?q={query.replace(' ', '+') }"

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
            "Accept-Language": "it-IT,it;q=0.9,en-US;q=0.8,en;q=0.7"
        }

        response = requests.get(url_ricerca, headers=headers)
        update_status(f"Codice di stato Google: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, "html.parser")
            
            # Prova diversi selettori CSS che Google potrebbe utilizzare
            selectors = [
                ".tF2Cxc .yuRUbf a",  # Selettore originale
                ".g .yuRUbf a",        # Alternativa 1
                ".g a[href*='volleybox.net']",  # Alternativa 2
                "a[href*='volleybox.net']"      # Alternativa 3 (più generica)
            ]
            
            for selector in selectors:
                links = soup.select(selector)
                for link in links:
                    url = link.get('href')
                    if url and "volleybox.net/it" in url:
                        update_status(f"URL trovato con selettore {selector}: {url}")
                        
                        # Modifica l'URL per puntare alla pagina "clubs" se necessario
                        if "/clubs" not in url:
                            url = url.rstrip("/") + "/clubs"
                            update_status(f"URL modificato per la pagina clubs: {url}")
                        
                        return url
            
            # Se i selettori non funzionano, cerca manualmente tutti i link
            update_status("Tentativo con ricerca manuale di tutti i link...")
            for a in soup.find_all('a', href=True):
                url = a['href']
                if "volleybox.net/it" in url:
                    update_status(f"URL trovato con ricerca manuale: {url}")
                    
                    # Modifica l'URL per puntare alla pagina "clubs" se necessario
                    if "/clubs" not in url:
                        url = url.rstrip("/") + "/clubs"
                        update_status(f"URL modificato per la pagina clubs: {url}")
                    
                    return url
    except Exception as e:
        update_status(f"Errore durante la ricerca Google: {str(e)}")
    
    # Fallback: prova a costruire un URL generico
    update_status("Nessun URL trovato, tentativo con URL generico...")
    nome_norm = normalize_name(nome)
    cognome_norm = normalize_name(cognome)
    return f"https://volleybox.net/it/{nome_norm}-{cognome_norm}/clubs"
        



def get_advanced_fingerprint_script():
    """
    Genera uno script JavaScript avanzato per modificare il fingerprint del browser.
    """
    script = """
    // Nasconde WebDriver
    Object.defineProperty(navigator, 'webdriver', {
        get: () => false,
    });
    
    // Modifica il fingerprint di Canvas
    const originalGetContext = HTMLCanvasElement.prototype.getContext;
    HTMLCanvasElement.prototype.getContext = function(type, attributes) {
        const context = originalGetContext.call(this, type, attributes);
        if (type === '2d') {
            const originalFillText = context.fillText;
            context.fillText = function() {
                const args = Array.from(arguments);
                // Aggiungi un leggero rumore alle coordinate
                if (typeof args[1] === 'number' && typeof args[2] === 'number') {
                    args[1] += Math.random() * 0.2 - 0.1;
                    args[2] += Math.random() * 0.2 - 0.1;
                }
                return originalFillText.apply(this, args);
            };
        }
        
        // Modifica WebGL fingerprint
        if (type === 'webgl' || type === 'experimental-webgl' || type === 'webgl2') {
            const originalGetParameter = context.getParameter;
            context.getParameter = function(parameter) {
                // Modifica vendor e renderer
                if (parameter === 0x1F00) { // VENDOR
                    return 'Google Inc.';
                }
                if (parameter === 0x1F01) { // RENDERER
                    return 'ANGLE (Intel(R) UHD Graphics Direct3D11 vs_5_0 ps_5_0)';
                }
                // Restituisci il valore originale per altri parametri
                return originalGetParameter.call(this, parameter);
            };
        }
        
        return context;
    };
    
    // Aggiungi funzioni di Chrome
    window.chrome = {
        app: {
            isInstalled: false,
        },
        webstore: {
            onInstallStageChanged: {},
            onDownloadProgress: {},
        },
        runtime: {
            PlatformOs: {
                MAC: 'mac',
                WIN: 'win',
                ANDROID: 'android',
                CROS: 'cros',
                LINUX: 'linux',
                OPENBSD: 'openbsd',
            },
            PlatformArch: {
                ARM: 'arm',
                X86_32: 'x86-32',
                X86_64: 'x86-64',
            },
            PlatformNaclArch: {
                ARM: 'arm',
                X86_32: 'x86-32',
                X86_64: 'x86-64',
            },
            RequestUpdateCheckStatus: {
                THROTTLED: 'throttled',
                NO_UPDATE: 'no_update',
                UPDATE_AVAILABLE: 'update_available',
            },
            OnInstalledReason: {
                INSTALL: 'install',
                UPDATE: 'update',
                CHROME_UPDATE: 'chrome_update',
                SHARED_MODULE_UPDATE: 'shared_module_update',
            },
            OnRestartRequiredReason: {
                APP_UPDATE: 'app_update',
                OS_UPDATE: 'os_update',
                PERIODIC: 'periodic',
            },
        },
    };
    
    // Aggiungi i plugin
    const mockPlugins = [
        { name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer', description: 'Portable Document Format' },
        { name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai', description: '' },
        { name: 'Native Client', filename: 'internal-nacl-plugin', description: '' }
    ];
    
    Object.defineProperty(navigator, 'plugins', {
        get: function() {
            const plugins = [];
            for (const plugin of mockPlugins) {
                plugins.push(plugin);
            }
            plugins.__proto__ = Object.getPrototypeOf([]);
            return plugins;
        },
    });
    
    // Aggiungi lingue
    Object.defineProperty(navigator, 'languages', {
        get: () => ['it-IT', 'it', 'en-US', 'en'],
    });
    """
    
    return script

def setup_stealth_browser():
    """
    Configura un browser stealth con Playwright per evitare il rilevamento di Cloudflare.
    
    Returns:
        tuple: (playwright, browser, context, page)
    """
    playwright = sync_playwright().start()
    
    # Configura il browser con impostazioni avanzate per evitare il rilevamento
    browser = playwright.chromium.launch(
        headless=True,
        args=[
            '--disable-blink-features=AutomationControlled',
            '--disable-features=IsolateOrigins,site-per-process',
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--disable-gpu'
        ]
    )
    
    # Crea un contesto con impostazioni avanzate
    context = browser.new_context(
        viewport={"width": 1920, "height": 1080},
        user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        locale="it-IT",
        timezone_id="Europe/Rome"
    )
    
    # Modifica il fingerprint del browser per evitare il rilevamento
    context.add_init_script(get_advanced_fingerprint_script())
    
    # Imposta header HTTP realistici
    context.set_extra_http_headers({
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'it-IT,it;q=0.8,en-US;q=0.5,en;q=0.3',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0',
    })
    
    # Crea una nuova pagina
    page = context.new_page()
    
    return playwright, browser, context, page

def save_cookies(context, file_path=COOKIES_FILE):
    """
    Salva i cookie del contesto in un file.
    
    Args:
        context: Contesto del browser Playwright
        file_path: Percorso del file in cui salvare i cookie
    """
    cookies = context.cookies()
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(cookies, f, indent=2)
    print(f"Cookie salvati in {file_path}")

def load_cookies(context, file_path=COOKIES_FILE):
    """
    Carica i cookie da un file nel contesto.
    
    Args:
        context: Contesto del browser Playwright
        file_path: Percorso del file da cui caricare i cookie
        
    Returns:
        bool: True se i cookie sono stati caricati con successo, False altrimenti
    """
    if not os.path.exists(file_path):
        print(f"File dei cookie {file_path} non trovato")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            cookies = json.load(f)
        context.add_cookies(cookies)
        print(f"Cookie caricati da {file_path}")
        return True
    except Exception as e:
        print(f"Errore nel caricamento dei cookie: {str(e)}")
        return False

def simulate_human_behavior(page):
    """
    Simula comportamento umano sulla pagina per evitare il rilevamento.
    
    Args:
        page: Pagina Playwright
    """
    # Ritardo casuale iniziale
    time.sleep(random.uniform(1, 3))
    
    # Movimento del mouse casuale
    for _ in range(3):
        page.mouse.move(
            random.randint(100, 800),
            random.randint(100, 600),
            steps=random.randint(5, 10)
        )
        time.sleep(random.uniform(0.3, 0.7))
    
    # Scroll naturale
    for _ in range(random.randint(3, 5)):
        page.mouse.wheel(0, random.randint(100, 300))
        time.sleep(random.uniform(0.5, 1.5))
    
    # Ritardo finale
    time.sleep(random.uniform(0.5, 1.5))

def extract_player_data_with_js(page):
    """
    Estrae i dati del giocatore utilizzando JavaScript direttamente nella pagina.
    
    Args:
        page: Pagina Playwright
        
    Returns:
        dict: Dati del giocatore
    """
    try:
        return page.evaluate("""() => {
            // Funzione per trovare il valore di un campo
            function findFieldValue(fieldName) {
                // Cerca tutti i div nella pagina
                const allDivs = Array.from(document.querySelectorAll('div'));
                
                // Trova il div che contiene il nome del campo
                const fieldDiv = allDivs.find(div => div.textContent === fieldName);
                if (!fieldDiv) return null;
                
                // Ottieni il div successivo che contiene il valore
                const valueDiv = fieldDiv.nextElementSibling;
                if (!valueDiv) return null;
                
                return valueDiv.textContent.trim();
            }
            
            // Estrai i dati
            return {
                nazionalita: findFieldValue('Nazionalità'),
                posizione: findFieldValue('Posizione'),
                data_nascita: findFieldValue('Data di nascita'),
                altezza: findFieldValue('Altezza'),
                peso: findFieldValue('Peso')
            };
        }""")
    except Exception as e:
        print(f"Errore nell'estrazione JavaScript: {str(e)}")
        return {}

def get_player_info(first_name, last_name, use_existing_cookies=True, callback=None):
    """
    Estrae le informazioni di un giocatore da volleybox.net utilizzando Playwright.
    
    Args:
        first_name (str): Nome del giocatore
        last_name (str): Cognome del giocatore
        use_existing_cookies (bool): Se utilizzare i cookie esistenti
        callback (function): Funzione di callback per aggiornare l'interfaccia
        
    Returns:
        dict: Dizionario contenente le informazioni del giocatore
    """
    # Trova l'URL corretto tramite Google
    url = trova_url_volleybox_google(first_name, last_name, callback)
    
    if not url:
        return {
            "nome": f"{first_name} {last_name}",
            "url": None,
            "nazionalità": None,
            "posizione": None,
            "data_di_nascita": None,
            "altezza": None,
            "peso": None,
            "errore": "Impossibile trovare l'URL del giocatore"
        }
    
    # Inizializza il dizionario per i risultati
    player_info = {
        "nome": f"{first_name} {last_name}",
        "url": url,
        "nazionalità": None,
        "posizione": None,
        "data_di_nascita": None,
        "altezza": None,
        "peso": None,
        "errore": None
    }
    
    # Funzione per aggiornare lo stato
    def update_status(message):
        print(message)
        if callback:
            callback(message)
    
    playwright = None
    browser = None
    context = None
    
    try:
        # Inizializza il browser stealth
        update_status("Inizializzando il browser...")
        playwright, browser, context, page = setup_stealth_browser()
        
        # Carica i cookie se richiesto e disponibili
        if use_existing_cookies:
            load_cookies(context)
        
        # Naviga alla pagina del giocatore
        update_status(f"Navigando a {url}...")
        page.goto(url, wait_until="networkidle", timeout=60000)
        
        # Simula comportamento umano
        update_status("Simulando comportamento umano...")
        simulate_human_behavior(page)
        
        # Verifica se siamo stati bloccati da Cloudflare
        if "Cloudflare" in page.title() and "challenge" in page.title():
            update_status("Rilevato challenge Cloudflare, attendere...")
            # Attendi che il challenge venga risolto (può richiedere fino a 30 secondi)
            page.wait_for_selector("body", state="visible", timeout=30000)
            page.wait_for_load_state("networkidle", timeout=30000)
            
            # Salva i cookie dopo aver superato il challenge
            save_cookies(context)
            
            # Ricarica la pagina
            update_status("Ricaricando la pagina...")
            page.reload(wait_until="networkidle", timeout=30000)
            simulate_human_behavior(page)
        
        # Salva i cookie per usi futuri
        save_cookies(context)
        
        # Verifica se la pagina è stata caricata correttamente
        if "404" in page.title() or "non trovato" in page.title().lower():
            player_info["errore"] = "Giocatore non trovato"
            return player_info
        
        # Estrai le informazioni dalla pagina
        update_status("Estraendo le informazioni del giocatore...")
        
        # Metodo 1: Estrazione diretta con selettori CSS
        try:
            # Nazionalità
            nationality_element = page.query_selector("div:text('Nazionalità') + div")
            if nationality_element:
                player_info["nazionalità"] = nationality_element.inner_text().strip()
            
            # Posizione
            position_element = page.query_selector("div:text('Posizione') + div")
            if position_element:
                player_info["posizione"] = position_element.inner_text().strip()
            
            # Data di nascita
            birth_date_element = page.query_selector("div:text('Data di nascita') + div")
            if birth_date_element:
                player_info["data_di_nascita"] = birth_date_element.inner_text().strip()
            
            # Altezza
            height_element = page.query_selector("div:text('Altezza') + div")
            if height_element:
                player_info["altezza"] = height_element.inner_text().strip()
            
            # Peso
            weight_element = page.query_selector("div:text('Peso') + div")
            if weight_element:
                player_info["peso"] = weight_element.inner_text().strip()
        except Exception as e:
            update_status(f"Errore nell'estrazione con selettori: {str(e)}")
        
        # Se non abbiamo trovato nessuna informazione, prova con JavaScript
        if not any([player_info["nazionalità"], player_info["posizione"], 
                   player_info["data_di_nascita"], player_info["altezza"], player_info["peso"]]):
            update_status("Tentativo di estrazione con JavaScript...")
            player_data = extract_player_data_with_js(page)
            
            if player_data:
                if player_data.get('nazionalita'):
                    player_info["nazionalità"] = player_data['nazionalita']
                if player_data.get('posizione'):
                    player_info["posizione"] = player_data['posizione']
                if player_data.get('data_nascita'):
                    player_info["data_di_nascita"] = player_data['data_nascita']
                if player_data.get('altezza'):
                    player_info["altezza"] = player_data['altezza']
                if player_data.get('peso'):
                    player_info["peso"] = player_data['peso']
        
        # Salva uno screenshot per debug
        screenshot_path = DATA_DIR / f"{normalize_name(first_name)}_{normalize_name(last_name)}.png"
        page.screenshot(path=str(screenshot_path))
        update_status(f"Screenshot salvato in {screenshot_path}")
        
        # Se ancora non abbiamo trovato informazioni, salva l'HTML per debug
        if not any([player_info["nazionalità"], player_info["posizione"], 
                   player_info["data_di_nascita"], player_info["altezza"], player_info["peso"]]):
            html_path = DATA_DIR / f"{normalize_name(first_name)}_{normalize_name(last_name)}.html"
            with open(html_path, "w", encoding="utf-8") as f:
                f.write(page.content())
            update_status(f"HTML salvato in {html_path}")
            
            # Prova un approccio alternativo cercando nella struttura JSON-LD
            try:
                update_status("Tentativo di estrazione da JSON-LD...")
                json_ld_data = page.evaluate("""() => {
                    const jsonLdScripts = Array.from(document.querySelectorAll('script[type="application/ld+json"]'));
                    for (const script of jsonLdScripts) {
                        try {
                            const data = JSON.parse(script.textContent);
                            if (data.birthDate || data.height || data.gender) {
                                return data;
                            }
                        } catch (e) {
                            // Ignora errori di parsing
                        }
                    }
                    return null;
                }""")
                
                if json_ld_data:
                    update_status("Dati trovati in JSON-LD")
                    if 'birthDate' in json_ld_data and not player_info["data_di_nascita"]:
                        player_info["data_di_nascita"] = json_ld_data['birthDate']
                    if 'height' in json_ld_data and not player_info["altezza"]:
                        player_info["altezza"] = json_ld_data['height']
                    if 'weight' in json_ld_data and not player_info["peso"]:
                        player_info["peso"] = json_ld_data['weight']
                    if 'jobTitle' in json_ld_data and not player_info["posizione"]:
                        player_info["posizione"] = json_ld_data['jobTitle'].replace('Volleyball player', 'Pallavolista')
            except Exception as e:
                update_status(f"Errore nell'estrazione JSON-LD: {str(e)}")
            
            # Se ancora non abbiamo trovato informazioni, segnala un errore
            if not any([player_info["nazionalità"], player_info["posizione"], 
                       player_info["data_di_nascita"], player_info["altezza"], player_info["peso"]]):
                player_info["errore"] = "Impossibile estrarre le informazioni dalla pagina"
        
    except TimeoutError:
        player_info["errore"] = "Timeout durante il caricamento della pagina"
        update_status("Timeout durante il caricamento della pagina")
    except Exception as e:
        player_info["errore"] = f"Errore durante lo scraping: {str(e)}"
        update_status(f"Errore durante lo scraping: {str(e)}")
    
    finally:
        # Chiudi il browser
        update_status("Chiudendo il browser...")
        if browser:
            browser.close()
        if playwright:
            playwright.stop()
    
    update_status("Estrazione completata")
    return player_info

def save_player_info_to_file(player_info):
    """
    Salva le informazioni del giocatore in un file di testo.
    
    Args:
        player_info (dict): Dizionario contenente le informazioni del giocatore
        
    Returns:
        str: Percorso del file salvato
    """
    # Crea un nome file basato sul nome del giocatore
    first_name, last_name = player_info["nome"].split(" ", 1)
    first_name_norm = normalize_name(first_name)
    last_name_norm = normalize_name(last_name)
    
    file_path = RESULTS_DIR / f"{first_name_norm}_{last_name_norm}.txt"
    
    with open(file_path, "w", encoding="utf-8") as f:
        f.write(f"Informazioni su {player_info['nome']}\n")
        f.write("="*50 + "\n\n")
        
        if player_info["errore"]:
            f.write(f"Errore: {player_info['errore']}\n")
        else:
            f.write(f"URL: {player_info['url']}\n")
            f.write(f"Nazionalità: {player_info['nazionalità'] or 'Non disponibile'}\n")
            f.write(f"Posizione: {player_info['posizione'] or 'Non disponibile'}\n")
            f.write(f"Data di nascita: {player_info['data_di_nascita'] or 'Non disponibile'}\n")
            f.write(f"Altezza: {player_info['altezza'] or 'Non disponibile'}\n")
            f.write(f"Peso: {player_info['peso'] or 'Non disponibile'}\n")
    
    return str(file_path)

def save_player_info_to_json(player_info):
    """
    Salva le informazioni del giocatore in un file JSON.
    
    Args:
        player_info (dict): Dizionario contenente le informazioni del giocatore
        
    Returns:
        str: Percorso del file salvato
    """
    # Crea un nome file basato sul nome del giocatore
    first_name, last_name = player_info["nome"].split(" ", 1)
    first_name_norm = normalize_name(first_name)
    last_name_norm = normalize_name(last_name)
    
    file_path = RESULTS_DIR / f"{first_name_norm}_{last_name_norm}.json"
    
    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(player_info, f, indent=2, ensure_ascii=False)
    
    return str(file_path)

class VolleyballScraperApp:
    """
    Applicazione GUI per lo scraping di dati dei giocatori di pallavolo.
    """
    def __init__(self, root):
        self.root = root
        self.root.title("Volleyball Scraper")
        self.root.geometry("800x600")
        self.root.minsize(800, 600)
        
        # Configura lo stile
        self.style = ttk.Style()
        self.style.configure("TButton", font=("Arial", 10))
        self.style.configure("TLabel", font=("Arial", 10))
        self.style.configure("TEntry", font=("Arial", 10))
        self.style.configure("Header.TLabel", font=("Arial", 14, "bold"))
        
        # Crea il frame principale
        self.main_frame = ttk.Frame(root, padding=10)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Titolo
        self.title_label = ttk.Label(self.main_frame, text="Volleyball Scraper", style="Header.TLabel")
        self.title_label.pack(pady=10)
        
        # Descrizione
        self.desc_label = ttk.Label(self.main_frame, 
                                   text="Estrai informazioni sui giocatori di pallavolo da volleybox.net",
                                   wraplength=700)
        self.desc_label.pack(pady=5)
        
        # Frame per l'input
        self.input_frame = ttk.Frame(self.main_frame)
        self.input_frame.pack(fill=tk.X, pady=10)
        
        # Nome
        self.first_name_label = ttk.Label(self.input_frame, text="Nome:")
        self.first_name_label.grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.first_name_entry = ttk.Entry(self.input_frame, width=30)
        self.first_name_entry.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        
        # Cognome
        self.last_name_label = ttk.Label(self.input_frame, text="Cognome:")
        self.last_name_label.grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.last_name_entry = ttk.Entry(self.input_frame, width=30)
        self.last_name_entry.grid(row=1, column=1, padx=5, pady=5, sticky=tk.W)
        
        # Checkbox per i cookie
        self.use_cookies_var = tk.BooleanVar(value=True)
        self.use_cookies_check = ttk.Checkbutton(self.input_frame, 
                                               text="Usa cookie esistenti", 
                                               variable=self.use_cookies_var)
        self.use_cookies_check.grid(row=2, column=0, columnspan=2, padx=5, pady=5, sticky=tk.W)
        
        # Pulsante di ricerca
        self.search_button = ttk.Button(self.input_frame, 
                                      text="Cerca Giocatore", 
                                      command=self.search_player)
        self.search_button.grid(row=3, column=0, columnspan=2, padx=5, pady=10)
        
        # Frame per lo stato
        self.status_frame = ttk.LabelFrame(self.main_frame, text="Stato", padding=5)
        self.status_frame.pack(fill=tk.X, pady=5)
        
        # Area di testo per lo stato
        self.status_text = scrolledtext.ScrolledText(self.status_frame, height=5, wrap=tk.WORD)
        self.status_text.pack(fill=tk.X, expand=True)
        self.status_text.config(state=tk.DISABLED)
        
        # Frame per i risultati
        self.results_frame = ttk.LabelFrame(self.main_frame, text="Risultati", padding=5)
        self.results_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # Area di testo per i risultati
        self.results_text = scrolledtext.ScrolledText(self.results_frame, wrap=tk.WORD)
        self.results_text.pack(fill=tk.BOTH, expand=True)
        self.results_text.config(state=tk.DISABLED)
        
        # Frame per i pulsanti dei risultati
        self.results_buttons_frame = ttk.Frame(self.main_frame)
        self.results_buttons_frame.pack(fill=tk.X, pady=5)
        
        # Pulsante per salvare i risultati
        self.save_txt_button = ttk.Button(self.results_buttons_frame, 
                                       text="Salva come TXT", 
                                       command=lambda: self.save_results("txt"),
                                       state=tk.DISABLED)
        self.save_txt_button.pack(side=tk.LEFT, padx=5)
        
        self.save_json_button = ttk.Button(self.results_buttons_frame, 
                                        text="Salva come JSON", 
                                        command=lambda: self.save_results("json"),
                                        state=tk.DISABLED)
        self.save_json_button.pack(side=tk.LEFT, padx=5)
        
        # Pulsante per aprire la cartella dei risultati
        self.open_folder_button = ttk.Button(self.results_buttons_frame, 
                                          text="Apri Cartella Risultati", 
                                          command=self.open_results_folder)
        self.open_folder_button.pack(side=tk.RIGHT, padx=5)
        
        # Variabili per i risultati
        self.current_player_info = None
    
    def update_status(self, message):
        """
        Aggiorna l'area di stato con un nuovo messaggio.
        
        Args:
            message (str): Messaggio da aggiungere
        """
        self.status_text.config(state=tk.NORMAL)
        self.status_text.insert(tk.END, message + "\n")
        self.status_text.see(tk.END)
        self.status_text.config(state=tk.DISABLED)
        self.root.update()
    
    def search_player(self):
        """
        Avvia la ricerca del giocatore in un thread separato.
        """
        first_name = self.first_name_entry.get().strip()
        last_name = self.last_name_entry.get().strip()
        
        if not first_name or not last_name:
            messagebox.showerror("Errore", "Inserisci sia il nome che il cognome del giocatore")
            return
        
        # Disabilita il pulsante di ricerca
        self.search_button.config(state=tk.DISABLED)
        
        # Pulisci le aree di testo
        self.status_text.config(state=tk.NORMAL)
        self.status_text.delete(1.0, tk.END)
        self.status_text.config(state=tk.DISABLED)
        
        self.results_text.config(state=tk.NORMAL)
        self.results_text.delete(1.0, tk.END)
        self.results_text.config(state=tk.DISABLED)
        
        # Disabilita i pulsanti di salvataggio
        self.save_txt_button.config(state=tk.DISABLED)
        self.save_json_button.config(state=tk.DISABLED)
        
        # Avvia la ricerca in un thread separato
        self.update_status(f"Avvio ricerca per {first_name} {last_name}...")
        
        search_thread = Thread(target=self.perform_search, 
                              args=(first_name, last_name, self.use_cookies_var.get()))
        search_thread.daemon = True
        search_thread.start()
    
    def perform_search(self, first_name, last_name, use_cookies):
        """
        Esegue la ricerca del giocatore.
        
        Args:
            first_name (str): Nome del giocatore
            last_name (str): Cognome del giocatore
            use_cookies (bool): Se utilizzare i cookie esistenti
        """
        try:
            # Ottieni le informazioni del giocatore
            player_info = get_player_info(first_name, last_name, use_cookies, self.update_status)
            
            # Salva le informazioni correnti
            self.current_player_info = player_info
            
            # Aggiorna l'area dei risultati
            self.root.after(0, self.update_results, player_info)
        except Exception as e:
            self.update_status(f"Errore durante la ricerca: {str(e)}")
            self.root.after(0, self.enable_search_button)
    
    def update_results(self, player_info):
        """
        Aggiorna l'area dei risultati con le informazioni del giocatore.
        
        Args:
            player_info (dict): Dizionario contenente le informazioni del giocatore
        """
        self.results_text.config(state=tk.NORMAL)
        self.results_text.delete(1.0, tk.END)
        
        self.results_text.insert(tk.END, f"Informazioni su {player_info['nome']}\n")
        self.results_text.insert(tk.END, "="*50 + "\n\n")
        
        if player_info["errore"]:
            self.results_text.insert(tk.END, f"Errore: {player_info['errore']}\n")
        else:
            self.results_text.insert(tk.END, f"URL: {player_info['url']}\n")
            self.results_text.insert(tk.END, f"Nazionalità: {player_info['nazionalità'] or 'Non disponibile'}\n")
            self.results_text.insert(tk.END, f"Posizione: {player_info['posizione'] or 'Non disponibile'}\n")
            self.results_text.insert(tk.END, f"Data di nascita: {player_info['data_di_nascita'] or 'Non disponibile'}\n")
            self.results_text.insert(tk.END, f"Altezza: {player_info['altezza'] or 'Non disponibile'}\n")
            self.results_text.insert(tk.END, f"Peso: {player_info['peso'] or 'Non disponibile'}\n")
            
            # Abilita i pulsanti di salvataggio
            self.save_txt_button.config(state=tk.NORMAL)
            self.save_json_button.config(state=tk.NORMAL)
        
        self.results_text.config(state=tk.DISABLED)
        
        # Riabilita il pulsante di ricerca
        self.enable_search_button()
    
    def enable_search_button(self):
        """
        Riabilita il pulsante di ricerca.
        """
        self.search_button.config(state=tk.NORMAL)
    
    def save_results(self, format_type):
        """
        Salva i risultati in un file.
        
        Args:
            format_type (str): Formato del file ('txt' o 'json')
        """
        if not self.current_player_info:
            messagebox.showerror("Errore", "Nessun risultato da salvare")
            return
        
        try:
            if format_type == "txt":
                file_path = save_player_info_to_file(self.current_player_info)
                messagebox.showinfo("Salvataggio completato", f"Risultati salvati in {file_path}")
            elif format_type == "json":
                file_path = save_player_info_to_json(self.current_player_info)
                messagebox.showinfo("Salvataggio completato", f"Risultati salvati in {file_path}")
        except Exception as e:
            messagebox.showerror("Errore", f"Errore durante il salvataggio: {str(e)}")
    
    def open_results_folder(self):
        """
        Apre la cartella dei risultati.
        """
        try:
            # Apri la cartella dei risultati con il comando appropriato per Windows
            os.startfile(RESULTS_DIR)
        except Exception as e:
            messagebox.showerror("Errore", f"Errore durante l'apertura della cartella: {str(e)}")

def main():
    """
    Funzione principale per avviare l'applicazione.
    """
    root = tk.Tk()
    app = VolleyballScraperApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
