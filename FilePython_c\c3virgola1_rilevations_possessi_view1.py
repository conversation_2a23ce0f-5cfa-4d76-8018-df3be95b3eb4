#Partiamo da rilevations_libero_view e aggiungiamo nuove colonne che ci servono a fare box scores e analisi
#Oppure partiamo da rilevations, per rendere le cose più leggere

import numpy as np 
import pandas as pd  #Realizzato con la versione di Pandas 2.2.3
import os
import psycopg
from sqlalchemy import create_engine, text


conn = psycopg.connect(
    dbname="db_modena",           # database creato in pgAdmin4
    user="postgres",              # Il tuo nome utente PostgreSQL
    password="AcquaLevissima1",   # La password che hai scelto per 'postgres'
    host="localhost",             # 'localhost' se è sul tuo PC
    port=5432                     # La porta predefinita è 5432
)

cur = conn.cursor() 







cur.execute("DROP MATERIALIZED VIEW IF EXISTS rilevations_possessi1_valid_only CASCADE")
conn.commit()

cur.execute("""
CREATE MATERIALIZED VIEW rilevations_possessi1_valid_only AS
WITH base AS (
  SELECT
    "GameID", "RilevationNumber", "whichTeam", "Foundamental"
  FROM rilevations
  WHERE "Foundamental" IN ('S','R','E','A','B','D','F')
),
azioni AS (
  SELECT *,
    SUM(CASE WHEN "Foundamental" = 'S' THEN 1 ELSE 0 END) OVER (
      PARTITION BY "GameID"
      ORDER BY "RilevationNumber"
    ) AS azione_id
  FROM base
),
con_prev AS (
  SELECT *,
    LAG("whichTeam") OVER (
      PARTITION BY "GameID", azione_id
      ORDER BY "RilevationNumber"
    ) AS prev_team
  FROM azioni
),
flagged AS (
  SELECT *,
    CASE
      WHEN "Foundamental" = 'S' THEN 1
      WHEN prev_team IS DISTINCT FROM "whichTeam" THEN 1
      ELSE 0
    END AS is_poss_start  --Se è 1 indica che è avvenuto un cambio di possesso palla, altrimenti è sempre 0.
  FROM con_prev
)
SELECT
  "GameID",
  "RilevationNumber",
  "is_poss_start",
  SUM(is_poss_start) OVER (
    PARTITION BY "GameID", azione_id
    ORDER BY "RilevationNumber"
  ) AS "AbsNumeroPossesso"
FROM flagged;


""")

cur.execute("""
CREATE MATERIALIZED VIEW rilevations_possessi1_view AS
SELECT
  r."GameID",
  r."RilevationNumber",
  v."is_poss_start",
  CASE
    WHEN r."Foundamental" IN ('S','R','E','A','B','D','F') THEN v."AbsNumeroPossesso"
    ELSE NULL
  END AS "AbsNumeroPossesso"
FROM rilevations r
LEFT JOIN rilevations_possessi1_valid_only v
  ON v."GameID" = r."GameID"
 AND v."RilevationNumber" = r."RilevationNumber";

""")

conn.commit()
print("✅ VIEW rilevations_possessi1_view creata con successo")














#Versione troppo lenta
'''
cur.execute("DROP VIEW IF EXISTS rilevations_possessi1_view CASCADE")
conn.commit()

cur.execute("""
CREATE MATERIALIZED VIEW rilevations_possessi1_view AS
WITH base AS (
  SELECT
    "GameID",
    "SetNumber",
    "ActionNumber",
    "RilevationNumber",
    "TouchNumber",
    "PunteggioCasa",
    "PunteggioOspiti",
    "whichTeam",
    "whichTeamID",
    "NumeroMaglia",
    "Foundamental",
    "Eval",
      CASE WHEN "Foundamental" IN ('S','R','E','A','B','D','F') THEN 1 ELSE 0 END AS is_valid
  FROM rilevations
),


solo_validi AS (
  SELECT *,
         SUM(CASE WHEN "Foundamental" = 'S' THEN 1 ELSE 0 END) OVER (
           PARTITION BY "GameID"
           ORDER BY "RilevationNumber"
         ) AS azione_id
  FROM base
  WHERE is_valid = 1
),


con_prev_team AS (
  SELECT *,
         LAG("whichTeam") OVER (
           PARTITION BY "GameID", azione_id
           ORDER BY "RilevationNumber"
         ) AS prev_team
  FROM solo_validi
),


con_possesso AS (
  SELECT *,
         CASE
           WHEN "Foundamental" = 'S' THEN 1
           WHEN prev_team IS DISTINCT FROM "whichTeam" THEN 1
           ELSE 0
         END AS is_poss_start
  FROM con_prev_team
),


final_valid AS (
  SELECT DISTINCT ON ("GameID", "RilevationNumber")
         *
  FROM (
    SELECT *,
           SUM(is_poss_start) OVER (
             PARTITION BY "GameID", azione_id
             ORDER BY "RilevationNumber"
           ) AS "AbsNumeroPossesso"
    FROM con_possesso
  ) sub
  ORDER BY "GameID", "RilevationNumber", "RilevationNumber" DESC
)


SELECT
    r."GameID",
    --r."SetNumber",
    --r."ActionNumber",
    r."RilevationNumber",
    --r."TouchNumber",
    --r."PunteggioCasa",
    --r."PunteggioOspiti",
    --r."whichTeam",
    --r."whichTeamID",
    --r."NumeroMaglia",
    --r."Foundamental",
    --r."Eval",



  CASE
    WHEN r."Foundamental" IN ('S','R','E','A','B','D','F') THEN (
      SELECT f."AbsNumeroPossesso"
      FROM final_valid f
      WHERE f."GameID" = r."GameID"
        AND f."RilevationNumber" = r."RilevationNumber"
      LIMIT 1
    )
    ELSE NULL
  END AS "AbsNumeroPossesso"
FROM rilevations r;



""")
conn.commit()
print("✅ VIEW rilevations1_view creata con successo")
'''








#versione che non funziona
'''
cur.execute("DROP VIEW IF EXISTS rilevations_possessi1_view CASCADE")
conn.commit()

cur.execute("""
CREATE OR REPLACE VIEW rilevations_possessi1_view AS
WITH flagged AS (
  SELECT
    "GameID",
    "SetNumber",
    "ActionNumber",
    "RilevationNumber",
    "TouchNumber",
    "PunteggioCasa",
    "PunteggioOspiti",
    "whichTeam",
    "whichTeamID",
    "NumeroMaglia",
    "Foundamental",
    "Eval",
    
    LAG("whichTeam") OVER (
      PARTITION BY "GameID", "ActionNumber"
      ORDER BY "RilevationNumber"
    ) AS prev_team
  FROM rilevations
),
starts AS (
  SELECT
    *,
    -- se è uno dei codici automatici, non è inizio possesso
    CASE
      WHEN "Foundamental" IN ('P','z','&','c','T','N') THEN 0
      -- primo tocco reale di quell'azione
      WHEN prev_team IS NULL THEN 1
      -- cambio di squadra = nuovo possesso
      WHEN prev_team <> "whichTeam" THEN 1
      ELSE 0
    END AS is_poss_start
  FROM flagged
)
SELECT
  *,
  -- cumulativa dei possessi, resetta per ogni GameID+ActionNumber,
  -- ma conta solo i flag reali (is_poss_start=1)
  SUM(is_poss_start) OVER (
    PARTITION BY "GameID", "ActionNumber"
    ORDER BY "RilevationNumber"
    ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
  ) AS "AbsNumeroPossesso"
FROM starts;

""")
conn.commit()
print("✅ VIEW rilevations1_view creata con successo")
'''





#Creiamo una VIEW in cui aggiungiamo NumPossessoHome e NumPossessoVisitor, che indicano il numero del possesso della squadra in quell'azione
cur.execute("DROP VIEW IF EXISTS rilevations_possessi2_view CASCADE")

cur.execute("""
CREATE OR REPLACE VIEW rilevations_possessi2_view AS
WITH 
-- 1) Calcolo del tocco reale precedente nella stessa azione e set
filtered AS (
  SELECT 
    "GameID",
    "SetNumber",
    "ActionNumber",
    "RilevationNumber",
    "TouchNumber",
    "PunteggioCasa",
    "PunteggioOspiti",
    "whichTeam",
    "whichTeamID",
    "NumeroMaglia",
    "Foundamental",
    "Eval"
  FROM rilevations
),
flagged AS (
  SELECT
    r.*,
    -- LAG solo tra i tocchi REALI (Foundamental NOT IN automatici),
    -- partition per GameID, SetNumber, ActionNumber
    LAG(r."whichTeam") OVER (
      PARTITION BY r."GameID", r."SetNumber", r."ActionNumber"
      ORDER BY r."RilevationNumber"
    ) AS prev_team_real,

    -- Manteniamo anche il prev_team per qualsiasi tocco, utile per debug
    LAG(r."whichTeam") OVER (
      PARTITION BY r."GameID", r."SetNumber", r."ActionNumber"
      ORDER BY r."RilevationNumber"
    ) AS prev_team_all

  FROM filtered AS r
),

-- 2) Flag di inizio possesso per casa e ospite
starts AS (
  SELECT
    f.*,

    -- incremento solo se tocco reale e -- 
    -- il tocco precedente (real) era dell'altra squadra
    CASE
      WHEN f."Foundamental" IN ('P','z','&','c','T','N') THEN 0
      WHEN f."whichTeam" = FALSE 
           AND f.prev_team_real = TRUE   THEN 1
      WHEN f."whichTeam" = FALSE 
           AND f.prev_team_real IS NULL  THEN 1  -- primo tocco reale in casa
      ELSE 0
    END AS is_home_start,

    CASE
      WHEN f."Foundamental" IN ('P','z','&','c','T','N') THEN 0
      WHEN f."whichTeam" = TRUE 
           AND f.prev_team_real = FALSE  THEN 1
      WHEN f."whichTeam" = TRUE 
           AND f.prev_team_real IS NULL  THEN 1  -- primo tocco reale in trasferta
      ELSE 0
    END AS is_visitor_start

  FROM flagged AS f
)

-- 3) Calcolo cumulativo dei possessi per casa e ospite
SELECT
  s.*,

  SUM(s.is_home_start) OVER (
    PARTITION BY s."GameID", s."SetNumber", s."ActionNumber"
    ORDER BY s."RilevationNumber"
    ROWS UNBOUNDED PRECEDING
  ) AS "NumPossessoHome",

  SUM(s.is_visitor_start) OVER (
    PARTITION BY s."GameID", s."SetNumber", s."ActionNumber"
    ORDER BY s."RilevationNumber"
    ROWS UNBOUNDED PRECEDING
  ) AS "NumPossessoVisitor"

FROM starts AS s;


""")
conn.commit()
print("✅ VIEW rilevations_possessi_view creata con successo")









