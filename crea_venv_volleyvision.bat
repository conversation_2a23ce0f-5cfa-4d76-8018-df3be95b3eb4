@echo off
echo Creazione dell'ambiente virtuale VolleyVisionVenv...

REM Verifica se Python è installato
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python non è installato o non è nel PATH. Installa Python 3.8 o superiore.
    exit /b 1
)

REM Rimuovi l'ambiente virtuale esistente se presente
if exist "VolleyVisionVenv" (
    echo Rimozione dell'ambiente virtuale esistente...
    rmdir /s /q VolleyVisionVenv
)

REM Crea un nuovo ambiente virtuale
echo Creazione di un nuovo ambiente virtuale...
python -m venv VolleyVisionVenv

REM Attiva l'ambiente virtuale
call VolleyVisionVenv\Scripts\activate

REM Aggiorna pip
echo Aggiornamento di pip...
python -m pip install --upgrade pip

REM Installa le dipendenze di base
echo Installazione delle dipendenze di base...
pip install ipykernel jupyter

REM Installa le dipendenze una alla volta per evitare conflitti
echo Installazione delle dipendenze principali...
pip install torch
pip install opencv-python
pip install pillow
pip install numpy
pip install matplotlib
pip install tqdm
pip install roboflow

REM Installa le dipendenze aggiuntive
echo Installazione delle dipendenze aggiuntive...
pip install albumentations
pip install pafy
pip install pandas
pip install PyYAML
pip install requests
pip install scipy
pip install seaborn
pip install thop
pip install wandb

REM Registra il kernel per Jupyter
echo Registrazione del kernel per Jupyter...
python -m ipykernel install --user --name=VolleyVisionVenv --display-name="VolleyVision"

echo.
echo Ambiente virtuale VolleyVisionVenv creato e configurato con successo!
echo.
echo Per utilizzare questo ambiente in VSCode:
echo 1. Apri il notebook VolleyVision_fix.ipynb in VSCode
echo 2. Fai clic sul selettore di kernel in alto a destra
echo 3. Seleziona "VolleyVision" dall'elenco dei kernel
echo.
echo Premi un tasto per chiudere questa finestra...
pause > nul
