
#In questo file io elaboro uno alla volta i file dvw dentro una cartella e li aggiungo uno alla volta dentro il database
#Se un file da errore, siccome ho messo dei vincoli e dei check, dovrò rimuovere il suo GameID dalla tabella games, e controllare che siano state eliminate le relative righe da Rilevations. Poi trovo qual è l'errore nel file, lo correggo manualmente nel file, e rilancio questo programma e in teoria dovrebbe essere inserito correttamente.


import os
#from readline import redisplay
import numpy as np
#import fireducks.pandas as pd
import pandas as pd
import warnings  #per gestire e mutare i FutureWarnings
import traceback   #per mostrare gli errori
#import sqlite3
#import duckdb
from sqlalchemy import create_engine, text
from sqlalchemy.types import Date
import psycopg
import datetime


eseguire_da_zero = True  #Se True droppa tutte le tabelle e ricarica tutti i file da zero. Se False, carica solo i file che non sono già dentro il database

#Per come è il programma adesso, se vuoi cancellare un game lo devi togliere da teams_each_game, e viene tolto a cascata anche in players_each_game, games, rilevations
'''
DELETE FROM teams_each_game t
WHERE t."GameID"=0
'''


# https://app.chartdb.io/d/b16063d43e7f 

Games = {
    "originalGameID": [],
    "GameID": [],
    "Date": [],
    "DateSQL": [],
    "Time": [],
    "TerritorioNeutro": [],
    "Annata": [],
    "Campionato": [],
    "Competition": [],
    "Phase": [],
    "Incontro": [],
    "Giornata": [],
    "IDHomeTeam": [],
    "HomeTeamSetWon": [],
    "HomeTeamCoach": [],
    "HomeTeamCoach2": [],
    "IDVisitorTeam": [],
    "VisitorTeamSetWon": [],
    "VisitorTeamCoach": [],
    "VisitorTeamCoach2": [],
    "Scoutman": [],
    "DurationSet1": [],
    "DurationSet2": [],
    "DurationSet3": [],
    "DurationSet4": [],
    "DurationSet5": [],
    "Spettatori": [],
    "Incasso": [],
    "Impianto": [],
    "Città": [],
    "Provincia": [],
    "url_legavolley": [],
    "VideoPath": []
}

#Come customchar metto solo 1 colonna
Rilevations = {
  "GameID": [],  #giorno mese anno IDsquadracasa
  "SetNumber": [],
  "ActionNumber": [],
  "RilevationNumber": [],  #da 1 a n quante sono le righe di df_3SCOUT_split del singolo file
  "TouchNumber": [],  #da 1 quando si batte a n quando l'azione finisce. Simboli come $$& o che iniziano con > hanno TouchNumber = 0
  "RilevationTime": [],
  "PunteggioCasa": [],
  "PunteggioOspiti": [],
  "EventiParticolari": [],  #codici che iniziano con >
  "whichTeam": [],  # 0 = Casa, 1 = Ospiti
  "whichTeamID": [],  #siccome a volte è sbagliata l'informazione di chi è in casa e chi è in trasferta, se ad un certo punto inverto la casa e la trasferta, dovrei fare un toggle di whichTeam nelle partite dove mi accorgo che è sbagliato e le inverto, ma fare un toggle è rischioso. Quindi aggiungo la colonna whichTeamID, che contiene il TeamID del team (che al momento dell'inserimento è = all'ID_auto), così mi affido a whichTeamID. Devo mettere una FK da whichTeamID di rilevations riferita a TeamID di teams_each_game. Poi metto anche un trigger che aggiorna whichTeam in base a HomeAway di teams_each_game per quel whichTeamID
  "NumeroMaglia": [],
  "Foundamental": [],
  "Type": [],
  "Eval": [],
  "SetterCall": [],
  "AttkCombination": [],
  "TargAttk": [],
  "StartZone": [],
  "EndZoneEsecZone": [],
  "EndSubzoneEsecSubzone": [],
  "SkillType": [],
  "PlayersInfo": [],
  "Special": [],
  "CustomChar": [],
  "SideoutBreakpoint": [],
  "Colonna3sez3SCOUT": [],
  "Colonna4sez3SCOUT": [],
  "Colonna5sez3SCOUT": [],  #start_coordinate (?)
  "Colonna6sez3SCOUT": [],  #mid_coordinate (?)
  "Colonna7sez3SCOUT": [],  #end_coordinate (?)
  #Siccome non so ancora chi sia VERAMENTE in casa e chi ospite, semplicemente invece di mettergli Home o Visitor, gli do dei numeri. Poi in seguito quando saprò chi è in casa e chi in trasferta li metterò in delle colonne Home e Visitor facendo dei JOIN con players_each_game su GameID, TeamID_home/TeamID_visitor e NumeroMaglia. (TeamID_home e TeamID_visitor sono delle colonne che metterò nella VIEW rilevations_view che in ogni riga, per tutta la partita, indicano l'ID della squadra di casa/trasferta)
  #PlayerX con X che va da 1 a 6 sono i presunti giocatori di casa (ovvero quelli che il file dvw dice che sono in casa). Pallegg1 è il presunto palleggiatore di casa, e Pallegg2 è il presunto palleggiatore ospite.
  "Pallegg1": [],
  "PosizPallegg1": [],
  "Pallegg2": [],
  "PosizPallegg2": [],
  "Player1": [],
  "Player2": [],
  "Player3": [],
  "Player4": [],
  "Player5": [],
  "Player6": [],
  "Player7": [],
  "Player8": [],
  "Player9": [],
  "Player10": [],
  "Player11": [],
  "Player12": [],
  #Siccome modificando chi è in casa e chi in trasferta posso modificare varie cose, ma non HomePlayerX e VisitorPlayerX, inserisco direttamente PlayerX_ID_auto al momento dell'inserimento, dandogli PlayerID_auto al momento dell'inserimento, e mettendogli una composite FK da rilevations(GameID, TeamID, PlayerX_ID_auto) riferita a players_each_game(GameID, TeamID, PlayerID_auto)
  "Player1_ID_auto": [],
  "Player2_ID_auto": [],
  "Player3_ID_auto": [],
  "Player4_ID_auto": [],
  "Player5_ID_auto": [],
  "Player6_ID_auto": [],
  "Player7_ID_auto": [],
  "Player8_ID_auto": [],
  "Player9_ID_auto": [],
  "Player10_ID_auto": [],
  "Player11_ID_auto": [],
  "Player12_ID_auto": [],
  "Pallegg1_ID_auto": [],
  "Pallegg2_ID_auto": []

}


TeamsEachGame_home = {
  "GameID": [],   #giorno mese anno IDsquadracasa
  "TeamID_auto": [],   
  "TeamID": [],    #ID della squadra (all'inizio è uguale a ID_auto)
  "TeamName": [],  #nome della squadra
  "HomeAway": [],  #0 = casa, 1 = ospiti
}

TeamsEachGame_visitor = TeamsEachGame_home.copy()


PlayersEachGame_home = {
  "GameID": [],   #giorno mese anno IDsquadracasa
  "TeamID_auto": [],   #ID automatico della squadra 
  "PlayerID_auto": [],   #ID autoincrementale della riga
  "originalPlayerID" : [],   #ID del giocatore indicato nel file .dvw
  "PlayerID": [],   #ID del giocatore (all'inizio è uguale a ID_auto)
  "Nome": [],  #nome del giocatore
  "Cognome": [],  #cognome del giocatore
  "NumeroMaglia": [],  #numero maglia del giocatore
  "Ruolo": [],  #ruolo del giocatore (se è una stringa vuota lo rimpiazzerò con 0)
  "NumAzioniDaRuolo5": [],  #numero azioni da ruolo 5
  "NumAzioniDaRuolo4": [],  #numero azioni da ruolo 4
  "NumAzioniDaRuolo3": [],  #numero azioni da ruolo 3
  "NumAzioniDaRuolo2": [],  #numero azioni da ruolo 2
  "RuoloCalc": [],  #ruolo calcolato
}
#Il TeamID (non auto) lo metterò nella VIEW players_each_game_view

PlayersEachGame_visitor = PlayersEachGame_home.copy()

'''
Players_updated = {
  "PlayerID": [],
  "Nome": [],
  "Cognome": [],
  "TeamID": [],
  "Ruolo": [],
  "NumeroMaglia": [],
  "Nazionalità": [],
  "DataNascita": [],
  "Altezza": [],
  "Peso": [],
  "Schiacciata": [],
  "Muro": [],
  "ManoDominante": [],
}
'''



#Aggiungi una funzione per controllare la versione del file


#Funzione che controlla che una data sia in formato corretto, ovvero XXXX-XX-XX con solo numeri
def is_valid_date_format(date_str):
  if not isinstance(date_str, str):
        return False
    
  if len(date_str) != 10:  #se la lunghezza non è 10
      return False
      
  if date_str[4] != '-' or date_str[7] != '-':  
      return False
  
  try:  #prova a convertire anno, mese, giorno in interi
      year = int(date_str[0:4])
      month = int(date_str[5:7])
      day = int(date_str[8:10])
          
      # Controllo range del mese (1-12)
      if month < 1 or month > 12:
          return False
          
      # Controllo range del giorno (1-31)      
      if day < 1 or day > 31:
          return False
          
      return True
      
  except ValueError:
      return False



def get_GameID_from_dvw(df):  #Estrae solo il GameID dal file DVW senza elaborare tutto il file (ovvero originalGameID)
  
    #Trovo quali righe del file sono l'inizio delle sezioni. Le trovo perchè iniziano con [
    righe_delle_sezioni = df[df.iloc[:, 0].astype(str).str.startswith('[')]

    #Metto queste righe in una lista
    row_indices = righe_delle_sezioni.index.tolist()

    df3MATCH = df.iloc[row_indices[0]+1:row_indices[1]]
    df3TEAMS = df.iloc[row_indices[1]+1:row_indices[2]]
    
    df3MATCH_split = df3MATCH.iloc[:, 0].str.split(';', expand=True)  #d'ora in poi uso come separatore ;
    df3MATCH_split.columns = ['Colonna1', 'Colonna2', 'Colonna3', 'Colonna4', 'Colonna5', 'Colonna6', 'Colonna7', 'Colonna8', 'Colonna9', 'Colonna10', 'Colonna11', 'Colonna12', 'Colonna13', 'Colonna14', 'Colonna15', 'Colonna16']

    df3TEAMS_split = df3TEAMS.iloc[:, 0].str.split(';', expand=True)
    df3TEAMS_split.columns = ['Col1', 'Col2', 'Col3', 'Col4', 'Col5', 'Col6', 'Col7', 'Col8', 'Col9', 'Col10']
    
    df3MATCH_split = df3MATCH_split.reset_index(drop=True)
    df3TEAMS_split = df3TEAMS_split.reset_index(drop=True)
    
    
    IDHomeTeam = df3TEAMS_split['Col1'].iloc[0]
    caratteri_data = df3MATCH_split['Colonna1'].iloc[0]

    #Creo il GameID come data (abbreviata) + l'ID della squadra di casa
    GameID = f'{caratteri_data[8]}{caratteri_data[9]}{caratteri_data[3]}{caratteri_data[4]}{caratteri_data[0]}{caratteri_data[1]}{IDHomeTeam}'
    
    return GameID



def elabora_file_dvw(df):
  #Trovo quali righe del file sono l'inizio delle sezioni. Le trovo perchè iniziano con [
  righe_delle_sezioni = df[df.iloc[:, 0].astype(str).str.startswith('[')]

  #Metto queste righe in una lista
  row_indices = righe_delle_sezioni.index.tolist()

  """## Creazione di un dataframe per ognuna delle 14 sezioni"""

  df3DATAVOLLEYSCOUT = df.iloc[:row_indices[0]]

  df3MATCH = df.iloc[row_indices[0]+1:row_indices[1]]

  df3TEAMS = df.iloc[row_indices[1]+1:row_indices[2]]

  df3MORE = df.iloc[row_indices[2]+1:row_indices[3]]

  #df3COMMENTS = df.iloc[row_indices[3]+1:row_indices[4]]  #non serve

  df3SET = df.iloc[row_indices[4]+1:row_indices[5]]

  df3PLAYERSH = df.iloc[row_indices[5]+1:row_indices[6]]

  df3PLAYERSV = df.iloc[row_indices[6]+1:row_indices[7]]

  df3ATTACKCOMBINATION = df.iloc[row_indices[7]+1:row_indices[8]]

  df3SETTERCALL = df.iloc[row_indices[8]+1:row_indices[9]]

  df3WINNINGSYMBOLS = df.iloc[row_indices[9]+1:row_indices[10]]

  df3RESERVE = df.iloc[row_indices[10]+1:row_indices[11]]

  df3VIDEO = df.iloc[row_indices[11]+1:row_indices[12]]

  df3SCOUT = df.iloc[row_indices[12]+1:]


  """## Creo nuovi dataframe da questi separandoli per colonne in base ai ;"""

  # Separa i valori nella prima colonna del dataframe usando il separatore ';'
  df3DATAVOLLEYSCOUT_split = df3DATAVOLLEYSCOUT.iloc[:, 0].str.split(':', n=1, expand=True)  #expand=True crea un nuovo dataframe con le colonne separate. Separa solo in base al primo ':' che incontra (n=1), questo perchè a volte l'orario è separato con '.' , altre volte con ':'

  # Assegna i nomi delle colonne alle nuove colonne create
  df3DATAVOLLEYSCOUT_split.columns = ['Colonna1', 'Colonna2']

  df3DATAVOLLEYSCOUT_split['Colonna2'].iloc[0].strip()  #forse dovrai creare un'altra funzione, oltre a elabora_file_dvw(), che elabora i file di versione 3.0

  df3MATCH_split = df3MATCH.iloc[:, 0].str.split(';', expand=True)  #d'ora in poi uso come separatore ;
  df3MATCH_split.columns = ['Colonna1', 'Colonna2', 'Colonna3', 'Colonna4', 'Colonna5', 'Colonna6', 'Colonna7', 'Colonna8', 'Colonna9', 'Colonna10', 'Colonna11', 'Colonna12', 'Colonna13', 'Colonna14', 'Colonna15', 'Colonna16']

  df3TEAMS_split = df3TEAMS.iloc[:, 0].str.split(';', expand=True)
  df3TEAMS_split.columns = ['Col1', 'Col2', 'Col3', 'Col4', 'Col5', 'Col6', 'Col7', 'Col8', 'Col9', 'Col10']

  df3MORE_split = df3MORE.iloc[:, 0].str.split(';', expand=True)
  df3MORE_split.columns = ['Col1', 'Col2', 'Col3', 'Col4', 'Col5', 'Col6', 'Col7', 'Col8', 'Col9', 'Col10', 'Col11']

  #df3COMMENTS_split = df3COMMENTS.iloc[:, 0].str.split(';', expand=True)
  #df3COMMENTS_split.columns = ['Col1', 'Col2', 'Col3', 'Col4', 'Col5']

  #df3COMMENTS_split

  df3SET_split = df3SET.iloc[:, 0].str.split(';', expand=True)
  df3SET_split.columns = ['Col1', 'Col2', 'Col3', 'Col4', 'Col5', 'Col6', 'Col7']

  df3PLAYERSH_split = df3PLAYERSH.iloc[:, 0].str.split(';', expand=True)
  df3PLAYERSH_split.columns = ['Col1', 'Col2', 'Col3', 'Col4', 'Col5', 'Col6', 'Col7', 'Col8', 'Col9', 'Col10', 'Col11', 'Col12', 'Col13', 'Col14', 'Col15', 'Col16', 'Col17', 'Col18', 'Col19', 'Col20', 'Col21', 'Col22', 'Col23']

  df3PLAYERSV_split = df3PLAYERSV.iloc[:, 0].str.split(';', expand=True)
  df3PLAYERSV_split.columns = ['Col1', 'Col2', 'Col3', 'Col4', 'Col5', 'Col6', 'Col7', 'Col8', 'Col9', 'Col10', 'Col11', 'Col12', 'Col13', 'Col14', 'Col15', 'Col16', 'Col17', 'Col18', 'Col19', 'Col20', 'Col21', 'Col22', 'Col23']

  df3ATTACKCOMBINATION_split = df3ATTACKCOMBINATION.iloc[:, 0].str.split(';', expand=True)
  df3ATTACKCOMBINATION_split.columns = ['Col1', 'Col2', 'Col3', 'Col4', 'Col5', 'Col6', 'Col7', 'Col8', 'Col9', 'Col10', 'Col11']

  df3SETTERCALL_split = df3SETTERCALL.iloc[:, 0].str.split(';', expand=True)
  df3SETTERCALL_split.columns = ['Col1', 'Col2', 'Col3', 'Col4', 'Col5', 'Col6', 'Col7', 'Col8', 'Col9', 'Col10', 'Col11']

  df3WINNINGSYMBOLS_split = df3WINNINGSYMBOLS.iloc[:, 0].str.split(';', expand=True)
  df3WINNINGSYMBOLS_split.columns = ['Col1']

  df3RESERVE_split = df3RESERVE.iloc[:, 0].str.split(';', expand=True)
  df3RESERVE_split.columns = []

  df3VIDEO_split = df3VIDEO.iloc[:, 0].str.split(';', expand=True)
  df3VIDEO_split.columns = ['Col1']

  df3SCOUT_split = df3SCOUT.iloc[:, 0].str.split(';', expand=True)
  df3SCOUT_split.columns = ['Col1', 'Col2', 'Col3', 'Col4', 'Col5', 'Col6', 'Col7', 'Col8', 'Col9', 'Col10', 'Col11', 'Col12', 'Col13', 'Col14', 'Col15', 'Col16', 'Col17', 'Col18', 'Col19', 'Col20', 'Col21', 'Col22', 'Col23', 'Col24', 'Col25', 'Col26', 'Col27']

  # Reset index for all dataframes. Faccio in modo che gli indici partano tutti da zero
  #È necessario altrimenti se provo a copiare alcuni valori di questo dataset a un altro li copia solo in base all'indice, e siccome qui gli indici partono da oltre 100 sarebbero diversi con un nuovo dataset.
  df3DATAVOLLEYSCOUT_split = df3DATAVOLLEYSCOUT_split.reset_index(drop=True)
  df3MATCH_split = df3MATCH_split.reset_index(drop=True)
  df3TEAMS_split = df3TEAMS_split.reset_index(drop=True)
  df3MORE_split = df3MORE_split.reset_index(drop=True)
  #df3COMMENTS_split = df3COMMENTS_split.reset_index(drop=True)
  df3SET_split = df3SET_split.reset_index(drop=True)
  df3PLAYERSH_split = df3PLAYERSH_split.reset_index(drop=True)
  df3PLAYERSV_split = df3PLAYERSV_split.reset_index(drop=True)
  df3ATTACKCOMBINATION_split = df3ATTACKCOMBINATION_split.reset_index(drop=True)
  df3SETTERCALL_split = df3SETTERCALL_split.reset_index(drop=True)
  df3WINNINGSYMBOLS_split = df3WINNINGSYMBOLS_split.reset_index(drop=True)
  df3RESERVE_split = df3RESERVE_split.reset_index(drop=True)
  df3VIDEO_split = df3VIDEO_split.reset_index(drop=True)
  df3SCOUT_split = df3SCOUT_split.reset_index(drop=True)
  
  
  
  #Ora è il momento di iniziare a popolare i dataframe.
  #Metto i valori a tutte le colonne, tranne a ID_auto, Team_ID, GameID, PlayerID, perchè il valore di queste colonne lo conoscerò (lo calcolerò) dopo aver inserito il primo team in teams_each_game, e aver ottenuto il suo ID_auto.
  

  """## Creo il dataframe df_rilevations che andrà inserito nella tabella rilevation"""

  df_rilevations = pd.DataFrame(Rilevations)

  originalIDHomeTeam = df3TEAMS_split['Col1'].iloc[0]
  caratteri_data = df3MATCH_split['Colonna1'].iloc[0]

  #Creo il GameID come data (abbreviata) + l'ID della squadra di casa
  originalGameID = f'{caratteri_data[8]}{caratteri_data[9]}{caratteri_data[3]}{caratteri_data[4]}{caratteri_data[0]}{caratteri_data[1]}{originalIDHomeTeam}'
  #i primi 6 caratteri sono la data della gara: Anno Mese Giorno
  #il carattere dal settimo al decimo è l'ID della squadra.
  #In questo modo, trattando gli ID delle squadre come numeri, anche i GameID potranno essere trattati come numeri, e mettendo l'anno davanti potrò ordinarli dal più piccolo al più grande, formando un ordine cronologico.
  #Inoltre in questo modo i GameID non inizieranno mai con uno 0, almeno fino al 2100.

  #Bisogna solo stare attenti che in un file la data non sia indicata senza zeri, ad esempio 6/8/2024, in quel caso mi finisce nella stringa GameID dei caratteri /
  #Quindi devo sempre verificare che i GameID siano lunghi 10 caratteri e che non contengano / o - o . 
  
  #Se capita che una squadra giochi in casa due partite nello stesso giorno, modifico il file e metto giorni diversi


  #La colonna SetNumber è la colonna Col9 di df3SCOUT_split
  df_rilevations['SetNumber'] = df3SCOUT_split['Col9']

  #POPOLO LA COLONNA ActionNumber
  #Dopo che è stato aggiornato il punteggio, aumenta il numero dell'azione. Ovvero nella riga dopo p, deve aumentare ActionNumber
  #Voglio che p abbia lo stesso ActionNumber dei tocchi a cui fa riferimento.

  my_list = []
  action = 1

  for i, row in df3SCOUT_split.iterrows():  #per ogni indice e rispettiva riga del dataframe df3SCOUT_split.iterrows
    try:
      if row['Col1'][0] == '*' and row['Col1'][1] == '*':  #se la colonna1 inizia con **, e quindi è finito un set, inizializza di nuovo le azioni da 1
        my_list.append(action)
        action = 1
      elif len(row['Col1']) <5:  #se la prima colonna ha meno di 5 caratteri metto l'attuale valore di action. (se andasse al prossimo elif darebbe errore, non riuscirebbe ad accedere al carattere 4)
        my_list.append(action)
      elif row['Col1'][1] == 'p':  #Se nella prima colonna il carattere in posizione 1 è p (punteggio) allora significa che il punteggio è stato aggiornato, quindi aumentiamo il numero dell'azione di 1
        my_list.append(action)
        action += 1              #Lo aggiorno dopo, così dal prossimo tocco ha ActionNumber aggiornato, aumentato di 1
      else:
        my_list.append(action)

    except (IndexError, TypeError):  # Handle other cases
      print(f"WARNING (ActionNumber): Skipping row {i} of the df3SCOUT_split dataframe due to an error")

  df_rilevations['ActionNumber'] = my_list  #aggiungo i valori alla colonna ActionNumber

  # my_list

  #POPOLO LA COLONNA RilevationNumber
  df_rilevations['RilevationNumber'] = df_rilevations.index

  #POPOLO LA COLONNA TouchNumber
  #Questa colonna probabilmente mi servirà solo per contare i tocchi medi di un'azione in una partita o in un anno, ad esempio confrontando vari anni, e per vedere l'azione più lunga in ogni anno.

  my_list = []
  action = 1
  touch = 1

  for i, row in df3SCOUT_split.iterrows():
    try:
      if (row['Col1'][0] == '*' and row['Col1'][1] == '*') or (row['Col1'][1] == 'z') or (row['Col1'][1] == 'p') or (row['Col1'][1] == 'P') or (row['Col1'][1] == 'c') or (row['Col1'][1] == 'T'):  #se la colonna1 inizia con **, o se il secondo carattere è z oppure p oppure P oppure c oppure T
        my_list.append(None)  #metto None (None come TouchNumber)
        touch = 0  #lo metto a zero così dopo questa iterazione va a 1 (tanto se ci sono questi caratteri l'azione è finita)
      elif row['Col1'][1] == '$' and row['Col1'][2] == '$' and row['Col1'][3] == '&':  #se il secondo,terzo,quarto carattere sono $$&, allora è uno di quei tocchi di fine azione non attribuiti a nessuno. (se fosse un tocco senza maglia, ma di cui si sa il fondamentale, al posto della & ci sarebbe il fondamentale, e per quei casi gli contiamo il tocco)
        my_list.append(0)  #Metto tocco zero, ma non resetto il valore del tocco
      elif row['Col1'][0] == '>':  #se il primo carattere è > allora metto None, ma non aggiorno il valore del tocco.
        my_list.append(0)  #Metto tocco zero, ma non resetto il valore del tocco
      else:
        touch += 1
        my_list.append(touch)

    except (IndexError, TypeError):  # Handle other cases
      print(f"WARNING (TouchNumber): Skipping row {i} of the df3SCOUT_split dataframe due to an error")


  #eventualmente metti 0 anche ai codici che iniziano con >. No! perchè a volte i videocheck sono in mezzo ad un'azione, e se gli metto 0 dopo il TouchNumber riparte da 0.

  df_rilevations['TouchNumber'] = my_list  #aggiungo i valori alla colonna TouchNumber

  # my_list

  #POPOLO LA COLONNA RilevationTime
  #La colonna RilevationTime è la colonna Col8 di df3SCOUT_split
  df_rilevations['RilevationTime'] = df3SCOUT_split['Col8']

  #POPOLO LE COLONNE PunteggioCasa E PunteggioOspiti

  lista_punteggio_casa = []
  lista_punteggio_ospiti = []
  punteggio_casa = 0
  punteggio_ospiti = 0

  for i, row in df3SCOUT_split.iterrows():
    if len(row['Col1']) <5:  #se la prima colonna ha meno di 5 caratteri metto l'attuale valore di Punteggio. (se andasse al prossimo elif darebbe errore, non riuscirebbe ad accedere al carattere 4)
      lista_punteggio_casa.append(punteggio_casa)
      lista_punteggio_ospiti.append(punteggio_ospiti)
    elif (row['Col1'][3] == '>' and row['Col1'][4] == 'L' and row['Col1'][5] == 'U' and row['Col1'][6] == 'p') or (row['Col1'][4] == '>' and row['Col1'][5] == 'L' and row['Col1'][6] == 'U' and row['Col1'][7] == 'p'):  #se i caratteri 3,4,5,6 oppure 4,5,6,7 sono >LUP, allora siamo a inizio set, quindi imposto i punteggi a 0
      punteggio_casa = 0
      punteggio_ospiti = 0
      lista_punteggio_casa.append(punteggio_casa)
      lista_punteggio_ospiti.append(punteggio_ospiti)
    elif (row['Col1'][0] == '*' or row['Col1'][0] == 'a') and row['Col1'][1] == 'p':  #Se la prima colonna inizia con *p o ap allora significa che il punteggio è stato aggiornato, quindi aggiorniamo il valore dei punteggi prendendoli dalla stringa
      stringaPunteggioCasa = row['Col1'][2:4]  #i due caratteri del punteggio di casa sono in posizione 2 e 3 della stringa
      stringaPunteggioOspiti = row['Col1'][5:7]
      try:  #Provo a convertire la stringa inintero, se non è possibile rimetto il punteggio attuale senza aggiornarlo (casi come *pET:RIE o ... non aggiornano il punteggio)
        punteggio_casa = int(stringaPunteggioCasa)
        punteggio_ospiti = int(stringaPunteggioOspiti)
        lista_punteggio_casa.append(punteggio_casa)
        lista_punteggio_ospiti.append(punteggio_ospiti)
      except ValueError:  #aggiungo i punteggi che c'erano prima di questo codice, perchè probabilmente questo codice non li ha aggiornati (ad esempio se il codice è *pRIPETE, metto il punteggio che c'era prima)
        lista_punteggio_casa.append(punteggio_casa)
        lista_punteggio_ospiti.append(punteggio_ospiti)
        print(f"WARNING (PunteggioCasa e PunteggioOspiti): Skipping row {i} of the df3SCOUT_split dataframe due to an error. {row['Col1']}")
    else:  #i tutti gli altri casi, ad esempio nei fondamentali, nei codici automatici, o nei codici che iniziano con >
      lista_punteggio_casa.append(punteggio_casa)
      lista_punteggio_ospiti.append(punteggio_ospiti)

  df_rilevations['PunteggioCasa'] = lista_punteggio_casa
  df_rilevations['PunteggioOspiti'] = lista_punteggio_ospiti



  #POPOLO LA COLONNA EventiParticolari
  #Sono i codici che iniziano con > (es >CONTESA, >CHECK, >RED...)

  my_list = []

  for i, row in df3SCOUT_split.iterrows():
    try:
      if row['Col1'][0] == '>':  #se la colonna1 inizia con >
        my_list.append(row['Col1'])
      else:
        my_list.append(None)
    except (IndexError, TypeError):  # Handle other cases
      print(f"WARNING (EventiParticolari): Skipping row {i} of the df3SCOUT_split dataframe due to an error")

  df_rilevations['EventiParticolari'] = my_list  #aggiungo i valori alla colonna TouchNumber

  #print(len(my_list))
  #df_rilevations.shape

  #my_list

  #df_rilevations['whichTeam'] = np.nan  # Resetta la colonna

  #POPOLO LA COLONNA whichTeam
  #Nella colonna whichTeam, metto 0 se il primo carattere di Col1 nel dataframe df3SCOUT_split è *, se invece è a metto 1
  df_rilevations['whichTeam'] = df3SCOUT_split["Col1"].astype(str).str[0].apply(lambda x: 1 if x == "a" else (0 if x == "*" else np.nan))
  
  #POPOLO LA COLONNA whichTeamID
  #Per adesso è NULL, saprò il suo valore quando inserisco i dati in teams_each_game
  df_rilevations['whichTeamID'] = None

  #POPOLO LA COLONNA NumeroMaglia

  my_list = []

  for i, row in df3SCOUT_split.iterrows():
    if len(row['Col1']) <5:  #se la prima colonna ha meno di 5 caratteri metto None. (se andasse al prossimo elif darebbe errore)
      my_list.append(None)
    elif (row['Col1'][0] == '*' and row['Col1'][1] == '*') or (row['Col1'][1] == 'z') or (row['Col1'][1] == 'p') or (row['Col1'][1] == 'P') or (row['Col1'][1] == 'c') or (row['Col1'][1] == 'T'):  #se la colonna1 inizia con **, o se il secondo carattere è z oppure p oppure P oppure c oppure T
      my_list.append(None)  #metto None
    elif row['Col1'][1] == '$' and row['Col1'][2] == '$':  #se il secondo,terzo carattere sono $$, allora non so il numero di maglia
      my_list.append(0)  #Metto numero di maglia zero
    else:
      stringaNumeroMaglia = row['Col1'][1:3]  #i due caratteri del numero maglia sono in posizione 1 e 2 della stringa
      try:  #Provo a convertire la stringa inintero, se non è possibile metto None e stampo la riga per vedere cos'era (es >CONTESA)
          NumeroMaglia = int(stringaNumeroMaglia)
      except ValueError:
          NumeroMaglia = None #or any other suitable value in this case
          print(f"WARNING (NumeroMaglia): Skipping row {i} of the df3SCOUT_split dataframe due to an error in {file_path}. {row['Col1']}")
      my_list.append(NumeroMaglia)

  df_rilevations['NumeroMaglia'] = my_list

  # my_list

  #POPOLO LA COLONNA Foundamental (metto il fondamentale se S,R,E,A,B,D,F,&, altrimenti se è un codice automatico metto la sua seconda lettera, quindi z,p,P,c,T,*)

  my_list = []

  for i, row in df3SCOUT_split.iterrows():
    try:
      if (row['Col1'][0] == '*' and row['Col1'][1] == '*') or (row['Col1'][1] == 'z') or (row['Col1'][1] == 'p') or (row['Col1'][1] == 'P') or (row['Col1'][1] == 'c') or (row['Col1'][1] == 'T'):  #se la colonna1 inizia con **, o se il secondo carattere è z oppure p oppure P oppure c oppure T
        Foundamental = row['Col1'][1]  #prendo come Fondamentale il carattere 1
        my_list.append(Foundamental)
      elif (row['Col1'][0] == '*' or row['Col1'][0] == 'a'): #se la riga inizia con * o con a
        Foundamental = row['Col1'][3]  #prendo come Fondamentale il carattere 3
        my_list.append(Foundamental)
      #TOGLI/COMMENTA QUESTO ELIF SE VUOI I WARNING PER >
      elif row['Col1'][0] == '>': #se la riga inizia con >
        my_list.append(None)
      else:
        my_list.append(None)
        print(row['Col1'], "   ", file_path)

    except (IndexError, TypeError):  # Handle other cases
      print(f"WARNING (Foundamental): Skipping row {i} of the df3SCOUT_split dataframe due to an error in {file_path}. {row['Col1']}")

  df_rilevations['Foundamental'] = my_list

  # my_list

  #POPOLO LA COLONNA Type (solo per i tocchi e $$&)

  my_list = []

  for i, row in df3SCOUT_split.iterrows():
    try:
      if (row['Col1'][0] == '*' and row['Col1'][1] == '*') or (row['Col1'][1] == 'z') or (row['Col1'][1] == 'p') or (row['Col1'][1] == 'P') or (row['Col1'][1] == 'c') or (row['Col1'][1] == 'T'):  #se la colonna1 inizia con **, o se il secondo carattere è z oppure p oppure P oppure c oppure T
        my_list.append(None)
      elif (row['Col1'][0] == '*' or row['Col1'][0] == 'a'): #se la riga inizia con * o con a
        Type = row['Col1'][4]  #prendo come Type il carattere 4
        my_list.append(Type)
      #TOGLI/COMMENTA QUESTO ELIF SE VUOI I WARNING PER >
      elif row['Col1'][0] == '>': #se la riga inizia con >
        my_list.append(None)
      else:
        my_list.append(None)
        print(row['Col1'], "   ", file_path)

    except (IndexError, TypeError):  # Handle other cases
      print(f"WARNING (Type): Skipping row {i} of the df3SCOUT_split dataframe due to an error in {file_path}. {row['Col1']}")

  df_rilevations['Type'] = my_list

  # my_list

  #POPOLO LA COLONNA Eval (solo per i tocchi e $$&)

  my_list = []

  for i, row in df3SCOUT_split.iterrows():
    try:
      if (row['Col1'][0] == '*' and row['Col1'][1] == '*') or (row['Col1'][1] == 'z') or (row['Col1'][1] == 'p') or (row['Col1'][1] == 'P') or (row['Col1'][1] == 'c') or (row['Col1'][1] == 'T'):  #se la colonna1 inizia con **, o se il secondo carattere è z oppure p oppure P oppure c oppure T
        my_list.append(None)
      elif (row['Col1'][0] == '*' or row['Col1'][0] == 'a'): #se la riga inizia con * o con a
        Eval = row['Col1'][5]  #prendo come Eval il carattere 4
        my_list.append(Eval)
      #TOGLI/COMMENTA QUESTO ELIF SE VUOI I WARNING PER >
      elif row['Col1'][0] == '>': #se la riga inizia con >
        my_list.append(None)
      else:
        my_list.append(None)
        print(row['Col1'], "   ", file_path)

    except (IndexError, TypeError):  # Handle other cases
      print(f"WARNING (Eval): Skipping row {i} of the df3SCOUT_split dataframe due to an error in {file_path}. {row['Col1']}")

  df_rilevations['Eval'] = my_list

  # my_list

  #POPOLO LA COLONNA SetterCall (solo per le alzate)

  my_list = []

  for i, row in df3SCOUT_split.iterrows():
    try:
      if (row['Col1'][0] == '*' and row['Col1'][1] == '*') or (row['Col1'][1] == 'z') or (row['Col1'][1] == 'p') or (row['Col1'][1] == 'P') or (row['Col1'][1] == 'c') or (row['Col1'][1] == 'T'):  #se la colonna1 inizia con **, o se il secondo carattere è z oppure p oppure P oppure c oppure T
        my_list.append(None)
      elif (row['Col1'][0] == '*' or row['Col1'][0] == 'a') and row['Col1'][3] == 'E':  #se la riga è un'alzata (E)
        StringaSetterCall = row['Col1'][6:8]
        my_list.append(StringaSetterCall)
      #TOGLI/COMMENTA QUESTO ELIF SE VUOI I WARNING PER >
      elif row['Col1'][0] == '>': #se la riga inizia con >
        my_list.append(None)
      else:
        my_list.append(None)

    except (IndexError, TypeError):  # Handle other cases
      print(f"WARNING (SetterCall): Skipping row {i} of the df3SCOUT_split dataframe due to an error in {file_path}. {row['Col1']}")


  df_rilevations['SetterCall'] = my_list

  # my_list

  #POPOLO LA COLONNA AttkCombination (solo per gli attacchi)

  my_list = []

  for i, row in df3SCOUT_split.iterrows():
    try:
      if (row['Col1'][0] == '*' and row['Col1'][1] == '*') or (row['Col1'][1] == 'z') or (row['Col1'][1] == 'p') or (row['Col1'][1] == 'P') or (row['Col1'][1] == 'c') or (row['Col1'][1] == 'T'):  #se la colonna1 inizia con **, o se il secondo carattere è z oppure p oppure P oppure c oppure T
        my_list.append(None)
      elif (row['Col1'][0] == '*' or row['Col1'][0] == 'a') and row['Col1'][3] == 'A':  #se la riga è un attacco (A)
        StringaAttkCombination = row['Col1'][6:8]
        my_list.append(StringaAttkCombination)
      #TOGLI/COMMENTA QUESTO ELIF SE VUOI I WARNING PER >
      elif row['Col1'][0] == '>': #se la riga inizia con >
        my_list.append(None)
      else:
        my_list.append(None)

    except (IndexError, TypeError):  # Handle other cases
      print(f"WARNING (AttkCombination): Skipping row {i} of the df3SCOUT_split dataframe due to an error in {file_path}. {row['Col1']}")


  df_rilevations['AttkCombination'] = my_list

  # my_list

  #POPOLO LA COLONNA TargAttk (solo per i tocchi e $$&)

  my_list = []

  for i, row in df3SCOUT_split.iterrows():
    try:
      if (row['Col1'][0] == '*' and row['Col1'][1] == '*') or (row['Col1'][1] == 'z') or (row['Col1'][1] == 'p') or (row['Col1'][1] == 'P') or (row['Col1'][1] == 'c') or (row['Col1'][1] == 'T'):  #se la colonna1 inizia con **, o se il secondo carattere è z oppure p oppure P oppure c oppure T
        my_list.append(None)
      elif len(row['Col1']) <9:  #se la prima colonna ha meno di 9 caratteri metto None.
        my_list.append(None)
      elif row['Col1'][8] == '~':  #se TargAttk è ~ allora metto None
        my_list.append(None)
      elif (row['Col1'][0] == '*' or row['Col1'][0] == 'a'): #se la riga inizia con * o con a
        TargAttk = row['Col1'][8]  #prendo come TargAttk il carattere 8
        my_list.append(TargAttk)
      #TOGLI/COMMENTA QUESTO ELIF SE VUOI I WARNING PER >
      elif row['Col1'][0] == '>': #se la riga inizia con >
        my_list.append(None)
      else:
        my_list.append(None)
        print(row['Col1'], "   ", file_path)
    except (IndexError, TypeError):  # Handle other cases
      print(f"WARNING (TargAttk): Skipping row {i} of the df3SCOUT_split dataframe due to an error in {file_path}. {row['Col1']}")


  df_rilevations['TargAttk'] = my_list

  # my_list

  #POPOLO LA COLONNA StartZone (solo per i tocchi e $$&)

  my_list = []

  for i, row in df3SCOUT_split.iterrows():
    try:
      if (row['Col1'][0] == '*' and row['Col1'][1] == '*') or (row['Col1'][1] == 'z') or (row['Col1'][1] == 'p') or (row['Col1'][1] == 'P') or (row['Col1'][1] == 'c') or (row['Col1'][1] == 'T'):  #se la colonna1 inizia con **, o se il secondo carattere è z oppure p oppure P oppure c oppure T
        my_list.append(None)
      elif len(row['Col1']) <10:  #se la prima colonna ha meno di 10 caratteri metto None.
        my_list.append(None)
      elif row['Col1'][9] == '~':  #se StartZone è ~ allora metto None
        my_list.append(None)
      elif (row['Col1'][0] == '*' or row['Col1'][0] == 'a'): #se la riga inizia con * o con a
        StartZone = row['Col1'][9]  #prendo come StartZone il carattere 9
        my_list.append(StartZone)
      #TOGLI/COMMENTA QUESTO ELIF SE VUOI I WARNING PER >
      elif row['Col1'][0] == '>': #se la riga inizia con >
        my_list.append(None)
      else:
        my_list.append(None)
        print(row['Col1'], "   ", file_path)
    except (IndexError, TypeError):  # Handle other cases
      print(f"WARNING (StartZone): Skipping row {i} of the df3SCOUT_split dataframe due to an error in {file_path}. {row['Col1']}")


  df_rilevations['StartZone'] = my_list

  # my_list

  #POPOLO LA COLONNA EndZoneEsecZone (solo per i tocchi e $$&)

  my_list = []

  for i, row in df3SCOUT_split.iterrows():
    try:
      if (row['Col1'][0] == '*' and row['Col1'][1] == '*') or (row['Col1'][1] == 'z') or (row['Col1'][1] == 'p') or (row['Col1'][1] == 'P') or (row['Col1'][1] == 'c') or (row['Col1'][1] == 'T'):  #se la colonna1 inizia con **, o se il secondo carattere è z oppure p oppure P oppure c oppure T
        my_list.append(None)
      elif len(row['Col1']) <11:  #se la prima colonna ha meno di 11 caratteri metto None.
        my_list.append(None)
      elif row['Col1'][10] == '~':  #se EndZoneEsecZone è ~ allora metto None
        my_list.append(None)
      elif (row['Col1'][0] == '*' or row['Col1'][0] == 'a'): #se la riga inizia con * o con a
        EndZoneEsecZone = row['Col1'][10]  #prendo come EndZoneEsecZone il carattere 10
        my_list.append(EndZoneEsecZone)
      #TOGLI/COMMENTA QUESTO ELIF SE VUOI I WARNING PER >
      elif row['Col1'][0] == '>': #se la riga inizia con >
        my_list.append(None)
      else:
        my_list.append(None)
        print(row['Col1'], "   ", file_path)
    except (IndexError, TypeError):  # Handle other cases
      print(f"WARNING (EndZoneEsecZone): Skipping row {i} of the df3SCOUT_split dataframe due to an error in {file_path}. {row['Col1']}")


  df_rilevations['EndZoneEsecZone'] = my_list

  # my_list

  #POPOLO LA COLONNA EndSubzoneEsecSubzone (solo per i tocchi e $$&)

  my_list = []

  for i, row in df3SCOUT_split.iterrows():
    try:
      if (row['Col1'][0] == '*' and row['Col1'][1] == '*') or (row['Col1'][1] == 'z') or (row['Col1'][1] == 'p') or (row['Col1'][1] == 'P') or (row['Col1'][1] == 'c') or (row['Col1'][1] == 'T'):  #se la colonna1 inizia con **, o se il secondo carattere è z oppure p oppure P oppure c oppure T
        my_list.append(None)
      elif len(row['Col1']) <12:  #se la prima colonna ha meno di 12 caratteri metto None.
        my_list.append(None)
      elif row['Col1'][11] == '~':  #se EndSubzoneEsecSubzone è ~ allora metto None
        my_list.append(None)
      elif (row['Col1'][0] == '*' or row['Col1'][0] == 'a'): #se la riga inizia con * o con a
        EndSubzoneEsecSubzone = row['Col1'][11]  #prendo come EndSubzoneEsecSubzone il carattere 11
        my_list.append(EndSubzoneEsecSubzone)
      #TOGLI/COMMENTA QUESTO ELIF SE VUOI I WARNING PER >
      elif row['Col1'][0] == '>': #se la riga inizia con >
        my_list.append(None)
      else:
        my_list.append(None)
        print(row['Col1'], "   ", file_path)
    except (IndexError, TypeError):  # Handle other cases
      print(f"WARNING (EndSubzoneEsecSubzone): Skipping row {i} of the df3SCOUT_split dataframe due to an error in {file_path}. {row['Col1']}")


  df_rilevations['EndSubzoneEsecSubzone'] = my_list

  # my_list

  #POPOLO LA COLONNA SkillType (solo per i tocchi e $$&)

  my_list = []

  for i, row in df3SCOUT_split.iterrows():
    try:
      if (row['Col1'][0] == '*' and row['Col1'][1] == '*') or (row['Col1'][1] == 'z') or (row['Col1'][1] == 'p') or (row['Col1'][1] == 'P') or (row['Col1'][1] == 'c') or (row['Col1'][1] == 'T'):  #se la colonna1 inizia con **, o se il secondo carattere è z oppure p oppure P oppure c oppure T
        my_list.append(None)
      elif len(row['Col1']) <13:  #se la prima colonna ha meno di 13 caratteri metto None.
        my_list.append(None)
      elif row['Col1'][12] == '~':  #se SkillType è ~ allora metto None
        my_list.append(None)
      elif (row['Col1'][0] == '*' or row['Col1'][0] == 'a'): #se la riga inizia con * o con a
        SkillType = row['Col1'][12]  #prendo come SkillType il carattere 12
        my_list.append(SkillType)
      #TOGLI/COMMENTA QUESTO ELIF SE VUOI I WARNING PER >
      elif row['Col1'][0] == '>': #se la riga inizia con >
        my_list.append(None)
      else:
        my_list.append(None)
        print(row['Col1'], "   ", file_path)
    except (IndexError, TypeError):  # Handle other cases
      print(f"WARNING (SkillType): Skipping row {i} of the df3SCOUT_split dataframe due to an error in {file_path}. {row['Col1']}")


  df_rilevations['SkillType'] = my_list

  # my_list

  #POPOLO LA COLONNA PlayersInfo (solo per i tocchi e $$&)

  my_list = []

  for i, row in df3SCOUT_split.iterrows():
    try:
      if (row['Col1'][0] == '*' and row['Col1'][1] == '*') or (row['Col1'][1] == 'z') or (row['Col1'][1] == 'p') or (row['Col1'][1] == 'P') or (row['Col1'][1] == 'c') or (row['Col1'][1] == 'T'):  #se la colonna1 inizia con **, o se il secondo carattere è z oppure p oppure P oppure c oppure T
        my_list.append(None)
      elif len(row['Col1']) <14:  #se la prima colonna ha meno di 14 caratteri metto None.
        my_list.append(None)
      elif row['Col1'][13] == '~':  #se PlayersInfo è ~ allora metto None
        my_list.append(None)
      elif (row['Col1'][0] == '*' or row['Col1'][0] == 'a'): #se la riga inizia con * o con a
        PlayersInfo = row['Col1'][13]  #prendo come PlayersInfo il carattere 13
        my_list.append(PlayersInfo)
      #TOGLI/COMMENTA QUESTO ELIF SE VUOI I WARNING PER >
      elif row['Col1'][0] == '>': #se la riga inizia con >
        my_list.append(None)
      else:
        my_list.append(None)
        print(row['Col1'], "   ", file_path)
    except (IndexError, TypeError):  # Handle other cases
      print(f"WARNING (PlayersInfo): Skipping row {i} of the df3SCOUT_split dataframe due to an error in {file_path}. {row['Col1']}")


  df_rilevations['PlayersInfo'] = my_list

  # my_list

  #POPOLO LA COLONNA Special (solo per i tocchi e $$&)

  my_list = []

  for i, row in df3SCOUT_split.iterrows():
    try:
      if (row['Col1'][0] == '*' and row['Col1'][1] == '*') or (row['Col1'][1] == 'z') or (row['Col1'][1] == 'p') or (row['Col1'][1] == 'P') or (row['Col1'][1] == 'c') or (row['Col1'][1] == 'T'):  #se la colonna1 inizia con **, o se il secondo carattere è z oppure p oppure P oppure c oppure T
        my_list.append(None)
      elif len(row['Col1']) <15:  #se la prima colonna ha meno di 15 caratteri metto None.
        my_list.append(None)
      elif row['Col1'][14] == '~':  #se Special è ~ allora metto None
        my_list.append(None)
      elif (row['Col1'][0] == '*' or row['Col1'][0] == 'a'): #se la riga inizia con * o con a
        Special = row['Col1'][14]  #prendo come Special il carattere 14
        my_list.append(Special)
      #TOGLI/COMMENTA QUESTO ELIF SE VUOI I WARNING PER >
      elif row['Col1'][0] == '>': #se la riga inizia con >
        my_list.append(None)
      else:
        my_list.append(None)
        print(row['Col1'], "   ", file_path)
    except (IndexError, TypeError):  # Handle other cases
      print(f"WARNING (Special): Skipping row {i} of the df3SCOUT_split dataframe due to an error in {file_path}. {row['Col1']}")


  df_rilevations['Special'] = my_list

  # my_list

  #POPOLO LA COLONNA CustomChar (è una stringa di massimo 5 caratteri) (solo per i tocchi e $$&)

  my_list = []

  for i, row in df3SCOUT_split.iterrows():
    try:
      if (row['Col1'][0] == '*' and row['Col1'][1] == '*') or (row['Col1'][1] == 'z') or (row['Col1'][1] == 'p') or (row['Col1'][1] == 'P') or (row['Col1'][1] == 'c') or (row['Col1'][1] == 'T'):  #se la colonna1 inizia con **, o se il secondo carattere è z oppure p oppure P oppure c oppure T
        my_list.append(None)
      elif len(row['Col1']) <16:  #se la prima colonna ha meno di 16 caratteri metto None.
        my_list.append(None)
      elif (row['Col1'][0] == '*' or row['Col1'][0] == 'a'): #se la riga inizia con * o con a
        CustomChar = row['Col1'][15:]  #prendo come CustomChar1 il carattere 15
        my_list.append(CustomChar)
      #TOGLI/COMMENTA QUESTO ELIF SE VUOI I WARNING PER >
      elif row['Col1'][0] == '>': #se la riga inizia con >
        my_list.append(None)
      else:
        my_list.append(None)
        print(row['Col1'], "   ", file_path)
    except (IndexError, TypeError):  # Handle other cases
      print(f"WARNING (CustomChar): Skipping row {i} of the df3SCOUT_split dataframe due to an error in {file_path}. {row['Col1']}")

  df_rilevations['CustomChar'] = my_list

  # my_list


  #POPOLO LA COLONNA SideoutBreakpoint
  df_rilevations['SideoutBreakpoint'] = df3SCOUT_split['Col2'].apply(lambda x: None if str(x).strip() == '' else str(x).strip())  #per ogni riga x della Series, applica la funzione lambda. Se la riga convertita in stringa e strippata è '', allora metti None, altrimenti metti la stringa strippata
  
  #POPOLO LA COLONNA Colonna3sez3SCOUT
  df_rilevations['Colonna3sez3SCOUT'] = df3SCOUT_split['Col3'].apply(lambda x: None if str(x).strip() == '' else str(x).strip())
  
  #POPOLO LA COLONNA Colonna4sez3SCOUT
  df_rilevations['Colonna4sez3SCOUT'] = df3SCOUT_split['Col4'].apply(lambda x: None if str(x).strip() == '' else str(x).strip())
  
  #POPOLO LA COLONNA Colonna5sez3SCOUT
  df_rilevations['Colonna5sez3SCOUT'] = df3SCOUT_split['Col5'].apply(lambda x: None if str(x).strip() == '' else str(x).strip())
  
  #POPOLO LA COLONNA Colonna6sez3SCOUT
  df_rilevations['Colonna6sez3SCOUT'] = df3SCOUT_split['Col6'].apply(lambda x: None if str(x).strip() == '' else str(x).strip())
  
  #POPOLO LA COLONNA Colonna7sez3SCOUT
  df_rilevations['Colonna7sez3SCOUT'] = df3SCOUT_split['Col7'].apply(lambda x: None if str(x).strip() == '' else str(x).strip())
  

  #POPOLO LA COLONNA Pallegg1
  #Il codice automatico P, a inizio set, con >LUp, riporta il numero di maglia con lo zero davanti, quindi se il numero è 4 riporta 04          (es aP06>LUp)
  #Il codice automatico P, in tutte le altre parti, riporta il numero di maglia senza zeri davanti, quindi se il numero è 4 riporta 4, non 04   (es *P4)

  palleggiatore_casa_attuale = 0
  my_list = []

  for i, row in df3SCOUT_split.iterrows():
    try:
      if row['Col1'][0] == '*' and row['Col1'][1] == 'P':  #se la colonna1 inizia con *P
        if len(row['Col1']) == 3:  #se la lunghezza della stringa è 3, significa che il numero di maglia è a una cifra, ed è il carattere in posizione 2
          stringaPalleggiatoreCasaAttuale = row['Col1'][2]  #il carattere del numero di maglia è in posizione 2
          palleggiatore_casa_attuale = int(stringaPalleggiatoreCasaAttuale)
          my_list.append(palleggiatore_casa_attuale)
        elif len(row['Col1']) == 4:  #altrimenti se la lunghezza della stringa è 4, significa che il numero di maglia è a due cifre, e i caratteri sono in posizione 2,3
          stringaPalleggiatoreCasaAttuale = row['Col1'][2:4]  #i due caratteri del numero maglia sono in posizione 2 e 3 della stringa
          palleggiatore_casa_attuale = int(stringaPalleggiatoreCasaAttuale)
          my_list.append(palleggiatore_casa_attuale)
        elif row['Col1'][4] == '>' and row['Col1'][5] == 'L' and row['Col1'][6] == 'U' and row['Col1'][7] == 'p':  #se i caratteri 4,5,6,7 sono >LUp
          stringaPalleggiatoreCasaAttuale = row['Col1'][2:4]  #i due caratteri del numero maglia sono in posizione 2 e 3 della stringa
          palleggiatore_casa_attuale = int(stringaPalleggiatoreCasaAttuale)
          my_list.append(palleggiatore_casa_attuale)
        else:  #altrimenti il numero di maglia è formato da due cifre
          my_list.append(palleggiatore_casa_attuale)
      else:
        my_list.append(palleggiatore_casa_attuale)
    except (IndexError, TypeError):  # Handle other cases
      print(f"WARNING (Pallegg1): Skipping row {i} of the df3SCOUT_split dataframe due to an error. {row['Col1']}")


  df_rilevations['Pallegg1'] = my_list

  #my_list

  #POPOLO LA COLONNA Pallegg2
  #Il codice automatico P, a inizio set, con >LUp, riporta il numero di maglia con lo zero davanti, quindi se il numero è 4 riporta 04          (es aP06>LUp)
  #Il codice automatico P, in tutte le altre parti, riporta il numero di maglia senza zeri davanti, quindi se il numero è 4 riporta 4, non 04   (es *P4)

  palleggiatore_ospite_attuale = 0
  my_list = []

  for i, row in df3SCOUT_split.iterrows():
    try:
      if row['Col1'][0] == 'a' and row['Col1'][1] == 'P':  #se la colonna1 inizia con aP
        if len(row['Col1']) == 3:  #se la lunghezza della stringa è 3, significa che il numero di maglia è a una cifra, ed è il carattere in posizione 2
          stringaPalleggiatoreOspiteAttuale = row['Col1'][2]  #il carattere del numero di maglia è in posizione 2
          palleggiatore_ospite_attuale = int(stringaPalleggiatoreOspiteAttuale)
          my_list.append(palleggiatore_ospite_attuale)
        elif len(row['Col1']) == 4:  #altrimenti se la lunghezza della stringa è 4, significa che il numero di maglia è a due cifre, e i caratteri sono in posizione 2,3
          stringaPalleggiatoreOspiteAttuale = row['Col1'][2:4]  #i due caratteri del numero maglia sono in posizione 2 e 3 della stringa
          palleggiatore_ospite_attuale = int(stringaPalleggiatoreOspiteAttuale)
          my_list.append(palleggiatore_ospite_attuale)
        elif row['Col1'][4] == '>' and row['Col1'][5] == 'L' and row['Col1'][6] == 'U' and row['Col1'][7] == 'p':  #se i caratteri 4,5,6,7 sono >LUp
          stringaPalleggiatoreOspiteAttuale = row['Col1'][2:4]  #i due caratteri del numero maglia sono in posizione 2 e 3 della stringa
          palleggiatore_ospite_attuale = int(stringaPalleggiatoreOspiteAttuale)
          my_list.append(palleggiatore_ospite_attuale)
        else:  #altrimenti il numero di maglia è formato da due cifre
          my_list.append(palleggiatore_ospite_attuale)
      else:
        my_list.append(palleggiatore_ospite_attuale)
    except (IndexError, TypeError):  # Handle other cases
      print(f"WARNING (Pallegg2): Skipping row {i} of the df3SCOUT_split dataframe due to an error. {row['Col1']}")


  df_rilevations['Pallegg2'] = my_list

  #my_list

  #POPOLO LA COLONNA PosizPallegg1
  #È il codice automatico z

  posiz_palleggiatore_casa_attuale = 0
  my_list = []

  for i, row in df3SCOUT_split.iterrows():
    try:
      if row['Col1'][0] == '*' and row['Col1'][1] == 'z':  #se la colonna1 inizia con *z
        stringaPosizPalleggiatoreCasaAttuale = row['Col1'][2]  #il carattere del numero di maglia è in posizione 2
        posiz_palleggiatore_casa_attuale = int(stringaPosizPalleggiatoreCasaAttuale)
        my_list.append(posiz_palleggiatore_casa_attuale)
      else:
        my_list.append(posiz_palleggiatore_casa_attuale)
    except (IndexError, TypeError):  # Handle other cases
      print(f"WARNING (PosizPallegg1): Skipping row {i} of the df3SCOUT_split dataframe due to an error. {row['Col1']}")


  df_rilevations['PosizPallegg1'] = my_list

  #my_list

  #POPOLO LA COLONNA PosizPallegg2
  #È il codice automatico z

  posiz_palleggiatore_ospite_attuale = 0
  my_list = []

  for i, row in df3SCOUT_split.iterrows():
    try:
      if row['Col1'][0] == 'a' and row['Col1'][1] == 'z':  #se la colonna1 inizia con az
        stringaPosizPalleggiatoreOspiteAttuale = row['Col1'][2]  #il carattere del numero di maglia è in posizione 2
        posiz_palleggiatore_ospite_attuale = int(stringaPosizPalleggiatoreOspiteAttuale)
        my_list.append(posiz_palleggiatore_ospite_attuale)
      else:
        my_list.append(posiz_palleggiatore_ospite_attuale)
    except (IndexError, TypeError):  # Handle other cases
      print(f"WARNING (PosizPallegg2): Skipping row {i} of the df3SCOUT_split dataframe due to an error. {row['Col1']}")


  df_rilevations['PosizPallegg2'] = my_list

  #my_list

  df_rilevations['Player1'] = df3SCOUT_split['Col15']  #POPOLO LA COLONNA Player1
  df_rilevations['Player2'] = df3SCOUT_split['Col16']  #POPOLO LA COLONNA Player2
  df_rilevations['Player3'] = df3SCOUT_split['Col17']  #POPOLO LA COLONNA Player3
  df_rilevations['Player4'] = df3SCOUT_split['Col18']  #POPOLO LA COLONNA Player4
  df_rilevations['Player5'] = df3SCOUT_split['Col19']  #POPOLO LA COLONNA Player5
  df_rilevations['Player6'] = df3SCOUT_split['Col20']  #POPOLO LA COLONNA Player6
  df_rilevations['Player7'] = df3SCOUT_split['Col21']  #POPOLO LA COLONNA Player7
  df_rilevations['Player8'] = df3SCOUT_split['Col22']  #POPOLO LA COLONNA Player8
  df_rilevations['Player9'] = df3SCOUT_split['Col23']  #POPOLO LA COLONNA Player9
  df_rilevations['Player10'] = df3SCOUT_split['Col24']  #POPOLO LA COLONNA Player10
  df_rilevations['Player11'] = df3SCOUT_split['Col25']  #POPOLO LA COLONNA Player11
  df_rilevations['Player12'] = df3SCOUT_split['Col26']  #POPOLO LA COLONNA Player12
  
  df_rilevations['Player1_ID_auto'] = None  #Conoscerò il suo valore al momento dell'inserimento
  df_rilevations['Player2_ID_auto'] = None
  df_rilevations['Player3_ID_auto'] = None
  df_rilevations['Player4_ID_auto'] = None
  df_rilevations['Player5_ID_auto'] = None
  df_rilevations['Player6_ID_auto'] = None
  df_rilevations['Player7_ID_auto'] = None
  df_rilevations['Player8_ID_auto'] = None
  df_rilevations['Player9_ID_auto'] = None
  df_rilevations['Player10_ID_auto'] = None
  df_rilevations['Player11_ID_auto'] = None
  df_rilevations['Player12_ID_auto'] = None

  
  # Sostituisci i valori di stringa vuota '' con None per tutte le colonne PlayerX (ovvero se risulta che in campo non c'è nessuno, metto None)
  #In questo modo queste colonne potranno essere considerate come float invece che object
  for i in range(1, 13):  # da Player1 a Player12
    columnX = f'Player{i}'
    df_rilevations[columnX] = df_rilevations[columnX].replace('', None)
    #df_rilevations[columnX] = df_rilevations[columnX].fillna(0)
    
  #Ora modifico i tipi interi in IntXX, che accettano valori nulli. Altrimenti sarebbero convertiti in stringhe.
  df_rilevations = df_rilevations.astype({
    'GameID': 'Int32',
    'SetNumber': 'Int16',
    'ActionNumber': 'Int16',
    'RilevationNumber': 'Int16',
    'TouchNumber': 'Int16',
    'PunteggioCasa': 'Int16',
    'PunteggioOspiti': 'Int16',
    'whichTeam': 'bool',
    'whichTeamID': 'Int32',
    'NumeroMaglia': 'Int16',
    'StartZone': 'Int16',
    'EndZoneEsecZone': 'Int16',
    'PlayersInfo': 'Int16',
    'Pallegg1': 'Int16',
    'PosizPallegg1': 'Int16',
    'Pallegg2': 'Int16',
    'PosizPallegg2': 'Int16',
    'Player1': 'Int16',
    'Player2': 'Int16',
    'Player3': 'Int16',
    'Player4': 'Int16',
    'Player5': 'Int16',
    'Player6': 'Int16',
    'Player7': 'Int16',
    'Player8': 'Int16',
    'Player9': 'Int16',
    'Player10': 'Int16',
    'Player11': 'Int16',
    'Player12': 'Int16',
    'Player1_ID_auto': 'Int32',
    'Player2_ID_auto': 'Int32',
    'Player3_ID_auto': 'Int32',
    'Player4_ID_auto': 'Int32',
    'Player5_ID_auto': 'Int32',
    'Player6_ID_auto': 'Int32',
    'Player7_ID_auto': 'Int32',
    'Player8_ID_auto': 'Int32',
    'Player9_ID_auto': 'Int32',
    'Player10_ID_auto': 'Int32',
    'Player11_ID_auto': 'Int32',
    'Player12_ID_auto': 'Int32',

  })
    


  """## Creo il dataframe df_games"""

  df_games = pd.DataFrame(Games)

  #POPOLO LA RIGA 0 DI df_games
  #se il valore è vuoto mettere int() o str() da errore

  with warnings.catch_warnings():
    warnings.simplefilter("ignore", category=FutureWarning)
    df_games.loc[0, 'originalGameID'] = originalGameID
    df_games.loc[0, 'GameID'] = None
    df_games.loc[0, 'Date'] = df3MATCH_split['Colonna1'].iloc[0]
    if df3MATCH_split['Colonna1'].iloc[0] != None:  #Se la data è nel formato YYYY-MM-DD, allora la aggiungo a DateSQL
        try:
            giorno = str(df3MATCH_split['Colonna1'].iloc[0][0:2])
            mese = str(df3MATCH_split['Colonna1'].iloc[0][3:5])
            anno = str(df3MATCH_split['Colonna1'].iloc[0][6:10])
            date_sqlite = f"{anno}-{mese}-{giorno}"
            if is_valid_date_format(date_sqlite):
                df_games.loc[0, 'DateSQL'] = date_sqlite  #datetime.strptime(date_sqlite, '%Y-%m-%d').date()
                df_games['DateSQL'] = pd.to_datetime(df_games['DateSQL']).dt.date
            else:
                print(f"WARNING: Data non valida {date_sqlite} per il file {file_path}")
                df_games.loc[0, 'DateSQL'] = None
        except:
            print(f"WARNING: Errore nel parsing della data per il file {file_path}")
            df_games.loc[0, 'DateSQL'] = None
    df_games.loc[0, 'Time'] = str(df3MATCH_split['Colonna2'].iloc[0])
    df_games.loc[0, 'Spettatori'] = None  #In questi metto None perchè li troverò in seguito facendo scraping da legavolley.it
    df_games.loc[0, 'Incasso'] = None
    df_games.loc[0, 'Impianto'] = None
    df_games.loc[0, 'Città'] = None
    df_games.loc[0, 'Provincia'] = None
    df_games.loc[0, 'TerritorioNeutro'] = None
    df_games.loc[0, 'Annata'] = None  #metto None perchè sarà calcolata dopo in base alla data. #str(df3MATCH_split['Colonna3'].iloc[0])
    df_games.loc[0, 'Campionato'] = df3MATCH_split['Colonna4'].iloc[0]
    df_games.loc[0, 'Competition'] = None
    df_games.loc[0, 'Phase'] = str(df3MATCH_split['Colonna5'].iloc[0])
    df_games.loc[0, 'Incontro'] = df3MATCH_split['Colonna6'].iloc[0]
    df_games.loc[0, 'Giornata'] = str(df3MATCH_split['Colonna7'].iloc[0])
    df_games.loc[0, 'IDHomeTeam'] = None  #metto None perchè non conosco ancora il TeamID di teams_each_game
    df_games.loc[0, 'HomeTeamSetWon'] = str(df3TEAMS_split['Col3'].iloc[0])
    df_games.loc[0, 'HomeTeamCoach'] = str(df3TEAMS_split['Col4'].iloc[0])
    df_games.loc[0, 'HomeTeamCoach2'] = str(df3TEAMS_split['Col5'].iloc[0])
    df_games.loc[0, 'IDVisitorTeam'] = None  #metto None perchè non conosco ancora il TeamID di teams_each_game
    df_games.loc[0, 'VisitorTeamSetWon'] = str(df3TEAMS_split['Col3'].iloc[1])
    df_games.loc[0, 'VisitorTeamCoach'] = str(df3TEAMS_split['Col4'].iloc[1])
    df_games.loc[0, 'VisitorTeamCoach2'] = str(df3TEAMS_split['Col5'].iloc[1])
    df_games.loc[0, 'Scoutman'] = str(df3MORE_split['Col6'].iloc[0])
    
    #Se la durata del set è una string avuota (non disputato) metto None. 
    df_games.loc[0, 'DurationSet1'] = None if (str(df3SET_split['Col6'].iloc[0]).strip() == '') else str(df3SET_split['Col6'].iloc[0]).strip()  #sostituisco i valori vuoti o nan con None
    df_games.loc[0, 'DurationSet2'] = None if str(df3SET_split['Col6'].iloc[1]).strip() == '' else str(df3SET_split['Col6'].iloc[1]).strip()
    df_games.loc[0, 'DurationSet3'] = None if str(df3SET_split['Col6'].iloc[2]).strip() == '' else str(df3SET_split['Col6'].iloc[2]).strip()
    df_games.loc[0, 'DurationSet4'] = None if str(df3SET_split['Col6'].iloc[3]).strip() == '' else str(df3SET_split['Col6'].iloc[3]).strip()
    df_games.loc[0, 'DurationSet5'] = None if str(df3SET_split['Col6'].iloc[4]).strip() == '' else str(df3SET_split['Col6'].iloc[4]).strip()
    df_games.loc[0, 'Spettatori'] = None
    df_games.loc[0, 'Incasso'] = None
    df_games.loc[0, 'Impianto'] = None
    df_games.loc[0, 'Città'] = None
    df_games.loc[0, 'Provincia'] = None
    df_games.loc[0, 'url_legavolley'] = None
    
    #Aggiungo la colonna che mi dice dove si trova il video (la metto per capire almeno come si chiama il file mp4, nel caso lo dovessi cercare). Se è '' metto file_path, da cui potrò ricavare il nome del file mp4 (in teoria)
    df_games.loc[0, 'VideoPath'] = file_path if str(df3VIDEO_split['Col1'].iloc[0]).strip() == '' else str(df3VIDEO_split['Col1'].iloc[0]).strip()
                 
    # Debug: stampiamo i valori prima della conversione
    for i in range(1,6):
        #print(f"DurationSet{i} prima della conversione:", df_games.loc[0, f'DurationSet{i}'], "Tipo:", type(df_games.loc[0, f'DurationSet{i}']))
        
        value = df_games.loc[0, f'DurationSet{i}']
        if value is not None and not pd.isna(value):  # Processo solo se non è ne None ne nan
            try:
                df_games.loc[0, f'DurationSet{i}'] = int(value)
            except ValueError:
                print(f"Impossibile convertire DurationSet{i}: {value}")
                df_games.loc[0, f'DurationSet{i}'] = -1
    '''
    # Debug: stampiamo i valori dopo la conversione
    for i in range(1,6):
        print(f"DurationSet{i} dopo la conversione:", df_games.loc[0, f'DurationSet{i}'], 
              "Tipo:", type(df_games.loc[0, f'DurationSet{i}']))
    '''
    
    
    df_games['Giornata'] = df_games['Giornata'].replace('', None)  #sostituisco i valori vuoti con None
    
    # Prima di fare l'astype, convertiamo i valori in numeri
    def convert_to_numeric(x):
        if pd.isna(x):
            return None
        try:
            return int(x)
        except (ValueError, TypeError):
            return None
    
    #Gli int che contengono None diventano stringhe, quindi li converto in Int, che accetta anche None
    df_games = df_games.astype({
      'GameID': 'Int32',
      'Spettatori': 'Int32',
      'Incasso': 'Int32',
      'TerritorioNeutro': 'boolean',
      'Annata': 'Int16',
      'Giornata': 'Int16',
      'IDHomeTeam': 'Int16',
      'HomeTeamSetWon': 'Int16',
      'IDVisitorTeam': 'Int16',
      'VisitorTeamSetWon': 'Int16',
      'DurationSet1': 'Int16',
      'DurationSet2': 'Int16',
      'DurationSet3': 'Int16',
      'DurationSet4': 'Int16',
      'DurationSet5': 'Int16',
    })
    
    
    
    """## Creo il dataframe df_teams_each_game_home"""
    
    df_teams_each_game_home = pd.DataFrame(TeamsEachGame_home)
    df_teams_each_game_home.loc[0, 'GameID'] = None
    df_teams_each_game_home.loc[0, 'TeamID_auto'] = None
    df_teams_each_game_home.loc[0, 'TeamID'] = None
    df_teams_each_game_home.loc[0, 'TeamName'] = df3TEAMS_split['Col2'].iloc[0]
    df_teams_each_game_home.loc[0, 'HomeAway'] = 0
    
    #Modifico i tipi. Metto le colonne intere come IntXX, che accetta valori nulli. Altrimenti sarebbero convertiti in stringhe.
    df_teams_each_game_home = df_teams_each_game_home.astype({
      'GameID': 'Int32',
      'TeamID_auto': 'Int32',
      'TeamID': 'Int32',
      'HomeAway': 'Int16',
    })
    
    
    """## Creo il dataframe df_teams_each_game_visitor"""
    
    df_teams_each_game_visitor = pd.DataFrame(TeamsEachGame_visitor)
    df_teams_each_game_visitor.loc[0, 'GameID'] = None
    df_teams_each_game_visitor.loc[0, 'TeamID_auto'] = None
    df_teams_each_game_visitor.loc[0, 'TeamID'] = None
    df_teams_each_game_visitor.loc[0, 'TeamName'] = df3TEAMS_split['Col2'].iloc[1]
    df_teams_each_game_visitor.loc[0, 'HomeAway'] = 1
    
    #Modifico i tipi. Metto le colonne intere come IntXX, che accetta valori nulli. Altrimenti sarebbero convertiti in stringhe.
    df_teams_each_game_visitor = df_teams_each_game_visitor.astype({
      'GameID': 'Int32',
      'TeamID_auto': 'Int32',
      'TeamID': 'Int32',
      'HomeAway': 'Int16',
    })
    
    
    """## Creo il dataframe df_players_each_game_home"""
    
    df_players_each_game_home = pd.DataFrame(PlayersEachGame_home)
    for idx, giocatore in df3PLAYERSH_split.iterrows():
      #Non scrivo le colonne GameID, TeamID_auto, ID_auto, PlayerID ecc perchè adesso sono None, il loro valore lo troverò dopo aver inserito il team in teams_each_game
      df_players_each_game_home.loc[idx, 'originalPlayerID'] = giocatore['Col9']
      df_players_each_game_home.loc[idx, 'Nome'] = giocatore['Col11']
      df_players_each_game_home.loc[idx, 'Cognome'] = giocatore['Col10']
      df_players_each_game_home.loc[idx, 'NumeroMaglia'] = giocatore['Col2']
      df_players_each_game_home.loc[idx, 'Ruolo'] = giocatore['Col14']
      
    df_players_each_game_home = df_players_each_game_home.replace('', None)  #rimpiazzo i valori vuoti con None
    df_players_each_game_home['Ruolo'] = df_players_each_game_home['Ruolo'].fillna(0)  #rimpiazzo i valori vuoti con None
    
    # Per df_players_each_game_home
    numeric_columns = ['GameID', 'TeamID_auto', 'PlayerID_auto', 'PlayerID', 'NumeroMaglia', 'Ruolo', 'NumAzioniDaRuolo5', 'NumAzioniDaRuolo4', 'NumAzioniDaRuolo3', 'NumAzioniDaRuolo2', 'RuoloCalc']

    for col in numeric_columns:
        df_players_each_game_home[col] = df_players_each_game_home[col].apply(convert_to_numeric)
    
    #Modifico i tipi. Metto le colonne intere come IntXX, che accetta valori nulli. Altrimenti sarebbero convertiti in stringhe.
    df_players_each_game_home = df_players_each_game_home.astype({
      'GameID': 'Int32',
      'TeamID_auto': 'Int32',
      'PlayerID_auto': 'Int32',
      'PlayerID': 'Int32',
      'NumeroMaglia': 'Int16',
      'Ruolo': 'Int16',
      'NumAzioniDaRuolo5': 'Int16',
      'NumAzioniDaRuolo4': 'Int16',
      'NumAzioniDaRuolo3': 'Int16',
      'NumAzioniDaRuolo2': 'Int16',
      'RuoloCalc': 'Int16',
    })


    """## Creo il dataframe df_players_each_game_visitor"""
    
    df_players_each_game_visitor = pd.DataFrame(PlayersEachGame_visitor)
    for idx, giocatore in df3PLAYERSV_split.iterrows():
      #Non scrivo le colonne GameID, TeamID, ID_auto, PlayerID ecc perchè adesso sono None, il loro valore lo troverò dopo aver inserito il team in teams_each_game
      df_players_each_game_visitor.loc[idx, 'originalPlayerID'] = giocatore['Col9']
      df_players_each_game_visitor.loc[idx, 'Nome'] = giocatore['Col11']
      df_players_each_game_visitor.loc[idx, 'Cognome'] = giocatore['Col10']
      df_players_each_game_visitor.loc[idx, 'NumeroMaglia'] = giocatore['Col2']
      df_players_each_game_visitor.loc[idx, 'Ruolo'] = giocatore['Col14']
      
    df_players_each_game_visitor = df_players_each_game_visitor.replace('', None)  #rimpiazzo i valori vuoti con None
    df_players_each_game_visitor['Ruolo'] = df_players_each_game_visitor['Ruolo'].fillna(0)  #rimpiazzo i valori vuoti con None. replace è più adatto quando vogliamo sostituire valori specifici, ma se vogliamo sostituire valori nulli meglio usare fillna

    for col in numeric_columns:
        df_players_each_game_visitor[col] = df_players_each_game_visitor[col].apply(convert_to_numeric)
    
    #Modifico i tipi. Metto le colonne intere come IntXX, che accetta valori nulli. Altrimenti sarebbero convertiti in stringhe.
    df_players_each_game_home = df_players_each_game_home.astype({
      'GameID': 'Int32',
      'TeamID_auto': 'Int32',
      'PlayerID_auto': 'Int32',
      'PlayerID': 'Int32',
      'NumeroMaglia': 'Int16',
      'Ruolo': 'Int16',
      'NumAzioniDaRuolo5': 'Int16',
      'NumAzioniDaRuolo4': 'Int16',
      'NumAzioniDaRuolo3': 'Int16',
      'NumAzioniDaRuolo2': 'Int16',
      'RuoloCalc': 'Int16',
    })
    
    
    # il dataframe df_players_updated non serve crearlo perchè la tabella players_updated è popolata automaticamente dal TRIGGER, e verrà popolata in futuro.

  return df_teams_each_game_home, df_teams_each_game_visitor, df_players_each_game_home, df_players_each_game_visitor, df_rilevations, df_games


#I dati li inserisco prima in teams_each_game, poi in players_each_game, poi in df_games, poi in df_rilevations
def carica_dataframe(df_teams_each_game_home, df_teams_each_game_visitor, df_players_each_game_home, df_players_each_game_visitor, df_rilevations, df_games):  
    with engine.connect() as conn:  # Connessione per eseguire il SQL
        try:
            with conn.begin():  # Inizia una transazione
              
                # CARICO df_teams_each_game_home
                # Crea la query SQL di inserimento, con RETURNING per ottenere il valore della colonna ID_auto (che è autoincrementale).
                insert_query_home = text("""
                    INSERT INTO teams_each_game ("TeamName", "HomeAway")
                    VALUES (:team_name, :home_away)
                    RETURNING "TeamID_auto";
                """)
                
                # Crea il dizionario dei parametri (SQLAlchemy li vuole come dizionario)
                params = {
                    "team_name": df_teams_each_game_home.iloc[0]['TeamName'],
                    "home_away": False  #Per la squadra di casa metto 0
                }
                
                # Esegui l'inserimento e recupera il valore di "ID_auto"
                result = conn.execute(insert_query_home, params)  #result può essere letto solo una volta da fetchone, scalar_one ecc

                # Ottieni il valore "ID_auto" appena inserito (siamo nella squadra di casa)
                ID_AUTO_TEAM = int(result.scalar_one())  #è come fetchone()[0], ma si aspetta un solo valore, altrimenti da errore
                GAMEID = ID_AUTO_TEAM  
                TEAMID_HOME = ID_AUTO_TEAM        #è il risultato della query 
                TEAMID_VISITOR = ID_AUTO_TEAM+1   #è il risultato della query +1
                #GameID = TeamID della squadra di casa
                #GameID = TeamID della squadra di ospite-1

                # Ora puoi aggiornare "TeamID" con il valore di "ID_auto". Metti a TeamID lo stesso valore di TeamID_auto dove id_auto = ID_AUTO_TEAM
                update_query = text("""
                    UPDATE teams_each_game
                    SET "GameID" = :game_id, "TeamID" = :team_id_home   --metto il TeamID, che deve essere uguale a TeamID_auto, non il TeamID_auto
                    WHERE "TeamID_auto" = :id_auto;
                """)

                params_update = {
                    "game_id": GAMEID,
                    "team_id_home": TEAMID_HOME,
                    "id_auto": ID_AUTO_TEAM
                }
                conn.execute(update_query, params_update)


                # CARICO df_teams_each_game_visitor
                # Crea la query SQL di inserimento della squadra di ospite.
                #In pratica adesso che ho inserito i dati di teams_each_game, ho ottenuto i valori automatici di quando inserisco nel database, quindi li imposto nei giocatori, e inserisco i giocatori.
                insert_query_visitor = text("""
                    INSERT INTO teams_each_game ("GameID", "TeamID", "TeamName", "HomeAway")  --metto il TeamID, che deve essere uguale a TeamID_auto, non il TeamID_auto
                    VALUES (:game_id, :team_id_visitor, :team_name, :home_away)
                """)

                params_visitor = {
                    "game_id": GAMEID,
                    "team_id_visitor": TEAMID_VISITOR,
                    "team_name": df_teams_each_game_visitor.iloc[0]['TeamName'],
                    "home_away": True
                }
                conn.execute(insert_query_visitor, params_visitor)

                
                contatore = 0

                # CARICO df_players_each_game_home
                for idx, giocatore in df_players_each_game_home.iterrows():  #inserisco un giocatore (riga) alla volta da df_players_each_game_home
                    insert_query = text("""
                        INSERT INTO players_each_game ("GameID", "TeamID_auto", "originalPlayerID", "Nome", "Cognome", "NumeroMaglia", "Ruolo", "NumAzioniDaRuolo5", "NumAzioniDaRuolo4", "NumAzioniDaRuolo3", "NumAzioniDaRuolo2", "RuoloCalc")
                        VALUES (:game_id, :team_id_auto, :originalPlayerID, :nome, :cognome, :numeroMaglia, :ruolo, :numeroAzioniDaRuolo5, :numeroAzioniDaRuolo4, :numeroAzioniDaRuolo3, :numeroAzioniDaRuolo2, :ruoloCalc)
                        RETURNING "PlayerID_auto";
                    """)
                    #Imposto il valore dei parametri da dare alla query di inserimento (insert_query)
                    params_players_home = {
                        "game_id": GAMEID,
                        "team_id_auto": TEAMID_HOME,
                        "originalPlayerID": giocatore['originalPlayerID'],
                        "nome": giocatore['Nome'],
                        "cognome": giocatore['Cognome'],
                        "numeroMaglia": None if pd.isna(giocatore['NumeroMaglia']) else giocatore['NumeroMaglia'],
                        "ruolo": 0 if pd.isna(giocatore['Ruolo']) else giocatore['Ruolo'],
                        "numeroAzioniDaRuolo5": None,
                        "numeroAzioniDaRuolo4": None,
                        "numeroAzioniDaRuolo3": None,
                        "numeroAzioniDaRuolo2": None,
                        "ruoloCalc": None,
                        
                    }
                    #eseguo la query
                    result = conn.execute(insert_query, params_players_home)
                    ID_AUTO_HOME = result.scalar_one()
                    
                    update_query = text("""
                        UPDATE players_each_game
                        SET "PlayerID" = :player_id_auto
                        WHERE "PlayerID_auto" = :player_id_auto;
                    """)

                    conn.execute(update_query, {"player_id_auto": ID_AUTO_HOME})
                    contatore += 1
                    
                if contatore != df_players_each_game_home.shape[0]:
                    print(f"ERRORE: Il file {file_path} ha un numero di giocatori diverso da quello previsto in df_players_each_game_home")
                    conn.rollback()
                
                #Leggo players_each_game_home come dataframe così ottengo un dataframe che ha i PlayerID_auto
                df_players_each_game_home = pd.read_sql("""
                    SELECT *
                    FROM players_each_game
                    WHERE "TeamID_auto" = %s AND "GameID" = %s
                """, conn, params=(TEAMID_HOME, GAMEID))
                    
                contatore = 0      
 
                # CARICO df_players_each_game_visitor
                for idx, giocatore in df_players_each_game_visitor.iterrows():  #inserisco un giocatore (riga) alla volta da df_players_each_game_visitor
                    insert_query = text("""
                        INSERT INTO players_each_game ("GameID", "TeamID_auto", "originalPlayerID", "Nome", "Cognome", "NumeroMaglia", "Ruolo", "NumAzioniDaRuolo5", "NumAzioniDaRuolo4", "NumAzioniDaRuolo3", "NumAzioniDaRuolo2", "RuoloCalc")
                        VALUES (:game_id, :team_id_auto, :originalPlayerID, :nome, :cognome, :numeroMaglia, :ruolo, :numeroAzioniDaRuolo5, :numeroAzioniDaRuolo4, :numeroAzioniDaRuolo3, :numeroAzioniDaRuolo2, :ruoloCalc)
                        RETURNING "PlayerID_auto";
                    """)
                    
                    params_players_home = {
                        "game_id": GAMEID,
                        "team_id_auto": TEAMID_VISITOR,
                        "originalPlayerID": giocatore['originalPlayerID'],
                        "nome": giocatore['Nome'],
                        "cognome": giocatore['Cognome'],
                        "numeroMaglia": None if pd.isna(giocatore['NumeroMaglia']) else giocatore['NumeroMaglia'],
                        "ruolo": 0 if pd.isna(giocatore['Ruolo']) else giocatore['Ruolo'],
                        "numeroAzioniDaRuolo5": None,
                        "numeroAzioniDaRuolo4": None,
                        "numeroAzioniDaRuolo3": None,
                        "numeroAzioniDaRuolo2": None,
                        "ruoloCalc": None,
                    }
                    
                    result = conn.execute(insert_query, params_players_home)
                    ID_AUTO_VISITOR = result.scalar_one()
                    
                    update_query = text("""
                        UPDATE players_each_game
                        SET "PlayerID" = :player_id_auto
                        WHERE "PlayerID_auto" = :player_id_auto;
                    """)

                    conn.execute(update_query, {"player_id_auto": ID_AUTO_VISITOR})
                    contatore += 1
                    
                if contatore != df_players_each_game_visitor.shape[0]:
                    print(f"ERRORE: Il file {file_path} ha un numero di giocatori diverso da quello previsto in df_players_each_game_visitor")
                    conn.rollback()
                    
                #Leggo players_each_game_visitor come dataframe così ottengo un dataframe che ha i PlayerID_auto
                df_players_each_game_visitor = pd.read_sql("""
                    SELECT *
                    FROM players_each_game
                    WHERE "TeamID_auto" = %s AND "GameID" = %s
                """, conn, params=(TEAMID_VISITOR, GAMEID))
                 
                #pd.set_option('display.max_columns', None)  # Show all columns
                #pd.set_option('display.width', 1000)  # Increase display width
                #print(df_players_each_game_visitor)

                
                # CARICO df_games
                # aggiorno la colonna GameID con il valore di GAMEID
                # aggiorno il valore di IDHomeTeam e IDVisitorTeam con il valore di TEAMID_HOME e TEAMID_VISITOR
                df_games.loc[0, 'GameID'] = GAMEID
                #df_games.loc[0, 'IDHomeTeam'] = TEAMID_HOME
                #df_games.loc[0, 'IDVisitorTeam'] = TEAMID_VISITOR
                
                #Aggiungo a games tutte le colonne a parte IDHomeTeam e IDVisitorTeam, perchè saranno solo nella VIEW games_view, in cui verranno calcolate in automatico
                cols_games = [col for col in df_games.columns if col not in ['IDHomeTeam', 'IDVisitorTeam']]
                df_games_to_insert = df_games[cols_games]
                df_games_to_insert.to_sql('games', conn, if_exists='append', index=False)
                
                

                # CARICO df_rilevations
                # aggiorno tutta la colonna GameID con il valore di GAMEID
                df_rilevations['GameID'] = GAMEID
                # nella colonna whichTeamID metto TEAMID_HOME se whichTeam è False, TEAMID_VISITOR se whichTeam è True
                df_rilevations.loc[df_rilevations['whichTeam'] == False, 'whichTeamID'] = TEAMID_HOME    #cerca tutte le righe in cui whichTeam è False e mette in whichTeamID il valore di TEAMID_HOME
                df_rilevations.loc[df_rilevations['whichTeam'] == True, 'whichTeamID'] = TEAMID_VISITOR  #cerca tutte le righe in cui whichTeam è True e mette in whichTeamID il valore di TEAMID_VISITOR

                # Mappo gli ID dei giocatori
                for i in range(1, 7):
                    # Mappo gli HomePlayerX_ID_auto e VisitorPlayerX_ID_auto in base ai NumeroMaglia e rispettivi PlayerID_auto
                    df_rilevations[f'Player{i}_ID_auto'] = df_rilevations[f'Player{i}'].map(df_players_each_game_home.set_index('NumeroMaglia')['PlayerID_auto'])
                    df_rilevations[f'Player{i+6}_ID_auto'] = df_rilevations[f'Player{i+6}'].map(df_players_each_game_visitor.set_index('NumeroMaglia')['PlayerID_auto'])
                    
                #Mappo i pallegg1 e pallegg2
                df_rilevations['Pallegg1_ID_auto'] = df_rilevations['Pallegg1'].map(df_players_each_game_home.set_index('NumeroMaglia')['PlayerID_auto'])
                df_rilevations['Pallegg2_ID_auto'] = df_rilevations['Pallegg2'].map(df_players_each_game_visitor.set_index('NumeroMaglia')['PlayerID_auto'])
                
                # Print the first few rows to check the mapping
                #print("\nFirst few rows of df_rilevations after mapping:")
                #print(df_rilevations[['GameID', 'whichTeamID', 'HomePlayer1', 'HomePlayer1_ID_auto', 'VisitorPlayer1', 'VisitorPlayer1_ID_auto']].head())
                
                #Inserisco df_rilevations nella tabella rilevations
                df_rilevations.to_sql('rilevations', conn, if_exists='append', index=False)

                
                # Controllo che tutte le maglie nelle colonne PlayerX di rilevations siano presenti nei numeri di maglia dei giocatori di casa in PlayersEachGame
                maglie_usate_casa = set()
                # Estrai tutti i numeri di maglia usati in ogni colonna PlayerX con X che va da 1 a 6
                for i in range(1, 7):
                    maglie_usate_casa.update(df_rilevations[f'Player{i}'].dropna().unique())

                # Recupera i numeri di maglia dei giocatori della squadra di casa da players_each_game
                query_numeri_maglia = text("""
                    SELECT "NumeroMaglia"
                    FROM players_each_game
                    WHERE "GameID" = :game_id AND "TeamID_auto" = :team_id_home 
                """)

                result = conn.execute(query_numeri_maglia, {"game_id": GAMEID, "team_id_home": TEAMID_HOME})
                maglie_presenti = set(row[0] for row in result)

                if not maglie_usate_casa.issubset(maglie_presenti):  #se c'è differenza tra i due insiemi
                    raise ValueError(f"I numeri di maglia dei giocatori di casa in campo non corrispondono al roster della squadra di casa. Maglie usate: {maglie_usate_casa}, Maglie nel roster: {maglie_presenti}")    
                
                
                #Faccio lo stesso controllo per le maglie dei giocatori ospiti
                maglie_usate_ospiti = set()
                # Estrai tutti i numeri di maglia usati in ogni colonna PlayerX con X che va da 7 a 12
                for i in range(7, 13):
                    maglie_usate_ospiti.update(df_rilevations[f'Player{i}'].dropna().unique())

                query_numeri_maglia = text("""
                    SELECT "NumeroMaglia"
                    FROM players_each_game
                    WHERE "GameID" = :game_id AND "TeamID_auto" = (:team_id_visitor) 
                """)

                result = conn.execute(query_numeri_maglia, {"game_id": GAMEID, "team_id_visitor": TEAMID_VISITOR})
                maglie_presenti = set(row[0] for row in result)

                if not maglie_usate_ospiti.issubset(maglie_presenti):  #se c'è differenza tra i due insiemi
                    raise ValueError(f"I numeri di maglia dei giocatori ospiti in campo non corrispondono al roster della squadra ospite. Maglie usate: {maglie_usate_ospiti}, Maglie nel roster: {maglie_presenti}")  
                
                
                global numero_file_aggiunti_al_database
                numero_file_aggiunti_al_database += 1
                      
                    
        except Exception as e:
            print(f"ERRORE: Il file {file_path} ha dato problemi nell'inserimento. La transazione non è stata eseguita.", e)






#SetNumber quando trova **Nset aumenta di uno, e questo fa sì che l'ultima riga vada un set oltre, ma è negli output che è così
#Action number va di 1 oltre nelle ultime 2 righe di ogni set.
#TouchNumber va da 1, quando c'è la battuta, fino a N, il tocco in cui viene fatto punto. Al tocco $$& viene assegnato tocco 0
#Ai tocchi **Nset viene assegnato 0 in whichTeam per comodità


# Assegna a RilevationID lo stesso numero di index
#df_rilevations['RilevationID'] = df3SCOUT_split.index


"""Fai anche un controllo all'inizio, se il file è in versione 3.0 saltalo, e stampa il nome del file

Guarda tutti i valori unici delle COl 3,4,5 di 3MATCH, per capire poi come potrai filtrarli.
"""




#############################################################
#CREAZIONE DEL DATABASE
#############################################################

current_directory = os.getcwd()
#print(f"Current working directory: {current_directory}")

#db_path = "C:/Users/<USER>/Documents/ModenaVolley/Codice/db_modena_c.duckdb"

# Connessione al database
conn = psycopg.connect(
    dbname="db_modena",           # database creato in pgAdmin4
    user="postgres",              # Il tuo nome utente PostgreSQL
    password="AcquaLevissima1",   # La password che hai scelto per 'postgres'
    host="localhost",             # 'localhost' se è sul tuo PC
    port=5432                     # La porta predefinita è 5432
)

# Crea un cursore per eseguire le query
cur = conn.cursor()

# Connessione SQLAlchemy
engine = create_engine('postgresql+psycopg://postgres:AcquaLevissima1@localhost:5432/db_modena')


#Se devi popolare il db da zero, cancella prima le tabelle. Altrimenti, commenta il blocco sotto così eviti di cancellare le tabelle esistenti e aggiungi solo i file che mancano

# Drop the tables if they exist to avoid conflicts
if eseguire_da_zero:
  cur.execute("DROP TABLE IF EXISTS teams_each_game CASCADE")
  conn.commit()
  cur.execute("DROP TABLE IF EXISTS players_each_game CASCADE")
  conn.commit()
  cur.execute("DROP TABLE IF EXISTS games CASCADE")
  conn.commit()
  cur.execute("DROP TABLE IF EXISTS rilevations CASCADE")
  conn.commit()
  #cur.execute("DROP TABLE IF EXISTS players_updated CASCADE")
  #conn.commit()




#CREAZIONE DELLE TABELLE

#Siccome le stesse squadre hanno GameID diversi, e quindi nei prossimi file riassegnerò gli ID ai Team, anche il GameID cambierà di conseguenza.
#Però devo anche mantenere l'informazione dei GameID originali, quelli basati sui file, perchè così posso sempre controllare la colonna originalGameID per vedere se un file è già stato aggiunto controllando se il suo GameID è già presente in originalGameID


#Creo la tabella teams_each_game
cur.execute("""
CREATE TABLE IF NOT EXISTS teams_each_game (
    "GameID" integer,  --Lascio che può essere anche NULL perchè all'inizio inserisco il df senza GameID, siccome ne calcolo il valore dopo averlo inserito
    "TeamID_auto" serial NOT NULL,
    "TeamID" integer,
    "TeamName" varchar,
    "HomeAway" boolean,
    PRIMARY KEY ("TeamID_auto"),
    CONSTRAINT unique_gameid_teamid UNIQUE ("GameID", "TeamID_auto")  --metto UNIQUE perchè queste due colonne formano una chiave unica, e sono un punto di riferimento per le FOREIGN KEY
);
""")

# Creo la tabella players_each_game
#Per questa tabella devo creare una FOREIGN KEY composta (GameID, TeamID) riferita a TeamsEachGame(GameID, TeamID)
cur.execute("""
CREATE TABLE IF NOT EXISTS players_each_game (
    "GameID" integer NOT NULL,
    "TeamID_auto" integer,
    "PlayerID_auto" serial,
    "originalPlayerID" varchar,
    "PlayerID" integer,
    "Nome" varchar,
    "Cognome" varchar,
    "NumeroMaglia" smallint,
    "Ruolo" smallint,
    "NumAzioniDaRuolo5" smallint,
    "NumAzioniDaRuolo4" smallint,
    "NumAzioniDaRuolo3" smallint,
    "NumAzioniDaRuolo2" smallint,
    "RuoloCalc" smallint,
    PRIMARY KEY ("PlayerID_auto"),
    
    CONSTRAINT uq_players_each_game_gameid_playeridauto UNIQUE("GameID", "PlayerID_auto")
);

""")

#Creo la tabella games
cur.execute("""
CREATE TABLE IF NOT EXISTS games (
    "originalGameID" varchar,
    "GameID" integer NOT NULL,
    "Date" varchar,
    "DateSQL" date,
    "Time" varchar,
    "TerritorioNeutro" boolean,
    "Annata" smallint,
    "Campionato" varchar,
    "Competition" varchar,
    "Phase" varchar,
    "Incontro" varchar,
    "Giornata" smallint,
    -- "IDHomeTeam" integer NOT NULL,
    "HomeTeamSetWon" smallint,
    "HomeTeamCoach" varchar,
    "HomeTeamCoach2" varchar,
    -- "IDVisitorTeam" integer NOT NULL,
    "VisitorTeamSetWon" smallint,
    "VisitorTeamCoach" varchar,
    "VisitorTeamCoach2" varchar,
    "Scoutman" varchar,
    "DurationSet1" smallint DEFAULT NULL,
    "DurationSet2" smallint DEFAULT NULL,
    "DurationSet3" smallint DEFAULT NULL,
    "DurationSet4" smallint DEFAULT NULL,
    "DurationSet5" smallint DEFAULT NULL,
    "Spettatori" integer,
    "Incasso" integer,
    "Impianto" varchar,
    "Città" varchar,
    "Provincia" varchar,
    "url_legavolley" varchar,
    "VideoPath" varchar,
    PRIMARY KEY ("GameID")
);
""")

# Creo la tabella rilevations
cur.execute("""
CREATE TABLE IF NOT EXISTS rilevations (
    "GameID" integer NOT NULL,
    "SetNumber" smallint,
    "ActionNumber" smallint,
    "RilevationNumber" smallint NOT NULL,
    "TouchNumber" smallint,
    "RilevationTime" varchar,
    "PunteggioCasa" smallint,
    "PunteggioOspiti" smallint,
    "EventiParticolari" varchar,
    "whichTeam" boolean,
    "whichTeamID" integer,
    "NumeroMaglia" smallint,
    "Foundamental" varchar(1),
    "Type" varchar(1),
    "Eval" varchar(1),
    "SetterCall" varchar(2),
    "AttkCombination" varchar(2),
    "TargAttk" varchar(2),
    "StartZone" smallint,
    "EndZoneEsecZone" smallint,
    "EndSubzoneEsecSubzone" varchar(2),
    "SkillType" varchar(1),
    "PlayersInfo" smallint,
    "Special" varchar(2),
    "CustomChar" varchar,
    "SideoutBreakpoint" varchar,
    "Colonna3sez3SCOUT" varchar,
    "Colonna4sez3SCOUT" varchar,
    "Colonna5sez3SCOUT" varchar,
    "Colonna6sez3SCOUT" varchar,
    "Colonna7sez3SCOUT" varchar,
    "Pallegg1" smallint,
    "PosizPallegg1" smallint,
    "Pallegg2" smallint,
    "PosizPallegg2" smallint,
    "Player1" smallint,
    "Player2" smallint,
    "Player3" smallint,
    "Player4" smallint,
    "Player5" smallint,
    "Player6" smallint,
    "Player7" smallint,
    "Player8" smallint,
    "Player9" smallint,
    "Player10" smallint,
    "Player11" smallint,
    "Player12" smallint,
    "Player1_ID_auto" integer,
    "Player2_ID_auto" integer,
    "Player3_ID_auto" integer,
    "Player4_ID_auto" integer,
    "Player5_ID_auto" integer,
    "Player6_ID_auto" integer,
    "Player7_ID_auto" integer,
    "Player8_ID_auto" integer,
    "Player9_ID_auto" integer,
    "Player10_ID_auto" integer,
    "Player11_ID_auto" integer,
    "Player12_ID_auto" integer,
    "Pallegg1_ID_auto" integer,
    "Pallegg2_ID_auto" integer,

    PRIMARY KEY ("GameID", "RilevationNumber")
);
""")

'''
#Creo la tabella players_updated
cur.execute("""
CREATE TABLE IF NOT EXISTS players_updated (
    "PlayerID" integer NOT NULL,
    "Nome" varchar,
    "Cognome" varchar,
    "TeamID" integer,
    "Ruolo" smallint,
    "NumeroMaglia" smallint,
    "Nazionalità" varchar,
    "DataNascita" varchar,
    "Altezza" varchar,
    "Peso" varchar,
    "Schiacciata" varchar,
    "Muro" varchar,
    "ManoDominante" varchar,
    PRIMARY KEY ("PlayerID")
);
""")
'''

conn.commit()



#CREAZIONE DEI TRIGGER
#Li elimino prima di definirli così se voglio modificarli posso farlo, altrimenti se provo a definirli quando esistono già mi da errore
#cur.execute("DROP TRIGGER IF EXISTS trigger_previeni_modifica_numero_miglia_players_each_game ON players_each_game;")

#Creo un trigger che impedisce la modifica del numero di maglia di un giocatore in players_each_game, a meno che il valore iniziale non fosse 0. (è comunque sempre possibile eliminare una riga)
#Questo perchè altrimenti sballerebbe i numeri di maglia in rilevations, e per mettere una FK da rilevations a players_each_game dovrei aggiungere la colonna HomeAway in players_each_game
#In realtà no, perchè i numeri di maglia in rilevations in teoria sono sempre giusti, invece è proprio in players_each_game che magari li devo aggiustare.
'''
cur.execute("""
CREATE OR REPLACE FUNCTION previeni_modifica_numero_miglia_players_each_game()
RETURNS TRIGGER AS $$
BEGIN
    -- Controlla se il valore della colonna è diverso da 0 e se si sta cercando di modificarla
    IF OLD."NumeroMaglia" <> 0 AND NEW."NumeroMaglia" <> OLD."NumeroMaglia" THEN
        RAISE EXCEPTION 'La colonna NumeroMaglia non può essere modificata, a meno che non sia 0 inizialmente';
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_previeni_modifica_numero_miglia_players_each_game
BEFORE UPDATE ON players_each_game
FOR EACH ROW
EXECUTE FUNCTION previeni_modifica_numero_miglia_players_each_game();
""")
'''

#Creo un trigger che aggiorna la colonna whichTeam in rilevations in base a whichTeamID e GameID, mettendo l'HomeAway di teams_each_game. Lo fa solo quando si fa un UPDATE di whichTeamID, e non quando si fa un INSERT, perchè in quel caso whichTeamID è già giusto
cur.execute("""
-- Crea o sostituisce la funzione trigger
CREATE OR REPLACE FUNCTION aggiorna_whichteam_rilevations()
RETURNS TRIGGER AS $$
DECLARE
    righe_aggiornate integer;
BEGIN
    UPDATE rilevations
    SET "whichTeam" = NEW."HomeAway"
    WHERE "GameID" = NEW."GameID" AND "whichTeamID" = NEW."TeamID_auto";
    GET DIAGNOSTICS righe_aggiornate = ROW_COUNT;
    RAISE NOTICE 'Trigger aggiorna_whichteam_rilevations: aggiornate % righe', righe_aggiornate;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Elimina il trigger precedente (se esiste)
DROP TRIGGER IF EXISTS trigger_aggiorna_whichteam_rilevations ON teams_each_game;

-- Crea il trigger che richiama la funzione quando HomeAway viene aggiornato
CREATE TRIGGER trigger_aggiorna_whichteam_rilevations
AFTER UPDATE OF "HomeAway" ON teams_each_game
FOR EACH ROW
EXECUTE FUNCTION aggiorna_whichteam_rilevations();
""")

conn.commit()




#CREAZIONE DELLE FOREIGN KEY

#L'obiettivo è verificare che ogni coppia (GameID, IDHomeTeam) e (GameID, IDVisitorTeam) esista realmente in teams_each_game. Quindi aggiungiamo 
#FOREIGN KEY da (GameID, IDHomeTeam) riferite a teams_each_game(GameID, TeamID)
#FOREIGN KEY da (GameID, IDVisitorTeam) riferite a teams_each_game(GameID, TeamID)

#Se la FOREIGN KEY è già presente non la aggiunge
# Quando controlli se esiste il constraint guardando conname, devi cercarlo con singoli apici '



#composite FK da games(GameID, IDHomeTeam) a teams_each_game(GameID, TeamID)
'''
cur.execute("""
DO $$
BEGIN
    -- Elimina il vincolo se esiste
    IF EXISTS (
        SELECT 1
        FROM pg_constraint
        WHERE conname = 'fk_games_home'
    ) THEN
        ALTER TABLE games
        DROP CONSTRAINT fk_games_home;
    END IF;

    -- Ricrea il vincolo con CASCADE
    ALTER TABLE games
    ADD CONSTRAINT fk_games_home
    FOREIGN KEY ("GameID", "IDHomeTeam")
    REFERENCES teams_each_game("GameID", "TeamID")
    ON UPDATE CASCADE
    ON DELETE CASCADE;
END $$;
""")
'''

#composite FK da games(GameID, IDVisitorTeam) a teams_each_game(GameID, TeamID)
'''
cur.execute("""
DO $$ 
BEGIN
    -- Elimina il vincolo se esiste
    IF EXISTS (
        SELECT 1
        FROM pg_constraint
        WHERE conname = 'fk_games_visitor'
    ) THEN
        ALTER TABLE games
        DROP CONSTRAINT fk_games_visitor;
    END IF;
    
    ALTER TABLE games
    ADD CONSTRAINT fk_games_visitor
    FOREIGN KEY ("GameID", "IDVisitorTeam")
    REFERENCES teams_each_game("GameID", "TeamID")
    ON UPDATE CASCADE
    ON DELETE CASCADE;
END $$;
""")
'''

#FK da rilevations(GameID) a games(GameID)
cur.execute("""
DO $$ 
BEGIN
    -- Elimina il vincolo se esiste
    IF EXISTS (
        SELECT 1
        FROM pg_constraint
        WHERE conname = 'fk_rilevations_GameID_games_GameID'
    ) THEN
        ALTER TABLE rilevations
        DROP CONSTRAINT "fk_rilevations_GameID_games_GameID";
    END IF;
    
    ALTER TABLE rilevations
    ADD CONSTRAINT "fk_rilevations_GameID_games_GameID"
    FOREIGN KEY("GameID") 
    REFERENCES games("GameID")
    ON UPDATE CASCADE
    ON DELETE CASCADE;
END $$;
""")


#composite FK da rilevations(GameID, whichTeamID) a teams_each_game(GameID, TeamID_auto)
#La metto a TeamID_auto, poi se voglio sapere effettivamente quale team è, nella view metto la colonna whichTeamID_actual con il team corrispondente
cur.execute("""
DO $$ 
BEGIN
    -- Elimina il vincolo se esiste
    IF EXISTS (
        SELECT 1
        FROM pg_constraint
        WHERE conname = 'fk_rilevations_GameID_whichTeamID_teams_each_game_GameID_TeamID'
    ) THEN
        ALTER TABLE rilevations
        DROP CONSTRAINT "fk_rilevations_GameID_whichTeamID_teams_each_game_GameID_TeamID";
    END IF;
    
    ALTER TABLE rilevations
    ADD CONSTRAINT "fk_rilevations_GameID_whichTeamID_teams_each_game_GameID_TeamID"
    FOREIGN KEY("GameID", "whichTeamID") 
    REFERENCES teams_each_game("GameID", "TeamID_auto")
    ON UPDATE NO ACTION  --Non voglio che vengano cambiati i valori TeamID_auto in teams_each_game. Con NO ACTION infatti lo impedisco
    ON DELETE CASCADE;
END $$;
""")


#composite FK da players_each_game(GameID, TeamID_auto) a teams_each_game(GameID, TeamID_auto)
cur.execute("""
DO $$ 
BEGIN
    -- Elimina il vincolo se esiste
    IF EXISTS (
        SELECT 1
        FROM pg_constraint
        WHERE conname = 'fk_players_each_game_GameID_teams_each_game_GameID'
    ) THEN
        ALTER TABLE players_each_game
        DROP CONSTRAINT "fk_players_each_game_GameID_teams_each_game_GameID";
    END IF;
    
    ALTER TABLE players_each_game
    ADD CONSTRAINT "fk_players_each_game_GameID_teams_each_game_GameID"
    FOREIGN KEY("GameID", "TeamID_auto")
    REFERENCES teams_each_game("GameID", "TeamID_auto")
    ON UPDATE NO ACTION
    ON DELETE CASCADE;
END $$;
""")


#Ora metto le 12 composite FK da rilevations(GameID, PlayerX_ID_auto) a players_each_game(GameID, PlayerID_auto). Non posso mettere anche whichTeamID perchè cambia ad ogni riga.
for i in range(1, 13):
    cur.execute(f"""
    DO $$ 
    BEGIN
        -- Elimina il vincolo se esiste
        IF EXISTS (
            SELECT 1
            FROM pg_constraint
            WHERE conname = 'fk_rilevations_GameID_Player{i}_ID_auto_players_each_game_GameID_PlayerID_auto'
        ) THEN
            ALTER TABLE rilevations
            DROP CONSTRAINT "fk_rilevations_GameID_Player{i}_ID_auto_players_each_game_GameID_PlayerID_auto";
        END IF;
        
        ALTER TABLE rilevations
        ADD CONSTRAINT "fk_rilevations_GameID_Player{i}_ID_auto_players_each_game_GameID_PlayerID_auto"
        FOREIGN KEY("GameID", "Player{i}_ID_auto")
        REFERENCES players_each_game("GameID", "PlayerID_auto")
        ON UPDATE CASCADE
        ON DELETE CASCADE;
    END $$;
    """)




#CREAZIONE DEGLI INDICI

#cur.execute('DROP INDEX IF EXISTS "idx_teams_each_game_GameID_IDHomeTeam";') 
#cur.execute('CREATE UNIQUE INDEX "idx_teams_each_game_GameID_IDHomeTeam" ON games ("GameID", "IDHomeTeam");')

#cur.execute('DROP INDEX IF EXISTS "idx_teams_each_game_GameID_IDVisitorTeam";') 
#cur.execute('CREATE UNIQUE INDEX "idx_teams_each_game_GameID_IDVisitorTeam" ON games ("GameID", "IDVisitorTeam");')

cur.execute('DROP INDEX IF EXISTS "idx_teams_each_game_GameID_TeamID";') 
cur.execute('CREATE UNIQUE INDEX "idx_teams_each_game_GameID_TeamID" ON teams_each_game ("GameID", "TeamID_auto");')

cur.execute('DROP INDEX IF EXISTS "idx_players_each_game_GameID";') 
cur.execute('CREATE INDEX "idx_players_each_game_GameID" ON players_each_game ("GameID");')

cur.execute('DROP INDEX IF EXISTS "idx_players_each_game_GameID_PlayerID";') 
cur.execute('CREATE UNIQUE INDEX "idx_players_each_game_GameID_PlayerID" ON players_each_game ("GameID", "PlayerID");')

cur.execute('DROP INDEX IF EXISTS "idx_rilevations_GameID_whichTeamID";') 
cur.execute('CREATE INDEX "idx_rilevations_GameID_whichTeamID" ON rilevations ("GameID", "whichTeamID");')

cur.execute('DROP INDEX IF EXISTS "idx_players_each_game_GameID_TeamID";') 
cur.execute('CREATE INDEX "idx_players_each_game_GameID_TeamID" ON players_each_game ("GameID", "TeamID_auto");')


conn.commit()





#Se vuoi che il trigger generi un errore quando trova un valore nullo nei vari HomePlayerX ecc togli AND NEW.HomePlayerX != '' dai TRIGGER sotto. Stessa cosa per i VisitorPlayerX
#Il punto è che quando il codice inizia con **, ovvero quando finisce un set, non vengono riportati i giocatori in campo, quindi in quel caso sono vuoti.
#In ogni caso, puoi contare per ogni GameID in Rilevations quanti sono i valori nulli in HomePlayer1 e VisitorPlayer1, ecc, e se sono più di 5 (visto che diamo massimo 5 set, in cui ci sono 5 ** dove i valori sono nulli), allora c'è un problema.






"""
Se provi a inserire un intero DataFrame in una tabella SQLite e uno dei vincoli CHECK viene violato in una delle righe, tutta l'operazione di inserimento fallisce e nessuna delle righe viene inserita.
SQLite infatti non supporta l'inserimento parziale di righe quando si utilizza un vincolo CHECK. Se una riga non rispetta il vincolo, l'inserimento dell'intero set di dati verrà annullato, e SQLite solleverà un errore.
"""




cartella_dvw = "C:/Users/<USER>/Documents/ModenaVolley/Codice/data/ALL_FILES"
numero_file_aggiunti_al_database = 0  #Ogni volta che runno, conto e mostro quanti nuovi file ho aggiunto

#try:
  #df_rilevationsFinale = pd.read_csv("C:/Users/<USER>/Documents/ModenaVolley/Codice/Rilevations.csv")
  #df_gamesFinale = pd.read_csv("C:/Users/<USER>/Documents/ModenaVolley/Codice/Games.csv")
  #print("I file Rilevations.csv e Games.csv sono stati letti come dataframe df_rilevationsFinale e df_gamesFinale")

#except:
#df_rilevationsFinale = pd.DataFrame(Rilevations)  # Crea un DataFrame vuoto iniziale
#df_gamesFinale = pd.DataFrame(Games)  # Crea un DataFrame vuoto iniziale
#print("Non erano presenti i file Rilevations.csv e Games.csv quindi i file df_rilevationsFinale e df_gamesFinale sono stati creati da zero")


#Itera su tutti i file nella cartella e applica la funzione:

for filename in os.listdir(cartella_dvw):  #os.listdir per ottenere una lista di tutti i file nella cartella specificata.
    if filename.endswith(".dvw"):
      file_path = os.path.join(cartella_dvw, filename)
        
      try:
        # Attempt to read the file using pandas
        df = pd.read_fwf(file_path, encoding='latin1', sep=';')  
        
        game_id = get_GameID_from_dvw(df)  #eventualmente convertilo in stringa se da problemi
        #print(game_id)
        
        #cursor = conn.cursor()  #Preparati a eseguire operazioni

        # Esegui la query per verificare la presenza di game_id nella colonna GameID
        cur.execute('SELECT 1 FROM games WHERE "originalGameID" = %s', (game_id,))  # %s è un segnaposto per un parametro. Utilizzare i parametri in questo modo è una buona pratica perché previene attacchi di tipo SQL injection e gestisce automaticamente la formattazione dei dati. Il parametro (game_id,) viene passato come tupla. (In SQLite non è %s ma è ?)
        result = cur.fetchone()

        
        if result is None:  # Se result è None, significa che game_id non è presente
          #print(f"Il GameID del file {file_path} non è presente nella tabella games (GameID: {game_id})")
            
          df_teams_each_game_home, df_teams_each_game_visitor, df_players_each_game_home, df_players_each_game_visitor, df_rilevations, df_games = elabora_file_dvw(df)  #Chiamiamo la funzione elabora_file_dvw per ogni file .dvw trovato, passando il percorso completo del file come argomento. Essa ci restituisce i due dataframe costruiti sul file
          #df_rilevations = df_rilevations.rename(columns={'EventiParticolari>': 'EventiParticolari'})
        
          #df_rilevations['GameID'] = df_rilevations['GameID'].astype('string').str.strip()
          #df_games['GameID'] = df_games['GameID'].astype('string').str.strip()

          if df_games.shape[0] == 0: #se il file non ha partita, allora non lo aggiungo ai dataframe finali
            print(f"ERRORE: Il file {file_path} non ha partita, non è stato aggiunto alcun GameID a df_games")
          elif df_games.shape[0] >1:
            print(f"ERRORE: Il file {file_path} ha più di una partita, sono stati aggiunti più GameID a df_games")
          else: #altrimenti, se c'è solo un record in df_games
            carica_dataframe(df_teams_each_game_home, df_teams_each_game_visitor, df_players_each_game_home, df_players_each_game_visitor, df_rilevations, df_games)

        else:  # Se result non è None, significa che game_id è già presente in games
            print(f"WARNING: Il GameID del file {file_path} è già presente nella tabella games (GameID: {game_id}). File non aggiunto al database.")
            pass
          
        #conn.commit()
             
        #df_rilevationsFinale = pd.concat([df_rilevationsFinale, df_rilevations], ignore_index=True)  #Concateniamo il DataFrame df_rilevations risultante al DataFrame finale
        #df_gamesFinale = pd.concat([df_gamesFinale, df_games], ignore_index=True)  #Concateniamo il DataFrame df_games risultante al DataFrame finale 
        
          
      except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
        traceback.print_exc() #Mostra l'errore, quindi anche la riga
      except pd.errors.ParserError:
        print(f"Error: Could not parse file at {file_path}. Please check the file format.")
        traceback.print_exc() #Mostra l'errore, quindi anche la riga
      except Exception as e:
        conn.rollback()  # Rollback in caso di errore
        print(f"Errore durante l'elaborazione del file {filename}: {e}")
        traceback.print_exc() #Mostra l'errore, quindi anche la riga


"""Alla fine controlla in games se due righe hanno lo stesso GameID, che è un problema, significa che c'è una partita duplicata, e va rimossa."""

#Controlla che RilevationNumber aumenti sempre di 1 oppure torni a 0. Se ci sono dei salti significa che ci sono delle righe mancanti.

#Se un file da errore, siccome ho messo dei vincoli e dei check, dovrò rimuovere il suo GameID dalla tabella games, e controllare che siano state eliminate le relative righe da Rilevations. Poi trovo qual è l'errore nel file, lo correggo manualmente nel file, e rilancio questo programma e in teoria dovrebbe essere inserito correttamente.



# Specifica il percorso e il nome del file CSV
#percorso_file_df_rilevations = 'C:/Users/<USER>/Documents/ModenaVolley/Codice/Rilevations.csv' 
#percorso_file_df_games = 'C:/Users/<USER>/Documents/ModenaVolley/Codice/Games.csv' 

# Salva il DataFrame in formato CSV
#df_rilevationsFinale.to_csv(percorso_file_df_rilevations, index=False)
#df_gamesFinale.to_csv(percorso_file_df_games, index=False)

#print(f"Rilevations.csv salvato con successo in: {percorso_file_df_rilevations}")
#print(f"Games.csv salvato con successo in: {percorso_file_df_games}")

cur.close()  # Chiudi la connessione
conn.close()

print("Sono stati aggiunti al database", numero_file_aggiunti_al_database, "nuovi file")
print(f"Database salvato con successo in: {current_directory}")



'''
#Se una riga non inizia con * o con a, la stampo per vedere cos'è (es >CONTESA)
for i, row in df3SCOUT_split.iterrows():
  if row['Col1'][0] != '*' and row['Col1'][0] != 'a':
    print(row['Col1'], "riga", i, file_path)
'''




