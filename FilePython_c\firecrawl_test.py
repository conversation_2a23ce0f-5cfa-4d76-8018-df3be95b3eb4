#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script per capire come funziona l'API di FireCrawl
"""

from firecrawl import FirecrawlApp
import json
from pathlib import Path
import os
import time

# Directory per salvare i dati
DATA_DIR = Path(os.path.expanduser("~")) / "Documents" / "ModenaVolley" / "VolleyballScraper" / "data"
DATA_DIR.mkdir(parents=True, exist_ok=True)

# API key per FireCrawl
FIRECRAWL_API_KEY = 'fc-b0cacd3f082a42e1b57afe5411d4a441'

def test_firecrawl_api():
    """
    Test dell'API di FireCrawl per capire come funziona
    """
    print("Test dell'API di FireCrawl")
    print("-" * 80)
    
    # Inizializza FireCrawl
    app = FirecrawlApp(api_key=FIRECRAWL_API_KEY)
    
    # URL di test (un giocatore di volleyball)
    test_url = "https://volleybox.net/it/mattia-orioli-p70358"
    
    print(f"Scaricando {test_url} con FireCrawl...")
    
    # Avvia il crawling
    crawl_result = app.crawl_url(test_url, params={
        'limit': 1,  # Solo la pagina specificata
        'scrapeOptions': {
            'formats': ['html']  # Vogliamo l'HTML completo
        }
    })
    
    # Stampa informazioni sull'oggetto restituito
    print("\nInformazioni sull'oggetto restituito:")
    print(f"Tipo: {type(crawl_result)}")
    print(f"Attributi: {dir(crawl_result)}")
    
    # Verifica se l'oggetto ha un metodo to_dict o to_json
    if hasattr(crawl_result, 'to_dict'):
        print("\nUsando il metodo to_dict:")
        crawl_dict = crawl_result.to_dict()
        print(f"Tipo del risultato: {type(crawl_dict)}")
        print(f"Chiavi: {list(crawl_dict.keys()) if isinstance(crawl_dict, dict) else 'Non è un dizionario'}")
    
    if hasattr(crawl_result, 'to_json'):
        print("\nUsando il metodo to_json:")
        crawl_json = crawl_result.to_json()
        print(f"Tipo del risultato: {type(crawl_json)}")
    
    # Verifica se l'oggetto ha un attributo urls
    if hasattr(crawl_result, 'urls'):
        print("\nInformazioni sull'attributo urls:")
        urls = crawl_result.urls
        print(f"Tipo: {type(urls)}")
        print(f"Lunghezza: {len(urls)}")
        
        if len(urls) > 0:
            print("\nInformazioni sul primo URL:")
            first_url = urls[0]
            print(f"Tipo: {type(first_url)}")
            print(f"Attributi: {dir(first_url)}")
            
            # Verifica se l'URL ha un attributo data
            if hasattr(first_url, 'data'):
                print("\nInformazioni sull'attributo data:")
                data = first_url.data
                print(f"Tipo: {type(data)}")
                print(f"Attributi: {dir(data)}")
                
                # Verifica se data ha un attributo html
                if hasattr(data, 'html'):
                    html = data.html
                    print(f"\nHTML trovato, lunghezza: {len(html)}")
                    
                    # Salva l'HTML per debug
                    html_file = DATA_DIR / "firecrawl_test.html"
                    with open(html_file, "w", encoding="utf-8") as f:
                        f.write(html)
                    print(f"HTML salvato in: {html_file}")
    
    # Prova a salvare l'oggetto come JSON
    try:
        print("\nTentativo di salvare l'oggetto come JSON:")
        result_file = DATA_DIR / "firecrawl_test_result.json"
        
        # Metodo 1: Converti manualmente in dizionario
        result_dict = {}
        
        # Aggiungi gli attributi principali
        for attr in ['id', 'status', 'created_at', 'updated_at']:
            if hasattr(crawl_result, attr):
                result_dict[attr] = getattr(crawl_result, attr)
        
        # Aggiungi gli URL
        if hasattr(crawl_result, 'urls'):
            result_dict['urls'] = []
            for url_obj in crawl_result.urls:
                url_dict = {}
                
                # Aggiungi gli attributi dell'URL
                for attr in ['url', 'title', 'status_code']:
                    if hasattr(url_obj, attr):
                        url_dict[attr] = getattr(url_obj, attr)
                
                # Aggiungi i dati
                if hasattr(url_obj, 'data'):
                    url_dict['data'] = {}
                    data_obj = url_obj.data
                    
                    # Aggiungi l'HTML
                    if hasattr(data_obj, 'html'):
                        url_dict['data']['html'] = data_obj.html
                    
                    # Aggiungi il markdown
                    if hasattr(data_obj, 'markdown'):
                        url_dict['data']['markdown'] = data_obj.markdown
                
                result_dict['urls'].append(url_dict)
        
        # Salva il dizionario come JSON
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(result_dict, f, indent=2, ensure_ascii=False)
        
        print(f"Risultato salvato in: {result_file}")
    except Exception as e:
        print(f"Errore durante il salvataggio come JSON: {str(e)}")
    
    print("\nTest completato!")

if __name__ == "__main__":
    test_firecrawl_api()
