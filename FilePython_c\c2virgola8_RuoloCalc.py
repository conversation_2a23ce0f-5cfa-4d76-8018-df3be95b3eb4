#In questo file calcoliamo le tabelle NumAzioniDaRuoloX in players_each_game, cos<PERSON> da poter calcolare RuoloCalc

#rinomina questo file invece di RuoloCalc, players_each_game_view_RuoloCalc
#Aggiungo le colonne NumAzioniDaRuoloX e RuoloCalc a players_each_game_view

import numpy as np 
import pandas as pd  #Realizzato con la versione di Pandas 2.2.3
import polars as pl
import os
import psycopg
from sqlalchemy import create_engine, text

conn = psycopg.connect(
    dbname="db_modena",           # database creato in pgAdmin4
    user="postgres",              # Il tuo nome utente PostgreSQL
    password="AcquaLevissima1",   # La password che hai scelto per 'postgres'
    host="localhost",             # 'localhost' se è sul tuo PC
    port=5432                     # La porta predefinita è 5432
)

# Crea un cursore per eseguire le query
cur = conn.cursor()

'''
cur.execute("""
WITH counts AS (
    SELECT 
        p."PlayerID",
        p."GameID",
        COUNT(CASE WHEN p."PlayerID" = r."Pcasa_ID" OR p."PlayerID" = r."Pospite_ID" THEN 1 END) AS "NumAzioniDaRuolo5",
        COUNT(CASE WHEN p."PlayerID" IN (r."C1casa_ID", r."C2casa_ID", r."C1ospite_ID", r."C2ospite_ID") THEN 1 END) AS "NumAzioniDaRuolo4",
        COUNT(CASE WHEN p."PlayerID" = r."Ocasa_ID" OR p."PlayerID" = r."Oospite_ID" THEN 1 END) AS "NumAzioniDaRuolo3",
        COUNT(CASE WHEN p."PlayerID" IN (r."S1casa_ID", r."S2casa_ID", r."S1ospite_ID", r."S2ospite_ID") THEN 1 END) AS "NumAzioniDaRuolo2"
    FROM players_each_game p
    LEFT JOIN rilevations_per_ruoli r 
        ON p."PlayerID" IN (r."Pcasa_ID", r."S1casa_ID", r."S2casa_ID", r."C1casa_ID", r."C2casa_ID", r."Ocasa_ID", r."Pospite_ID", r."S1ospite_ID", r."S2ospite_ID", r."C1ospite_ID", r."C2ospite_ID", r."Oospite_ID") AND p."GameID" = r."GameID"
    GROUP BY p."PlayerID", p."GameID"
)

UPDATE players_each_game p
SET 
    "NumAzioniDaRuolo5" = COALESCE(c."NumAzioniDaRuolo5", 0),
    "NumAzioniDaRuolo4" = COALESCE(c."NumAzioniDaRuolo4", 0),
    "NumAzioniDaRuolo3" = COALESCE(c."NumAzioniDaRuolo3", 0),
    "NumAzioniDaRuolo2" = COALESCE(c."NumAzioniDaRuolo2", 0)
FROM counts c
WHERE p."PlayerID" = c."PlayerID" AND p."GameID" = c."GameID";
""")

conn.commit()
print(f"Colonne NumAzioniDaRuoloX aggiornate con successo")


#Pendi solo le righe che non hanno null nell'ID e Nome e Cognome
# Contiamo il numero totale di giocatori



cur.execute("""
UPDATE players_each_game
SET 
    "RuoloCalc" = CASE 
        WHEN "Ruolo" = 1 THEN 1
        WHEN ("NumAzioniDaRuolo5" + "NumAzioniDaRuolo4" + "NumAzioniDaRuolo3" + "NumAzioniDaRuolo2") < 6 THEN "Ruolo"   --Perchè se uno entra per poche azioni al posto di un altro con un altro ruolo, non voglio contarlo come quel ruolo
        WHEN "NumAzioniDaRuolo5" >= GREATEST("NumAzioniDaRuolo4", "NumAzioniDaRuolo3", "NumAzioniDaRuolo2") THEN 5
        WHEN "NumAzioniDaRuolo4" >= GREATEST("NumAzioniDaRuolo5", "NumAzioniDaRuolo3", "NumAzioniDaRuolo2") THEN 4
        WHEN "NumAzioniDaRuolo3" >= GREATEST("NumAzioniDaRuolo5", "NumAzioniDaRuolo4", "NumAzioniDaRuolo2") THEN 3
        WHEN "NumAzioniDaRuolo2" >= GREATEST("NumAzioniDaRuolo5", "NumAzioniDaRuolo4", "NumAzioniDaRuolo3") THEN 2
        ELSE 0
    END
""")
'''



cur = conn.cursor()

conn.rollback()



# Creiamo la vista calcolando NumAzioniDaRuoloX direttamente nella query

# Ad ogni partita metto come RuoloCalcRaw il ruolo con il maggior numero di azioni (ovvero il NumAzioniDaRuoloX più alto). Se però la somma di NumAzioniDaRuoloX è 0, allora RuoloCalcRaw = Ruolo
# Alla fine, se RuoloCalcRaw è 0, allora metto come RuoloCalc il RuoloCalcRaw più frequente per quel giocatore in quella annata
cur.execute("""
DROP VIEW IF EXISTS players_each_game_view;
""")
conn.commit()
print("View players_each_game_view cancellata")

cur.execute("""
CREATE VIEW players_each_game_view AS
WITH counts AS (
    SELECT 
        p."PlayerID",
        p."GameID",
        p."TeamID_auto",
        COUNT(CASE WHEN p."PlayerID" = r."Pcasa_ID" OR p."PlayerID" = r."Pospite_ID" THEN 1 END) AS "NumAzioniDaRuolo5",
        COUNT(CASE WHEN p."PlayerID" IN (r."C1casa_ID", r."C2casa_ID", r."C1ospite_ID", r."C2ospite_ID") THEN 1 END) AS "NumAzioniDaRuolo4",
        COUNT(CASE WHEN p."PlayerID" = r."Ocasa_ID" OR p."PlayerID" = r."Oospite_ID" THEN 1 END) AS "NumAzioniDaRuolo3",
        COUNT(CASE WHEN p."PlayerID" IN (r."S1casa_ID", r."S2casa_ID", r."S1ospite_ID", r."S2ospite_ID") THEN 1 END) AS "NumAzioniDaRuolo2"
    FROM players_each_game p
    LEFT JOIN rilevations_per_ruoli r 
        ON p."PlayerID" IN (r."Pcasa_ID", r."S1casa_ID", r."S2casa_ID", r."C1casa_ID", r."C2casa_ID", r."Ocasa_ID", r."Pospite_ID", r."S1ospite_ID", r."S2ospite_ID", r."C1ospite_ID", r."C2ospite_ID", r."Oospite_ID") 
        AND p."GameID" = r."GameID"
    GROUP BY p."PlayerID", p."GameID", p."TeamID_auto"
),
view_base AS (
    SELECT
        g."Annata",
        EXTRACT(YEAR FROM g."DateSQL") AS "Year",
        peg."GameID",
        peg."TeamID_auto",
        peg."PlayerID_auto",
        peg."originalPlayerID",
        peg."PlayerID",
        peg."Nome",
        peg."Cognome",
        peg."NumeroMaglia",
        peg."Ruolo",
        t."TeamID",
        t."TeamName",
        t."HomeAway",
        COALESCE(c."NumAzioniDaRuolo2", 0) AS "NumAzioniDaRuolo2",
        COALESCE(c."NumAzioniDaRuolo3", 0) AS "NumAzioniDaRuolo3",
        COALESCE(c."NumAzioniDaRuolo4", 0) AS "NumAzioniDaRuolo4",
        COALESCE(c."NumAzioniDaRuolo5", 0) AS "NumAzioniDaRuolo5",
        -- Calcolo RuoloCalcRaw, quello iniziale
        CASE 
            -- WHEN peg."Ruolo" = 1 THEN 1
            WHEN (COALESCE(c."NumAzioniDaRuolo2", 0) +
                  COALESCE(c."NumAzioniDaRuolo3", 0) +
                  COALESCE(c."NumAzioniDaRuolo4", 0) +
                  COALESCE(c."NumAzioniDaRuolo5", 0)) = 0 THEN peg."Ruolo"
            ELSE (
                SELECT ruolo FROM (
                    VALUES 
                        (2, COALESCE(c."NumAzioniDaRuolo2", 0)),
                        (3, COALESCE(c."NumAzioniDaRuolo3", 0)),
                        (4, COALESCE(c."NumAzioniDaRuolo4", 0)),
                        (5, COALESCE(c."NumAzioniDaRuolo5", 0))
                ) AS ruolo_count(ruolo, count)
                ORDER BY count DESC, ruolo ASC
                LIMIT 1
            )
        END AS "RuoloCalcRaw"
    FROM
        players_each_game peg
    LEFT JOIN
        counts c ON peg."PlayerID" = c."PlayerID" AND peg."GameID" = c."GameID" AND peg."TeamID_auto" = c."TeamID_auto"
    JOIN
        teams_each_game t ON peg."GameID" = t."GameID" AND peg."TeamID_auto" = t."TeamID_auto"
    LEFT JOIN
        games g ON peg."GameID" = g."GameID"
),
ruolo_preferito AS (
    SELECT
        "PlayerID",
        "Annata",
        ruolo,
        COUNT(*) AS cnt,
        ROW_NUMBER() OVER (
            PARTITION BY "PlayerID", "Annata"
            ORDER BY COUNT(*) DESC, ruolo ASC
        ) AS rn
    FROM (
        SELECT 
            "PlayerID", 
            "Annata", 
            "RuoloCalcRaw" AS ruolo
        FROM view_base
        WHERE "RuoloCalcRaw" <> 0
    ) AS ruoli
    GROUP BY "PlayerID", "Annata", ruolo
),
preferenza_unica AS (
    SELECT 
        "PlayerID", 
        "Annata", 
        ruolo AS "RuoloPreferito"
    FROM ruolo_preferito
    WHERE rn = 1
)


SELECT
    vb.*,
    CASE 
        WHEN vb."RuoloCalcRaw" = 0 AND p."RuoloPreferito" IS NOT NULL THEN p."RuoloPreferito"
        ELSE vb."RuoloCalcRaw"
    END AS "RuoloCalc"
FROM view_base vb
LEFT JOIN preferenza_unica p 
    ON vb."PlayerID" = p."PlayerID" AND vb."Annata" = p."Annata";

""")
conn.commit()

print("Vista players_each_game_view creata con successo")






