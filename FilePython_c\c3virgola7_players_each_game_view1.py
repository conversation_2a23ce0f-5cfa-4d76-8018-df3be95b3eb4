#In questo file creo una nuova VIEW players_each_game_view1, sperando sia la view definitiva, in cui metto come colonne tutte le metriche box score che posso, usando le CTE
#Le metriche (colonne) le calcolo in base alla view rilevations_libero_view, siccome voglio considerare il libero in campo (oppure in base a rilevations_libero_battute_view, che contiene solo le azioni con le battute)


import numpy as np 
import pandas as pd  #Realizzato con la versione di Pandas 2.2.3
import os
import psycopg
from sqlalchemy import create_engine, text


conn = psycopg.connect(
    dbname="db_modena",           # database creato in pgAdmin4
    user="postgres",              # Il tuo nome utente PostgreSQL
    password="AcquaLevissima1",   # La password che hai scelto per 'postgres'
    host="localhost",             # 'localhost' se è sul tuo PC
    port=5432                     # La porta predefinita è 5432
)

cur = conn.cursor() 

conn.rollback()





cur.execute("DROP VIEW IF EXISTS players_each_game_view1")
conn.commit()

cur.execute("""
CREATE OR REPLACE VIEW players_each_game_view2 AS

WITH base AS (
  SELECT *
  FROM players_each_game_view
),

num_azioni_giocate AS (
    SELECT "GameID", "probHomePlayer1_ID" AS "PlayerID" FROM rilevations_libero_battute_view
    UNION ALL
    SELECT "GameID", "probHomePlayer2_ID" FROM rilevations_libero_battute_view
    UNION ALL
    SELECT "GameID", "probHomePlayer3_ID" FROM rilevations_libero_battute_view
    UNION ALL
    SELECT "GameID", "probHomePlayer4_ID" FROM rilevations_libero_battute_view
    UNION ALL
    SELECT "GameID", "probHomePlayer5_ID" FROM rilevations_libero_battute_view
    UNION ALL
    SELECT "GameID", "probHomePlayer6_ID" FROM rilevations_libero_battute_view
    UNION ALL
    SELECT "GameID", "probVisitorPlayer1_ID" FROM rilevations_libero_battute_view
    UNION ALL
    SELECT "GameID", "probVisitorPlayer2_ID" FROM rilevations_libero_battute_view
    UNION ALL
    SELECT "GameID", "probVisitorPlayer3_ID" FROM rilevations_libero_battute_view
    UNION ALL
    SELECT "GameID", "probVisitorPlayer4_ID" FROM rilevations_libero_battute_view
    UNION ALL
    SELECT "GameID", "probVisitorPlayer5_ID" FROM rilevations_libero_battute_view
    UNION ALL
    SELECT "GameID", "probVisitorPlayer6_ID" FROM rilevations_libero_battute_view
),

conteggi AS (
    SELECT "GameID", "PlayerID", COUNT(*) AS "NumAzioniGiocate"
    FROM num_azioni_giocate
    WHERE "PlayerID" IS NOT NULL
    GROUP BY "GameID", "PlayerID"
),

num_azioni_giocate_1_linea AS (
    SELECT "GameID", "probHomePlayer2_ID" AS "PlayerID" FROM rilevations_libero_battute_view
    UNION ALL
    SELECT "GameID", "probHomePlayer3_ID" FROM rilevations_libero_battute_view
    UNION ALL
    SELECT "GameID", "probHomePlayer4_ID" FROM rilevations_libero_battute_view
    UNION ALL
    SELECT "GameID", "probVisitorPlayer2_ID" FROM rilevations_libero_battute_view
    UNION ALL
    SELECT "GameID", "probVisitorPlayer3_ID" FROM rilevations_libero_battute_view
    UNION ALL
    SELECT "GameID", "probVisitorPlayer4_ID" FROM rilevations_libero_battute_view
),

conteggi_1_linea AS (
    SELECT "GameID", "PlayerID", COUNT(*) AS "NumAzioniGiocate1Linea"
    FROM num_azioni_giocate_1_linea
    WHERE "PlayerID" IS NOT NULL
    GROUP BY "GameID", "PlayerID"
),

num_azioni_giocate_2_linea AS (
    SELECT "GameID", "probHomePlayer1_ID" AS "PlayerID" FROM rilevations_libero_battute_view
    UNION ALL
    SELECT "GameID", "probHomePlayer5_ID" FROM rilevations_libero_battute_view
    UNION ALL
    SELECT "GameID", "probHomePlayer6_ID" FROM rilevations_libero_battute_view
    UNION ALL
    SELECT "GameID", "probVisitorPlayer1_ID" FROM rilevations_libero_battute_view
    UNION ALL
    SELECT "GameID", "probVisitorPlayer5_ID" FROM rilevations_libero_battute_view
    UNION ALL
    SELECT "GameID", "probVisitorPlayer6_ID" FROM rilevations_libero_battute_view
),

conteggi_2_linea AS (
    SELECT "GameID", "PlayerID", COUNT(*) AS "NumAzioniGiocate2Linea"
    FROM num_azioni_giocate_2_linea
    WHERE "PlayerID" IS NOT NULL
    GROUP BY "GameID", "PlayerID"
),

total_touch AS (
    SELECT "GameID", "NumeroMaglia_ID" AS "PlayerID", COUNT(*) AS "TotalTouch"
    FROM rilevations_libero_view
    WHERE "NumeroMaglia_ID" IS NOT NULL
    GROUP BY "GameID", "NumeroMaglia_ID"
),

--Attenzione perchè forse devo contare anche i punti in alzata che hanno TargAttk = 'S'
punti_fatti AS (
    SELECT "GameID", "NumeroMaglia_ID" AS "PlayerID", COUNT(*) AS "PuntiFatti"
    FROM rilevations_libero_view
    WHERE "Foundamental" IN ('S', 'A', 'B')
      AND "Eval" = '#'
      AND "NumeroMaglia_ID" IS NOT NULL
    GROUP BY "GameID", "NumeroMaglia_ID"
),

set_giocati AS (
    SELECT "GameID", "SetNumber", "NumeroMaglia_ID" AS "PlayerID"
    FROM rilevations_libero_view
    WHERE "NumeroMaglia_ID" IS NOT NULL
    GROUP BY "GameID", "SetNumber", "NumeroMaglia_ID"
),

num_set_giocati AS (
    SELECT "GameID", "PlayerID", COUNT(*) AS "NumSetGiocati"
    FROM set_giocati
    GROUP BY "GameID", "PlayerID"
),

punti_per_set AS (
    SELECT
        pf."GameID",
        pf."PlayerID",
        pf."PuntiFatti",
        ns."NumSetGiocati",
        CASE
            WHEN ns."NumSetGiocati" > 0 THEN pf."PuntiFatti"::FLOAT / ns."NumSetGiocati"
            ELSE NULL
        END AS "PuntiPerSet"
    FROM punti_fatti pf
    LEFT JOIN num_set_giocati ns ON pf."GameID" = ns."GameID" AND pf."PlayerID" = ns."PlayerID"
),

punti_per_azione AS (
    SELECT
        pf."GameID",
        pf."PlayerID",
        pf."PuntiFatti",
        c."NumAzioniGiocate",
        CASE
            WHEN c."NumAzioniGiocate" > 0 THEN pf."PuntiFatti"::FLOAT / c."NumAzioniGiocate"
            ELSE NULL
        END AS "PuntiPerAzione"
    FROM punti_fatti pf
    LEFT JOIN conteggi c
        ON pf."GameID" = c."GameID" AND pf."PlayerID" = c."PlayerID"
),

punti_per_100azioni AS (
    SELECT
        ppa."GameID",
        ppa."PlayerID",
        ppa."PuntiPerAzione" * 100 AS "PuntiPer100Azioni"
    FROM punti_per_azione ppa
),

punti_fatti1linea AS (
    SELECT
        r."GameID",
        r."NumeroMaglia_ID" AS "PlayerID",
        COUNT(*) AS "PuntiFatti1Linea"
    FROM
        rilevations_libero_view r
    WHERE
        r."Foundamental" IN ('S', 'A', 'B')
        AND r."Eval" = '#'
        AND r."NumeroMaglia_ID" IS NOT NULL
        AND (
            r."probHomePlayer2_ID" = r."NumeroMaglia_ID" OR
            r."probHomePlayer3_ID" = r."NumeroMaglia_ID" OR
            r."probHomePlayer4_ID" = r."NumeroMaglia_ID" OR
            r."probVisitorPlayer2_ID" = r."NumeroMaglia_ID" OR
            r."probVisitorPlayer3_ID" = r."NumeroMaglia_ID" OR
            r."probVisitorPlayer4_ID" = r."NumeroMaglia_ID"
        )
    GROUP BY
        r."GameID", r."NumeroMaglia_ID"
),

punti_fatti2linea AS (
    SELECT
        r."GameID",
        r."NumeroMaglia_ID" AS "PlayerID",
        COUNT(*) AS "PuntiFatti2Linea"
    FROM
        rilevations_libero_view r
    WHERE
        r."Foundamental" IN ('S', 'A', 'B')
        AND r."Eval" = '#'
        AND r."NumeroMaglia_ID" IS NOT NULL
        AND (
            r."probHomePlayer1_ID" = r."NumeroMaglia_ID" OR
            r."probHomePlayer5_ID" = r."NumeroMaglia_ID" OR
            r."probHomePlayer6_ID" = r."NumeroMaglia_ID" OR
            r."probVisitorPlayer1_ID" = r."NumeroMaglia_ID" OR
            r."probVisitorPlayer5_ID" = r."NumeroMaglia_ID" OR
            r."probVisitorPlayer6_ID" = r."NumeroMaglia_ID"
        )
    GROUP BY
        r."GameID", r."NumeroMaglia_ID"
),

punti_per_azione1Linea AS (
    SELECT
        punti_fatti1linea."GameID",
        punti_fatti1linea."PlayerID",
        punti_fatti1linea."PuntiFatti1Linea",
        c1."NumAzioniGiocate1Linea",
        CASE
            WHEN c1."NumAzioniGiocate1Linea" > 0 THEN punti_fatti1linea."PuntiFatti1Linea"::FLOAT / c1."NumAzioniGiocate1Linea"
            ELSE NULL
        END AS "PuntiPerAzione1Linea"
    FROM punti_fatti1linea
    LEFT JOIN conteggi_1_linea AS c1
        ON punti_fatti1linea."GameID" = c1."GameID" AND punti_fatti1linea."PlayerID" = c1."PlayerID"
),

punti_per_azione2Linea AS (
    SELECT
        punti_fatti2linea."GameID",
        punti_fatti2linea."PlayerID",
        punti_fatti2linea."PuntiFatti2Linea",
        c2."NumAzioniGiocate2Linea",
        CASE
            WHEN c2."NumAzioniGiocate2Linea" > 0 THEN punti_fatti2linea."PuntiFatti2Linea"::FLOAT / c2."NumAzioniGiocate2Linea"
            ELSE NULL
        END AS "PuntiPerAzione2Linea"
    FROM punti_fatti2linea
    LEFT JOIN conteggi_2_linea AS c2
        ON punti_fatti2linea."GameID" = c2."GameID" AND punti_fatti2linea."PlayerID" = c2."PlayerID"
),


--AverageEval


total_error AS (
    SELECT "GameID", "NumeroMaglia_ID" AS "PlayerID", COUNT(*) AS "TotalError"
    FROM rilevations_libero_view
    WHERE "Eval" = '='
      AND "NumeroMaglia_ID" IS NOT NULL
    GROUP BY "GameID", "NumeroMaglia_ID"
),

total_error_percentage AS (
    SELECT
        te."GameID",
        te."PlayerID",
        te."TotalError",
        tt."TotalTouch",
        CASE
            WHEN tt."TotalTouch" > 0 THEN te."TotalError"::FLOAT / tt."TotalTouch"
            ELSE NULL
        END AS "TotalErrorPercentage"
    FROM total_error te
    LEFT JOIN total_touch tt
        ON te."GameID" = tt."GameID" AND te."PlayerID" = tt."PlayerID"
),

total_negative AS (
    SELECT "GameID", "NumeroMaglia_ID" AS "PlayerID", COUNT(*) AS "TotalNegative"
    FROM rilevations_libero_view
    WHERE "Eval" IN ('-', '=')
      AND "NumeroMaglia_ID" IS NOT NULL
    GROUP BY "GameID", "NumeroMaglia_ID"
),

total_negative_percentage AS (
    SELECT
        tn."GameID",
        tn."PlayerID",
        tn."TotalNegative",
        tt."TotalTouch",
        CASE
            WHEN tt."TotalTouch" > 0 THEN tn."TotalNegative"::FLOAT / tt."TotalTouch"
            ELSE NULL
        END AS "TotalNegativePercentage"
    FROM total_negative tn
    LEFT JOIN total_touch tt
        ON tn."GameID" = tt."GameID" AND tn."PlayerID" = tt."PlayerID"
),

total_perfect AS (
    SELECT "GameID", "NumeroMaglia_ID" AS "PlayerID", COUNT(*) AS "TotalPerfect"
    FROM rilevations_libero_view
    WHERE "Eval" IN ('#')
      AND "NumeroMaglia_ID" IS NOT NULL
    GROUP BY "GameID", "NumeroMaglia_ID"
),

total_perfect_percentage AS (
    SELECT
        tp."GameID",
        tp."PlayerID",
        tp."TotalPerfect",
        tt."TotalTouch",
        CASE
            WHEN tt."TotalTouch" > 0 THEN tp."TotalPerfect"::FLOAT / tt."TotalTouch"
            ELSE NULL
        END AS "TotalPerfectPercentage"
    FROM total_perfect tp
    LEFT JOIN total_touch tt
        ON tp."GameID" = tt."GameID" AND tp."PlayerID" = tt."PlayerID"
),

total_positive AS (
    SELECT "GameID", "NumeroMaglia_ID" AS "PlayerID", COUNT(*) AS "TotalPositive"
    FROM rilevations_libero_view
    WHERE "Eval" IN ('#', '+')
      AND "NumeroMaglia_ID" IS NOT NULL
    GROUP BY "GameID", "NumeroMaglia_ID"
),

total_positive_percentage AS (
    SELECT
        tp."GameID",
        tp."PlayerID",
        tp."TotalPositive",
        tt."TotalTouch",
        CASE
            WHEN tt."TotalTouch" > 0 THEN tp."TotalPositive"::FLOAT / tt."TotalTouch"
            ELSE NULL
        END AS "TotalPositivePercentage"
    FROM total_positive tp
    LEFT JOIN total_touch tt
        ON tp."GameID" = tt."GameID" AND tp."PlayerID" = tt."PlayerID"
),

attacks AS (
    SELECT "GameID", "NumeroMaglia_ID" AS "PlayerID", COUNT(*) AS "Attacks"
    FROM rilevations_libero_view
    WHERE "Foundamental" IN ('A')
      AND "NumeroMaglia_ID" IS NOT NULL
    GROUP BY "GameID", "NumeroMaglia_ID"
),

team_attacks AS (
  SELECT
    b."GameID",
    b."TeamID_auto",
    COUNT(DISTINCT r."RilevationNumber") AS "TeamAttacks"  --conto quanti tocchi distinti ci sono stati
  FROM base AS b
  JOIN rilevations_libero_view AS r
    ON r."GameID" = b."GameID"
    AND r."whichTeamID" = b."TeamID_auto"
    AND r."Foundamental" = 'A'
  GROUP BY b."GameID", b."TeamID_auto"
),

team_attacks_while_on_field AS (
  -- unpivot dei giocatori in campo in cui si verifica un attacco di squadra
  SELECT 
    r."GameID",
    slot."PlayerID",
    COUNT(*) AS "TeamAttacksWhileOnField"
  FROM rilevations_libero_view r
  -- consideriamo solo gli attacchi
  JOIN LATERAL (
    VALUES
      (r."probHomePlayer1_ID"), (r."probHomePlayer2_ID"), (r."probHomePlayer3_ID"),
      (r."probHomePlayer4_ID"), (r."probHomePlayer5_ID"), (r."probHomePlayer6_ID"),
      (r."probVisitorPlayer1_ID"), (r."probVisitorPlayer2_ID"), (r."probVisitorPlayer3_ID"),
      (r."probVisitorPlayer4_ID"), (r."probVisitorPlayer5_ID"), (r."probVisitorPlayer6_ID")
  ) AS slot("PlayerID")
    ON slot."PlayerID" IS NOT NULL
  -- solo quando è un attacco e la squadra che attacca è quella del player
  WHERE r."Foundamental" = 'A'
    AND r."whichTeamID" = (
      SELECT b."TeamID_auto"
      FROM base b
      WHERE b."GameID" = r."GameID"
        AND b."PlayerID" = slot."PlayerID"
    )
  GROUP BY r."GameID", slot."PlayerID"
),

role_team_attacks AS (
  SELECT 
    b."GameID",
    b."PlayerID",
    COUNT(*) AS "RoleTeamAttacks"
  FROM base b

  -- Righe di attacco fatte dalla squadra del player
  JOIN rilevations_libero_view r
    ON r."GameID" = b."GameID"
   AND r."whichTeamID" = b."TeamID_auto"
   AND r."Foundamental" = 'A'

  -- JOIN per controllare se chi ha fatto l'attacco ha lo stesso ruolo
  JOIN base compagno
    ON compagno."GameID" = r."GameID"
   AND compagno."PlayerID" = r."NumeroMaglia_ID"
   AND compagno."TeamID_auto" = b."TeamID_auto"
   AND compagno."RuoloCalc" = b."RuoloCalc"

  GROUP BY b."GameID", b."PlayerID"
),

--Tabella che contiene le azioni in cui ogni giocatore era in campo
actions_on_field AS (
    -- 1) recupera le azioni in cui ogni giocatore era in campo
    SELECT DISTINCT
      r."GameID",
      r."ActionNumber",
      slot."PlayerID"
    FROM rilevations_libero_view r
    JOIN LATERAL (
      VALUES
        (r."probHomePlayer1_ID"), (r."probHomePlayer2_ID"),
        (r."probHomePlayer3_ID"), (r."probHomePlayer4_ID"),
        (r."probHomePlayer5_ID"), (r."probHomePlayer6_ID"),
        (r."probVisitorPlayer1_ID"), (r."probVisitorPlayer2_ID"),
        (r."probVisitorPlayer3_ID"), (r."probVisitorPlayer4_ID"),
        (r."probVisitorPlayer5_ID"), (r."probVisitorPlayer6_ID")
    ) AS slot("PlayerID")
      ON slot."PlayerID" IS NOT NULL
),

--Tabella che contiene le azioni di attacco di ogni compagno con stesso ruolo
role_attacks AS (
    -- 2) recupera le azioni di attacco di ogni compagno con stesso ruolo
    SELECT DISTINCT
      r."GameID",
      r."ActionNumber",
      comp."PlayerID" AS "PlayerID_base"
    FROM base b
    JOIN rilevations_libero_view r
      ON r."GameID" = b."GameID"
     AND r."whichTeamID" = b."TeamID_auto"
     AND r."Foundamental" = 'A'
    -- unpivot per trovare il compagno che ha eseguito l'attacco
    JOIN LATERAL (
      VALUES
        (r."probHomePlayer1_ID"), (r."probHomePlayer2_ID"),
        (r."probHomePlayer3_ID"), (r."probHomePlayer4_ID"),
        (r."probHomePlayer5_ID"), (r."probHomePlayer6_ID"),
        (r."probVisitorPlayer1_ID"), (r."probVisitorPlayer2_ID"),
        (r."probVisitorPlayer3_ID"), (r."probVisitorPlayer4_ID"),
        (r."probVisitorPlayer5_ID"), (r."probVisitorPlayer6_ID")
    ) AS slot("PlayerID")
      ON slot."PlayerID" IS NOT NULL
    -- filtro sul ruolo: il compagno deve avere lo stesso ruolo di b
    JOIN base comp
      ON comp."GameID"   = r."GameID"
     AND comp."PlayerID" = slot."PlayerID"
     AND comp."RuoloCalc" = b."RuoloCalc"
     AND comp."TeamID_auto" = b."TeamID_auto"
),


role_team_attacks_while_on_field AS (
    -- 3) conta quante di queste azioni sono comuni ad entrambi i set
    SELECT
      f."PlayerID"       AS "PlayerID_base",
      f."GameID",
      COUNT(*) AS "RoleTeamAttacksWhileOnField"
    FROM actions_on_field f
    JOIN role_attacks a
      ON a."GameID" = f."GameID"
     AND a."ActionNumber" = f."ActionNumber"
     AND a."PlayerID_base" = f."PlayerID"
    GROUP BY
      f."PlayerID", f."GameID"
),


AttkParticipation AS (
    SELECT
        attacks."GameID",
        attacks."PlayerID",
        attacks."Attacks",
        tawof."TeamAttacksWhileOnField",
        CASE
            WHEN tawof."TeamAttacksWhileOnField" > 0 THEN attacks."Attacks"::FLOAT / tawof."TeamAttacksWhileOnField"
            ELSE NULL
        END AS "AttkParticipation"
    FROM attacks
    LEFT JOIN team_attacks_while_on_field AS tawof
        ON attacks."GameID" = tawof."GameID" AND attacks."PlayerID" = tawof."PlayerID"
)










--Adesso seleziono tutte le colonne che ho calcolato finora

SELECT 
    base.*, 
    c."NumAzioniGiocate",
    c1."NumAzioniGiocate1Linea",
    c2."NumAzioniGiocate2Linea",
    tt."TotalTouch",
    pf."PuntiFatti",
    ns."NumSetGiocati",
    pps."PuntiPerSet",
    ppa."PuntiPerAzione",
    ppa100."PuntiPer100Azioni",
    pf1."PuntiFatti1Linea",
    pf2."PuntiFatti2Linea",
    ppa1."PuntiPerAzione1Linea",
    ppa2."PuntiPerAzione2Linea",
    te."TotalError",
    tep."TotalErrorPercentage",
    tn."TotalNegative",
    tnp."TotalNegativePercentage",
    tp."TotalPerfect",
    tpp."TotalPerfectPercentage",
    tpos."TotalPositive",
    tposp."TotalPositivePercentage",
    attks."Attacks",
    rta."RoleTeamAttacks",
    rtawf."RoleTeamAttacksWhileOnField",
    ta."TeamAttacks",
    tac."TeamAttacksWhileOnField",
    ap."AttkParticipation"
    
    
FROM 
    base
LEFT JOIN conteggi AS c
    ON base."GameID" = c."GameID" AND base."PlayerID" = c."PlayerID"
LEFT JOIN conteggi_1_linea AS c1
    ON base."GameID" = c1."GameID" AND base."PlayerID" = c1."PlayerID"
LEFT JOIN conteggi_2_linea AS c2
    ON base."GameID" = c2."GameID" AND base."PlayerID" = c2."PlayerID"
LEFT JOIN total_touch AS tt
    ON base."GameID" = tt."GameID" AND base."PlayerID" = tt."PlayerID"
LEFT JOIN punti_fatti AS pf
    ON base."GameID" = pf."GameID" AND base."PlayerID" = pf."PlayerID"
LEFT JOIN num_set_giocati AS ns
    ON base."GameID" = ns."GameID" AND base."PlayerID" = ns."PlayerID"
LEFT JOIN punti_per_set AS pps
    ON base."GameID" = pps."GameID" AND base."PlayerID" = pps."PlayerID"
LEFT JOIN punti_per_azione AS ppa
    ON base."GameID" = ppa."GameID" AND base."PlayerID" = ppa."PlayerID"
LEFT JOIN punti_per_100azioni AS ppa100
    ON base."GameID" = ppa100."GameID" AND base."PlayerID" = ppa100."PlayerID"
LEFT JOIN punti_fatti1linea AS pf1
    ON base."GameID" = pf1."GameID" AND base."PlayerID" = pf1."PlayerID"
LEFT JOIN punti_fatti2linea AS pf2
    ON base."GameID" = pf2."GameID" AND base."PlayerID" = pf2."PlayerID"
LEFT JOIN punti_per_azione1Linea AS ppa1
    ON base."GameID" = ppa1."GameID" AND base."PlayerID" = ppa1."PlayerID"
LEFT JOIN punti_per_azione2Linea AS ppa2
    ON base."GameID" = ppa2."GameID" AND base."PlayerID" = ppa2."PlayerID"
LEFT JOIN total_error AS te
    ON base."GameID" = te."GameID" AND base."PlayerID" = te."PlayerID"
LEFT JOIN total_error_percentage AS tep
    ON base."GameID" = tep."GameID" AND base."PlayerID" = tep."PlayerID"
LEFT JOIN total_negative AS tn
    ON base."GameID" = tn."GameID" AND base."PlayerID" = tn."PlayerID"
LEFT JOIN total_negative_percentage AS tnp
    ON base."GameID" = tnp."GameID" AND base."PlayerID" = tnp."PlayerID"
LEFT JOIN total_perfect AS tp
    ON base."GameID" = tp."GameID" AND base."PlayerID" = tp."PlayerID"
LEFT JOIN total_perfect_percentage AS tpp
    ON base."GameID" = tpp."GameID" AND base."PlayerID" = tpp."PlayerID"
LEFT JOIN total_positive AS tpos
    ON base."GameID" = tpos."GameID" AND base."PlayerID" = tpos."PlayerID"
LEFT JOIN total_positive_percentage AS tposp
    ON base."GameID" = tposp."GameID" AND base."PlayerID" = tposp."PlayerID"
LEFT JOIN attacks AS attks
    ON base."GameID" = attks."GameID" AND base."PlayerID" = attks."PlayerID"
LEFT JOIN team_attacks_while_on_field AS tac
    ON base."GameID" = tac."GameID" AND base."PlayerID" = tac."PlayerID"
LEFT JOIN team_attacks AS ta
    ON base."GameID" = ta."GameID" AND base."TeamID_auto" = ta."TeamID_auto"
LEFT JOIN role_team_attacks rta
    ON base."GameID" = rta."GameID"
    AND base."PlayerID" = rta."PlayerID"
LEFT JOIN role_team_attacks_while_on_field rtawf
    ON base."GameID"   = rtawf."GameID" AND base."PlayerID" = rtawf."PlayerID_base"
LEFT JOIN AttkParticipation AS ap
    ON base."GameID" = ap."GameID" AND base."PlayerID" = ap."PlayerID";


""")

conn.commit()
print("✅ VIEW players_each_game_view1 creata con successo")


#Invece di lasciare valori NULL metti 0




'''

cur.execute("DROP TABLE IF EXISTS players_each_game1_table")
cur.execute("""
    CREATE TABLE players_each_game1_table AS
    SELECT * FROM players_each_game_view1
""")
print("✅ MATERIALIZED VIEW players_each_game1_table creata con successo")
conn.commit()

'''

