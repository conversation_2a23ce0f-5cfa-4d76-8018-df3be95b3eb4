<p align="center">
  <img src="https://github.com/shukkkur/VolleyVision/blob/280fed79d290c1cf6d53c869fa60355eeb04d148/assets/vv_logo.png" width=200>
</p>

<h1 align="center">
   Contributing to VolleyVision
</h1>


Hey there!

Thank you for considering contributing to VolleyVision! Your help and support are immensly appreciated.

## How to Contribute

There are several ways you can contribute to the project:

1. **Open an Issue**: If you come across any bugs, have a feature request, or want to provide feedback, please open a new issue in the [issue tracker](https://github.com/shukkkur/VolleyVision/issues/new/choose). We appreciate detailed explanations and clear descriptions of the problems or ideas you have.

2. **Start a Discussion**: For more open-ended discussions, ideas, or questions, you can visit the [discussions](https://github.com/shukkkur/VolleyVision/discussions) section. Feel free to share your thoughts on the project's potentials, drawbacks, current methods in use, improvements, deployment, documentation, or any other relevant topic.



### Write Documentation

VolleyVision could always use more documentation, whether as part of the
official tennis-tracking docs, in docstrings, or even on the web in blog posts,
articles, and such.

### Annotate Data

Created [datasets](https://github.com/shukkkur/VolleyVision#-datasets) are still far from perfect, therefore data annotation on [RoboFlow](https://roboflow.com/annotate) is the best way to contribute! If you are interested, let me know - <<EMAIL>>
### Submit Feedback

The best way to send feedback is to open a new [discussions](https://github.com/shukkkur/VolleyVision/discussions) or an [issue](https://github.com/shukkkur/VolleyVision/issues/new/choose). 

If you are proposing a feature:

- Explain in detail how it would work.
- Keep the scope as narrow as possible, to make it easier to implement.
- Remember that this is a open-source project, and that contributions 
  are welcomed :)

Thank you for your interest in contributing to VolleyVision. Your contributions will help make this project better!
