
#Intanto creo una nuova VIEW che contiene solo le righe di Rilevations da cui voglio capire la formazione.
#Per ogni GameID, prendo solo la riga della prima azione di ogni set

import numpy as np 
import pandas as pd  #Realizzato con la versione di Pandas 2.2.3
import polars as pl
import os
import psycopg
from sqlalchemy import create_engine, text

conn = psycopg.connect(
    dbname="db_modena",           # database creato in pgAdmin4
    user="postgres",              # Il tuo nome utente PostgreSQL
    password="AcquaLevissima1",   # La password che hai scelto per 'postgres'
    host="localhost",             # 'localhost' se è sul tuo PC
    port=5432                     # La porta predefinita è 5432
)

# Crea un cursore per eseguire le query
cur = conn.cursor()


cur.execute("DROP VIEW IF EXISTS rilevations_per_ruoli")
conn.commit()

cur.execute("""
CREATE VIEW rilevations_per_ruoli AS
SELECT 
    r."GameID",
    r."SetNumber",
    r."ActionNumber",
    r."RilevationNumber",
    r."TouchNumber",
    r."Foundamental",
    r."Pcasa_ID",
    r."S1casa_ID",
    r."C2casa_ID",
    r."Ocasa_ID",
    r."S2casa_ID",
    r."C1casa_ID",
    r."Pospite_ID",
    r."S1ospite_ID",
    r."C2ospite_ID",
    r."Oospite_ID",
    r."S2ospite_ID",
    r."C1ospite_ID"

FROM rilevations_view r
WHERE r."Foundamental" = 'S'
""")

conn.commit()

print("VIEW Rilevations_per_ruoli creata con successo")




