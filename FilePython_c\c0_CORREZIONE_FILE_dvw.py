#In questo file io correggo i file dvw che hanno problemi, in modo che possano essere letti dal programma di conversione

#Siccome dobbiamo inserire le colonne HomePlayerX_ID_auto al momento dell'inserimento, abbiamo bisogno che non ci siano problemi nei file dvw del tipo che ci sia più volte lo stesso giocatore in un roster, oppure che manchi nella formazione quando invece c'è, oppure che manchi tra i giocatori in campo.
#Per questo motivo, una volta scoperti i problemi, li aggiustiamo alla fonte, modificando i file dvw

import os
import pandas as pd
import numpy as np
import re
import traceback

cartella_dvw = "C:/Users/<USER>/Documents/ModenaVolley/Codice/ALL_FILES"


#non serve farlo
'''

#Nel file "&2023-03-15 cev bel-mod 2-3 r.dvw" Mateusz Bieniek compare due volte con maglie diverse nella sezione [3PLAYERS-H]. Elimino la riga in cui ha maglia 17 e lascio la riga in cui ha maglia 20.
file_path = os.path.join(cartella_dvw, "&2023-03-15 cev bel-mod 2-3 r.dvw")
riga = "0;17;10;;;;;;511;Bieniek;Mateusz;;;4;False;;;24269656E69656B;24D61746575737A;2;;;"

# Apro il file in lettura
with open(file_path, "r", encoding="latin-1") as f:
    lines = f.readlines()  # Legge tutte le righe del file in una lista

# Rimuovi la riga esatta (attenzione a eventuali spazi o newline)
lines = [line for line in lines if line.strip() != riga.strip()]

# Sovrascrivi il file con le righe modificate (ovvero quelle rimanenti)
with open(file_path, "w", encoding="latin-1") as f:
    f.writelines(lines)

print(f"✅ Riga rimossa da {file_path}")


#Nel file "&24-01-24 rit05 MON-VER 2-3_bm.dvw" elimino Diego Frascio, segnato col numero 8, non ha giocato e non è segnato in partita. (c'è già Mujanovic col numero 8)
file_path = os.path.join(cartella_dvw, "&24-01-24 rit05 MON-VER 2-3_bm.dvw")
riga = "0;8;5;*;;;;;191774;Frascio;Diego;Frascio;;2;False;191774;;24672617363696F;2446965676F;24672617363696F;;;"

with open(file_path, "r", encoding="latin-1") as f:
    lines = f.readlines()

lines = [line for line in lines if line.strip() != riga.strip()]

with open(file_path, "w", encoding="latin-1") as f:
    f.writelines(lines)

print(f"✅ Riga rimossa da {file_path}")


#Nel file "&24-02-10 rit07 PIA-MON 2-3_bm.dvw" elimino Diego Frascio, segnato col numero 8, non ha giocato e non è segnato in partita. (c'è già Mujanovic col numero 8)
file_path = os.path.join(cartella_dvw, "&24-02-10 rit07 PIA-MON 2-3_bm.dvw")
riga = "1;8;26;;*;;;;191774;Frascio;Diego;Frascio;;2;False;191774;;24672617363696F;2446965676F;24672617363696F;;;"

with open(file_path, "r", encoding="latin-1") as f:
    lines = f.readlines()

lines = [line for line in lines if line.strip() != riga.strip()]

with open(file_path, "w", encoding="latin-1") as f:
    f.writelines(lines)

print(f"✅ Riga rimossa da {file_path}")


#Nel file "&24-02-14 rit08 MIL-MON 1-3_bm.dvw" elimino Andrea Innocenzi con il numero 3 (lascio quello con il numero 10)
file_path = os.path.join(cartella_dvw, "&24-02-14 rit08 MIL-MON 1-3_bm.dvw")
riga = "0;3;13;;;;;;206628;Innocenzi;Andrea;Innocenzi;;2;False;206628;;2496E6E6F63656E7A69;2416E64726561;2496E6E6F63656E7A69;;;"

with open(file_path, "r", encoding="latin-1") as f:
    lines = f.readlines()

# Rimuovi la riga esatta (attenzione a eventuali spazi o newline)
lines = [line for line in lines if line.strip() != riga.strip()]

with open(file_path, "w", encoding="latin-1") as f:
    f.writelines(lines)

print(f"✅ Riga rimossa da {file_path}")


#Nel file "&24-04-14 po5 PAD-PIA 0-3_bm.dvw" elimino Wataru Taniguchi con il numero 6 (lascio quello con il numero 20)
file_path = os.path.join(cartella_dvw, "&24-04-14 po5 PAD-PIA 0-3_bm.dvw")
riga = "0;6;14;;;;;;205982;Taniguchi;Wataru;Taniguchi;L;1;;205982;;254616E696775636869;2576174617275;254616E696775636869;;;"

with open(file_path, "r", encoding="latin-1") as f:
    lines = f.readlines()

# Rimuovi la riga esatta (attenzione a eventuali spazi o newline)
lines = [line for line in lines if line.strip() != riga.strip()]

with open(file_path, "w", encoding="latin-1") as f:
    f.writelines(lines)

print(f"✅ Riga rimossa da {file_path}")


'''



#Nel file "&24-12-15 rit01 MOD-PIA 2-3_bm.dvw" ad un certo punto viene messo *P8, ovvero si indica che il palleggiatore in campo per il Modena da adesso è il numero 8. Però non esiste, non c'è nel roster, e infatti il palleggiatore da quel momento dovrebbe essere De Cecco, quindi *P15. Correggiamo quindi da *P8 a *P15.
#Correggo questa cosa direttamente dal file perchè al momento dell'inserimento voglio poter mettere già il Player_ID_auto del giocatore, ma non posso se non esiste.
file_path = os.path.join(cartella_dvw, "&24-12-15 rit01 MOD-PIA 2-3_bm.dvw")
riga_old1 = "*P8;;;;;;;21.48.48;3;2;5;1;5037;;6;15;9;7;19;22;13;23;7;12;6;2;"
riga_new1 = "*P15;;;;;;;21.48.48;3;2;5;1;5037;;6;15;9;7;19;22;13;23;7;12;6;2;"

riga_old2 = "*P8;;;;;;;22.18.28;4;6;6;1;6817;;22;6;15;9;7;19;2;13;23;7;12;6;"
riga_new2 = "*P15;;;;;;;22.18.28;4;6;6;1;6817;;22;6;15;9;7;19;2;13;23;7;12;6;"


# Apro il file in lettura
with open(file_path, "r", encoding="latin-1") as f:
    lines = f.readlines()  # Legge tutte le righe del file in una lista

# Sostituisco la riga_old con riga_new
for i in range(len(lines)):
    if lines[i].strip() == riga_old1.strip():
        lines[i] = riga_new1 + "\n"  # Aggiungo il newline per mantenere il formato del file
    if lines[i].strip() == riga_old2.strip():
        lines[i] = riga_new2 + "\n"  

# Sovrascrivi il file con le righe modificate
with open(file_path, "w", encoding="latin-1") as f:
    f.writelines(lines)

print(f"✅ Righe sostituita nel file {file_path}")









