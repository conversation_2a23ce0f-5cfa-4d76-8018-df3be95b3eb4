#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script per leggere le prime 10 righe dalla tabella players_latest,
cercare i dati completi di ogni giocatore in base al nome e cognome,
e salvare il dataframe risultante come CSV.
"""

import pandas as pd
import psycopg
from sqlalchemy import create_engine
import os
from pathlib import Path
from volleyball_scraper1 import get_player_info

# Configurazione della connessione al database
DB_CONFIG = {
    "dbname": "db_modena",
    "user": "postgres",
    "password": "AcquaLevissima1",
    "host": "localhost",
    "port": 5432
}

# Directory per salvare il file CSV
DATA_DIR = Path(os.path.expanduser("~")) / "Documents" / "VolleyballScraper" / "data"
DATA_DIR.mkdir(parents=True, exist_ok=True)

def main():
    """
    Funzione principale che:
    1. <PERSON>gg<PERSON> le prime 10 righe dalla tabella players_latest
    2. Per ogni giocatore, cerca i dati completi in base al nome e cognome
    3. Salva il dataframe risultante come CSV
    """
    print("Lettura dei dati dalla tabella players_latest...")
    
    # Connessione al database
    engine = create_engine(f'postgresql+psycopg://{DB_CONFIG["user"]}:{DB_CONFIG["password"]}@{DB_CONFIG["host"]}:{DB_CONFIG["port"]}/{DB_CONFIG["dbname"]}')
    
    # Leggi le prime 10 righe dalla tabella players_latest
    df_players_latest = pd.read_sql_query("SELECT * FROM players_latest LIMIT 10", engine)
    
    print(f"Lette {len(df_players_latest)} righe dalla tabella players_latest")
    
    # Itera su ogni riga del dataframe
    for index, row in df_players_latest.iterrows():
        nome = row["Nome"]
        cognome = row["Cognome"]
        
        print(f"\nElaborazione di {nome} {cognome}...")
        
        # Ottieni i dati completi del giocatore
        player_info = get_player_info(nome, cognome)
        
        # Aggiorna le colonne del dataframe con i dati ottenuti
        if not player_info["errore"]:
            print(f"Dati trovati per {nome} {cognome}")
            
            # Aggiorna le colonne del dataframe
            df_players_latest.at[index, "Nazionalità"] = player_info["nazionalità"]
            df_players_latest.at[index, "DataNascita"] = player_info["data_di_nascita"]
            df_players_latest.at[index, "Altezza"] = player_info["altezza"]
            df_players_latest.at[index, "Peso"] = player_info["peso"]
            df_players_latest.at[index, "Schiacciata"] = player_info["schiacciata"]
            df_players_latest.at[index, "Muro"] = player_info["muro"]
            df_players_latest.at[index, "ManoDominante"] = player_info["mano_dominante"]
        else:
            print(f"Errore per {nome} {cognome}: {player_info['errore']}")
    
    # Salva il dataframe come CSV
    csv_path = DATA_DIR / "players_latest.csv"
    df_players_latest.to_csv(csv_path, index=False)
    print(f"\nDataframe salvato come CSV in: {csv_path}")
    
    # Salva il dataframe aggiornato nel database
    print("\nSalvataggio delle modifiche nel database...")
    try:
        df_players_latest.to_sql('players_latest', engine, if_exists='replace', index=False)
        print("Dati salvati con successo nel database!")
        
        # Verifica che i dati siano stati salvati correttamente
        df_check = pd.read_sql_query("SELECT * FROM players_latest", engine)
        print(f"Numero di righe nel database: {len(df_check)}")
        print(f"Numero di valori non nulli in Nazionalità: {df_check['Nazionalità'].count()}")
        print(f"Numero di valori non nulli in Altezza: {df_check['Altezza'].count()}")
    except Exception as e:
        print(f"Errore durante il salvataggio nel database: {e}")

if __name__ == "__main__":
    main()
