{"cells": [{"cell_type": "markdown", "id": "804fb0a3", "metadata": {}, "source": ["## R<PERSON><PERSON> giocatori/maglie duplicati da una partita."]}, {"cell_type": "markdown", "id": "b62317e8", "metadata": {}, "source": ["In questo file controlliamo che non ci siano due giocatori con lo stesso nome e cognome o lo stesso numero di maglia o lo stesso originalPlayerID all'interno dello stesso team in una partita.\n", "\n", "Per farlo facciamo dei raggruppamenti in players_each_game e stampiamo quale game è e i suoi duplicati"]}, {"cell_type": "code", "execution_count": 4, "id": "a8f3cb9c", "metadata": {}, "outputs": [], "source": ["import numpy as np \n", "import pandas as pd  #Realizzato con la versione di Pandas 2.2.3\n", "import polars as pl\n", "import os\n", "import psycopg\n", "from sqlalchemy import create_engine, text"]}, {"cell_type": "code", "execution_count": 5, "id": "959d6a43", "metadata": {}, "outputs": [], "source": ["conn = psycopg.connect(\n", "    dbname=\"db_modena\",           # database creato in pgAdmin4\n", "    user=\"postgres\",              # Il tuo nome utente PostgreSQL\n", "    password=\"AcquaLevissima1\",   # La password che hai scelto per 'postgres'\n", "    host=\"localhost\",             # 'localhost' se è sul tuo PC\n", "    port=5432                     # La porta predefinita è 5432\n", ")\n", "\n", "# Crea un cursore per eseguire le query\n", "cur = conn.cursor()"]}, {"cell_type": "code", "execution_count": 7, "id": "3af9ee6c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Duplicati trovati:\n"]}], "source": ["conn.rollback()\n", "\n", "cur.execute(\"\"\"\n", "WITH duplicates AS (\n", "    SELECT \n", "        p1.\"GameID\",\n", "        p1.\"TeamID\",\n", "        t.\"Team<PERSON>ame\" as \"<PERSON><PERSON><PERSON>\",  -- nome del team del giocatore\n", "        home_t.\"TeamName\" as \"HomeTeamName\",  -- nome della squadra in casa\n", "        visitor_t.\"TeamName\" as \"VisitorTeamName\",  -- nome della squadra ospite\n", "        p1.\"PlayerID\" as \"PlayerID1\",\n", "        p1.\"Nome\" as \"Nome1\",\n", "        p1.\"Cognome\" as \"Cognome1\",\n", "        p1.\"NumeroMaglia\" as \"Maglia1\",\n", "        p2.\"PlayerID\" as \"PlayerID2\",\n", "        p2.\"Nome\" as \"Nome2\",\n", "        p2.\"Cognome\" as \"Cognome2\",\n", "        p2.\"NumeroMaglia\" as \"Maglia2\",\n", "        g.\"DateSQL\"\n", "    FROM players_each_game_view p1\n", "    JOIN players_each_game_view p2 ON \n", "        p1.\"GameID\" = p2.\"GameID\" AND\n", "        p1.\"TeamID_auto\" = p2.\"TeamID_auto\" AND\n", "        p1.\"PlayerID_auto\" < p2.\"PlayerID_auto\" AND  -- <PERSON><PERSON><PERSON> duplica<PERSON> simmetrici\n", "        (\n", "            (p1.\"Nome\" = p2.\"Nome\" AND p1.\"Cognome\" = p2.\"Cognome\") OR\n", "            (p1.\"NumeroMaglia\" = p2.\"NumeroMaglia\") OR\n", "            (p1.\"PlayerID\" = p2.\"PlayerID\")\n", "        )\n", "    LEFT JOIN games_view g ON p1.\"GameID\" = g.\"GameID\"\n", "    LEFT JOIN teams t ON p1.\"TeamID_auto\" = t.\"TeamID\"\n", "    LEFT JOIN teams home_t ON g.\"IDHomeTeam\" = home_t.\"TeamID\"\n", "    LEFT JOIN teams visitor_t ON g.\"IDVisitorTeam\" = visitor_t.\"TeamID\"\n", ")\n", "\n", "\n", "SELECT \n", "    \"GameID\",\n", "    \"DateSQL\",\n", "    \"TeamID\",\n", "    \"TeamName\",\n", "    \"HomeTeamName\",\n", "    \"VisitorTeamName\",\n", "    \"PlayerID1\",\n", "    \"Nome1\",\n", "    \"Cognome1\",\n", "    \"Maglia1\",\n", "    \"PlayerID2\",\n", "    \"Nome2\",\n", "    \"Cognome2\",\n", "    \"Maglia2\",\n", "    CASE \n", "        WHEN \"Nome1\" = \"Nome2\" AND \"Cognome1\" = \"Cognome2\" THEN 'Nome/Cognome duplicato'\n", "        WHEN \"Maglia1\" = \"Maglia2\" THEN 'Numero maglia duplicato'\n", "        WHEN \"PlayerID1\" = \"PlayerID2\" THEN 'PlayerID duplicato'\n", "    END as \"Tipo Duplicazione\"\n", "FROM duplicates\n", "ORDER BY \"GameID\", \"TeamID\", \"Maglia1\";\n", "\n", "\"\"\")\n", "\n", "results = cur.fetchall()\n", "print(\"Duplicati trovati:\")\n", "for r in results:\n", "    print(f\"\\nPartita {r[0]} ({r[1]})\")\n", "    print(f\"{r[4]}  - {r[5]}\")  # r[4] = HomeTeamName, r[5] = VisitorTeamName\n", "    print(f\"Team con duplicato: {r[2]} ({r[3]})\")        # TeamID + TeamName del team che ha il duplicato\n", "    print(f\"Giocatore1: ID={r[6]}, {r[7]} {r[8]}, Maglia={r[9]}\")\n", "    print(f\"Giocatore2: ID={r[10]}, {r[11]} {r[12]}, Maglia={r[13]}\")\n", "    print(f\"Tipo: {r[14]}\")\n"]}, {"cell_type": "markdown", "id": "a8abb1e4", "metadata": {}, "source": ["Procedo a sistemare i giocatori.\n", "\n", "Se due giocatori hanno lo stesso numero di maglia, cercando su Internet la partita trovo a quale dei due lasciarlo e a quale mettergliene un altro.\n", "\n", "Se un giocatore compare due volte con il numero di maglia diverso, prima guardo in rilevations quale dei due numeri di maglia compare in quella squadra, così lascio quello. Se nessuno dei due compare perchè il giocatore non ha giocato, cerco su Internet la partita e guardo che numero aveva. e/o guardo anche in players_each_game quale numero ha di solito nelle partite."]}, {"cell_type": "markdown", "id": "2fb22765", "metadata": {}, "source": ["Per rimuovere un giocatore con maglia duplicata, faccio un dizionario con tutti i valori DateSQL, TeamID, Nome, Cognome, e poi faccio un ciclo su questo dizionario, eseguendo query con i suoi elementi."]}, {"cell_type": "markdown", "id": "bf2e377f", "metadata": {}, "source": ["Se è una partita di Superlega controlla i numeri su https://www.legavolley.it/calendario/?Anno=2023&refCampionato=947 (a volte gli articoli sbagliano i numeri dei giocatori)"]}, {"cell_type": "code", "execution_count": 8, "id": "343b315d", "metadata": {}, "outputs": [], "source": ["#Il dizionario ha come chiave un numero da 0 al numero di giocatori che devo correggere.\n", "#Come valore ha una lista di DateSQL della partita incriminata, il TeamID del giocatore(NO), il suo Nome, il suo Cognome, e il numero maglia. Se trovo questa riga in players_each_game, allora la elimino.\n", "\n", "dizionario_correzione_duplicati_maglie = {\n", "    0: ['2023-03-15', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 17],  #elimino il Mateusz Bieniek con maglia 17 (<PERSON><PERSON> quello con maglia 20)\n", "    1: ['2024-01-24', '<PERSON>', '<PERSON><PERSON><PERSON>', 8],  #elim<PERSON>, segnato col numero 8, non ha giocato e non è segnato in partita. (c'è gi<PERSON> col numero 8)\n", "    2: ['2024-02-10', '<PERSON>', '<PERSON><PERSON><PERSON>', 8],  #elim<PERSON>, segnato col numero 8, non ha giocato e non è segnato in partita. (c'è gi<PERSON> col numero 8)\n", "    3: ['2024-02-14', '<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 3],  #elimino <PERSON> con il numero 3 (las<PERSON> quello con il numero 10)\n", "    4: ['2024-04-14', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 6],  #elimino <PERSON> Tan<PERSON>uchi con il numero 6 (las<PERSON> quello con il numero 20)\n", "\n", "}"]}, {"cell_type": "code", "execution_count": 9, "id": "7d16f035", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Riga 2023-03-15, <PERSON><PERSON><PERSON>, 17 <PERSON><PERSON> con successo\n", "✅ Riga 2024-01-24, <PERSON>, 8 rim<PERSON> con <PERSON>o\n", "✅ Riga 2024-02-10, <PERSON>, 8 rim<PERSON> con <PERSON>o\n", "✅ Riga 2024-02-14, <PERSON>, 3 rim<PERSON> con <PERSON>o\n", "✅ Riga 2024-04-14, <PERSON><PERSON><PERSON>, 6 rim<PERSON> con <PERSON>o\n"]}], "source": ["conn.rollback()\n", "\n", "for key, value in dizionario_correzione_duplicati_maglie.items():\n", "    datesql, nome, cognome, maglia = value\n", "\n", "    cur.execute(\"\"\"\n", "    DELETE FROM players_each_game p\n", "    USING games g  --facciamo il JOIN direttamente qui nel DELETE\n", "    WHERE p.\"GameID\" = g.\"GameID\"\n", "        AND g.\"DateSQL\" = %s\n", "        AND p.\"Nome\" = %s\n", "        AND p.\"Cognome\" = %s\n", "        AND p.\"NumeroMaglia\" = %s;\n", "    \"\"\", (datesql, nome, cognome, maglia)\n", "    )\n", "    conn.commit()\n", "\n", "    # Verifica che la riga sia stata rimossa\n", "    cur.execute(\"\"\"\n", "    SELECT p.\"GameID\", g.\"DateSQL\", p.\"Nome\", p.\"Cognome\", p.\"NumeroMaglia\"\n", "    FROM players_each_game p\n", "    JOIN games g ON p.\"GameID\" = g.\"GameID\"\n", "    WHERE g.\"DateSQL\" = %s\n", "    AND p.\"Nome\" = %s\n", "    AND p.\"Cognome\" = %s\n", "    AND p.\"NumeroMaglia\" = %s\n", "    \"\"\", (datesql, nome, cognome, maglia)\n", "    )\n", "\n", "    results = cur.fetchall()\n", "    if not results:\n", "        print(f\"✅ Riga {datesql}, {nome} {cognome}, {maglia} rimossa con successo\")\n", "    else:\n", "        print(f\"⚠️ La riga {datesql}, {nome} {cognome}, {maglia} è ancora presente nel database\")"]}, {"cell_type": "markdown", "id": "3bcf9098", "metadata": {}, "source": ["Se vedi che rilevationswithid ha più righe di rilevations, il problema lo puoi risolvere qui, cercando i duplicati."]}, {"cell_type": "markdown", "id": "4df7e557", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "004fc65a", "metadata": {}, "source": ["## Riempiamo gli HomePlayerX e VisitorPlayerX nelle righe *"]}, {"cell_type": "markdown", "id": "9f001df8", "metadata": {}, "source": ["Si<PERSON>me in rilevations le righe con Foundamental = '*' non hanno i giocatori in campo, e hanno le colonne HomePlayerX e VisitorPlayerX NULL, le riempiamo con i valori della riga precedente (quella con lo stesso GameID e RilevationNumber - 1)"]}, {"cell_type": "code", "execution_count": 10, "id": "03bd1e5b", "metadata": {}, "outputs": [], "source": ["cur.execute(\"\"\"\n", "UPDATE rilevations r\n", "SET\n", "    \"Player1\" = COALESCE(r.\"Player1\", prev.\"Player1\"),  --COALESCE restituisce il primo argomento non NULL\n", "    \"Player2\" = COALESCE(r.\"Player2\", prev.\"Player2\"),\n", "    \"Player3\" = COALESCE(r.\"Player3\", prev.\"Player3\"),\n", "    \"Player4\" = COALESCE(r.\"Player4\", prev.\"Player4\"),\n", "    \"Player5\" = COALESCE(r.\"Player5\", prev.\"Player5\"),\n", "    \"Player6\" = COALESCE(r.\"Player6\", prev.\"Player6\"),\n", "    \"Player7\" = COALESCE(r.\"Player7\", prev.\"Player7\"),\n", "    \"Player8\" = COALESCE(r.\"Player8\", prev.\"Player8\"),\n", "    \"Player9\" = COALESCE(r.\"Player9\", prev.\"Player9\"),\n", "    \"Player10\" = COALESCE(r.\"Player10\", prev.\"Player10\"),\n", "    \"Player11\" = COALESCE(r.\"Player11\", prev.\"Player11\"),\n", "    \"Player12\" = COALESCE(r.\"Player12\", prev.\"Player12\")\n", "    \n", "FROM rilevations prev\n", "WHERE r.\"Foundamental\" = '*'\n", "  AND r.\"GameID\" = prev.\"GameID\"\n", "  AND r.\"RilevationNumber\" = prev.\"RilevationNumber\" + 1;  --<PERSON><PERSON> righe di r dove RilevationNumber è uguale a quello di prev +1 (ovvero nelle righe di r con RilevationNumber maggiore di uno rispetto a prev, siccome a prev aggiungo 1 e li voglio uguali)\n", "\n", "\"\"\")\n", "conn.commit()\n"]}, {"cell_type": "markdown", "id": "93dd318a", "metadata": {}, "source": ["## Sistemiamo i giocatori in campo mancanti"]}, {"cell_type": "markdown", "id": "ac9009c4", "metadata": {}, "source": ["Ora ci occupiamo di un'altra questione"]}, {"cell_type": "markdown", "id": "c9935655", "metadata": {}, "source": ["In alcuni file, alcuni giocatori mancano nelle colonne HomePlayerX, non sono riportati, e quindi non risultano in campo. \n", "\n", "In questa sezione sistemiamo questo problema controllando su Internet su https://www.legavolley.it/calendario/?Anno=2023&refCampionato=947 o dagli highlights o guardando tra le righe con Foundamental='S' quale numero non è presente in HomePlayerX (S così escludo il libero che so già che non c'è), qual è il giocatore che manca, e in tutti i valori NULL di HomePlayerX, metto quel numero."]}, {"cell_type": "markdown", "id": "170eb87d", "metadata": {}, "source": ["#### Risoluzione 1"]}, {"cell_type": "markdown", "id": "4c5adec7", "metadata": {}, "source": ["Nella partita del 2023-01-15 tra Piacenza (TeamID 5) e Siena (TeamID 8), file \"&2023-01-15 04r pia-sie 1-3 r.dvw\", negli scout manca un giocatore tra i giocatori di casa. Guardando online scopriamo che questo è il giocatore 1, Luka Basic.\n", "\n", "In rilevations sostituiamo quindi tutti i valori NULL nelle colonne HomePlayerX, con 1"]}, {"cell_type": "code", "execution_count": 11, "id": "9360260b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["257\n"]}], "source": ["conn.rollback()\n", "\n", "cur.execute(\"\"\"\n", "SELECT \"GameID\"\n", "FROM games_view\n", "WHERE \"DateSQL\" = '2023-01-15' \n", "AND \"IDHomeTeam\" = 5 \n", "AND \"IDVisitorTeam\" = 8\n", "\"\"\")\n", "\n", "result = cur.fetchone()\n", "gameid_luka_basic = result[0]\n", "print(gameid_luka_basic)  #stampo il GameID incriminato, quello dove manca il numero di maglia di Luka Basic tra i Player1...6"]}, {"cell_type": "code", "execution_count": 12, "id": "e0257ff2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3460\n"]}], "source": ["cur.execute(\"\"\"\n", "SELECT \n", "    peg.\"TeamID_auto\", \n", "    peg.\"PlayerID_auto\",\n", "    peg.\"NumeroMaglia\",\n", "    peg.\"Nome\",\n", "    peg.\"Cognome\"\n", "FROM players_each_game peg\n", "WHERE peg.\"GameID\" = %s\n", "AND peg.\"Nome\" IN ('Luka', 'luka')\n", "AND peg.\"Cognome\" IN ('Basic', 'basic')\n", "AND peg.\"NumeroMaglia\" = 1\n", "\"\"\", (gameid_luka_basic,))\n", "\n", "result = cur.fetchone()\n", "playerid_auto_luka_basic = result[1]\n", "print(playerid_auto_luka_basic)  #Stampo il PlayerID_auto di Luka Basic nel GameID incriminato"]}, {"cell_type": "code", "execution_count": 13, "id": "967d9855", "metadata": {}, "outputs": [], "source": ["conn.rollback()\n", "\n", "cur.execute(f\"\"\"\n", "UPDATE rilevations r\n", "SET \n", "    --Aggiungiamo 1 nei Player1...6 dove c'era NULL\n", "    \"Player1\" = CASE WHEN \"Player1\" IS NULL THEN 1 ELSE \"Player1\" END,\n", "    \"Player2\" = CASE WHEN \"Player2\" IS NULL THEN 1 ELSE \"Player2\" END,\n", "    \"Player3\" = CASE WHEN \"Player3\" IS NULL THEN 1 ELSE \"Player3\" END,\n", "    \"Player4\" = CASE WHEN \"Player4\" IS NULL THEN 1 ELSE \"Player4\" END,\n", "    \"Player5\" = CASE WHEN \"Player5\" IS NULL THEN 1 ELSE \"Player5\" END,\n", "    \"Player6\" = CASE WHEN \"Player6\" IS NULL THEN 1 ELSE \"Player6\" END,\n", "\n", "    --Agg<PERSON><PERSON><PERSON><PERSON> playerid_auto_luka_basic nei Player1...6_ID_auto dove c'era NULL\n", "    \"Player1_ID_auto\" = CASE WHEN \"Player1_ID_auto\" IS NULL THEN {playerid_auto_luka_basic} ELSE \"Player1_ID_auto\" END,\n", "    \"Player2_ID_auto\" = CASE WHEN \"Player2_ID_auto\" IS NULL THEN {playerid_auto_luka_basic} ELSE \"Player2_ID_auto\" END,\n", "    \"Player3_ID_auto\" = CASE WHEN \"Player3_ID_auto\" IS NULL THEN {playerid_auto_luka_basic} ELSE \"Player3_ID_auto\" END,\n", "    \"Player4_ID_auto\" = CASE WHEN \"Player4_ID_auto\" IS NULL THEN {playerid_auto_luka_basic} ELSE \"Player4_ID_auto\" END,\n", "    \"Player5_ID_auto\" = CASE WHEN \"Player5_ID_auto\" IS NULL THEN {playerid_auto_luka_basic} ELSE \"Player5_ID_auto\" END,\n", "    \"Player6_ID_auto\" = CASE WHEN \"Player6_ID_auto\" IS NULL THEN {playerid_auto_luka_basic} ELSE \"Player6_ID_auto\" END\n", "\n", "FROM games_view g\n", "WHERE r.\"GameID\" = {gameid_luka_basic}\n", "\n", "\"\"\")\n", "conn.commit()"]}, {"cell_type": "code", "execution_count": null, "id": "4db934cb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "de2f6c21", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "eb70e5f1", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "e3b3a6a2", "metadata": {}, "source": ["In futuro puoi controllare che se mancano INSIEME (in una stessa riga) ben DUE colonne HomePlayerX, allora mancano due giocatori, e lì devi farlo a mano per forza.\n", "\n", "Altrimenti se manca ogni volta solo una colonna in HomePlayerX, puoi guardare tra le righe con Foundamental='S', whichTeam=quellochetiserve, quali numeri in NumeroMaglia non compaiono in HomePlayerX. Quello è il numero che andrai a sostituire in tutti i valori nulli delle colonne HomePlayerX di quel GameID (con quella DateSQL, quel IDHomeTeam e quel IDVisitorTeam)"]}, {"cell_type": "markdown", "id": "7a0e0e8f", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": ".venv (3.13.2)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}