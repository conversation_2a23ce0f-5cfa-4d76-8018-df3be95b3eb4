<p align="center">
  <img src="https://github.com/shukkkur/VolleyVision/blob/b9e2ea29be1337f8cd7c25f7f06741ecfde9fc62/README_files/vv_logo.png" width=200>
</p>

<h1 align="center">
  Stage III - Court Detection
</h1>

<p align='center'>
  <a href="https://ucentralasia.org/home"><code>University of Central Asia</a>⛰️</code>
</p>

<h2>🏃‍♂️ How to Run</h2>

<a href="https://colab.research.google.com/drive/1X16GNjksEfwVL1090bj3CYHGo772fG6H?usp=sharing"><img src="https://colab.research.google.com/assets/colab-badge.svg"></a>

<ol>
  
  <li>
    Clone this repository 
  </li>

  ```
  git clone https://github.com/shukkkur/VolleyVision.git
  ```

  <li>
    Install the requirements
  </li>
  
  ```
  cd VolleyVision\Stage III - Court Detection
  pip install -r requirements.txt
  ```

  
  <li>
    You can run <strong>court_detect.py</strong> on either an image or a video. Let's run on both and see the results
  </li>
  
```
python court_detect.py path_to_file --output_path
```

</ol>

❗If you are facing errors when trying to use <strong>RoboFlow model</strong>, please refer to this <a href="https://github.com/shukkkur/VolleyVision/discussions/5#discussioncomment-7737081">disscusion</a>

<br>
  
|   <code>assets/modena.jpg</code>   |   <code>assets/rally_women.mp4</code>   |
|--------------|--------------|
|  <img src="https://github.com/shukkkur/VolleyVision/blob/c0cab9585a9eb195d96d836f1243c97b20c80025/Stage%20III%20-%20Court%20Detection/assets/court_5/approxPolyDP.jpg" width="500">  |  <img src="https://github.com/shukkkur/VolleyVision/blob/c0cab9585a9eb195d96d836f1243c97b20c80025/Stage%20III%20-%20Court%20Detection/assets/court.gif" width="500">  |

  
<ul>
  <li>
    <p>You can get all list or arguments using the following command:</p>
  </li>
</ul>

```python
python court_detect.py -h
```  
  
<img src="https://github.com/shukkkur/VolleyVision/blob/48ad586dec4ad981df145c73fc0ba176552ea5b6/Stage%20III%20-%20Court%20Detection/assets/args_help.png">

<h4>For any additional quesitons feel free to take part in <a href="https://github.com/shukkkur/VolleyVision/discussions">discussions</a>, open an <a href="https://github.com/shukkkur/VolleyVision/issues/new">issue</a> or <a href="https://github.com/shukkkur#feel-free-to-connectcontact">contact</a> me.</h4>

<a href="https://github.com/shukkkur/VolleyVision/discussions">
<img src="https://github.com/shukkkur/VolleyVision/blob/1d1836c3a7968cbcde4bcf5cfb5e8eaf4c16acfb/assets/header.png">
</a>
