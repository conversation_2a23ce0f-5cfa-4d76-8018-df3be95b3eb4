#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Volleyball Scraper - Programma per estrarre dati dei giocatori da volleybox.net
Versione che utilizza proxy rotanti per aggirare Cloudflare
"""

import sys
import re
import unicodedata
import time
import json
import random
import os
import requests
from bs4 import BeautifulSoup
from pathlib import Path

import pandas as pd
from sqlalchemy import create_engine

# Directory per salvare i dati
DATA_DIR = Path(os.path.expanduser("~")) / "Documents" / "ModenaVolley" / "VolleyballScraper" / "data"
DATA_DIR.mkdir(parents=True, exist_ok=True)

# Lista di proxy gratuiti (da sostituire con proxy reali o un servizio a pagamento)
# Formato: "***************************:port" o "http://ip:port"
FREE_PROXIES = [
    # Aggiungi qui i tuoi proxy
    # "*******************:port",
]

# Oppure usa un servizio di proxy rotanti come Bright Data, ScraperAPI, etc.
# PROXY_URL = "http://brd-customer-xxxxxxxx:<EMAIL>:22225"

def normalize_name(name):
    """
    Normalizza il nome per la ricerca.
    Rimuove accenti, converte in minuscolo.
    """
    # Rimuovi accenti e caratteri speciali
    name = unicodedata.normalize('NFKD', name).encode('ASCII', 'ignore').decode('utf-8')
    # Converti in minuscolo
    name = name.lower()
    return name

def get_random_proxy():
    """
    Restituisce un proxy casuale dalla lista dei proxy disponibili.
    
    Returns:
        str or None: URL del proxy se disponibile, altrimenti None
    """
    if FREE_PROXIES:
        return random.choice(FREE_PROXIES)
    return None

def download_with_proxy(url, max_retries=3):
    """
    Scarica una pagina web utilizzando un proxy rotante.
    
    Args:
        url (str): URL della pagina da scaricare
        max_retries (int): Numero massimo di tentativi
        
    Returns:
        tuple: (success, html_content, error_message)
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'it-IT,it;q=0.8,en-US;q=0.5,en;q=0.3',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Cache-Control': 'max-age=0',
    }
    
    for attempt in range(max_retries):
        try:
            proxy = get_random_proxy()
            proxies = {'http': proxy, 'https': proxy} if proxy else None
            
            print(f"Tentativo {attempt+1}/{max_retries} - Scaricando {url}")
            if proxy:
                print(f"Usando proxy: {proxy}")
            
            # Aggiungi un ritardo casuale prima della richiesta
            time.sleep(random.uniform(2, 5))
            
            # Esegui la richiesta
            response = requests.get(
                url, 
                headers=headers, 
                proxies=proxies, 
                timeout=30,
                allow_redirects=True
            )
            
            # Verifica se la risposta è valida
            if response.status_code == 200:
                html_content = response.text
                
                # Verifica se siamo stati bloccati da Cloudflare
                if "Cloudflare" in html_content or "challenge" in html_content or "Ci siamo quasi" in html_content:
                    print(f"Bloccato da Cloudflare con il proxy {proxy}")
                    continue  # Prova con un altro proxy
                
                print("Download completato con successo!")
                return True, html_content, None
            else:
                print(f"Errore HTTP {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"Errore durante la richiesta: {str(e)}")
        
        # Attendi prima di riprovare
        if attempt < max_retries - 1:
            delay = random.uniform(5, 10)
            print(f"Attendo {delay:.1f} secondi prima del prossimo tentativo...")
            time.sleep(delay)
    
    return False, None, f"Tutti i {max_retries} tentativi falliti"

def extract_player_data_from_html(html_content):
    """
    Estrae i dati del giocatore da una pagina HTML usando BeautifulSoup.
    
    Args:
        html_content (str): Contenuto HTML della pagina del giocatore
        
    Returns:
        dict: Dizionario con i dati del giocatore
    """
    player_info = {
        "nazionalità": None,
        "posizione": None,
        "data_di_nascita": None,
        "altezza": None,
        "peso": None,
        "schiacciata": None,
        "muro": None,
        "mano_dominante": None,
        "errore": None
    }
    
    try:
        # Analizza l'HTML con BeautifulSoup
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # Cerca il div con classe "new_box pRelative" che contiene i dati del giocatore
        player_data_div = soup.find('div', class_='new_box pRelative')
        
        if player_data_div:
            print("Div 'new_box pRelative' trovato con BeautifulSoup")
            
            # Cerca tutte le coppie dt/dd all'interno del div
            dts = player_data_div.find_all('dt', class_='info-header')
            dds = player_data_div.find_all('dd', class_='info-data')
            
            if dts and dds:
                print(f"Trovati {len(dts)} elementi dt e {len(dds)} elementi dd")
                
                for i in range(min(len(dts), len(dds))):
                    label = dts[i].get_text().strip().lower()
                    value = dds[i].get_text().strip()
                    
                    print(f"Coppia {i}: '{label}' = '{value}'")
                    
                    if "nazionalità" in label:
                        player_info["nazionalità"] = value
                    elif "posizione" in label:
                        player_info["posizione"] = value
                    elif "data di nascita" in label:
                        player_info["data_di_nascita"] = value
                    elif "altezza" in label:
                        player_info["altezza"] = value
                    elif "peso" in label:
                        player_info["peso"] = value
                    elif "schiacciata" in label:
                        player_info["schiacciata"] = value
                    elif "muro" in label:
                        player_info["muro"] = value
                    elif "mano dominante" in label:
                        player_info["mano_dominante"] = value
            else:
                print("Nessun elemento dt/dd trovato nel div")
        else:
            print("Div 'new_box pRelative' non trovato con BeautifulSoup")
            
            # Prova a cercare in modo più generico
            print("Tentativo di ricerca più generico...")
            
            # Cerca qualsiasi div che potrebbe contenere informazioni sul giocatore
            info_divs = soup.find_all('div', class_=lambda c: c and ('info' in c.lower() or 'player' in c.lower() or 'data' in c.lower()))
            
            if info_divs:
                print(f"Trovati {len(info_divs)} div potenzialmente rilevanti")
                
                for div in info_divs:
                    # Cerca coppie di etichette e valori
                    labels = div.find_all(['dt', 'th', 'strong', 'label'])
                    values = div.find_all(['dd', 'td', 'span'])
                    
                    if labels and values and len(labels) == len(values):
                        print(f"Trovate {len(labels)} coppie etichetta-valore")
                        
                        for i in range(len(labels)):
                            label = labels[i].get_text().strip().lower()
                            value = values[i].get_text().strip()
                            
                            print(f"Coppia {i}: '{label}' = '{value}'")
                            
                            if "nazionalità" in label or "nationality" in label:
                                player_info["nazionalità"] = value
                            elif "posizione" in label or "position" in label:
                                player_info["posizione"] = value
                            elif "data di nascita" in label or "birth" in label:
                                player_info["data_di_nascita"] = value
                            elif "altezza" in label or "height" in label:
                                player_info["altezza"] = value
                            elif "peso" in label or "weight" in label:
                                player_info["peso"] = value
                            elif "schiacciata" in label or "spike" in label:
                                player_info["schiacciata"] = value
                            elif "muro" in label or "block" in label:
                                player_info["muro"] = value
                            elif "mano dominante" in label or "hand" in label:
                                player_info["mano_dominante"] = value
        
        # Verifica se abbiamo trovato almeno un dato
        found_data = any([
            player_info["nazionalità"],
            player_info["posizione"],
            player_info["data_di_nascita"],
            player_info["altezza"],
            player_info["peso"],
            player_info["schiacciata"],
            player_info["muro"],
            player_info["mano_dominante"]
        ])
        
        if not found_data:
            print("ATTENZIONE: Nessun dato trovato per il giocatore!")
            
            # Salva l'HTML per debug
            with open("debug_page.html", "w", encoding="utf-8") as f:
                f.write(html_content)
            print("HTML della pagina salvato in debug_page.html per analisi")
        else:
            print("Dati estratti con successo!")
            
    except Exception as e:
        player_info["errore"] = f"Errore nell'estrazione dati: {str(e)}"
        print(f"Eccezione durante l'estrazione: {str(e)}")
        
        # Salva l'HTML per debug in caso di errore
        try:
            with open("error_page.html", "w", encoding="utf-8") as f:
                f.write(html_content)
            print("HTML della pagina con errore salvato in error_page.html")
        except:
            pass
    
    return player_info

def get_player_info_with_proxy(url):
    """
    Estrae le informazioni di un giocatore da un URL di volleybox.net usando proxy rotanti.
    
    Args:
        url (str): URL del giocatore
        
    Returns:
        dict: Dizionario contenente le informazioni del giocatore
    """
    # Inizializza il dizionario per i risultati
    player_info = {
        "url": url,
        "nazionalità": None,
        "posizione": None,
        "data_di_nascita": None,
        "altezza": None,
        "peso": None,
        "schiacciata": None,
        "muro": None,
        "mano_dominante": None,
        "errore": None
    }
    
    try:
        # Modifica l'URL per andare alla pagina principale del giocatore invece che alla pagina dei club
        # L'URL originale è del tipo https://volleybox.net/it/nome-cognome-pXXXXX/clubs
        # Vogliamo https://volleybox.net/it/nome-cognome-pXXXXX
        player_url = url.replace("/clubs", "")
        print(f"URL modificato: {player_url}")
        
        # Scarica la pagina con proxy rotanti
        success, html_content, error = download_with_proxy(player_url)
        
        if success and html_content:
            print("Pagina scaricata con successo")
            
            # Salva l'HTML per debug
            debug_file = f"proxy_player_{player_url.split('/')[-1]}.html"
            with open(debug_file, "w", encoding="utf-8") as f:
                f.write(html_content)
            print(f"HTML salvato in {debug_file} per debug")
            
            # Estrai le informazioni dalla pagina HTML
            print("Estraendo le informazioni del giocatore...")
            extracted_info = extract_player_data_from_html(html_content)
            
            # Aggiorna il dizionario con le informazioni estratte
            for key, value in extracted_info.items():
                player_info[key] = value
        else:
            player_info["errore"] = f"Errore durante il download: {error}"
            print(f"Errore durante il download: {error}")
    
    except Exception as e:
        player_info["errore"] = f"Errore durante lo scraping: {str(e)}"
        print(f"Errore durante lo scraping: {str(e)}")
    
    print("Estrazione completata")
    return player_info

def get_player_info_from_url(url):
    """
    Estrae le informazioni di un giocatore da un URL di volleybox.net.
    Questa funzione è un wrapper per get_player_info_with_proxy.

    Args:
        url (str): URL del giocatore

    Returns:
        dict: Dizionario contenente le informazioni del giocatore
    """
    return get_player_info_with_proxy(url)

def find_player_url(nome, cognome, url_file_path="url_giocatori.txt"):
    """
    Cerca l'URL di un giocatore nel file url_giocatori.txt basandosi sul nome e cognome normalizzati.

    Args:
        nome (str): Nome del giocatore
        cognome (str): Cognome del giocatore
        url_file_path (str): Percorso del file contenente gli URL

    Returns:
        str or None: URL del giocatore se trovato, altrimenti None
    """
    # Normalizza nome e cognome
    nome_norm = normalize_name(nome)
    cognome_norm = normalize_name(cognome)

    print(f"Nome normalizzato: '{nome_norm}', Cognome normalizzato: '{cognome_norm}'")

    try:
        with open(url_file_path, 'r', encoding='utf-8') as file:
            for line in file:
                url = line.strip()
                # Estrai la parte del nome dall'URL
                url_parts = url.split('/')
                if len(url_parts) >= 5:  # Assicurati che l'URL abbia abbastanza parti
                    player_part = url_parts[4]  # La parte con nome-cognome
                    player_part_lower = player_part.lower()

                    # Verifica se entrambi nome e cognome sono presenti nella parte del giocatore
                    if nome_norm in player_part_lower and cognome_norm in player_part_lower:
                        print(f"Match esatto trovato: {player_part}")
                        return url

                    # Cerca anche corrispondenze parziali o con ordine invertito
                    if f"{nome_norm}-{cognome_norm}" in player_part_lower or f"{cognome_norm}-{nome_norm}" in player_part_lower:
                        print(f"Match con pattern trovato: {player_part}")
                        return url
    except Exception as e:
        print(f"Errore nella lettura del file URL: {str(e)}")

    return None

def process_players_dataframe(limit=None):
    """
    Legge il dataframe dalla tabella players_latest filtrando solo le righe che hanno
    tutti i valori nulli nelle colonne di interesse (Nazionalità, DataNascita, Altezza, ecc.),
    cerca gli URL dei giocatori, estrae le informazioni e aggiorna il dataframe.
    
    Args:
        limit (int, optional): Numero massimo di giocatori da elaborare. Se None, elabora tutti.
        
    Returns:
        pandas.DataFrame: Il dataframe aggiornato con i dati dei giocatori che avevano valori nulli
    """
    # Crea un engine per poter usare pandas
    engine = create_engine('postgresql+psycopg://postgres:AcquaLevissima1@localhost:5432/db_modena')

    # Leggi solo le righe dalla tabella players_latest che hanno tutti i valori nulli nelle colonne di interesse
    df_players_latest = pd.read_sql_query("""
    SELECT * FROM players_latest 
    WHERE "Nazionalità" IS NULL 
    AND "DataNascita" IS NULL 
    AND "Altezza" IS NULL 
    AND "Peso" IS NULL 
    AND "Schiacciata" IS NULL 
    AND "Muro" IS NULL 
    AND "ManoDominante" IS NULL
    """, engine)
    
    # Applica il limite se specificato
    if limit is not None and limit > 0 and limit < len(df_players_latest):
        print(f"Limitando l'elaborazione a {limit} giocatori dei {len(df_players_latest)} trovati")
        df_players_latest = df_players_latest.head(limit)
    
    print(f"Lette {len(df_players_latest)} righe dalla tabella players_latest con tutti i valori nulli nelle colonne di interesse")

    # Itera su ogni riga del dataframe
    for index, row in df_players_latest.iterrows():
        nome = row["Nome"]
        cognome = row["Cognome"]

        print(f"\nElaborazione di {nome} {cognome}...")

        # Cerca l'URL del giocatore
        player_url = find_player_url(nome, cognome)

        if player_url:
            print(f"URL trovato per {nome} {cognome}: {player_url}")

            # Aggiungi un ritardo casuale tra le richieste per evitare di essere bloccati
            if index > 0:  # Non ritardare la prima richiesta
                delay = random.uniform(30, 60)  # Ritardo casuale tra 30 e 60 secondi
                print(f"Attendo {delay:.1f} secondi prima della prossima richiesta...")
                time.sleep(delay)

            # Ottieni i dati del giocatore
            player_info = get_player_info_from_url(player_url)

            # Aggiorna le colonne del dataframe con i dati ottenuti
            if not player_info["errore"]:
                print(f"Dati trovati per {nome} {cognome}")

                # Stampa i dati trovati
                print("=" * 40)
                print(f"DATI ESTRATTI PER {nome} {cognome}:")
                print(f"Nazionalità: {player_info['nazionalità']}")
                print(f"Posizione: {player_info['posizione']}")
                print(f"Data di nascita: {player_info['data_di_nascita']}")
                print(f"Altezza: {player_info['altezza']}")
                print(f"Peso: {player_info['peso']}")
                print(f"Schiacciata: {player_info['schiacciata']}")
                print(f"Muro: {player_info['muro']}")
                print(f"Mano dominante: {player_info['mano_dominante']}")
                print("=" * 40)

                # Aggiorna le colonne del dataframe
                df_players_latest.at[index, "Nazionalità"] = player_info["nazionalità"]
                df_players_latest.at[index, "DataNascita"] = player_info["data_di_nascita"]
                df_players_latest.at[index, "Altezza"] = player_info["altezza"]
                df_players_latest.at[index, "Peso"] = player_info["peso"]
                df_players_latest.at[index, "Schiacciata"] = player_info["schiacciata"]
                df_players_latest.at[index, "Muro"] = player_info["muro"]
                df_players_latest.at[index, "ManoDominante"] = player_info["mano_dominante"]
            else:
                print(f"Errore per {nome} {cognome}: {player_info['errore']}")
        else:
            print(f"Nessun URL trovato per {nome} {cognome}")

    # Salva il dataframe con le righe che avevano tutti i valori nulli come CSV
    csv_path = DATA_DIR / "players_latest_updated.csv"
    df_players_latest.to_csv(csv_path, index=False)
    print(f"\nDataframe salvato come CSV in: {csv_path}")

    # Stampa un riepilogo dei dati raccolti
    print("\n" + "=" * 60)
    print("RIEPILOGO DEI DATI RACCOLTI (RIGHE PRECEDENTEMENTE NULLE):")
    print("=" * 60)
    for index, row in df_players_latest.iterrows():
        print(f"{row['Nome']} {row['Cognome']}:")
        print(f"  Nazionalità: {row['Nazionalità']}")
        print(f"  Data di nascita: {row['DataNascita']}")
        print(f"  Altezza: {row['Altezza']}")
        print(f"  Peso: {row['Peso']}")
        print(f"  Schiacciata: {row['Schiacciata']}")
        print(f"  Muro: {row['Muro']}")
        print(f"  Mano dominante: {row['ManoDominante']}")
        print("-" * 40)

    # Salva il dataframe aggiornato nel database
    print("\nSalvataggio delle modifiche nel database...")
    try:
        df_players_latest.to_sql('players_latest', engine, if_exists='replace', index=False)
        print("Dati salvati con successo nel database!")

        # Verifica che i dati siano stati salvati correttamente
        df_check = pd.read_sql_query("SELECT * FROM players_latest", engine)
        print(f"Numero di righe nel database: {len(df_check)}")
        print(f"Numero di valori non nulli in Nazionalità: {df_check['Nazionalità'].count()}")
        print(f"Numero di valori non nulli in Altezza: {df_check['Altezza'].count()}")
    except Exception as e:
        print(f"Errore durante il salvataggio nel database: {e}")

    return df_players_latest

def main():
    """
    Funzione principale per l'esecuzione da riga di comando.
    """
    import argparse
    
    # Verifica se ci sono proxy disponibili
    if not FREE_PROXIES:
        print("ATTENZIONE: Nessun proxy configurato!")
        print("Per utilizzare questo script, devi aggiungere almeno un proxy alla lista FREE_PROXIES.")
        print("Puoi trovare proxy gratuiti su siti come https://free-proxy-list.net/ o utilizzare un servizio a pagamento.")
        return
    
    # Crea un parser per gli argomenti da riga di comando
    parser = argparse.ArgumentParser(description="Volleyball Scraper - Estrai informazioni sui giocatori da volleybox.net usando proxy rotanti")
    parser.add_argument("--limit", type=int, default=None, help="Limita il numero di giocatori da elaborare")
    args = parser.parse_args()
    
    print("Volleyball Scraper - Estrai informazioni sui giocatori da volleybox.net usando proxy rotanti")
    print("-"*80)
    
    if args.limit:
        print(f"Elaborazione limitata a {args.limit} giocatori.")
    
    try:
        # Elabora il dataframe e aggiorna con i dati dei giocatori
        process_players_dataframe(limit=args.limit)
        print("\nOperazione completata con successo!")
    except KeyboardInterrupt:
        print("\nOperazione interrotta dall'utente.")
    except Exception as e:
        print(f"\nErrore durante l'esecuzione: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
