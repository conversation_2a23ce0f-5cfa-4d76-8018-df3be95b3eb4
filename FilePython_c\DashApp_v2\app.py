import dash
from dash import html, dcc, Input, Output, State, callback
import dash_bootstrap_components as dbc
import pandas as pd
import numpy as np
import calendar
from datetime import datetime
from dash_extensions import DashProxy, Serverside
from dash_extensions.enrich import ServersideOutputTransform

# Importa i dati e configurazioni
from utils.data_loader import load_data
from utils.filters import apply_filters

# Carica i dati una volta all'avvio dell'app
data_dict = load_data()

# Converti i DataFrame in dizionari per la serializzazione JSON
def convert_dataframes_to_dict(data_dict):
    """Converte tutti i DataFrame in dizionari per la serializzazione JSON"""
    serializable_dict = {}
    for key, value in data_dict.items():
        if isinstance(value, pd.DataFrame):
            # Converti DataFrame in lista di dizionari
            serializable_dict[key] = value.to_dict('records')
        else:
            serializable_dict[key] = value
    return serializable_dict

# Converti i dati per la serializzazione
data_dict_serializable = convert_dataframes_to_dict(data_dict)

# Configurazioni globali
ruolo_color_map = {
    0: 'gray',
    1: 'green',
    2: 'red',
    3: 'darkorange',
    4: 'deepskyblue',
    5: 'purple'
}

ruolo_descrizione = {
    0: 'Ruolo Sconosciuto',
    1: 'Libero',
    2: 'Schiacciatore',
    3: 'Opposto',
    4: 'Centrale',
    5: 'Palleggiatore'
}

rotazione_descrizione = {
    1: 'P1',
    2: 'P2',
    3: 'P3',
    4: 'P4',
    5: 'P5',
    6: 'P6'
}

lista_variabili_pca = ['Serve', 'Reception', 'Set', 'Attack', 'Block', 'Defense', 'FreeBall', 
                       'ServeType_Q', 'ServeType_M', 'ServeStartZoneC_5', 'ServeStartZoneC_6', 'ServeStartZoneC_1', 'ServeEndZone3_5', 'ServeEndZone3_6', 'ServeEndZone3_1', 'ServeCCCA_R', 'ServeCCCA_T', 'ServeCCCA_C', 
                       'ReceptionType_Q', 'ReceptionType_M', 'ReceptionStartZoneC_5', 'ReceptionStartZoneC_6', 'ReceptionStartZoneC_1', 'ReceptionEndZone3_5', 'ReceptionEndZone3_6', 'ReceptionEndZone3_1', 'ReceptionServeCCCA_R', 'ReceptionServeCCCA_T', 'ReceptionServeCCCA_C', 
                       'SetType_T', 'SetType_Q', 'SetType_M', 'SetType_H', 'SetType_O', 'SetTypeNonH', 'SetEsecZone3_5', 'SetEsecZone3_6', 'SetEsecZone3_1', 'SetThisAppoggio_Positivo', 'SetThisAppoggio_Esclamativo', 'SetThisAppoggio_Negativo', 
                       'AttackType_T', 'AttackType_Q', 'AttackType_M', 'AttackType_H', 'AttackType_O', 'AttackTypeNonH', 'AttackStartZoneC_4', 'AttackStartZoneC_3', 'AttackStartZoneC_2', 'AttackStartZoneC_6', 'AttackStartZoneC_1', 'AttackThisAppoggio_Positivo', 'AttackThisAppoggio_Esclamativo', 'AttackThisAppoggio_Negativo', 'AttackThisSetEval_DoppioPositivo', 'AttackThisSetEval_Positivo', 'AttackPlayersInfo_0', 'AttackPlayersInfo_1', 'AttackPlayersInfo_2', 'AttackPlayersInfo_3', 'AttackPlayersInfo_4', 
                       'BlockType_T', 'BlockType_Q', 'BlockType_M', 'BlockType_H', 'BlockType_O', 'BlockTypeNonH', 'BlockEsecZone_4', 'BlockEsecZone_3', 'BlockEsecZone_2', 'BlockPlayersInfo_1', 'BlockPlayersInfo_2', 'BlockPlayersInfo_3', 'BlockPlayersInfo_4', 'BlockPrevAppoggio_Positivo', 'BlockPrevAppoggio_Esclamativo', 'BlockPrevAppoggio_Negativo', 
                       'DefenseCorrectSkillType_S', 'DefenseCorrectSkillType_B', 'DefenseCorrectSkillType_C', 'DefenseStartZoneC_2', 'DefenseStartZoneC_3', 'DefenseStartZoneC_4', 'DefenseStartZoneC_6', 'DefenseStartZoneC_1', 'DefenseEsecZone_1', 'DefenseEsecZone_2', 'DefenseEsecZone_3', 'DefenseEsecZone_4', 'DefenseEsecZone_5', 'DefenseEsecZone_6', 'DefenseEsecZone_7', 'DefenseEsecZone_8', 'DefenseEsecZone_9']

lista_variabili_pca_battuta = [v for v in lista_variabili_pca if v.startswith('Serve')]         #Prendo tutti gli items di lista_variabili_pca in cui la variabile inizia con "Serve"
lista_variabili_pca_ricezione = [v for v in lista_variabili_pca if v.startswith('Reception')]
lista_variabili_pca_alzata = [v for v in lista_variabili_pca if v.startswith('Set')]
lista_variabili_pca_attacco = [v for v in lista_variabili_pca if v.startswith('Attack')]
lista_variabili_pca_muro = [v for v in lista_variabili_pca if v.startswith('Block')]
lista_variabili_pca_difesa = [v for v in lista_variabili_pca if v.startswith('Defense')]
lista_variabili_pca_freeball = [v for v in lista_variabili_pca if v.startswith('FreeBall')]

THEMES = {
    "light": "https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css",
    "dark": "https://cdn.jsdelivr.net/npm/bootswatch@5.3.0/dist/darkly/bootstrap.min.css"
}

app = DashProxy(__name__, 
              external_stylesheets=[dbc.themes.BOOTSTRAP], 
              suppress_callback_exceptions=True,
              transforms=[ServersideOutputTransform()])

# Ora importa e registra le pagine DOPO aver creato l'app
from pages import home, fondamentali, tabelle, confronti

# Definisci le pagine disponibili
pages = [
    {"name": "Home", "href": "/home", "icon": "🏠", "module": home},
    {"name": "Fondamentali", "href": "/fondamentali", "icon": "📊", "module": fondamentali},
    {"name": "Tabelle", "href": "/tabelle", "icon": "📋", "module": tabelle},
    {"name": "Confronto", "href": "/confronti", "icon": "👥", "module": confronti},
    # Decomenta queste righe quando creerai le altre pagine:
    # {"name": "Progressioni", "href": "/progressioni", "icon": "📈", "module": progressioni},
    # {"name": "Grafici a zone", "href": "/grafici-zone", "icon": "🗺️", "module": grafici_a_zone},
    # {"name": "Sankey", "href": "/sankey", "icon": "🔀", "module": sankey},
    # {"name": "Rotazioni", "href": "/rotazioni", "icon": "🔄", "module": rotazioni},
    # {"name": "Similarità", "href": "/similarita", "icon": "🤝", "module": similarita},
]

# Date limits
min_date = data_dict['df_games']["Date"].min().strftime("%Y-%m-%d")
max_date = datetime.today().strftime("%Y-%m-%d")

def create_sidebar():
    return html.Div([
        html.Div("≡", className='menu-icon'),
        html.Div([
            html.H5("Menu"),
            html.Nav([
                html.Div([
                    dcc.Link([
                        html.Span(page["icon"], className="me-2"),
                        html.Span(page["name"], className="sidebar-text")
                    ], href=page["href"], className="nav-link")
                ]) for page in pages
            ], style={"marginBottom": "1rem"}),

            html.Hr(),
            html.Div("Filtri globali", style={'fontWeight':'600', 'marginBottom':'8px'}),
            
            # Radiobutton suddivisione dati
            html.Div([
                html.Label("Suddivisione dati", id="label-suddivisione-dati"),
                dbc.RadioItems(
                    id="radios-suddivisione-dati",
                    className="btn-group",
                    inputClassName="btn-check",
                    labelClassName="btn btn-outline-primary",
                    labelCheckedClassName="active",
                    options=[
                        {"label": "Complessiva", "value": 1},
                        {"label": "Per stagione", "value": 2},
                        {"label": "Per mese", "value": 3},
                        {"label": "Per partita", "value": 4},
                    ],
                    value=1,
                ),
            ], className="radio-group", style={"marginBottom": "0.8rem"}),

            # Slider Annata
            html.Div([
                html.Label("Annata", id="label-annata"),
                dcc.RangeSlider(
                    id='anno-range-slider',
                    min=data_dict['df_games']["Annata"].min(),
                    max=data_dict['df_games']["Annata"].max(),
                    value=[data_dict['df_games']["Annata"].min(), data_dict['df_games']["Annata"].max()],
                    marks={y: str(y) for y in range(data_dict['df_games']["Annata"].min(), 
                                                   data_dict['df_games']["Annata"].max()+1)},
                    step=1,
                    tooltip={"placement": "bottom"}
                ),
            ], style={"marginBottom": "0.8rem"}),

            # Date selectors
            dbc.Row([
                dbc.Col([
                    html.Label("Data iniziale", style={"fontSize":"1rem"}),
                    dbc.Input(
                        id="start-date",
                        type="date",
                        value=min_date,
                        min=min_date,
                        max=max_date,
                        style={"width": "130px", "fontSize": "1rem", "padding": "4px"}
                    ),
                ], width="auto"),
                dbc.Col([
                    html.Label("Data finale", style={"fontSize":"1rem"}),
                    dbc.Input(
                        id="end-date",
                        type="date",
                        value=max_date,
                        min=min_date,
                        max=max_date,
                        style={"width": "130px", "fontSize": "1rem", "padding": "4px"}
                    ),
                ], width="auto")
            ], className="g-2", style={"marginBottom": "0.8rem", "alignItems": "center"}),
            
            # Altri filtri...
            html.Div([
                dbc.Row([
                    dbc.Col([
                        html.Label("Campionato"),
                        dbc.Checklist(
                            id='campionato-checklist',
                            options=[{"label": c, "value": c} 
                                   for c in sorted(data_dict['df_games']["Campionato"].dropna().unique())],
                            value=sorted(data_dict['df_games']["Campionato"].dropna().unique()),
                            inline=False
                        )
                    ], width=6),
                    dbc.Col([
                        html.Label("Competition"),
                        dbc.Checklist(
                            id='competition-checklist',
                            options=[{"label": comp, "value": comp}
                                   for comp in sorted(data_dict['df_games']["Competition"].dropna().unique())],
                            value=sorted(data_dict['df_games']["Competition"].dropna().unique()),
                            inline=False
                        )
                    ], width=6)
                ])
            ], style={"marginBottom": "0.8rem"}),

            # Team dropdowns
            html.Div([
                html.Label("Team/TeamProprio"),
                dcc.Dropdown(
                    id='team-proprio-dropdown',
                    options=[{"label": f"{row['TeamID']} - {row['TeamNameShort']}", "value": row['TeamID']}
                           for _, row in data_dict['df_teams'].sort_values('TeamID').iterrows()],
                    value=[],
                    multi=True,
                    placeholder="Tutti i team selezionati (nessun filtro)"
                ),
            ], style={"marginBottom": "0.8rem"}),

            html.Div([
                html.Label("TeamContro"),
                dcc.Dropdown(
                    id='team-contro-dropdown',
                    options=[{"label": f"{row['TeamID']} - {row['TeamNameShort']}", "value": row['TeamID']}
                           for _, row in data_dict['df_teams'].sort_values('TeamID').iterrows()],
                    value=[],
                    multi=True,
                    placeholder="Tutti i team selezionati (nessun filtro)"
                ),
            ], style={"marginBottom": "0.8rem"}),

            # Altri filtri (giocatore, ruolo, rotazioni, etc.)
            html.Div([
                html.Label("Partita"),
                dcc.Dropdown(
                    id='partita-dropdown',
                    options=[],
                    value=[],
                    multi=True,
                    placeholder="Tutte le partite selezionate (nessun filtro)"
                )
            ], style={"marginBottom": "0.8rem"}),

            html.Div([
                html.Label("Giocatore"),
                dcc.Dropdown(
                    id='giocatore-dropdown',
                    options=[{"label": f"{row['Nome']} {row['Cognome']}", "value": row['PlayerID']}
                           for _, row in data_dict['df_players'].sort_values('Nome').iterrows()],
                    value=[],
                    multi=True,
                    placeholder="Tutti i giocatori selezionati (nessun filtro)"
                )
            ], style={"marginBottom": "0.8rem"}),

            # checkbox RuoloCalc            
            html.Div([
                html.Label("RuoloCalc"),
                dbc.Checklist(
                    id='ruolocalc-checklist',
                    options=[
                        {
                            "label": html.Span(label, style={"color": ruolo_color_map[key]}),
                            "value": key
                        }
                        for key, label in ruolo_descrizione.items()
                    ],
                    value=list(ruolo_descrizione.keys()),  # selezionati tutti di default
                    inline=False,
                    style={
                        "columnCount": 2,  # due colonne
                        "columnGap": "1rem"
                    }
                ),
            ], style={"marginBottom": "0.8rem"}),
            
            # checkbox rotazione propria
            html.Div([
                html.Label("Rotazione propria"),
                dbc.Checklist(
                    id='rotazione-propria-checklist',
                    options=[{"label": label, "value": key} for key, label in rotazione_descrizione.items()],
                    value=list(rotazione_descrizione.keys()),
                    inline=True,
                ),
            ], style={"marginBottom": "0.8rem"}),

            # checkbox rotazione contro
            html.Div([
                html.Label("Rotazione contro"),
                dbc.Checklist(
                    id='rotazione-contro-checklist',
                    options=[{"label": label, "value": key} for key, label in rotazione_descrizione.items()],
                    value=list(rotazione_descrizione.keys()),
                    inline=True,
                ),
            ], style={"marginBottom": "0.8rem"}),

        ], className='dropdown-vertical-content')
    ], className='hover-menu-vertical')

def create_right_sidebar():
    return html.Div([
        html.Span("⚙️", className="menu-icon-horizontal"),        
        html.Div([
            
            #Radiobutton Metodo di valutazione
            html.Div([
                html.Label("Metodo di valutazione", id="label-metodo-di-valutazione"),
                dbc.RadioItems(
                    id="radios-metodo-valutazione",
                    className="btn-group d-flex w-100",  # d-flex + w-100 = bottoni full-width
                    inputClassName="btn-check",
                    labelClassName="btn btn-outline-primary",
                    labelCheckedClassName="active",
                    options=[
                        {"label": "Generale", "value": "Generale", "label_id": "label-generale"},                    # Qui nei grafici uso le variabili che finiscono in _Generale
                        {"label": "Per ruolo", "value": "PerRuolo", "label_id": "label-per-ruolo"},                  # Qui nei grafici uso le variabili che finiscono in _PerRuolo
                        {"label": "Per giocatore", "value": "PerGiocatore", "label_id": "label-per-giocatore"},      # Qui nei grafici uso le variabili che finiscono in _PerGiocatore
                        {"label": "Efficienza", "value": "Efficienza", "label_id": "label-efficienza"},      # Qui nei grafici uso le variabili che finiscono in _Efficienza
                    ],
                    value="PerRuolo",   # valore iniziale
                    style={"width": "100%"}  # assicura full-width
                ),
                html.Div(id="output"),

                # Tooltip (testo mostrato su hover) delle etichette
                dbc.Tooltip("Scegli il metodo con cui calcolare i punteggi di giocatori e squadre", target="label-metodo-di-valutazione",placement="right"),     #Tooltip del titolo "Metodo di valutazione"
                dbc.Tooltip("Ad ogni Eval (#, +, !, -, /, =) è associato un punteggio in base a quanto quell'Eval in quel fondamentale finisce in un punto, considerando tutti i tocchi di tutti di tutti i giocatori", target="label-generale", placement="right"),
                dbc.Tooltip("Per ogni ruolo, ad ogni Eval (#, +, !, -, /, =) è associato un punteggio in base a quanto quell'Eval in quel fondamentale finisce in un punto, considerando tutti i tocchi dei giocatori di quel ruolo", target="label-per-ruolo", placement="right"), 
                dbc.Tooltip("Per ogni giocatore, il punteggio di quella variabile è (numero di tocchi fatti dal giocatore nella variabile, finiti in un punto) / (numero di tocchi fatti dal giocatore nella variabile)", target="label-per-giocatore", placement="right"),
                dbc.Tooltip("To be decided", target="label-efficienza", placement="right"),
            ], className="radio-group", style={"marginBottom": "1rem", "width": "100%"}),


                html.Div([
                    html.Div([
                        html.B("Battuta"),
                        dbc.Checklist(
                            id='serve-checklist',
                            options=[{"label": v, "value": v} for v in lista_variabili_pca_battuta],
                            value=['Serve', 'ServeType_Q', 'ServeType_M', 'ServeEndZone3_5', 'ServeEndZone3_6', 'ServeEndZone3_1'],  # Qui indico quali voglio che partano come True all'avvio dell'applicazione
                            inline=False
                        )
                    ],),

                    html.Div([
                        html.B("Ricezione"),
                        dbc.Checklist(
                            id='reception-checklist',
                            options=[{"label": v, "value": v} for v in lista_variabili_pca_ricezione],
                            value=['Reception', 'ReceptionType_Q', 'ReceptionType_M', 'ReceptionStartZoneC_5', 'ReceptionStartZoneC_6', 'ReceptionStartZoneC_1'],
                            inline=False
                        )
                    ],),

                    html.Div([
                        html.B("Alzata"),
                        dbc.Checklist(
                            id='set-checklist',
                            options=[{"label": v, "value": v} for v in lista_variabili_pca_alzata],
                            value=['Set', 'SetType_H', 'SetTypeNonH', 'SetThisAppoggio_Positivo', 'SetThisAppoggio_Esclamativo', 'SetThisAppoggio_Negativo',],
                            inline=False
                        )
                    ],),

                    html.Div([
                        html.B("Attacco"),
                        dbc.Checklist(
                            id='attack-checklist',
                            options=[{"label": v, "value": v} for v in lista_variabili_pca_attacco],
                            value=['Attack', 'AttackType_H', 'AttackTypeNonH', 'AttackStartZoneC_4', 'AttackStartZoneC_3', 'AttackStartZoneC_2', 'AttackStartZoneC_6', 'AttackStartZoneC_1', 'AttackThisAppoggio_Positivo', 'AttackThisAppoggio_Esclamativo', 'AttackThisAppoggio_Negativo', 'AttackThisSetEval_DoppioPositivo', 'AttackThisSetEval_Positivo', 'AttackPlayersInfo_0', 'AttackPlayersInfo_1', 'AttackPlayersInfo_2', 'AttackPlayersInfo_3', 'AttackPlayersInfo_4', ],
                            inline=False
                        )
                    ],),

                    html.Div([
                        html.B("Muro"),
                        dbc.Checklist(
                            id='block-checklist',
                            options=[{"label": v, "value": v} for v in lista_variabili_pca_muro],
                            value=lista_variabili_pca_muro,
                            inline=False
                        )
                    ],),

                    html.Div([
                        html.B("Difesa"),
                        dbc.Checklist(
                            id='defense-checklist',
                            options=[{"label": v, "value": v} for v in lista_variabili_pca_difesa],
                            value=lista_variabili_pca_difesa,
                            inline=False
                        )
                    ],),

                    html.Div([
                        html.B("Freeball"),
                        dbc.Checklist(
                            id='freeball-checklist',
                            options=[{"label": v, "value": v} for v in lista_variabili_pca_freeball],
                            value=lista_variabili_pca_freeball,
                            inline=False
                        )
                    ],),
                ], style={"display": "flex", "flexDirection": "row"})

        ], className="dropdown-horizontal-content")
    ], className='hover-menu-horizontal')

app.layout = html.Div([
    # Store components per salvare i dati persistenti
    dcc.Store(id="filters-store", storage_type="session", data={}),  # I filtri rimangono salvati
    # Utilizzando Serverside per i dati filtrati - molto più veloce per grandi dataset
    dcc.Store(id="filtered-data-store"),  # Serverside storage - gestito automaticamente
    dcc.Store(id="raw-data-store", storage_type="memory", data=data_dict_serializable),
    
    # Location component per il routing
    dcc.Location(id="url", refresh=False),
    
    # Sidebars
    create_sidebar(),
    create_right_sidebar(),
    
    # Theme link
    html.Link(id="theme-link", rel="stylesheet", href=THEMES["light"]),
    
    # Page content
    html.Div(id="page-content", style={"marginLeft": "220px", "padding": "20px"}),
])


# Callback principale per salvare i filtri
@app.callback(
    Output("filters-store", "data"),
    [
        Input("radios-suddivisione-dati", "value"),
        Input("anno-range-slider", "value"),
        Input("start-date", "value"),
        Input("end-date", "value"),
        Input("campionato-checklist", "value"),
        Input("competition-checklist", "value"),
        Input("team-proprio-dropdown", "value"),
        Input("team-contro-dropdown", "value"),
        Input("partita-dropdown", "value"),
        Input("giocatore-dropdown", "value"),
        Input("ruolocalc-checklist", "value"),
        Input("rotazione-propria-checklist", "value"),
        Input("rotazione-contro-checklist", "value"),
        Input("radios-metodo-valutazione", "value"),
        Input("serve-checklist", "value"),
        # Aggiungi altri input per le variabili...
    ]
)
def save_filters(*args):
    """Salva tutti i filtri in un dizionario"""
    filter_names = [
        "suddivisione_dati", "annata_range", "start_date", "end_date",
        "campionati", "competitions", "team_proprio", "team_contro",
        "partite", "giocatori", "ruoli", "rotazione_propria", "rotazione_contro",
        "metodo_valutazione", "serve_variables"
    ]
    
    return dict(zip(filter_names, args))


# Callback per aggiornare i dataframe filtrati - VERSIONE CON SERVERSIDE
@app.callback(
    Output("filtered-data-store", "data"),
    [Input("filters-store", "data")],
    [State("raw-data-store", "data")]
)
def update_filtered_data(filters, raw_data):
    """Applica i filtri ai dataframe e utilizza Serverside per il caching"""
    if not filters:
        return Serverside({})
    
    try:
        # I dati raw sono già in formato dizionario, quindi li convertiamo in DataFrame per il filtraggio
        data_dict_for_filtering = {}
        for key, data in raw_data.items():
            if isinstance(data, list) and data:
                data_dict_for_filtering[key] = pd.DataFrame(data)
            else:
                data_dict_for_filtering[key] = pd.DataFrame()
        
        # Applica i filtri usando la funzione utility
        filtered_data = apply_filters(data_dict_for_filtering, filters)
        
        # Salva i dataframe completi utilizzando Serverside - molto più veloce
        result = {}
        for key, df in filtered_data.items():
            if df is not None and not df.empty:
                # Con Serverside possiamo salvare direttamente i DataFrame senza conversioni
                result[key] = {
                    'data': df,  # DataFrame completo, non convertito in records
                    'total_rows': len(df),
                    'columns': df.columns.tolist()
                }
        
        return Serverside(result)
    except Exception as e:
        print(f"Errore nel filtraggio dei dati: {e}")
        return Serverside({})


# Callback per il routing delle pagine
@app.callback(
    Output("page-content", "children"),
    [Input("url", "pathname")],
    [State("filtered-data-store", "data"), State("filters-store", "data")]
)
def display_page(pathname, filtered_data, filters):
    """Mostra il contenuto della pagina corrente"""
    if pathname is None or pathname == "/":
        pathname = "/home"
    
    # Trova la pagina corrispondente
    for page in pages:
        if page["href"] == pathname:
            # Passa i dati filtrati e i filtri alla pagina
            return page["module"].layout(filtered_data, filters)
    
    # Pagina di default se non trova corrispondenze
    return home.layout(filtered_data, filters)


# Callback per aggiornare il dropdown delle partite
@app.callback(
    Output("partita-dropdown", "options"),
    [
        Input("team-proprio-dropdown", "value"),
        Input("team-contro-dropdown", "value"),
        Input("campionato-checklist", "value"),
        Input("anno-range-slider", "value")
    ],
    [State("raw-data-store", "data")]
)
def update_partita_options(team_proprio, team_contro, campionati, annata_range, raw_data):
    """Aggiorna le opzioni del dropdown delle partite"""
    # I dati raw sono già in formato dizionario, quindi li convertiamo in DataFrame
    df_games = pd.DataFrame(raw_data['df_games']) if raw_data.get('df_games') else pd.DataFrame()
    df_teams = pd.DataFrame(raw_data['df_teams']) if raw_data.get('df_teams') else pd.DataFrame()
    
    team_proprio_effettivi = team_proprio if team_proprio else df_teams['TeamID'].tolist()
    team_contro_effettivi = team_contro if team_contro else df_teams['TeamID'].tolist()
    annata_min, annata_max = annata_range
    
    df_games_filtered = df_games[
        ((df_games["IDHomeTeam"].isin(team_proprio_effettivi) | 
          df_games["IDVisitorTeam"].isin(team_proprio_effettivi))) &
        ((df_games["IDHomeTeam"].isin(team_contro_effettivi) | 
          df_games["IDVisitorTeam"].isin(team_contro_effettivi)) if team_contro else True) &
        (df_games["Campionato"].isin(campionati)) &
        (df_games["Annata"].between(annata_min, annata_max))
    ]
    
    options = [
        {
            "label": f"{row['HomeTeamNameShort']} {row['HomeTeamSetWon']} - "
                    f"{row['VisitorTeamSetWon']} {row['VisitorTeamNameShort']} ({row['DateStr']})",
            "value": row["GameID"]
        }
        for _, row in df_games_filtered.sort_values('Date', ascending=False).iterrows()
    ]
    
    return options


if __name__ == '__main__':
    app.run(debug=True, port=8030)