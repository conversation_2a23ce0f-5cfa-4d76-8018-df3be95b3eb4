import numpy as np 
import pandas as pd  #Realizzato con la versione di Pandas 2.2.3

import psycopg
from sqlalchemy import create_engine, text
from functools import reduce  #Per unire i dataframe dentro la lista di dataframe

import dash
from dash import dcc, html, Input, Output, State
import plotly.express as px
import plotly.graph_objects as go
import matplotlib.pyplot as plt
import seaborn as sns


from statsmodels.datasets import get_rdataset
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import \
(KMeans ,
AgglomerativeClustering)
from scipy.cluster.hierarchy import \
(dendrogram ,
cut_tree)
from sklearn.datasets import load_wine
from sklearn.preprocessing import MinMaxScaler
from scipy.cluster.hierarchy import dendrogram, linkage
from scipy.cluster.hierarchy import fcluster
from matplotlib.colors import ListedColormap


#Leggo la tabella rilevations_libero_view in un dataframe pandas
engine = create_engine('postgresql+psycopg://postgres:AcquaLevissima1@localhost:5432/db_modena')
query = """
SELECT *

FROM rilevations_libero_view
ORDER BY "GameID", "RilevationNumber"
"""

chunks = pd.read_sql_query(query, engine, chunksize=50000)
# Unisco i chunk in un unico DataFrame
df_rilevations = pd.concat(chunks, ignore_index=True)
df_rilevations

#Creo df_rilevations_ruolo1, che contiene solo le righe di df_rilevations in cui RuoloCalc = 1
df_rilevations_ruolo0 = df_rilevations[df_rilevations["RuoloCalc"] == 0]
df_rilevations_ruolo1 = df_rilevations[df_rilevations["RuoloCalc"] == 1]
df_rilevations_ruolo2 = df_rilevations[df_rilevations["RuoloCalc"] == 2]
df_rilevations_ruolo3 = df_rilevations[df_rilevations["RuoloCalc"] == 3]
df_rilevations_ruolo4 = df_rilevations[df_rilevations["RuoloCalc"] == 4]
df_rilevations_ruolo5 = df_rilevations[df_rilevations["RuoloCalc"] == 5]

print(f"df_rilevations_ruolo0 {df_rilevations_ruolo0.shape}")
print(f"df_rilevations_ruolo1 {df_rilevations_ruolo1.shape}")
print(f"df_rilevations_ruolo2 {df_rilevations_ruolo2.shape}")
print(f"df_rilevations_ruolo3 {df_rilevations_ruolo3.shape}")
print(f"df_rilevations_ruolo4 {df_rilevations_ruolo4.shape}")
print(f"df_rilevations_ruolo5 {df_rilevations_ruolo5.shape}")

df_rilevations_ruolo5

#Leggo la tabella players_each_game_view in un dataframe pandas
engine = create_engine('postgresql+psycopg://postgres:AcquaLevissima1@localhost:5432/db_modena')
df_players_each_game = pd.read_sql_query("""
SELECT *
FROM players_each_game_view
""", engine)
df_players_each_game['NomeCompleto'] = df_players_each_game['Nome'] + ' ' + df_players_each_game['Cognome']
df_players_each_game['MatchInfo'] = df_players_each_game.apply(
    lambda row: f"{row['HomeTeamNameShort']} {row['HomeTeamSetWon']} - {row['VisitorTeamSetWon']} {row['VisitorTeamNameShort']}" 
    if 'HomeTeamNameShort' in row.index else "",
    axis=1
)
df_players_each_game = df_players_each_game.sort_values(by=["PlayerID_auto"])
df_players_each_game

#Leggo la tabella players in un dataframe pandas
engine = create_engine('postgresql+psycopg://postgres:AcquaLevissima1@localhost:5432/db_modena')
df_players = pd.read_sql_query("""
SELECT *
FROM players_latest
""", engine)
df_players['NomeCompleto'] = df_players['Nome'] + ' ' + df_players['Cognome']
df_players = df_players.sort_values(by=["PlayerID"])
df_players

#Leggo la tabella games_view in un dataframe pandas
engine = create_engine('postgresql+psycopg://postgres:AcquaLevissima1@localhost:5432/db_modena')
df_games = pd.read_sql_query("""
SELECT *
FROM games_view
""", engine)
df_games = df_games.sort_values(by=["GameID"])
df_games

# Lista delle variabili che vuoi calcolare
variabili = {
    'Serve': df_rilevations['Foundamental'] == 'S',
    'RecTypeQ': (df_rilevations['Foundamental'] == 'R') & (df_rilevations['Type'] == 'Q'),
    'RecTypeM': (df_rilevations['Foundamental'] == 'R') & (df_rilevations['Type'] == 'M'),
    'SetThisAppoggioPositivo': (df_rilevations['Foundamental'] == 'E') & (df_rilevations['ThisAppoggio'] == '+'),
    'SetThisAppoggioEsclamativo': (df_rilevations['Foundamental'] == 'E') & (df_rilevations['ThisAppoggio'] == '!'),
    'SetThisAppoggioNegativo': (df_rilevations['Foundamental'] == 'E') & (df_rilevations['ThisAppoggio'] == '-'),
    'AttkThisAppoggioPositivo': (df_rilevations['Foundamental'] == 'A') & (df_rilevations['ThisAppoggio'] == '+'),
    'AttkThisAppoggioEsclamativo': (df_rilevations['Foundamental'] == 'A') & (df_rilevations['ThisAppoggio'] == '!'),
    'AttkThisAppoggioNegativo': (df_rilevations['Foundamental'] == 'A') & (df_rilevations['ThisAppoggio'] == '-'),
    'BlockPrevAppoggioPositivo': (df_rilevations['Foundamental'] == 'B') & (df_rilevations['PrevAppoggio'] == '+'),
    'BlockPrevAppoggioEsclamativo': (df_rilevations['Foundamental'] == 'B') & (df_rilevations['PrevAppoggio'] == '!'),
    'BlockPrevAppoggioNegativo': (df_rilevations['Foundamental'] == 'B') & (df_rilevations['PrevAppoggio'] == '-'),
    'DefCorrectSkillTypeS': (df_rilevations['Foundamental'] == 'D') & (df_rilevations['SkillType'] == 'S'),
    'DefCorrectSkillTypeB': (df_rilevations['Foundamental'] == 'D') & (df_rilevations['SkillType'] == 'B'),
    'DefCorrectSkillTypeC': (df_rilevations['Foundamental'] == 'D') & (df_rilevations['SkillType'] == 'C'),
    'FreeBall': df_rilevations['Foundamental'] == 'F'
}

# Consideriamo solo gli Eval che ti interessano
eval_validi = ['#', '+', '!', '-', '/', '=']



def calcola_dizionario_voti(variabili_da_includere, df_rilevations):
    dizionario_voti = {}

    for nome_var, filtro in variabili_da_includere.items():
        # Allineo filtro all'indice di df_rilevations per evitare warning
        filtro = filtro.reindex(df_rilevations.index, fill_value=False)  #forzi filtro ad avere lo stesso indice di df_rilevations, impostando a False tutte le righe che non combaciano
        
        df_filtrato = df_rilevations.loc[filtro & df_rilevations['Eval'].isin(eval_validi)]
        
        # Calcolo media di EndedInPoint per ciascun Eval
        distribuzione = df_filtrato.groupby('Eval')['EndedInPoint'].mean()
        
        # Riempie gli Eval mancanti con NaN (o 0 se preferisci)
        distribuzione = distribuzione.reindex(eval_validi, fill_value=0)

        # Salva il dizionario
        dizionario_voti[nome_var] = distribuzione.to_dict()

    return dizionario_voti



eval_scores = calcola_dizionario_voti(variabili, df_rilevations)








df_rilevations_libero = df_rilevations.loc[df_rilevations["RuoloCalc"] == 1]
df_rilevations_schiacciatore = df_rilevations.loc[df_rilevations["RuoloCalc"] == 2]
df_rilevations_opposto = df_rilevations.loc[df_rilevations["RuoloCalc"] == 3]
df_rilevations_centrale = df_rilevations.loc[df_rilevations["RuoloCalc"] == 4]
df_rilevations_palleggiatore = df_rilevations.loc[df_rilevations["RuoloCalc"] == 5]


df_players_libero = df_players.loc[df_players["RuoloCalc"] == 1].copy()
df_players_schiacciatore = df_players.loc[df_players["RuoloCalc"]==2].copy()
df_players_opposto = df_players.loc[df_players["RuoloCalc"]==3].copy()
df_players_centrale = df_players.loc[df_players["RuoloCalc"]==4].copy()
df_players_palleggiatore = df_players.loc[df_players["RuoloCalc"]==5].copy()

df_players_each_game_libero = df_players_each_game[df_players_each_game['RuoloCalc'] == 1]
df_players_each_game_schiacciatore = df_players_each_game[df_players_each_game['RuoloCalc'] == 2]
df_players_each_game_opposto = df_players_each_game[df_players_each_game['RuoloCalc'] == 3]
df_players_each_game_centrale = df_players_each_game[df_players_each_game['RuoloCalc'] == 4]
df_players_each_game_palleggiatore = df_players_each_game[df_players_each_game['RuoloCalc'] == 5]


lista_tutte_le_variabili_voto = ['Serve', 'Reception', 'Set', 'Attack', 'Block', 'Defense', 'FreeBall',
                                 'ServeTypeQ', 'ServeTypeM', 'ServeStartZoneC_5', 'ServeStartZoneC_6', 'ServeStartZoneC_1', 'ServeEndZone3_5', 'ServeEndZone3_6', 'ServeEndZone3_1', 'ServeCCCA_R', 'ServeCCCA_T', 'ServeCCCA_C',
                                 'ReceptionTypeQ', 'ReceptionTypeM', 'ReceptionStartZoneC_5', 'ReceptionStartZoneC_6', 'ReceptionStartZoneC_1', 'ReceptionEndZone3_5', 'ReceptionEndZone3_6', 'ReceptionEndZone3_1', 'ReceptionServeCCCA_R', 'ReceptionServeCCCA_T', 'ReceptionServeCCCA_C',
                                 'SetTypeT', 'SetTypeQ', 'SetTypeM', 'SetTypeH', 'SetTypeO', 'SetTypeNonH', 'SetEsecZone3_5', 'SetEsecZone3_6', 'SetEsecZone3_1', 'SetThisAppoggioPositivo', 'SetThisAppoggioEsclamativo', 'SetThisAppoggioNegativo',
                                 'AttackTypeT', 'AttackTypeQ', 'AttackTypeM', 'AttackTypeH', 'AttackTypeO', 'AttackTypeNonH', 'AttackStartZoneC_4', 'AttackStartZoneC_5', 'AttackStartZoneC_6', 'AttackStartZoneC_1', 'AttackThisAppoggioPositivo', 'AttackThisAppoggioEsclamativo', 'AttackThisAppoggioNegativo',
                                 'BlockTypeT', 'BlockTypeQ', 'BlockTypeM', 'BlockTypeH', 'BlockTypeO', 'BlockTypeNonH', 'BlockEsecZone3_5', 'BlockEsecZone3_6', 'BlockEsecZone3_1', 'BlockPrevAppoggioPositivo', 'BlockPrevAppoggioEsclamativo', 'BlockPrevAppoggioNegativo',
                                 'DefenseCorrectSkillTypeS', 'DefenseCorrectSkillTypeB', 'DefenseCorrectSkillTypeC', 'DefenseStartZoneC_2', 'DefenseStartZoneC_3', 'DefenseStartZoneC_4', 'DefenseStartZoneC_6', 'DefenseStartZoneC_1', 'DefenseEsecZone_1', 'DefenseEsecZone_2', 'DefenseEsecZone_3', 'DefenseEsecZone_4', 'DefenseEsecZone_5', 'DefenseEsecZone_6', 'DefenseEsecZone_7', 'DefenseEsecZone_8', 'DefenseEsecZone_9'
                                 ]

#Aggiungo a df_players la colonna TotalTouch, che per ogni giocatore indica il numero di tocchi che ha fatto da sempre. 
# Se lo calcolo per df_players conta il numero di righe in df_rilevations in cui NumeroMaglia_ID = PlayerID
# Se lo calcolo per df_players_each_game conta il numero di righe in df_rilevations in cui NumeroMaglia_ID = PlayerID e GameID = GameID


#Qui posso tranquillamente aggiungere tutte la variabili che voglio, non solo i 7 fondamentali
def calcola_totale_tocchi(df_rilevations, df_players, per_players=True):
        
    if per_players:    # Se per_players=True, conta i tocchi totali per ogni NumeroMaglia_ID. Per ognuno di questi conto i tocchi
        Touch = df_rilevations.groupby("NumeroMaglia_ID").size()
        Serve = df_rilevations[df_rilevations["Foundamental"] == "S"].groupby("NumeroMaglia_ID").size()
        Reception = df_rilevations[df_rilevations["Foundamental"] == "R"].groupby("NumeroMaglia_ID").size()
        Set = df_rilevations[df_rilevations["Foundamental"] == "E"].groupby("NumeroMaglia_ID").size()
        Attack = df_rilevations[df_rilevations["Foundamental"] == "A"].groupby("NumeroMaglia_ID").size()
        Block = df_rilevations[df_rilevations["Foundamental"] == "B"].groupby("NumeroMaglia_ID").size()
        Defense = df_rilevations[df_rilevations["Foundamental"] == "D"].groupby("NumeroMaglia_ID").size()
        Freeball = df_rilevations[df_rilevations["Foundamental"] == "F"].groupby("NumeroMaglia_ID").size()
        ServeTypeQ = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["Type"] == "Q")].groupby("NumeroMaglia_ID").size()
        ServeTypeM = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["Type"] == "M")].groupby("NumeroMaglia_ID").size()
        ServeStartZoneC_5 = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["StartZoneCompact"] == 5)].groupby("NumeroMaglia_ID").size()
        ServeStartZoneC_6 = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["StartZoneCompact"] == 6)].groupby("NumeroMaglia_ID").size()
        ServeStartZoneC_1 = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["StartZoneCompact"] == 1)].groupby("NumeroMaglia_ID").size()
        ServeEndZone3_5 = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["EndZoneEsecZone3aree"] == 5)].groupby("NumeroMaglia_ID").size()
        ServeEndZone3_6 = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["EndZoneEsecZone3aree"] == 6)].groupby("NumeroMaglia_ID").size()
        ServeEndZone3_1 = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["EndZoneEsecZone3aree"] == 1)].groupby("NumeroMaglia_ID").size()
        ServeCCCA_R = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["correctCustomCharAggregate"] == "R")].groupby("NumeroMaglia_ID").size()
        ServeCCCA_T = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["correctCustomCharAggregate"] == "T")].groupby("NumeroMaglia_ID").size()
        ServeCCCA_C = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["correctCustomCharAggregate"] == "C")].groupby("NumeroMaglia_ID").size()
        ReceptionTypeQ = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["Type"] == "Q")].groupby("NumeroMaglia_ID").size()
        ReceptionTypeM = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["Type"] == "M")].groupby("NumeroMaglia_ID").size()
        ReceptionStartZoneC_5 = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["StartZoneCompact"] == 5)].groupby("NumeroMaglia_ID").size()
        ReceptionStartZoneC_6 = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["StartZoneCompact"] == 6)].groupby("NumeroMaglia_ID").size()
        ReceptionStartZoneC_1 = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["StartZoneCompact"] == 1)].groupby("NumeroMaglia_ID").size()
        ReceptionEndZone3_5 = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["EndZoneEsecZone3aree"] == 5)].groupby("NumeroMaglia_ID").size()
        ReceptionEndZone3_6 = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["EndZoneEsecZone3aree"] == 6)].groupby("NumeroMaglia_ID").size()
        ReceptionEndZone3_1 = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["EndZoneEsecZone3aree"] == 1)].groupby("NumeroMaglia_ID").size()
        ReceptionServeCCCA_R = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["PrevServeCorrectCustomCharAggregate"] == "R")].groupby("NumeroMaglia_ID").size()
        ReceptionServeCCCA_T = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["PrevServeCorrectCustomCharAggregate"] == "T")].groupby("NumeroMaglia_ID").size()
        ReceptionServeCCCA_C = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["PrevServeCorrectCustomCharAggregate"] == "C")].groupby("NumeroMaglia_ID").size()
        SetTypeT = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["Type"] == "T")].groupby("NumeroMaglia_ID").size()
        SetTypeQ = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["Type"] == "Q")].groupby("NumeroMaglia_ID").size()
        SetTypeM = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["Type"] == "M")].groupby("NumeroMaglia_ID").size()
        SetTypeH = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["Type"] == "H")].groupby("NumeroMaglia_ID").size()
        SetTypeO = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["Type"] == "O")].groupby("NumeroMaglia_ID").size()
        SetTypeNonH = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["Type"] != "H")].groupby("NumeroMaglia_ID").size()
        SetEsecZone3_5 = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["EndZoneEsecZone3aree"] == 5)].groupby("NumeroMaglia_ID").size()
        SetEsecZone3_6 = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["EndZoneEsecZone3aree"] == 6)].groupby("NumeroMaglia_ID").size()
        SetEsecZone3_1 = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["EndZoneEsecZone3aree"] == 1)].groupby("NumeroMaglia_ID").size()
        SetThisAppoggioPositivo = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["ThisAppoggio"] == '+')].groupby("NumeroMaglia_ID").size()
        SetThisAppoggioEsclamativo = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["ThisAppoggio"] == '!')].groupby("NumeroMaglia_ID").size()
        SetThisAppoggioNegativo = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["ThisAppoggio"] == '-')].groupby("NumeroMaglia_ID").size()
        AttackTypeT = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["Type"] == "T")].groupby("NumeroMaglia_ID").size()
        AttackTypeQ = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["Type"] == "Q")].groupby("NumeroMaglia_ID").size()
        AttackTypeM = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["Type"] == "M")].groupby("NumeroMaglia_ID").size()
        AttackTypeH = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["Type"] == "H")].groupby("NumeroMaglia_ID").size()
        AttackTypeO = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["Type"] == "O")].groupby("NumeroMaglia_ID").size()
        AttackTypeNonH = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["Type"] != "H")].groupby("NumeroMaglia_ID").size()
        AttackStartZoneC_4 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["StartZoneCompact"] == 4)].groupby("NumeroMaglia_ID").size()
        AttackStartZoneC_3 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["StartZoneCompact"] == 3)].groupby("NumeroMaglia_ID").size()
        AttackStartZoneC_2 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["StartZoneCompact"] == 2)].groupby("NumeroMaglia_ID").size()
        AttackStartZoneC_1 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["StartZoneCompact"] == 1)].groupby("NumeroMaglia_ID").size()
        AttackStartZoneC_6 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["StartZoneCompact"] == 6)].groupby("NumeroMaglia_ID").size()
        AttackThisAppoggioPositivo = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["ThisAppoggio"] == '+')].groupby("NumeroMaglia_ID").size()
        AttackThisAppoggioEsclamativo = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["ThisAppoggio"] == '!')].groupby("NumeroMaglia_ID").size()
        AttackThisAppoggioNegativo = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["ThisAppoggio"] == '-')].groupby("NumeroMaglia_ID").size()
        AttackThisSetEvalDoppioPositivo = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["ThisSetEval"] == '#')].groupby("NumeroMaglia_ID").size()
        AttackThisSetEvalPositivo = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["ThisSetEval"] == '+')].groupby("NumeroMaglia_ID").size()
        AttackPlayersInfo0 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["PlayersInfo"] == 0)].groupby("NumeroMaglia_ID").size()
        AttackPlayersInfo1 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["PlayersInfo"] == 1)].groupby("NumeroMaglia_ID").size()
        AttackPlayersInfo2 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["PlayersInfo"] == 2)].groupby("NumeroMaglia_ID").size()
        AttackPlayersInfo3 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["PlayersInfo"] == 3)].groupby("NumeroMaglia_ID").size()
        AttackPlayersInfo4 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["PlayersInfo"] == 4)].groupby("NumeroMaglia_ID").size()
        BlockTypeT = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["Type"] == "T")].groupby("NumeroMaglia_ID").size()
        BlockTypeQ = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["Type"] == "Q")].groupby("NumeroMaglia_ID").size()
        BlockTypeM = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["Type"] == "M")].groupby("NumeroMaglia_ID").size()
        BlockTypeH = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["Type"] == "H")].groupby("NumeroMaglia_ID").size()
        BlockTypeO = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["Type"] == "O")].groupby("NumeroMaglia_ID").size()
        BlockTypeNonH = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["Type"] != "H")].groupby("NumeroMaglia_ID").size()
        BlockEsecZone4 = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["EndZoneEsecZone"] == 4)].groupby("NumeroMaglia_ID").size()
        BlockEsecZone3 = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["EndZoneEsecZone"] == 3)].groupby("NumeroMaglia_ID").size()
        BlockEsecZone2 = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["EndZoneEsecZone"] == 2)].groupby("NumeroMaglia_ID").size()
        BlockPlayersInfo1 = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["PlayersInfo"] == 1)].groupby("NumeroMaglia_ID").size()
        BlockPlayersInfo2 = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["PlayersInfo"] == 2)].groupby("NumeroMaglia_ID").size()
        BlockPlayersInfo3 = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["PlayersInfo"] == 3)].groupby("NumeroMaglia_ID").size()
        BlockPlayersInfo4 = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["PlayersInfo"] == 4)].groupby("NumeroMaglia_ID").size()
        BlockPrevAppoggioPositivo = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["PrevAppoggio"] == '+')].groupby("NumeroMaglia_ID").size()
        BlockPrevAppoggioEsclamativo = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["PrevAppoggio"] == '!')].groupby("NumeroMaglia_ID").size()
        BlockPrevAppoggioNegativo = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["PrevAppoggio"] == '-')].groupby("NumeroMaglia_ID").size()
        DefenseCorrectSkillTypeS = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["correctSkillType"] == "S")].groupby("NumeroMaglia_ID").size()
        DefenseCorrectSkillTypeB = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["correctSkillType"] == "B")].groupby("NumeroMaglia_ID").size()
        DefenseCorrectSkillTypeC = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["correctSkillType"] == "C")].groupby("NumeroMaglia_ID").size()
        DefenseStartZoneC_2 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["StartZoneCompact"] == 2)].groupby("NumeroMaglia_ID").size()
        DefenseStartZoneC_3 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["StartZoneCompact"] == 3)].groupby("NumeroMaglia_ID").size()
        DefenseStartZoneC_4 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["StartZoneCompact"] == 4)].groupby("NumeroMaglia_ID").size()
        DefenseStartZoneC_1 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["StartZoneCompact"] == 1)].groupby("NumeroMaglia_ID").size()
        DefenseStartZoneC_6 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["StartZoneCompact"] == 6)].groupby("NumeroMaglia_ID").size() 
        DefenseEsecZone_1 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 1)].groupby("NumeroMaglia_ID").size()  
        DefenseEsecZone_2 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 2)].groupby("NumeroMaglia_ID").size()  
        DefenseEsecZone_3 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 3)].groupby("NumeroMaglia_ID").size()  
        DefenseEsecZone_4 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 4)].groupby("NumeroMaglia_ID").size()  
        DefenseEsecZone_5 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 5)].groupby("NumeroMaglia_ID").size()  
        DefenseEsecZone_6 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 6)].groupby("NumeroMaglia_ID").size()  
        DefenseEsecZone_7 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 7)].groupby("NumeroMaglia_ID").size()  
        DefenseEsecZone_8 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 8)].groupby("NumeroMaglia_ID").size()  
        DefenseEsecZone_9 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 9)].groupby("NumeroMaglia_ID").size() 
        
        
        # Dizionario: chiave = nome colonna finale, valore = dizionario dei conteggi
        #Creo counts_dict2 che ha come chiave "Total_{variabile}" e come valore {variabile}, per ogni variabile in lista_tutte_le_variabili_voto
        counts_dict = {
            'Serve': Serve,
            'Reception': Reception,
            'Set': Set,
            'Attack': Attack,
            'Block': Block,
            'Defense': Defense,
            'Freeball': Freeball,
            'ServeTypeQ': ServeTypeQ,
            'ServeTypeM': ServeTypeM,
            'ServeStartZoneC_5': ServeStartZoneC_5,
            'ServeStartZoneC_6': ServeStartZoneC_6,
            'ServeStartZoneC_1': ServeStartZoneC_1,
            'ServeEndZone3_5': ServeEndZone3_5,
            'ServeEndZone3_6': ServeEndZone3_6,
            'ServeEndZone3_1': ServeEndZone3_1,
            'ServeCCCA_R': ServeCCCA_R,
            'ServeCCCA_T': ServeCCCA_T,
            'ServeCCCA_C': ServeCCCA_C,
            'ReceptionTypeQ': ReceptionTypeQ,
            'ReceptionTypeM': ReceptionTypeM,
            'ReceptionStartZoneC_5': ReceptionStartZoneC_5,
            'ReceptionStartZoneC_6': ReceptionStartZoneC_6,
            'ReceptionStartZoneC_1': ReceptionStartZoneC_1,
            'ReceptionEndZone3_5': ReceptionEndZone3_5,
            'ReceptionEndZone3_6': ReceptionEndZone3_6,
            'ReceptionEndZone3_1': ReceptionEndZone3_1,
            'ReceptionServeCCCA_R': ReceptionServeCCCA_R,
            'ReceptionServeCCCA_T': ReceptionServeCCCA_T,
            'ReceptionServeCCCA_C': ReceptionServeCCCA_C,
            'SetTypeT': SetTypeT,
            'SetTypeQ': SetTypeQ,
            'SetTypeM': SetTypeM,
            'SetTypeH': SetTypeH,
            'SetTypeO': SetTypeO,
            'SetTypeNonH': SetTypeNonH,
            'SetEsecZone3_5': SetEsecZone3_5,
            'SetEsecZone3_6': SetEsecZone3_6,
            'SetEsecZone3_1': SetEsecZone3_1,
            'SetThisAppoggioPositivo': SetThisAppoggioPositivo,
            'SetThisAppoggioEsclamativo': SetThisAppoggioEsclamativo,
            'SetThisAppoggioNegativo': SetThisAppoggioNegativo,
            'AttackTypeT': AttackTypeT,
            'AttackTypeQ': AttackTypeQ,
            'AttackTypeM': AttackTypeM,
            'AttackTypeH': AttackTypeH,
            'AttackTypeO': AttackTypeO,
            'AttackTypeNonH': AttackTypeNonH,
            'AttackStartZoneC_4': AttackStartZoneC_4,
            'AttackStartZoneC_3': AttackStartZoneC_3,  
            'AttackStartZoneC_2': AttackStartZoneC_2,
            'AttackStartZoneC_6': AttackStartZoneC_6,  
            'AttackStartZoneC_1': AttackStartZoneC_1,
            'AttackThisAppoggioPositivo': AttackThisAppoggioPositivo,  
            'AttackThisAppoggioEsclamativo': AttackThisAppoggioEsclamativo,  
            'AttackThisAppoggioNegativo': AttackThisAppoggioNegativo,  
            'AttackThisSetEvalDoppioPositivo': AttackThisSetEvalDoppioPositivo,  
            'AttackThisSetEvalPositivo': AttackThisSetEvalPositivo,  
            'AttackPlayersInfo0': AttackPlayersInfo0,  
            'AttackPlayersInfo1': AttackPlayersInfo1,  
            'AttackPlayersInfo2': AttackPlayersInfo2,  
            'AttackPlayersInfo3': AttackPlayersInfo3,  
            'AttackPlayersInfo4': AttackPlayersInfo4,  
            'BlockTypeT': BlockTypeT,
            'BlockTypeQ': BlockTypeQ,
            'BlockTypeM': BlockTypeM,
            'BlockTypeH': BlockTypeH,
            'BlockTypeO': BlockTypeO,
            'BlockTypeNonH': BlockTypeNonH,
            'BlockEsecZone_4': BlockEsecZone4,  
            'BlockEsecZone_3': BlockEsecZone3,  
            'BlockEsecZone_2': BlockEsecZone2, 
            'BlockPlayersInfo_1': BlockPlayersInfo1,  
            'BlockPlayersInfo_2': BlockPlayersInfo2,  
            'BlockPlayersInfo_3': BlockPlayersInfo3,  
            'BlockPlayersInfo_4': BlockPlayersInfo4,  
            'BlockPrevAppoggioPositivo': BlockPrevAppoggioPositivo,
            'BlockPrevAppoggioEsclamativo': BlockPrevAppoggioEsclamativo,
            'BlockPrevAppoggioNegativo': BlockPrevAppoggioNegativo,
            'DefenseCorrectSkillTypeS': DefenseCorrectSkillTypeS,
            'DefenseCorrectSkillTypeB': DefenseCorrectSkillTypeB,
            'DefenseCorrectSkillTypeC': DefenseCorrectSkillTypeC,
            'DefenseStartZoneC_2': DefenseStartZoneC_2,
            'DefenseStartZoneC_3': DefenseStartZoneC_3,
            'DefenseStartZoneC_4': DefenseStartZoneC_4,
            'DefenseStartZoneC_6': DefenseStartZoneC_6,
            'DefenseStartZoneC_1': DefenseStartZoneC_1,
            'DefenseEsecZone_1': DefenseEsecZone_1,
            'DefenseEsecZone_2': DefenseEsecZone_2,
            'DefenseEsecZone_3': DefenseEsecZone_3,
            'DefenseEsecZone_4': DefenseEsecZone_4,
            'DefenseEsecZone_5': DefenseEsecZone_5,
            'DefenseEsecZone_6': DefenseEsecZone_6,
            'DefenseEsecZone_7': DefenseEsecZone_7,
            'DefenseEsecZone_8': DefenseEsecZone_8,
            'DefenseEsecZone_9': DefenseEsecZone_9
        }
        
        
        #Aggiungo le colonne Total_{variabile} che contano il numero di azioni di ciascun tipo che ha fatto ogni giocatore. Ovvero conta il numero di righe in df_rilevations in cui NumeroMaglia_ID = PlayerID e Foundamental = 'S', 'R', 'E', 'A', 'B', 'D', 'F' ecc
        for col, counts in counts_dict.items():
            df_players[col] = df_players["PlayerID"].map(counts).fillna(0).astype(int)


    else:             # Se per_players=False, conta i tocchi totali per ogni NumeroMaglia_ID e GameID
        Touch = df_rilevations.groupby("NumeroMaglia_ID", "GameID").size()
        Serve = df_rilevations[df_rilevations["Foundamental"] == "S"].groupby("NumeroMaglia_ID", "GameID").size()
        Reception = df_rilevations[df_rilevations["Foundamental"] == "R"].groupby("NumeroMaglia_ID", "GameID").size()
        Set = df_rilevations[df_rilevations["Foundamental"] == "E"].groupby("NumeroMaglia_ID", "GameID").size()
        Attack = df_rilevations[df_rilevations["Foundamental"] == "A"].groupby("NumeroMaglia_ID", "GameID").size()
        Block = df_rilevations[df_rilevations["Foundamental"] == "B"].groupby("NumeroMaglia_ID", "GameID").size()
        Defense = df_rilevations[df_rilevations["Foundamental"] == "D"].groupby("NumeroMaglia_ID", "GameID").size()
        Freeball = df_rilevations[df_rilevations["Foundamental"] == "F"].groupby("NumeroMaglia_ID", "GameID").size()
        ServeTypeQ = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["Type"] == "Q")].groupby("NumeroMaglia_ID", "GameID").size()
        ServeTypeM = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["Type"] == "M")].groupby("NumeroMaglia_ID", "GameID").size()
        ServeStartZoneC_5 = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["StartZoneCompact"] == 5)].groupby("NumeroMaglia_ID", "GameID").size()
        ServeStartZoneC_6 = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["StartZoneCompact"] == 6)].groupby("NumeroMaglia_ID", "GameID").size()
        ServeStartZoneC_1 = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["StartZoneCompact"] == 1)].groupby("NumeroMaglia_ID", "GameID").size()
        ServeEndZone3_5 = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["EndZoneEsecZone3aree"] == 5)].groupby("NumeroMaglia_ID", "GameID").size()
        ServeEndZone3_6 = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["EndZoneEsecZone3aree"] == 6)].groupby("NumeroMaglia_ID", "GameID").size()
        ServeEndZone3_1 = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["EndZoneEsecZone3aree"] == 1)].groupby("NumeroMaglia_ID", "GameID").size()
        ServeCCCA_R = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["correctCustomCharAggregate"] == "R")].groupby("NumeroMaglia_ID", "GameID").size()
        ServeCCCA_T = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["correctCustomCharAggregate"] == "T")].groupby("NumeroMaglia_ID", "GameID").size()
        ServeCCCA_C = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["correctCustomCharAggregate"] == "C")].groupby("NumeroMaglia_ID", "GameID").size()
        ReceptionTypeQ = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["Type"] == "Q")].groupby("NumeroMaglia_ID", "GameID").size()
        ReceptionTypeM = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["Type"] == "M")].groupby("NumeroMaglia_ID", "GameID").size()
        ReceptionStartZoneC_5 = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["StartZoneCompact"] == 5)].groupby("NumeroMaglia_ID", "GameID").size()
        ReceptionStartZoneC_6 = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["StartZoneCompact"] == 6)].groupby("NumeroMaglia_ID", "GameID").size()
        ReceptionStartZoneC_1 = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["StartZoneCompact"] == 1)].groupby("NumeroMaglia_ID", "GameID").size()
        ReceptionEndZone3_5 = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["EndZoneEsecZone3aree"] == 5)].groupby("NumeroMaglia_ID", "GameID").size()
        ReceptionEndZone3_6 = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["EndZoneEsecZone3aree"] == 6)].groupby("NumeroMaglia_ID", "GameID").size()
        ReceptionEndZone3_1 = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["EndZoneEsecZone3aree"] == 1)].groupby("NumeroMaglia_ID", "GameID").size()
        ReceptionServeCCCA_R = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["PrevServeCorrectCustomCharAggregate"] == "R")].groupby("NumeroMaglia_ID", "GameID").size()
        ReceptionServeCCCA_T = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["PrevServeCorrectCustomCharAggregate"] == "T")].groupby("NumeroMaglia_ID", "GameID").size()
        ReceptionServeCCCA_C = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["PrevServeCorrectCustomCharAggregate"] == "C")].groupby("NumeroMaglia_ID", "GameID").size()
        SetTypeT = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["Type"] == "T")].groupby("NumeroMaglia_ID", "GameID").size()
        SetTypeQ = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["Type"] == "Q")].groupby("NumeroMaglia_ID", "GameID").size()
        SetTypeM = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["Type"] == "M")].groupby("NumeroMaglia_ID", "GameID").size()
        SetTypeH = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["Type"] == "H")].groupby("NumeroMaglia_ID", "GameID").size()
        SetTypeO = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["Type"] == "O")].groupby("NumeroMaglia_ID", "GameID").size()
        SetTypeNonH = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["Type"] != "H")].groupby("NumeroMaglia_ID", "GameID").size()
        SetEsecZone3_5 = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["EndZoneEsecZone3aree"] == 5)].groupby("NumeroMaglia_ID", "GameID").size()
        SetEsecZone3_6 = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["EndZoneEsecZone3aree"] == 6)].groupby("NumeroMaglia_ID", "GameID").size()
        SetEsecZone3_1 = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["EndZoneEsecZone3aree"] == 1)].groupby("NumeroMaglia_ID", "GameID").size()
        SetThisAppoggioPositivo = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["ThisAppoggio"] == '+')].groupby("NumeroMaglia_ID", "GameID").size()
        SetThisAppoggioEsclamativo = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["ThisAppoggio"] == '!')].groupby("NumeroMaglia_ID", "GameID").size()
        SetThisAppoggioNegativo = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["ThisAppoggio"] == '-')].groupby("NumeroMaglia_ID", "GameID").size()
        AttackTypeT = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["Type"] == "T")].groupby("NumeroMaglia_ID", "GameID").size()
        AttackTypeQ = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["Type"] == "Q")].groupby("NumeroMaglia_ID", "GameID").size()
        AttackTypeM = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["Type"] == "M")].groupby("NumeroMaglia_ID", "GameID").size()
        AttackTypeH = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["Type"] == "H")].groupby("NumeroMaglia_ID", "GameID").size()
        AttackTypeO = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["Type"] == "O")].groupby("NumeroMaglia_ID", "GameID").size()
        AttackTypeNonH = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["Type"] != "H")].groupby("NumeroMaglia_ID", "GameID").size()
        AttackStartZoneC_4 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["StartZoneCompact"] == 4)].groupby("NumeroMaglia_ID", "GameID").size()
        AttackStartZoneC_3 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["StartZoneCompact"] == 3)].groupby("NumeroMaglia_ID", "GameID").size()
        AttackStartZoneC_2 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["StartZoneCompact"] == 2)].groupby("NumeroMaglia_ID", "GameID").size()
        AttackStartZoneC_1 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["StartZoneCompact"] == 1)].groupby("NumeroMaglia_ID", "GameID").size()
        AttackStartZoneC_6 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["StartZoneCompact"] == 6)].groupby("NumeroMaglia_ID", "GameID").size()
        AttackThisAppoggioPositivo = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["ThisAppoggio"] == '+')].groupby("NumeroMaglia_ID", "GameID").size()
        AttackThisAppoggioEsclamativo = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["ThisAppoggio"] == '!')].groupby("NumeroMaglia_ID", "GameID").size()
        AttackThisAppoggioNegativo = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["ThisAppoggio"] == '-')].groupby("NumeroMaglia_ID", "GameID").size()
        AttackThisSetEvalDoppioPositivo = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["ThisSetEval"] == '#')].groupby("NumeroMaglia_ID", "GameID").size()
        AttackThisSetEvalPositivo = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["ThisSetEval"] == '+')].groupby("NumeroMaglia_ID", "GameID").size()
        AttackPlayersInfo0 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["PlayersInfo"] == 0)].groupby("NumeroMaglia_ID", "GameID").size()
        AttackPlayersInfo1 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["PlayersInfo"] == 1)].groupby("NumeroMaglia_ID", "GameID").size()
        AttackPlayersInfo2 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["PlayersInfo"] == 2)].groupby("NumeroMaglia_ID", "GameID").size()
        AttackPlayersInfo3 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["PlayersInfo"] == 3)].groupby("NumeroMaglia_ID", "GameID").size()
        AttackPlayersInfo4 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["PlayersInfo"] == 4)].groupby("NumeroMaglia_ID", "GameID").size()
        BlockTypeT = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["Type"] == "T")].groupby("NumeroMaglia_ID", "GameID").size()
        BlockTypeQ = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["Type"] == "Q")].groupby("NumeroMaglia_ID", "GameID").size()
        BlockTypeM = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["Type"] == "M")].groupby("NumeroMaglia_ID", "GameID").size()
        BlockTypeH = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["Type"] == "H")].groupby("NumeroMaglia_ID", "GameID").size()
        BlockTypeO = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["Type"] == "O")].groupby("NumeroMaglia_ID", "GameID").size()
        BlockTypeNonH = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["Type"] != "H")].groupby("NumeroMaglia_ID", "GameID").size()
        BlockEsecZone4 = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["EndZoneEsecZone"] == 4)].groupby("NumeroMaglia_ID", "GameID").size()
        BlockEsecZone3 = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["EndZoneEsecZone"] == 3)].groupby("NumeroMaglia_ID", "GameID").size()
        BlockEsecZone2 = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["EndZoneEsecZone"] == 2)].groupby("NumeroMaglia_ID", "GameID").size()
        BlockPlayersInfo1 = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["PlayersInfo"] == 1)].groupby("NumeroMaglia_ID", "GameID").size()
        BlockPlayersInfo2 = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["PlayersInfo"] == 2)].groupby("NumeroMaglia_ID", "GameID").size()
        BlockPlayersInfo3 = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["PlayersInfo"] == 3)].groupby("NumeroMaglia_ID", "GameID").size()
        BlockPlayersInfo4 = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["PlayersInfo"] == 4)].groupby("NumeroMaglia_ID", "GameID").size()
        BlockPrevAppoggioPositivo = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["PrevAppoggio"] == '+')].groupby("NumeroMaglia_ID", "GameID").size()
        BlockPrevAppoggioEsclamativo = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["PrevAppoggio"] == '!')].groupby("NumeroMaglia_ID", "GameID").size()
        BlockPrevAppoggioNegativo = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["PrevAppoggio"] == '-')].groupby("NumeroMaglia_ID", "GameID").size()
        DefenseCorrectSkillTypeS = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["correctSkillType"] == "S")].groupby("NumeroMaglia_ID", "GameID").size()
        DefenseCorrectSkillTypeB = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["correctSkillType"] == "B")].groupby("NumeroMaglia_ID", "GameID").size()
        DefenseCorrectSkillTypeC = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["correctSkillType"] == "C")].groupby("NumeroMaglia_ID", "GameID").size()
        DefenseStartZoneC_2 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["StartZoneCompact"] == 2)].groupby("NumeroMaglia_ID", "GameID").size()
        DefenseStartZoneC_3 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["StartZoneCompact"] == 3)].groupby("NumeroMaglia_ID", "GameID").size()
        DefenseStartZoneC_4 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["StartZoneCompact"] == 4)].groupby("NumeroMaglia_ID", "GameID").size()
        DefenseStartZoneC_1 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["StartZoneCompact"] == 1)].groupby("NumeroMaglia_ID", "GameID").size()
        DefenseStartZoneC_6 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["StartZoneCompact"] == 6)].groupby("NumeroMaglia_ID", "GameID").size() 
        DefenseEsecZone_1 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 1)].groupby("NumeroMaglia_ID", "GameID").size()  
        DefenseEsecZone_2 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 2)].groupby("NumeroMaglia_ID", "GameID").size()  
        DefenseEsecZone_3 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 3)].groupby("NumeroMaglia_ID", "GameID").size()  
        DefenseEsecZone_4 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 4)].groupby("NumeroMaglia_ID", "GameID").size()  
        DefenseEsecZone_5 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 5)].groupby("NumeroMaglia_ID", "GameID").size()  
        DefenseEsecZone_6 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 6)].groupby("NumeroMaglia_ID", "GameID").size()  
        DefenseEsecZone_7 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 7)].groupby("NumeroMaglia_ID", "GameID").size()  
        DefenseEsecZone_8 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 8)].groupby("NumeroMaglia_ID", "GameID").size()  
        DefenseEsecZone_9 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 9)].groupby("NumeroMaglia_ID", "GameID").size() 
        
        
        # Dizionario: chiave = nome colonna finale, valore = dizionario dei conteggi
        #Creo counts_dict2 che ha come chiave "Total_{variabile}" e come valore {variabile}, per ogni variabile in lista_tutte_le_variabili_voto
        counts_dict = {
            'Serve': Serve,
            'Reception': Reception,
            'Set': Set,
            'Attack': Attack,
            'Block': Block,
            'Defense': Defense,
            'Freeball': Freeball,
            'ServeTypeQ': ServeTypeQ,
            'ServeTypeM': ServeTypeM,
            'ServeStartZoneC_5': ServeStartZoneC_5,
            'ServeStartZoneC_6': ServeStartZoneC_6,
            'ServeStartZoneC_1': ServeStartZoneC_1,
            'ServeEndZone3_5': ServeEndZone3_5,
            'ServeEndZone3_6': ServeEndZone3_6,
            'ServeEndZone3_1': ServeEndZone3_1,
            'ServeCCCA_R': ServeCCCA_R,
            'ServeCCCA_T': ServeCCCA_T,
            'ServeCCCA_C': ServeCCCA_C,
            'ReceptionTypeQ': ReceptionTypeQ,
            'ReceptionTypeM': ReceptionTypeM,
            'ReceptionStartZoneC_5': ReceptionStartZoneC_5,
            'ReceptionStartZoneC_6': ReceptionStartZoneC_6,
            'ReceptionStartZoneC_1': ReceptionStartZoneC_1,
            'ReceptionEndZone3_5': ReceptionEndZone3_5,
            'ReceptionEndZone3_6': ReceptionEndZone3_6,
            'ReceptionEndZone3_1': ReceptionEndZone3_1,
            'ReceptionServeCCCA_R': ReceptionServeCCCA_R,
            'ReceptionServeCCCA_T': ReceptionServeCCCA_T,
            'ReceptionServeCCCA_C': ReceptionServeCCCA_C,
            'SetTypeT': SetTypeT,
            'SetTypeQ': SetTypeQ,
            'SetTypeM': SetTypeM,
            'SetTypeH': SetTypeH,
            'SetTypeO': SetTypeO,
            'SetTypeNonH': SetTypeNonH,
            'SetEsecZone3_5': SetEsecZone3_5,
            'SetEsecZone3_6': SetEsecZone3_6,
            'SetEsecZone3_1': SetEsecZone3_1,
            'SetThisAppoggioPositivo': SetThisAppoggioPositivo,
            'SetThisAppoggioEsclamativo': SetThisAppoggioEsclamativo,
            'SetThisAppoggioNegativo': SetThisAppoggioNegativo,
            'AttackTypeT': AttackTypeT,
            'AttackTypeQ': AttackTypeQ,
            'AttackTypeM': AttackTypeM,
            'AttackTypeH': AttackTypeH,
            'AttackTypeO': AttackTypeO,
            'AttackTypeNonH': AttackTypeNonH,
            'AttackStartZoneC_4': AttackStartZoneC_4,
            'AttackStartZoneC_3': AttackStartZoneC_3,  
            'AttackStartZoneC_2': AttackStartZoneC_2,
            'AttackStartZoneC_6': AttackStartZoneC_6,  
            'AttackStartZoneC_1': AttackStartZoneC_1,
            'AttackThisAppoggioPositivo': AttackThisAppoggioPositivo,  
            'AttackThisAppoggioEsclamativo': AttackThisAppoggioEsclamativo,  
            'AttackThisAppoggioNegativo': AttackThisAppoggioNegativo,  
            'AttackThisSetEvalDoppioPositivo': AttackThisSetEvalDoppioPositivo,  
            'AttackThisSetEvalPositivo': AttackThisSetEvalPositivo,  
            'AttackPlayersInfo0': AttackPlayersInfo0,  
            'AttackPlayersInfo1': AttackPlayersInfo1,  
            'AttackPlayersInfo2': AttackPlayersInfo2,  
            'AttackPlayersInfo3': AttackPlayersInfo3,  
            'AttackPlayersInfo4': AttackPlayersInfo4,  
            'BlockTypeT': BlockTypeT,
            'BlockTypeQ': BlockTypeQ,
            'BlockTypeM': BlockTypeM,
            'BlockTypeH': BlockTypeH,
            'BlockTypeO': BlockTypeO,
            'BlockTypeNonH': BlockTypeNonH,
            'BlockEsecZone_4': BlockEsecZone4,  
            'BlockEsecZone_3': BlockEsecZone3,  
            'BlockEsecZone_2': BlockEsecZone2, 
            'BlockPlayersInfo_1': BlockPlayersInfo1,  
            'BlockPlayersInfo_2': BlockPlayersInfo2,  
            'BlockPlayersInfo_3': BlockPlayersInfo3,  
            'BlockPlayersInfo_4': BlockPlayersInfo4,  
            'BlockPrevAppoggioPositivo': BlockPrevAppoggioPositivo,
            'BlockPrevAppoggioEsclamativo': BlockPrevAppoggioEsclamativo,
            'BlockPrevAppoggioNegativo': BlockPrevAppoggioNegativo,
            'DefenseCorrectSkillTypeS': DefenseCorrectSkillTypeS,
            'DefenseCorrectSkillTypeB': DefenseCorrectSkillTypeB,
            'DefenseCorrectSkillTypeC': DefenseCorrectSkillTypeC,
            'DefenseStartZoneC_2': DefenseStartZoneC_2,
            'DefenseStartZoneC_3': DefenseStartZoneC_3,
            'DefenseStartZoneC_4': DefenseStartZoneC_4,
            'DefenseStartZoneC_6': DefenseStartZoneC_6,
            'DefenseStartZoneC_1': DefenseStartZoneC_1,
            'DefenseEsecZone_1': DefenseEsecZone_1,
            'DefenseEsecZone_2': DefenseEsecZone_2,
            'DefenseEsecZone_3': DefenseEsecZone_3,
            'DefenseEsecZone_4': DefenseEsecZone_4,
            'DefenseEsecZone_5': DefenseEsecZone_5,
            'DefenseEsecZone_6': DefenseEsecZone_6,
            'DefenseEsecZone_7': DefenseEsecZone_7,
            'DefenseEsecZone_8': DefenseEsecZone_8,
            'DefenseEsecZone_9': DefenseEsecZone_9
        }
        
        
        #Aggiungo le colonne Total_{variabile} che contano il numero di azioni di ciascun tipo che ha fatto ogni giocatore. Ovvero conta il numero di righe in df_rilevations in cui NumeroMaglia_ID = PlayerID e Foundamental = 'S', 'R', 'E', 'A', 'B', 'D', 'F' ecc
        for col, counts in counts_dict.items():
            df_players[col] = df_players["PlayerID", "GameID"].map(counts).fillna(0).astype(int)



    return df_players



#Aggiungo a df_players la colonna TotalTouch, che per ogni giocatore indica il numero di tocchi che ha fatto da sempre. 
# Se lo calcolo per df_players conta il numero di righe in df_rilevations in cui NumeroMaglia_ID = PlayerID
# Se lo calcolo per df_players_each_game conta il numero di righe in df_rilevations in cui NumeroMaglia_ID = PlayerID e GameID = GameID


#Qui posso tranquillamente aggiungere tutte la variabili che voglio, non solo i 7 fondamentali
def calcola_totale_tocchi(df_rilevations, df_players, per_players=True):
    if per_players:
        raggruppamento = ["NumeroMaglia_ID"]
    else:
        raggruppamento = ["NumeroMaglia_ID", "GameID"]

    Touch = df_rilevations.groupby(raggruppamento).size()
    Serve = df_rilevations[df_rilevations["Foundamental"] == "S"].groupby(raggruppamento).size()
    Reception = df_rilevations[df_rilevations["Foundamental"] == "R"].groupby(raggruppamento).size()
    Set = df_rilevations[df_rilevations["Foundamental"] == "E"].groupby(raggruppamento).size()
    Attack = df_rilevations[df_rilevations["Foundamental"] == "A"].groupby(raggruppamento).size()
    Block = df_rilevations[df_rilevations["Foundamental"] == "B"].groupby(raggruppamento).size()
    Defense = df_rilevations[df_rilevations["Foundamental"] == "D"].groupby(raggruppamento).size()
    Freeball = df_rilevations[df_rilevations["Foundamental"] == "F"].groupby(raggruppamento).size()
    ServeTypeQ = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["Type"] == "Q")].groupby(raggruppamento).size()
    ServeTypeM = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["Type"] == "M")].groupby(raggruppamento).size()
    ServeStartZoneC_5 = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["StartZoneCompact"] == 5)].groupby(raggruppamento).size()
    ServeStartZoneC_6 = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["StartZoneCompact"] == 6)].groupby(raggruppamento).size()
    ServeStartZoneC_1 = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["StartZoneCompact"] == 1)].groupby(raggruppamento).size()
    ServeEndZone3_5 = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["EndZoneEsecZone3aree"] == 5)].groupby(raggruppamento).size()
    ServeEndZone3_6 = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["EndZoneEsecZone3aree"] == 6)].groupby(raggruppamento).size()
    ServeEndZone3_1 = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["EndZoneEsecZone3aree"] == 1)].groupby(raggruppamento).size()
    ServeCCCA_R = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["correctCustomCharAggregate"] == "R")].groupby(raggruppamento).size()
    ServeCCCA_T = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["correctCustomCharAggregate"] == "T")].groupby(raggruppamento).size()
    ServeCCCA_C = df_rilevations[(df_rilevations["Foundamental"] == "S") & (df_rilevations["correctCustomCharAggregate"] == "C")].groupby(raggruppamento).size()
    ReceptionTypeQ = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["Type"] == "Q")].groupby(raggruppamento).size()
    ReceptionTypeM = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["Type"] == "M")].groupby(raggruppamento).size()
    ReceptionStartZoneC_5 = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["StartZoneCompact"] == 5)].groupby(raggruppamento).size()
    ReceptionStartZoneC_6 = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["StartZoneCompact"] == 6)].groupby(raggruppamento).size()
    ReceptionStartZoneC_1 = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["StartZoneCompact"] == 1)].groupby(raggruppamento).size()
    ReceptionEndZone3_5 = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["EndZoneEsecZone3aree"] == 5)].groupby(raggruppamento).size()
    ReceptionEndZone3_6 = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["EndZoneEsecZone3aree"] == 6)].groupby(raggruppamento).size()
    ReceptionEndZone3_1 = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["EndZoneEsecZone3aree"] == 1)].groupby(raggruppamento).size()
    ReceptionServeCCCA_R = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["PrevServeCorrectCustomCharAggregate"] == "R")].groupby(raggruppamento).size()
    ReceptionServeCCCA_T = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["PrevServeCorrectCustomCharAggregate"] == "T")].groupby(raggruppamento).size()
    ReceptionServeCCCA_C = df_rilevations[(df_rilevations["Foundamental"] == "R") & (df_rilevations["PrevServeCorrectCustomCharAggregate"] == "C")].groupby(raggruppamento).size()
    SetTypeT = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["Type"] == "T")].groupby(raggruppamento).size()
    SetTypeQ = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["Type"] == "Q")].groupby(raggruppamento).size()
    SetTypeM = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["Type"] == "M")].groupby(raggruppamento).size()
    SetTypeH = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["Type"] == "H")].groupby(raggruppamento).size()
    SetTypeO = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["Type"] == "O")].groupby(raggruppamento).size()
    SetTypeNonH = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["Type"] != "H")].groupby(raggruppamento).size()
    SetEsecZone3_5 = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["EndZoneEsecZone3aree"] == 5)].groupby(raggruppamento).size()
    SetEsecZone3_6 = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["EndZoneEsecZone3aree"] == 6)].groupby(raggruppamento).size()
    SetEsecZone3_1 = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["EndZoneEsecZone3aree"] == 1)].groupby(raggruppamento).size()
    SetThisAppoggioPositivo = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["ThisAppoggio"] == '+')].groupby(raggruppamento).size()
    SetThisAppoggioEsclamativo = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["ThisAppoggio"] == '!')].groupby(raggruppamento).size()
    SetThisAppoggioNegativo = df_rilevations[(df_rilevations["Foundamental"] == "E") & (df_rilevations["ThisAppoggio"] == '-')].groupby(raggruppamento).size()
    AttackTypeT = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["Type"] == "T")].groupby(raggruppamento).size()
    AttackTypeQ = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["Type"] == "Q")].groupby(raggruppamento).size()
    AttackTypeM = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["Type"] == "M")].groupby(raggruppamento).size()
    AttackTypeH = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["Type"] == "H")].groupby(raggruppamento).size()
    AttackTypeO = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["Type"] == "O")].groupby(raggruppamento).size()
    AttackTypeNonH = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["Type"] != "H")].groupby(raggruppamento).size()
    AttackStartZoneC_4 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["StartZoneCompact"] == 4)].groupby(raggruppamento).size()
    AttackStartZoneC_3 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["StartZoneCompact"] == 3)].groupby(raggruppamento).size()
    AttackStartZoneC_2 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["StartZoneCompact"] == 2)].groupby(raggruppamento).size()
    AttackStartZoneC_1 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["StartZoneCompact"] == 1)].groupby(raggruppamento).size()
    AttackStartZoneC_6 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["StartZoneCompact"] == 6)].groupby(raggruppamento).size()
    AttackThisAppoggioPositivo = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["ThisAppoggio"] == '+')].groupby(raggruppamento).size()
    AttackThisAppoggioEsclamativo = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["ThisAppoggio"] == '!')].groupby(raggruppamento).size()
    AttackThisAppoggioNegativo = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["ThisAppoggio"] == '-')].groupby(raggruppamento).size()
    AttackThisSetEvalDoppioPositivo = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["ThisSetEval"] == '#')].groupby(raggruppamento).size()
    AttackThisSetEvalPositivo = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["ThisSetEval"] == '+')].groupby(raggruppamento).size()
    AttackPlayersInfo0 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["PlayersInfo"] == 0)].groupby(raggruppamento).size()
    AttackPlayersInfo1 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["PlayersInfo"] == 1)].groupby(raggruppamento).size()
    AttackPlayersInfo2 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["PlayersInfo"] == 2)].groupby(raggruppamento).size()
    AttackPlayersInfo3 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["PlayersInfo"] == 3)].groupby(raggruppamento).size()
    AttackPlayersInfo4 = df_rilevations[(df_rilevations["Foundamental"] == "A") & (df_rilevations["PlayersInfo"] == 4)].groupby(raggruppamento).size()
    BlockTypeT = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["Type"] == "T")].groupby(raggruppamento).size()
    BlockTypeQ = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["Type"] == "Q")].groupby(raggruppamento).size()
    BlockTypeM = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["Type"] == "M")].groupby(raggruppamento).size()
    BlockTypeH = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["Type"] == "H")].groupby(raggruppamento).size()
    BlockTypeO = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["Type"] == "O")].groupby(raggruppamento).size()
    BlockTypeNonH = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["Type"] != "H")].groupby(raggruppamento).size()
    BlockEsecZone4 = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["EndZoneEsecZone"] == 4)].groupby(raggruppamento).size()
    BlockEsecZone3 = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["EndZoneEsecZone"] == 3)].groupby(raggruppamento).size()
    BlockEsecZone2 = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["EndZoneEsecZone"] == 2)].groupby(raggruppamento).size()
    BlockPlayersInfo1 = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["PlayersInfo"] == 1)].groupby(raggruppamento).size()
    BlockPlayersInfo2 = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["PlayersInfo"] == 2)].groupby(raggruppamento).size()
    BlockPlayersInfo3 = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["PlayersInfo"] == 3)].groupby(raggruppamento).size()
    BlockPlayersInfo4 = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["PlayersInfo"] == 4)].groupby(raggruppamento).size()
    BlockPrevAppoggioPositivo = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["PrevAppoggio"] == '+')].groupby(raggruppamento).size()
    BlockPrevAppoggioEsclamativo = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["PrevAppoggio"] == '!')].groupby(raggruppamento).size()
    BlockPrevAppoggioNegativo = df_rilevations[(df_rilevations["Foundamental"] == "B") & (df_rilevations["PrevAppoggio"] == '-')].groupby(raggruppamento).size()
    DefenseCorrectSkillTypeS = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["correctSkillType"] == "S")].groupby(raggruppamento).size()
    DefenseCorrectSkillTypeB = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["correctSkillType"] == "B")].groupby(raggruppamento).size()
    DefenseCorrectSkillTypeC = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["correctSkillType"] == "C")].groupby(raggruppamento).size()
    DefenseStartZoneC_2 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["StartZoneCompact"] == 2)].groupby(raggruppamento).size()
    DefenseStartZoneC_3 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["StartZoneCompact"] == 3)].groupby(raggruppamento).size()
    DefenseStartZoneC_4 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["StartZoneCompact"] == 4)].groupby(raggruppamento).size()
    DefenseStartZoneC_1 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["StartZoneCompact"] == 1)].groupby(raggruppamento).size()
    DefenseStartZoneC_6 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["StartZoneCompact"] == 6)].groupby(raggruppamento).size() 
    DefenseEsecZone_1 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 1)].groupby(raggruppamento).size()  
    DefenseEsecZone_2 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 2)].groupby(raggruppamento).size()  
    DefenseEsecZone_3 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 3)].groupby(raggruppamento).size()  
    DefenseEsecZone_4 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 4)].groupby(raggruppamento).size()  
    DefenseEsecZone_5 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 5)].groupby(raggruppamento).size()  
    DefenseEsecZone_6 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 6)].groupby(raggruppamento).size()  
    DefenseEsecZone_7 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 7)].groupby(raggruppamento).size()  
    DefenseEsecZone_8 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 8)].groupby(raggruppamento).size()  
    DefenseEsecZone_9 = df_rilevations[(df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 9)].groupby(raggruppamento).size() 
        

    # Dizionario: chiave = nome colonna finale, valore = dizionario dei conteggi
    #Creo counts_dict2 che ha come chiave "Total_{variabile}" e come valore {variabile}, per ogni variabile in lista_tutte_le_variabili_voto
    counts_dict = {
        'Serve': Serve,
        'Reception': Reception,
        'Set': Set,
        'Attack': Attack,
        'Block': Block,
        'Defense': Defense,
        'Freeball': Freeball,
        'ServeTypeQ': ServeTypeQ,
        'ServeTypeM': ServeTypeM,
        'ServeStartZoneC_5': ServeStartZoneC_5,
        'ServeStartZoneC_6': ServeStartZoneC_6,
        'ServeStartZoneC_1': ServeStartZoneC_1,
        'ServeEndZone3_5': ServeEndZone3_5,
        'ServeEndZone3_6': ServeEndZone3_6,
        'ServeEndZone3_1': ServeEndZone3_1,
        'ServeCCCA_R': ServeCCCA_R,
        'ServeCCCA_T': ServeCCCA_T,
        'ServeCCCA_C': ServeCCCA_C,
        'ReceptionTypeQ': ReceptionTypeQ,
        'ReceptionTypeM': ReceptionTypeM,
        'ReceptionStartZoneC_5': ReceptionStartZoneC_5,
        'ReceptionStartZoneC_6': ReceptionStartZoneC_6,
        'ReceptionStartZoneC_1': ReceptionStartZoneC_1,
        'ReceptionEndZone3_5': ReceptionEndZone3_5,
        'ReceptionEndZone3_6': ReceptionEndZone3_6,
        'ReceptionEndZone3_1': ReceptionEndZone3_1,
        'ReceptionServeCCCA_R': ReceptionServeCCCA_R,
        'ReceptionServeCCCA_T': ReceptionServeCCCA_T,
        'ReceptionServeCCCA_C': ReceptionServeCCCA_C,
        'SetTypeT': SetTypeT,
        'SetTypeQ': SetTypeQ,
        'SetTypeM': SetTypeM,
        'SetTypeH': SetTypeH,
        'SetTypeO': SetTypeO,
        'SetTypeNonH': SetTypeNonH,
        'SetEsecZone3_5': SetEsecZone3_5,
        'SetEsecZone3_6': SetEsecZone3_6,
        'SetEsecZone3_1': SetEsecZone3_1,
        'SetThisAppoggioPositivo': SetThisAppoggioPositivo,
        'SetThisAppoggioEsclamativo': SetThisAppoggioEsclamativo,
        'SetThisAppoggioNegativo': SetThisAppoggioNegativo,
        'AttackTypeT': AttackTypeT,
        'AttackTypeQ': AttackTypeQ,
        'AttackTypeM': AttackTypeM,
        'AttackTypeH': AttackTypeH,
        'AttackTypeO': AttackTypeO,
        'AttackTypeNonH': AttackTypeNonH,
        'AttackStartZoneC_4': AttackStartZoneC_4,
        'AttackStartZoneC_3': AttackStartZoneC_3,  
        'AttackStartZoneC_2': AttackStartZoneC_2,
        'AttackStartZoneC_6': AttackStartZoneC_6,  
        'AttackStartZoneC_1': AttackStartZoneC_1,
        'AttackThisAppoggioPositivo': AttackThisAppoggioPositivo,  
        'AttackThisAppoggioEsclamativo': AttackThisAppoggioEsclamativo,  
        'AttackThisAppoggioNegativo': AttackThisAppoggioNegativo,  
        'AttackThisSetEvalDoppioPositivo': AttackThisSetEvalDoppioPositivo,  
        'AttackThisSetEvalPositivo': AttackThisSetEvalPositivo,  
        'AttackPlayersInfo0': AttackPlayersInfo0,  
        'AttackPlayersInfo1': AttackPlayersInfo1,  
        'AttackPlayersInfo2': AttackPlayersInfo2,  
        'AttackPlayersInfo3': AttackPlayersInfo3,  
        'AttackPlayersInfo4': AttackPlayersInfo4,  
        'BlockTypeT': BlockTypeT,
        'BlockTypeQ': BlockTypeQ,
        'BlockTypeM': BlockTypeM,
        'BlockTypeH': BlockTypeH,
        'BlockTypeO': BlockTypeO,
        'BlockTypeNonH': BlockTypeNonH,
        'BlockEsecZone_4': BlockEsecZone4,  
        'BlockEsecZone_3': BlockEsecZone3,  
        'BlockEsecZone_2': BlockEsecZone2, 
        'BlockPlayersInfo_1': BlockPlayersInfo1,  
        'BlockPlayersInfo_2': BlockPlayersInfo2,  
        'BlockPlayersInfo_3': BlockPlayersInfo3,  
        'BlockPlayersInfo_4': BlockPlayersInfo4,  
        'BlockPrevAppoggioPositivo': BlockPrevAppoggioPositivo,
        'BlockPrevAppoggioEsclamativo': BlockPrevAppoggioEsclamativo,
        'BlockPrevAppoggioNegativo': BlockPrevAppoggioNegativo,
        'DefenseCorrectSkillTypeS': DefenseCorrectSkillTypeS,
        'DefenseCorrectSkillTypeB': DefenseCorrectSkillTypeB,
        'DefenseCorrectSkillTypeC': DefenseCorrectSkillTypeC,
        'DefenseStartZoneC_2': DefenseStartZoneC_2,
        'DefenseStartZoneC_3': DefenseStartZoneC_3,
        'DefenseStartZoneC_4': DefenseStartZoneC_4,
        'DefenseStartZoneC_6': DefenseStartZoneC_6,
        'DefenseStartZoneC_1': DefenseStartZoneC_1,
        'DefenseEsecZone_1': DefenseEsecZone_1,
        'DefenseEsecZone_2': DefenseEsecZone_2,
        'DefenseEsecZone_3': DefenseEsecZone_3,
        'DefenseEsecZone_4': DefenseEsecZone_4,
        'DefenseEsecZone_5': DefenseEsecZone_5,
        'DefenseEsecZone_6': DefenseEsecZone_6,
        'DefenseEsecZone_7': DefenseEsecZone_7,
        'DefenseEsecZone_8': DefenseEsecZone_8,
        'DefenseEsecZone_9': DefenseEsecZone_9
    }
        

    if per_players:    # Se per_players=True, conta i tocchi totali per ogni NumeroMaglia_ID. Per ognuno di questi conto i tocchi
        #Aggiungo le colonne Total_{variabile} che contano il numero di azioni di ciascun tipo che ha fatto ogni giocatore. Ovvero conta il numero di righe in df_rilevations in cui NumeroMaglia_ID = PlayerID e Foundamental = 'S', 'R', 'E', 'A', 'B', 'D', 'F' ecc
        for col, counts in counts_dict.items():
            df_players[f"Total_{col}"] = df_players["PlayerID"].map(counts).fillna(0).astype(int)
    else:
        #Aggiungo le colonne Total_{variabile} che contano il numero di azioni di ciascun tipo che ha fatto ogni giocatore. Ovvero conta il numero di righe in df_rilevations in cui NumeroMaglia_ID = PlayerID e Foundamental = 'S', 'R', 'E', 'A', 'B', 'D', 'F' ecc
        for col, counts in counts_dict.items():
            df_players[f"Total_{col}"] = df_players["PlayerID", "GameID"].map(counts).fillna(0).astype(int)


    return df_players



df_players

df_players1 = calcola_totale_tocchi(df_rilevations, df_players, per_players=True)
df_players1

#Preparo l'oggetto per standardizzare i dati
scaler = StandardScaler(with_std=True , with_mean=True)    ##Per medie e deviazioni standard






dizionario_variabili_Generale = {
    'Serve': df_rilevations['Foundamental'] == 'S',
    'Reception': df_rilevations['Foundamental'] == 'R',
    'Set': df_rilevations['Foundamental'] == 'E',
    'Attack': df_rilevations['Foundamental'] == 'A',
    'Block': df_rilevations['Foundamental'] == 'B',
    'Defense': df_rilevations['Foundamental'] == 'D',
    'FreeBall': df_rilevations['Foundamental'] == 'F',
    'ServeTypeQ': (df_rilevations['Foundamental'] == 'S') & (df_rilevations['Type'] == 'Q'),
    'ServeTypeM': df_rilevations['Foundamental'] == 'S' & df_rilevations['Type'] == 'M',
    'ServeStartZoneC_5': df_rilevations['Foundamental'] == 'S' & df_rilevations['StartZoneCompact'] == 5,
    'ServeStartZoneC_6': df_rilevations['Foundamental'] == 'S' & df_rilevations['StartZoneCompact'] == 6,
    'ServeStartZoneC_1': df_rilevations['Foundamental'] == 'S' & df_rilevations['StartZoneCompact'] == 1,
    'ServeEndZone3_5': df_rilevations['Foundamental'] == 'S' & df_rilevations['EndZoneEsecZone3aree'] == 5,
    'ServeEndZone3_6': df_rilevations['Foundamental'] == 'S' & df_rilevations['EndZoneEsecZone3aree'] == 6,
    'ServeEndZone3_1': df_rilevations['Foundamental'] == 'S' & df_rilevations['EndZoneEsecZone3aree'] == 1,
    'ServeCCCA_R': df_rilevations['Foundamental'] == 'S' & df_rilevations['correctCustomCharAggregate'] == 'R',
    'ServeCCCA_T': df_rilevations['Foundamental'] == 'S' & df_rilevations['correctCustomCharAggregate'] == 'T',
    'ServeCCCA_C': df_rilevations['Foundamental'] == 'S' & df_rilevations['correctCustomCharAggregate'] == 'C',
    'ReceptionTypeQ': df_rilevations['Foundamental'] == 'R' & df_rilevations['Type'] == 'Q',
    'ReceptionTypeM': df_rilevations['Foundamental'] == 'R' & df_rilevations['Type'] == 'M',
    'ReceptionStartZoneC_5': df_rilevations['Foundamental'] == 'R' & df_rilevations['StartZoneCompact'] == 5,
    'ReceptionStartZoneC_6': df_rilevations['Foundamental'] == 'R' & df_rilevations['StartZoneCompact'] == 6,
    'ReceptionStartZoneC_1': df_rilevations['Foundamental'] == 'R' & df_rilevations['StartZoneCompact'] == 1,
    'ReceptionEndZone3_5': df_rilevations['Foundamental'] == 'R' & df_rilevations['EndZoneEsecZone3aree'] == 5,
    'ReceptionEndZone3_6': df_rilevations['Foundamental'] == 'R' & df_rilevations['EndZoneEsecZone3aree'] == 6,
    'ReceptionEndZone3_1': df_rilevations['Foundamental'] == 'R' & df_rilevations['EndZoneEsecZone3aree'] == 1,
    'ReceptionServeCCCA_R': df_rilevations['Foundamental'] == 'R' & df_rilevations['PrevServeCorrectCustomCharAggregate'] == 'R',
    'ReceptionServeCCCA_T': df_rilevations['Foundamental'] == 'R' & df_rilevations['PrevServeCorrectCustomCharAggregate'] == 'T',
    'ReceptionServeCCCA_C': df_rilevations['Foundamental'] == 'R' & df_rilevations['PrevServeCorrectCustomCharAggregate'] == 'C',
    'SetTypeT': df_rilevations['Foundamental'] == 'E' & df_rilevations['Type'] == 'T',
    'SetTypeQ': df_rilevations['Foundamental'] == 'E' & df_rilevations['Type'] == 'Q',
    'SetTypeM': df_rilevations['Foundamental'] == 'E' & df_rilevations['Type'] == 'M',
    'SetTypeH': df_rilevations['Foundamental'] == 'E' & df_rilevations['Type'] == 'H',
    'SetTypeO': df_rilevations['Foundamental'] == 'E' & df_rilevations['Type'] == 'O',
    'SetTypeNonH': df_rilevations['Foundamental'] == 'E' & df_rilevations['Type'] != 'H',
    'SetEsecZone3_5': df_rilevations['Foundamental'] == 'E' & df_rilevations['EndZoneEsecZone3aree'] == 5,
    'SetEsecZone3_6': df_rilevations['Foundamental'] == 'E' & df_rilevations['EndZoneEsecZone3aree'] == 6,
    'SetEsecZone3_1': df_rilevations['Foundamental'] == 'E' & df_rilevations['EndZoneEsecZone3aree'] == 1,
    'SetThisAppoggioPositivo': df_rilevations['Foundamental'] == 'E' & df_rilevations['ThisAppoggio'] == '+',
    'SetThisAppoggioEsclamativo': df_rilevations['Foundamental'] == 'E' & df_rilevations['ThisAppoggio'] == '!',
    'SetThisAppoggioNegativo': df_rilevations['Foundamental'] == 'E' & df_rilevations['ThisAppoggio'] == '-',
    'AttackTypeT': df_rilevations['Foundamental'] == 'A' & df_rilevations['Type'] == 'T',
    'AttackTypeQ': df_rilevations['Foundamental'] == 'A' & df_rilevations['Type'] == 'Q',
    'AttackTypeM': df_rilevations['Foundamental'] == 'A' & df_rilevations['Type'] == 'M',
    'AttackTypeH': df_rilevations['Foundamental'] == 'A' & df_rilevations['Type'] == 'H',
    'AttackTypeO': df_rilevations['Foundamental'] == 'A' & df_rilevations['Type'] == 'O',
    'AttackTypeNonH': df_rilevations['Foundamental'] == 'A' & df_rilevations['Type'] != 'H',
    'AttackStartZoneC_4': df_rilevations['Foundamental'] == 'A' & df_rilevations['StartZoneCompact'] == 4,
    'AttackStartZoneC_3': df_rilevations['Foundamental'] == 'A' & df_rilevations['StartZoneCompact'] == 3,
    'AttackStartZoneC_2': df_rilevations['Foundamental'] == 'A' & df_rilevations['StartZoneCompact'] == 2,
    'AttackStartZoneC_6': df_rilevations['Foundamental'] == 'A' & df_rilevations['StartZoneCompact'] == 6,
    'AttackStartZoneC_1': df_rilevations['Foundamental'] == 'A' & df_rilevations['StartZoneCompact'] == 1,
    'AttackThisAppoggioPositivo': df_rilevations['Foundamental'] == 'A' & df_rilevations['ThisAppoggio'] == '+',
    'AttackThisAppoggioEsclamativo': df_rilevations['Foundamental'] == 'A' & df_rilevations['ThisAppoggio'] == '!',
    'AttackThisAppoggioNegativo': df_rilevations['Foundamental'] == 'A' & df_rilevations['ThisAppoggio'] == '-',
    'AttackThisSetEvalDoppioPositivo': df_rilevations['Foundamental'] == 'A' & df_rilevations['ThisSetEval'] == '#',
    'AttackThisSetEvalPositivo': df_rilevations['Foundamental'] == 'A' & df_rilevations['ThisSetEval'] == '+',
    'AttackPlayersInfo0': df_rilevations['Foundamental'] == 'A' & df_rilevations['PlayersInfo'] == 0,
    'AttackPlayersInfo1': df_rilevations['Foundamental'] == 'A' & df_rilevations['PlayersInfo'] == 1,
    'AttackPlayersInfo2': df_rilevations['Foundamental'] == 'A' & df_rilevations['PlayersInfo'] == 2,
    'AttackPlayersInfo3': df_rilevations['Foundamental'] == 'A' & df_rilevations['PlayersInfo'] == 3,
    'AttackPlayersInfo4': df_rilevations['Foundamental'] == 'A' & df_rilevations['PlayersInfo'] == 4,
    'BlockTypeT': df_rilevations['Foundamental'] == 'B' & df_rilevations['Type'] == 'T',
    'BlockTypeQ': df_rilevations['Foundamental'] == 'B' & df_rilevations['Type'] == 'Q',
    'BlockTypeM': df_rilevations['Foundamental'] == 'B' & df_rilevations['Type'] == 'M',
    'BlockTypeH': df_rilevations['Foundamental'] == 'B' & df_rilevations['Type'] == 'H',
    'BlockTypeO': df_rilevations['Foundamental'] == 'B' & df_rilevations['Type'] == 'O',
    'BlockTypeNonH': df_rilevations['Foundamental'] == 'B' & df_rilevations['Type'] != 'H',
    'BlockEsecZone4': df_rilevations['Foundamental'] == 'B' & df_rilevations['EndZoneEsecZone'] == 4,
    'BlockEsecZone3': df_rilevations['Foundamental'] == 'B' & df_rilevations['EndZoneEsecZone'] == 3,
    'BlockEsecZone2': df_rilevations['Foundamental'] == 'B' & df_rilevations['EndZoneEsecZone'] == 2,
    'BlockPlayersInfo1': df_rilevations['Foundamental'] == 'B' & df_rilevations['PlayersInfo'] == 1,
    'BlockPlayersInfo2': df_rilevations['Foundamental'] == 'B' & df_rilevations['PlayersInfo'] == 2,
    'BlockPlayersInfo3': df_rilevations['Foundamental'] == 'B' & df_rilevations['PlayersInfo'] == 3,
    'BlockPlayersInfo4': df_rilevations['Foundamental'] == 'B' & df_rilevations['PlayersInfo'] == 4,
    'BlockPrevAppoggioPositivo': df_rilevations['Foundamental'] == 'B' & df_rilevations['PrevAppoggio'] == '+',
    'BlockPrevAppoggioEsclamativo': df_rilevations['Foundamental'] == 'B' & df_rilevations['PrevAppoggio'] == '!',
    'BlockPrevAppoggioNegativo': df_rilevations['Foundamental'] == 'B' & df_rilevations['PrevAppoggio'] == '-',
    'DefenseCorrectSkillTypeS': df_rilevations['Foundamental'] == 'D' & df_rilevations['correctSkillType'] == 'S',
    'DefenseCorrectSkillTypeB': df_rilevations['Foundamental'] == 'D' & df_rilevations['correctSkillType'] == 'B',
    'DefenseCorrectSkillTypeC': df_rilevations['Foundamental'] == 'D' & df_rilevations['correctSkillType'] == 'C',
    'DefenseStartZoneC_2': df_rilevations['Foundamental'] == 'D' & df_rilevations['StartZoneCompact'] == 2,
    'DefenseStartZoneC_3': df_rilevations['Foundamental'] == 'D' & df_rilevations['StartZoneCompact'] == 3,
    'DefenseStartZoneC_4': df_rilevations['Foundamental'] == 'D' & df_rilevations['StartZoneCompact'] == 4,
    'DefenseStartZoneC_6': df_rilevations['Foundamental'] == 'D' & df_rilevations['StartZoneCompact'] == 6,
    'DefenseStartZoneC_1': df_rilevations['Foundamental'] == 'D' & df_rilevations['StartZoneCompact'] == 1,
    'DefenseEsecZone_1': df_rilevations['Foundamental'] == 'D' & df_rilevations['EndZoneEsecZone'] == 1,
    'DefenseEsecZone_2': df_rilevations['Foundamental'] == 'D' & df_rilevations['EndZoneEsecZone'] == 2,
    'DefenseEsecZone_3': df_rilevations['Foundamental'] == 'D' & df_rilevations['EndZoneEsecZone'] == 3,
    'DefenseEsecZone_4': df_rilevations['Foundamental'] == 'D' & df_rilevations['EndZoneEsecZone'] == 4,    
    'DefenseEsecZone_5': df_rilevations['Foundamental'] == 'D' & df_rilevations['EndZoneEsecZone'] == 5,
    'DefenseEsecZone_6': df_rilevations['Foundamental'] == 'D' & df_rilevations['EndZoneEsecZone'] == 6,
    'DefenseEsecZone_7': df_rilevations['Foundamental'] == 'D' & df_rilevations['EndZoneEsecZone'] == 7,
    'DefenseEsecZone_8': df_rilevations['Foundamental'] == 'D' & df_rilevations['EndZoneEsecZone'] == 8,
    'DefenseEsecZone_9': df_rilevations['Foundamental'] == 'D' & df_rilevations['EndZoneEsecZone'] == 9
    
}

# Mappa delle variabili ai nomi delle colonne corrispondenti con i tocchi
dizionario_variabili_to_tocchi_Generale = {
    'Serve': 'Total_Serve',
    'Reception': 'Total_Reception',
    'Set': 'Total_Set',
    'Attack': 'Total_Attack',
    'Block': 'Total_Block',
    'Defense': 'Total_Defense',
    'Freeball': 'Total_Freeball',
    'ServeTypeQ': 'Total_ServeTypeQ',
    'ServeTypeM': 'Total_ServeTypeM',
    'ServeStartZoneC_5': 'Total_ServeStartZoneC_5',
    'ServeStartZoneC_6': 'Total_ServeStartZoneC_6',
    'ServeStartZoneC_1': 'Total_ServeStartZoneC_1',
    'ServeEndZone3_5': 'Total_ServeEndZone3_5',
    'ServeEndZone3_6': 'Total_ServeEndZone3_6',
    'ServeEndZone3_1': 'Total_ServeEndZone3_1',
    'ServeCCCA_R': 'Total_ServeCCCA_R',
    'ServeCCCA_T': 'Total_ServeCCCA_T',
    'ServeCCCA_C': 'Total_ServeCCCA_C',
    'ReceptionTypeQ': 'Total_ReceptionTypeQ',
    'ReceptionTypeM': 'Total_ReceptionTypeM',
    'ReceptionStartZoneC_5': 'Total_ReceptionStartZoneC_5',
    'ReceptionStartZoneC_6': 'Total_ReceptionStartZoneC_6',
    'ReceptionStartZoneC_1': 'Total_ReceptionStartZoneC_1',
    'ReceptionEndZone3_5': 'Total_ReceptionEndZone3_5',
    'ReceptionEndZone3_6': 'Total_ReceptionEndZone3_6',
    'ReceptionEndZone3_1': 'Total_ReceptionEndZone3_1',
    'ReceptionServeCCCA_R': 'Total_ReceptionServeCCCA_R',
    'ReceptionServeCCCA_T': 'Total_ReceptionServeCCCA_T',
    'ReceptionServeCCCA_C': 'Total_ReceptionServeCCCA_C',
    'SetTypeT': 'Total_SetTypeT',
    'SetTypeQ': 'Total_SetTypeQ',
    'SetTypeM': 'Total_SetTypeM',
    'SetTypeH': 'Total_SetTypeH',
    'SetTypeO': 'Total_SetTypeO',
    'SetTypeNonH': 'Total_SetTypeNonH',
    'SetEsecZone3_5': 'Total_SetEsecZone3_5',
    'SetEsecZone3_6': 'Total_SetEsecZone3_6',
    'SetEsecZone3_1': 'Total_SetEsecZone3_1',
    'SetThisAppoggioPositivo': 'Total_SetThisAppoggioPositivo',
    'SetThisAppoggioEsclamativo': 'Total_SetThisAppoggioEsclamativo',
    'SetThisAppoggioNegativo': 'Total_SetThisAppoggioNegativo',
    'AttackTypeT': 'Total_AttackTypeT',
    'AttackTypeQ': 'Total_AttackTypeQ',
    'AttackTypeM': 'Total_AttackTypeM',
    'AttackTypeH': 'Total_AttackTypeH',
    'AttackTypeO': 'Total_AttackTypeO',
    'AttackTypeNonH': 'Total_AttackTypeNonH',
    'AttackStartZoneC_4': 'Total_AttackStartZoneC_4',
    'AttackStartZoneC_3': 'Total_AttackStartZoneC_3',  
    'AttackStartZoneC_2': 'Total_AttackStartZoneC_2',
    'AttackStartZoneC_6': 'Total_AttackStartZoneC_6',  
    'AttackStartZoneC_1': 'Total_AttackStartZoneC_1',
    'AttackThisAppoggioPositivo': 'Total_AttackThisAppoggioPositivo',  
    'AttackThisAppoggioEsclamativo': 'Total_AttackThisAppoggioEsclamativo',  
    'AttackThisAppoggioNegativo': 'Total_AttackThisAppoggioNegativo',  
    'AttackThisSetEvalDoppioPositivo': 'Total_AttackThisSetEvalDoppioPositivo',  
    'AttackThisSetEvalPositivo': 'Total_AttackThisSetEvalPositivo',  
    'AttackPlayersInfo0': 'Total_AttackPlayersInfo0',  
    'AttackPlayersInfo1': 'Total_AttackPlayersInfo1',  
    'AttackPlayersInfo2': 'Total_AttackPlayersInfo2',  
    'AttackPlayersInfo3': 'Total_AttackPlayersInfo3',  
    'AttackPlayersInfo4': 'Total_AttackPlayersInfo4',  
    'BlockTypeT': 'Total_BlockTypeT',
    'BlockTypeQ': 'Total_BlockTypeQ',
    'BlockTypeM': 'Total_BlockTypeM',
    'BlockTypeH': 'Total_BlockTypeH',
    'BlockTypeO': 'Total_BlockTypeO',
    'BlockTypeNonH': 'Total_BlockTypeNonH',
    'BlockEsecZone_4': 'Total_BlockEsecZone4',  
    'BlockEsecZone_3': 'Total_BlockEsecZone3',  
    'BlockEsecZone_2': 'Total_BlockEsecZone2', 
    'BlockPlayersInfo_1': 'Total_BlockPlayersInfo1',  
    'BlockPlayersInfo_2': 'Total_BlockPlayersInfo2',  
    'BlockPlayersInfo_3': 'Total_BlockPlayersInfo3',  
    'BlockPlayersInfo_4': 'Total_BlockPlayersInfo4',  
    'BlockPrevAppoggioPositivo': 'Total_BlockPrevAppoggioPositivo',
    'BlockPrevAppoggioEsclamativo': 'Total_BlockPrevAppoggioEsclamativo',
    'BlockPrevAppoggioNegativo': 'Total_BlockPrevAppoggioNegativo',
    'DefenseCorrectSkillTypeS': 'Total_DefenseCorrectSkillTypeS',
    'DefenseCorrectSkillTypeB': 'Total_DefenseCorrectSkillTypeB',
    'DefenseCorrectSkillTypeC': 'Total_DefenseCorrectSkillTypeC',
    'DefenseStartZoneC_2': 'Total_DefenseStartZoneC_2',
    'DefenseStartZoneC_3': 'Total_DefenseStartZoneC_3',
    'DefenseStartZoneC_4': 'Total_DefenseStartZoneC_4',
    'DefenseStartZoneC_6': 'Total_DefenseStartZoneC_6',
    'DefenseStartZoneC_1': 'Total_DefenseStartZoneC_1',
    'DefenseEsecZone_1': 'Total_DefenseEsecZone_1',
    'DefenseEsecZone_2': 'Total_DefenseEsecZone_2',
    'DefenseEsecZone_3': 'Total_DefenseEsecZone_3',
    'DefenseEsecZone_4': 'Total_DefenseEsecZone_4',
    'DefenseEsecZone_5': 'Total_DefenseEsecZone_5',
    'DefenseEsecZone_6': 'Total_DefenseEsecZone_6',
    'DefenseEsecZone_7': 'Total_DefenseEsecZone_7',
    'DefenseEsecZone_8': 'Total_DefenseEsecZone_8',
    'DefenseEsecZone_9': 'Total_DefenseEsecZone_9'
        
}

lista_variabili_Generale = list(dizionario_variabili_Generale.keys())
lista_variabili_to_tocchi_Generale = list(dizionario_variabili_to_tocchi_Generale.values())  #Anche chiamata colonne_filtro_tocchi

#Calcolo eval_scores_Generale, ovvero contando tutti i tocchi
eval_scores_Generale = calcola_dizionario_voti(dizionario_variabili_Generale, df_rilevations)

#Calcolo eval_scores_PerRuolo, ovvero contando tutti i tocchi
eval_scores_PerRuolo1 = calcola_dizionario_voti(dizionario_variabili_Generale, df_rilevations_libero)
eval_scores_PerRuolo2 = calcola_dizionario_voti(dizionario_variabili_Generale, df_rilevations_schiacciatore)
eval_scores_PerRuolo3 = calcola_dizionario_voti(dizionario_variabili_Generale, df_rilevations_opposto)
eval_scores_PerRuolo4 = calcola_dizionario_voti(dizionario_variabili_Generale, df_rilevations_centrale)
eval_scores_PerRuolo5 = calcola_dizionario_voti(dizionario_variabili_Generale, df_rilevations_palleggiatore)

eval_scores_PerRuolo = {
    1: eval_scores_PerRuolo1,
    2: eval_scores_PerRuolo2,
    3: eval_scores_PerRuolo3,
    4: eval_scores_PerRuolo4,
    5: eval_scores_PerRuolo5
}

eval_scores_PerRuolo5

def calcola_voti_oggettivi(df, col_eval="Eval", col_ruolo="Ruolo", col_giocatore="NumeroMaglia_ID"):
    """
    Calcola i 3 voti oggettivi (Generale, PerRuolo, PerGiocatore)
    per un dataframe già filtrato sui tocchi della variabile di interesse.

    Parametri:
    ----------
    df : pd.DataFrame
        Dataframe già filtrato sui tocchi della variabile (es. Foundamental='A')
    col_eval : str
        Colonna con il valore di Eval
    col_ruolo : str
        Colonna con il ruolo del giocatore
    col_giocatore : str
        Colonna con l'ID giocatore

    Ritorna:
    --------
    dict di DataFrame con le chiavi:
        - "Generale"
        - "PerRuolo"
        - "PerGiocatore"
    """

    risultati = {}

    # 1️⃣ Generale
    df_gen = (
        df.groupby(col_eval)
        .agg(
            tocchi=("EndedInPoint", "count"),
            punti=("EndedInPoint", "sum")
        )
        .reset_index()
    )
    df_gen["Voto"] = df_gen["punti"] / df_gen["tocchi"]
    risultati["Generale"] = df_gen

    # 2️⃣ PerRuolo
    df_ruolo = (
        df.groupby([col_ruolo, col_eval])
        .agg(
            tocchi=("EndedInPoint", "count"),
            punti=("EndedInPoint", "sum")
        )
        .reset_index()
    )
    df_ruolo["Voto"] = df_ruolo["punti"] / df_ruolo["tocchi"]
    risultati["PerRuolo"] = df_ruolo

    # 3️⃣ PerGiocatore
    df_gioc = (
        df.groupby(col_giocatore)
        .agg(
            tocchi=("EndedInPoint", "count"),
            punti=("EndedInPoint", "sum")
        )
        .reset_index()
    )
    df_gioc["Voto"] = df_gioc["punti"] / df_gioc["tocchi"]
    risultati["PerGiocatore"] = df_gioc

    return risultati


# Supponiamo di voler calcolare i voti per l'attacco
df_attacco = df_rilevations[df_rilevations["Foundamental"] == "A"]

voti = calcola_voti_oggettivi(
    df_attacco,
    col_eval="Eval",
    col_ruolo="RuoloCalc",
    col_giocatore="NumeroMaglia_ID"
)

print(voti["Generale"])
print(voti["PerRuolo"])
print(voti["PerGiocatore"])


def crea_maschera_in_base_alla_variabile(var_name, df_rilevations):

    # Definizione maschera di filtro in base alla variabile
    if var_name == "Serve":
        mask = df_rilevations["Foundamental"] == "S"
    elif var_name == "Reception":
        mask = df_rilevations["Foundamental"] == "R"
    elif var_name == "Set":
        mask = df_rilevations["Foundamental"] == "E"
    elif var_name == "Attack":
        mask = df_rilevations["Foundamental"] == "A"
    elif var_name == "Block":
        mask = df_rilevations["Foundamental"] == "B"
    elif var_name == "Defense":
        mask = df_rilevations["Foundamental"] == "D"
    elif var_name == "FreeBall":
        mask = df_rilevations["Foundamental"] == "F"
    
    #Variabili battuta
    elif var_name == "ServeTypeQ":
        mask = (df_rilevations["Foundamental"] == "S") & (df_rilevations["Type"] == "Q")
    elif var_name == "ServeTypeM":
        mask = (df_rilevations["Foundamental"] == "S") & (df_rilevations["Type"] == "M")
    elif var_name == "ServeStartZoneC_5":
        mask = (df_rilevations["Foundamental"] == "S") & (df_rilevations["StartZoneCompact"] == 5)
    elif var_name == "ServeStartZoneC_6":
        mask = (df_rilevations["Foundamental"] == "S") & (df_rilevations["StartZoneCompact"] == 6)
    elif var_name == "ServeStartZoneC_1":
        mask = (df_rilevations["Foundamental"] == "S") & (df_rilevations["StartZoneCompact"] == 1)    
    elif var_name == "ServeEndZone3_5":
        mask = (df_rilevations["Foundamental"] == "S") & (df_rilevations["EndZoneEsecZone3aree"] == 5)
    elif var_name == "ServeEndZone3_6":
        mask = (df_rilevations["Foundamental"] == "S") & (df_rilevations["EndZoneEsecZone3aree"] == 6)
    elif var_name == "ServeEndZone3_1":
        mask = (df_rilevations["Foundamental"] == "S") & (df_rilevations["EndZoneEsecZone3aree"] == 1)
    elif var_name == "ServeCCCA_R":
        mask = (df_rilevations["Foundamental"] == "S") & (df_rilevations["correctCustomCharAggregate"] == "R")
    elif var_name == "ServeCCCA_T":
        mask = (df_rilevations["Foundamental"] == "S") & (df_rilevations["correctCustomCharAggregate"] == "T")
    elif var_name == "ServeCCCA_C":
        mask = (df_rilevations["Foundamental"] == "S") & (df_rilevations["correctCustomCharAggregate"] == "C")
        
    #Variabili ricezione
    elif var_name == "RecTypeQ":
        mask = (df_rilevations["Foundamental"] == "R") & (df_rilevations["Type"] == "Q")
    elif var_name == "RecTypeM":
        mask = (df_rilevations["Foundamental"] == "R") & (df_rilevations["Type"] == "M")
    elif var_name == "RecStartZoneC_5":
        mask = (df_rilevations["Foundamental"] == "R") & (df_rilevations["StartZoneCompact"] == 5)
    elif var_name == "RecStartZoneC_6":
        mask = (df_rilevations["Foundamental"] == "R") & (df_rilevations["StartZoneCompact"] == 6)
    elif var_name == "RecStartZoneC_1":
        mask = (df_rilevations["Foundamental"] == "R") & (df_rilevations["StartZoneCompact"] == 1)
    elif var_name == "RecEndZone3_5":
        mask = (df_rilevations["Foundamental"] == "R") & (df_rilevations["EndZoneEsecZone3aree"] == 5)
    elif var_name == "RecEndZone3_6":
        mask = (df_rilevations["Foundamental"] == "R") & (df_rilevations["EndZoneEsecZone3aree"] == 6)
    elif var_name == "RecEndZone3_1":
        mask = (df_rilevations["Foundamental"] == "R") & (df_rilevations["EndZoneEsecZone3aree"] == 1)
    elif var_name == "RecServeCCCA_R":
        mask = (df_rilevations["Foundamental"] == "R") & (df_rilevations["PrevCorrectCustomCharAggregate"] == "R")
    elif var_name == "RecServeCCCA_T":
        mask = (df_rilevations["Foundamental"] == "R") & (df_rilevations["PrevCorrectCustomCharAggregate"] == "T")
    elif var_name == "RecServeCCCA_C":
        mask = (df_rilevations["Foundamental"] == "R") & (df_rilevations["PrevCorrectCustomCharAggregate"] == "C")
        
    #Variabili alzata
    elif var_name == "SetTypeT":
        mask = (df_rilevations["Foundamental"] == "E") & (df_rilevations["Type"] == "T")
    elif var_name == "SetTypeQ":
        mask = (df_rilevations["Foundamental"] == "E") & (df_rilevations["Type"] == "Q")
    elif var_name == "SetTypeM":
        mask = (df_rilevations["Foundamental"] == "E") & (df_rilevations["Type"] == "M")
    elif var_name == "SetTypeH":
        mask = (df_rilevations["Foundamental"] == "E") & (df_rilevations["Type"] == "H")
    elif var_name == "SetTypeO":
        mask = (df_rilevations["Foundamental"] == "E") & (df_rilevations["Type"] == "O")
    elif var_name == "SetTypeNonH":
        mask = (df_rilevations["Foundamental"] == "E") & (df_rilevations["Type"] != "H") 
    elif var_name == "SetEsecZone3_5":
        mask = (df_rilevations["Foundamental"] == "E") & (df_rilevations["EndZoneEsecZone3aree"] == 5)
    elif var_name == "SetEsecZone3_6":
        mask = (df_rilevations["Foundamental"] == "E") & (df_rilevations["EndZoneEsecZone3aree"] == 6)
    elif var_name == "SetEsecZone3_1":
        mask = (df_rilevations["Foundamental"] == "E") & (df_rilevations["EndZoneEsecZone3aree"] == 1)
    elif var_name == "SetThisAppoggioPositivo":
        mask = (df_rilevations["Foundamental"] == "E") & (df_rilevations["ThisAppoggio"] == '+')
    elif var_name == "SetThisAppoggioEsclamativo":
        mask = (df_rilevations["Foundamental"] == "E") & (df_rilevations["ThisAppoggio"] == '!')
    elif var_name == "SetThisAppoggioNegativo":
        mask = (df_rilevations["Foundamental"] == "E") & (df_rilevations["ThisAppoggio"] == '-')
        
    #Variabili attacco
    elif var_name == "AttkTypeT":
        mask = (df_rilevations["Foundamental"] == "A") & (df_rilevations["Type"] == "T")
    elif var_name == "AttkTypeQ":
        mask = (df_rilevations["Foundamental"] == "A") & (df_rilevations["Type"] == "Q")
    elif var_name == "AttkTypeM":
        mask = (df_rilevations["Foundamental"] == "A") & (df_rilevations["Type"] == "M")
    elif var_name == "AttkTypeH":
        mask = (df_rilevations["Foundamental"] == "A") & (df_rilevations["Type"] == "H")
    elif var_name == "AttkTypeO":
        mask = (df_rilevations["Foundamental"] == "A") & (df_rilevations["Type"] == "O")
    elif var_name == "AttkTypeNonH":
        mask = (df_rilevations["Foundamental"] == "A") & (df_rilevations["Type"] != "H")
    elif var_name == "AttkStartZoneC_4":
        mask = (df_rilevations["Foundamental"] == "A") & (df_rilevations["StartZoneCompact"] == 4)
    elif var_name == "AttkStartZoneC_3":
        mask = (df_rilevations["Foundamental"] == "A") & (df_rilevations["StartZoneCompact"] == 3)
    elif var_name == "AttkStartZoneC_2":
        mask = (df_rilevations["Foundamental"] == "A") & (df_rilevations["StartZoneCompact"] == 2)
    elif var_name == "AttkStartZoneC_1":
        mask = (df_rilevations["Foundamental"] == "A") & (df_rilevations["StartZoneCompact"] == 1)
    elif var_name == "AttkStartZoneC_6":
        mask = (df_rilevations["Foundamental"] == "A") & (df_rilevations["StartZoneCompact"] == 6)
    elif var_name == "AttkThisAppoggioPositivo":
        mask = (df_rilevations["Foundamental"] == "A") & (df_rilevations["ThisAppoggio"] == '+')
    elif var_name == "AttkThisAppoggioEsclamativo":
        mask = (df_rilevations["Foundamental"] == "A") & (df_rilevations["ThisAppoggio"] == '!')
    elif var_name == "AttkThisAppoggioNegativo":
        mask = (df_rilevations["Foundamental"] == "A") & (df_rilevations["ThisAppoggio"] == '-')
    elif var_name == "AttkAlzataDoppioPos":
        mask = (df_rilevations["Foundamental"] == "A") & (df_rilevations["ThisSetEval"] == '#')
    elif var_name == "AttkAlzataPos":
        mask = (df_rilevations["Foundamental"] == "A") & (df_rilevations["ThisSetEval"] == '+')
    elif var_name == "AttkPlayersInfo0":
        mask = (df_rilevations["Foundamental"] == "A") & (df_rilevations["PlayersInfo"] == 0)  
    elif var_name == "AttkPlayersInfo1":
        mask = (df_rilevations["Foundamental"] == "A") & (df_rilevations["PlayersInfo"] == 1)  
    elif var_name == "AttkPlayersInfo2":
        mask = (df_rilevations["Foundamental"] == "A") & (df_rilevations["PlayersInfo"] == 2)  
    elif var_name == "AttkPlayersInfo3":
        mask = (df_rilevations["Foundamental"] == "A") & (df_rilevations["PlayersInfo"] == 3)  
    elif var_name == "AttkPlayersInfo4":
        mask = (df_rilevations["Foundamental"] == "A") & (df_rilevations["PlayersInfo"] == 4)        
    
    #variabili muro
    elif var_name == "BlockTypeT":
        mask = (df_rilevations["Foundamental"] == "B") & (df_rilevations["Type"] == "T")
    elif var_name == "BlockTypeQ":
        mask = (df_rilevations["Foundamental"] == "B") & (df_rilevations["Type"] == "Q")
    elif var_name == "BlockTypeM":
        mask = (df_rilevations["Foundamental"] == "B") & (df_rilevations["Type"] == "M")
    elif var_name == "BlockTypeH":
        mask = (df_rilevations["Foundamental"] == "B") & (df_rilevations["Type"] == "H")
    elif var_name == "BlockTypeO":
        mask = (df_rilevations["Foundamental"] == "B") & (df_rilevations["Type"] == "O")
    elif var_name == "BlockTypeNonH":
        mask = (df_rilevations["Foundamental"] == "B") & (df_rilevations["Type"] != "H")
    elif var_name == "BlockEsecZone4":
        mask = (df_rilevations["Foundamental"] == "B") & (df_rilevations["EndZoneEsecZone"] == 4)
    elif var_name == "BlockEsecZone3":
        mask = (df_rilevations["Foundamental"] == "B") & (df_rilevations["EndZoneEsecZone"] == 3)
    elif var_name == "BlockEsecZone2":
        mask = (df_rilevations["Foundamental"] == "B") & (df_rilevations["EndZoneEsecZone"] == 2)
    elif var_name == "BlockPlayersInfo1":
        mask = (df_rilevations["Foundamental"] == "B") & (df_rilevations["PlayersInfo"] == 1)
    elif var_name == "BlockPlayersInfo2":
        mask = (df_rilevations["Foundamental"] == "B") & (df_rilevations["PlayersInfo"] == 2)
    elif var_name == "BlockPlayersInfo3":
        mask = (df_rilevations["Foundamental"] == "B") & (df_rilevations["PlayersInfo"] == 3)
    elif var_name == "BlockPlayersInfo4":
        mask = (df_rilevations["Foundamental"] == "B") & (df_rilevations["PlayersInfo"] == 4)
    elif var_name == "BlockPrevAppoggioPositivo":
        mask = (df_rilevations["Foundamental"] == "B") & (df_rilevations["PrevAppoggio"] == '+')
    elif var_name == "BlockPrevAppoggioEsclamativo":
        mask = (df_rilevations["Foundamental"] == "B") & (df_rilevations["PrevAppoggio"] == '!')
    elif var_name == "BlockPrevAppoggioNegativo":
        mask = (df_rilevations["Foundamental"] == "B") & (df_rilevations["PrevAppoggio"] == '-')
        
    #variabili difesa
    elif var_name == "DefCorSkillTypeS":
        mask = (df_rilevations["Foundamental"] == "D") & (df_rilevations["correctSkillType"] == "S")
    elif var_name == "DefCorSkillTypeB":
        mask = (df_rilevations["Foundamental"] == "D") & (df_rilevations["correctSkillType"] == "B")
    elif var_name == "DefCorSkillTypeC":
        mask = (df_rilevations["Foundamental"] == "D") & (df_rilevations["correctSkillType"] == "C")
    elif var_name == "DefStartZoneC_2":
        mask = (df_rilevations["Foundamental"] == "D") & (df_rilevations["StartZoneCompact"] == 2)
    elif var_name == "DefStartZoneC_3":
        mask = (df_rilevations["Foundamental"] == "D") & (df_rilevations["StartZoneCompact"] == 3)
    elif var_name == "DefStartZoneC_4":
        mask = (df_rilevations["Foundamental"] == "D") & (df_rilevations["StartZoneCompact"] == 4)
    elif var_name == "DefStartZoneC_1":
        mask = (df_rilevations["Foundamental"] == "D") & (df_rilevations["StartZoneCompact"] == 1)
    elif var_name == "DefStartZoneC_6":
        mask = (df_rilevations["Foundamental"] == "D") & (df_rilevations["StartZoneCompact"] == 6)
    elif var_name == "DefEsecZone_1":
        mask = (df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 1)
    elif var_name == "DefEsecZone_2":
        mask = (df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 2)
    elif var_name == "DefEsecZone_3":
        mask = (df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 3)
    elif var_name == "DefEsecZone_4":
        mask = (df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 4)
    elif var_name == "DefEsecZone_5":
        mask = (df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 5)
    elif var_name == "DefEsecZone_6":
        mask = (df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 6)
    elif var_name == "DefEsecZone_7":
        mask = (df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 7)
    elif var_name == "DefEsecZone_8":
        mask = (df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 8)
    elif var_name == "DefEsecZone_9":
        mask = (df_rilevations["Foundamental"] == "D") & (df_rilevations["EndZoneEsecZone"] == 9)
    else:
        mask=None
    
    return mask


# Funzione per ciascuna variabile che filtra i tocchi rilevanti e assegna i punteggi
# È una funzione che prende un dataframe e un dizionario come input, e restituisce un dataframe con le variabili aggregate. Il dataframe di input è il dizionario dei vori, che ad ogni variabile assegna dei voti in base all'Eval. In questa funzione diciamo per ogni variabile, quali osservazioni prendere per calcolare il voto, ad esempio per RecTypeQ prendiamo le osservazioni che hanno Foundamental = 'R' e Type = 'Q'.
# Ci metto dentro tante cose, lo rendo universale per tutte le possibili PCA che voglio fare, tanto alla fine conta cosa gli do come argomento, ovvero quale dizionario di voti (eval_scores) gli do

def calcola_scores_Generale(df_players, df_rilevations, eval_scores_Generale, per_players=True, sostituisciNoneConMediana=False):
    results = []
    for var_name, mapping in eval_scores_Generale.items():
        mask = crea_maschera_in_base_alla_variabile(var_name, df_rilevations)
        if mask is None:
            continue
        
        # Calcola le X variabili aggregate per giocatore e partita.
        # results è una lista di dataframe. Ogni elemento (dataframe) per una certa nuova variabile. Ogni dataframe ha le colonne GameID, NumeroMaglia_ID, e la nuova variabile. In ogni dataframe le osservazioni sono tutti i giocatori che hanno almeno un tocco in un game.
        
        df_tmp = df_rilevations[mask].copy()
        df_tmp[var_name] = df_tmp["Eval"].map(mapping)
        
        if per_players:
            # Aggregazione su tutti i tocchi per giocatore (PlayerID)
            df_agg = (
                df_tmp.groupby("NumeroMaglia_ID")[var_name]
                .mean()
                .reset_index()
                .rename(columns={var_name: var_name + "_Generale"})
            )
            results.append(df_agg)
        else:
            # Aggregazione su ogni partita per ogni giocatore
            df_agg = (
                df_tmp.groupby(["GameID", "NumeroMaglia_ID"])[var_name]
                .mean()
                .reset_index()
                .rename(columns={var_name: var_name + "_Generale"})
            )
            results.append(df_agg)
            
    
    # Unione di tutti i dataframe aggregati
    # Unisci tutte le aggregazioni in un unico dataframe con la libreria functools
    if per_players:
        df_all = reduce(lambda left, right: pd.merge(left, right, on="NumeroMaglia_ID", how="outer"), results)  # Fondo i vari dataframe in uno solo, unendo le righe che sono lo stesso giocatore
        # Rimuovo eventuali colonne duplicate da df_players (esclusa la colonna chiave)
        cols_to_drop = [col for col in df_all.columns if col in df_players.columns and col != "NumeroMaglia_ID"]
        df_players = df_players.drop(columns=cols_to_drop)
        # Merge finale con df_players, usando PlayerID come chiave
        df_players = df_players.merge(
            df_all,
            left_on="PlayerID",
            right_on="NumeroMaglia_ID",
            how="left"
        ).drop(columns=["NumeroMaglia_ID"])

    else:
        df_all = reduce(lambda left, right: pd.merge(left, right, on=["GameID", "NumeroMaglia_ID"], how="outer"), results)  # Fondo i vari dataframe in uno solo, unendo le righe che sono lo stesso giocatore nello stesso GameID
        # Rimuovo eventuali colonne duplicate da df_players (esclusa la chiave composta)
        cols_to_drop = [col for col in df_all.columns if col in df_players.columns and col not in ["GameID", "NumeroMaglia_ID"]]
        df_players = df_players.drop(columns=cols_to_drop)
        # Merge finale con df_players_each_game, chiave su GameID e PlayerID
        df_players = df_players.merge(
            df_all,
            left_on=["GameID", "PlayerID"],
            right_on=["GameID", "NumeroMaglia_ID"],
            how="left"
        ).drop(columns=["NumeroMaglia_ID"])


    # Sostituzione dei None con mediana se sostituisciNoneConMediana=True
    if sostituisciNoneConMediana:
        for col in df_players.columns:
            if col.endswith("_Generale"):
                median_value = df_players[col].median(skipna=True)
                df_players[col] = df_players[col].fillna(median_value)

    return df_players




def calcola_scores_PerRuolo(df_players, df_rilevations, eval_scores_PerRuolo, per_players=True, sostituisciNoneConMediana=False):
    
    # Dizionario per accumulare i risultati per ogni variabile
    # Chiave: var_name, Valore: lista di DataFrame da concatenare
    var_results = {}
    
    for ruolo, eval_scores in eval_scores_PerRuolo.items():
        # Filtro i giocatori di questo ruolo
        players_ruolo = df_players[df_players['RuoloCalc'] == ruolo][['PlayerID']]
        
        # Filtro df_rilevations per i giocatori di questo ruolo tramite join
        df_rilevations_ruolo = df_rilevations[df_rilevations['NumeroMaglia_ID'].isin(players_ruolo['PlayerID'])]
        
        for var_name, mapping in eval_scores.items():
            # Definisco la maschera in base al fondamentale e condizioni associate
            mask = crea_maschera_in_base_alla_variabile(var_name, df_rilevations)
            if mask is None:
                continue
            

            df_tmp = df_rilevations_ruolo[mask].copy()
            if len(df_tmp) == 0:
                continue
                
            df_tmp[var_name] = df_tmp["Eval"].map(mapping)  #Associo i punteggi a ciascun Eval
            
            # Aggrego per giocatore o per partita
            if per_players:
                df_agg = (
                    df_tmp.groupby(["NumeroMaglia_ID"])[var_name].mean()
                    .reset_index()
                )
            else:
                df_agg = (
                    df_tmp.groupby(["GameID", "NumeroMaglia_ID"])[var_name].mean()
                    .reset_index()
                )
            
            # Aggiungo al dizionario dei risultati per questa variabile
            if var_name not in var_results:
                var_results[var_name] = []
            var_results[var_name].append(df_agg)
    
    # Se non ci sono risultati, ritorno df_players così com'è
    if len(var_results) == 0:
        return df_players
    
    # Per ogni variabile, concateno tutti i dataframe e poi aggrego
    final_results = []
    
    for var_name, dfs_list in var_results.items():
        if len(dfs_list) == 1:
            # Solo un DataFrame per questa variabile
            df_var = dfs_list[0]
        else:
            # Concateno tutti i DataFrame per questa variabile
            df_var = pd.concat(dfs_list, ignore_index=True)
            
            # Raggruppo nuovamente per aggregare i valori dei diversi ruoli
            if per_players:
                df_var = (
                    df_var.groupby(["NumeroMaglia_ID"])[var_name].mean()
                    .reset_index()
                )
            else:
                df_var = (
                    df_var.groupby(["GameID", "NumeroMaglia_ID"])[var_name].mean()
                    .reset_index()
                )
        
        # Rinomino la colonna aggiungendo il suffisso _PerRuolo
        df_var = df_var.rename(columns={var_name: var_name + "_PerRuolo"})
        final_results.append(df_var)
    
    # Ora unisco tutti i risultati finali
    if per_players:
        df_all = reduce(lambda left, right: pd.merge(left, right, on=["NumeroMaglia_ID"], how="outer"), final_results)
        
        # Evito colonne duplicate
        cols_to_drop = [col for col in df_all.columns if col in df_players.columns and col != "NumeroMaglia_ID"]
        if cols_to_drop:
            df_players = df_players.drop(columns=cols_to_drop)
        
        # Merge finale su PlayerID
        df_players = df_players.merge(
            df_all,
            left_on="PlayerID",
            right_on="NumeroMaglia_ID",
            how="left"
        ).drop(columns=["NumeroMaglia_ID"])
    else:
        # Per partita
        df_all = reduce(lambda left, right: pd.merge(left, right, on=["GameID", "NumeroMaglia_ID"], how="outer"), final_results)
        
        cols_to_drop = [col for col in df_all.columns if col in df_players.columns and col not in ["GameID", "NumeroMaglia_ID"]]
        if cols_to_drop:
            df_players = df_players.drop(columns=cols_to_drop)
        
        df_players = df_players.merge(
            df_all,
            left_on=["GameID", "PlayerID"],
            right_on=["GameID", "NumeroMaglia_ID"],
            how="left"
        ).drop(columns=["NumeroMaglia_ID"])
        
        
    # Sostituzione dei None con la mediana per ruolo se sostituisciNoneConMediana=True
    if sostituisciNoneConMediana:
        for col in [c for c in df_players.columns if c.endswith("_PerRuolo")]:
            for ruolo, gruppo in df_players.groupby("RuoloCalc"):
                valori = gruppo[col].dropna()
                if not valori.empty:
                    mediana = valori.median()
                    df_players.loc[(df_players["RuoloCalc"] == ruolo) & (df_players[col].isna()), col] = mediana
                    
        
    return df_players


def calcola_scores_PerGiocatore(df_players, df_rilevations, lista_variabili, per_players=True, sostituisciNoneConMediana=False):
    
    # Lista per contenere i dataframe aggregati per ciascuna variabile
    results = []
    
    for var_name in lista_variabili:
        # Definisco la maschera in base al fondamentale e condizioni associate
        mask = crea_maschera_in_base_alla_variabile(var_name, df_rilevations)
        if mask is None:
            continue
        

        # Filtro i dati per questa variabile
        df_tmp = df_rilevations[mask].copy()
        if len(df_tmp) == 0:    #Se non ci sono tocchi per questa variabile, passo alla successiva
            continue
        
        # Calcolo il punteggio: (tocchi con EndedInPoint=True) / (totale tocchi)
        if per_players:
            # Aggregazione per giocatore
            df_agg = df_tmp.groupby("NumeroMaglia_ID").agg({
                "EndedInPoint": ["sum", "count"]  # sum = tocchi che hanno fatto punto, count = totale tocchi
            }).reset_index()
            
            # Appiattisco le colonne multi-level
            df_agg.columns = ["NumeroMaglia_ID", f"{var_name}_points", f"{var_name}_total"]
            
            # Calcolo la percentuale di successo (punteggio)
            df_agg[f"{var_name}_PerGiocatore"] = df_agg[f"{var_name}_points"] / df_agg[f"{var_name}_total"]
            
            # Tengo solo le colonne necessarie
            df_agg = df_agg[["NumeroMaglia_ID", f"{var_name}_PerGiocatore"]]
            
        else:
            # Aggregazione per partita e giocatore
            df_agg = df_tmp.groupby(["GameID", "NumeroMaglia_ID"]).agg({
                "EndedInPoint": ["sum", "count"]  # sum = tocchi che hanno fatto punto, count = totale tocchi
            }).reset_index()
            
            # Appiattisco le colonne multi-level
            df_agg.columns = ["GameID", "NumeroMaglia_ID", f"{var_name}_points", f"{var_name}_total"]
            
            # Calcolo la percentuale di successo (punteggio)
            df_agg[f"{var_name}_PerGiocatore"] = df_agg[f"{var_name}_points"] / df_agg[f"{var_name}_total"]
            
            # Tengo solo le colonne necessarie
            df_agg = df_agg[["GameID", "NumeroMaglia_ID", f"{var_name}_PerGiocatore"]]
        
        results.append(df_agg)
    
    # Se non ci sono risultati, ritorno df_players così com'è
    if len(results) == 0:
        return df_players
    
    # Unisco tutti i risultati in un unico DataFrame
    if per_players:
        df_all = reduce(lambda left, right: pd.merge(left, right, on=["NumeroMaglia_ID"], how="outer"), results)
        
        # Evito colonne duplicate
        cols_to_drop = [col for col in df_all.columns if col in df_players.columns and col != "NumeroMaglia_ID"]
        if cols_to_drop:
            df_players = df_players.drop(columns=cols_to_drop)
        
        # Merge finale su PlayerID
        df_players = df_players.merge(
            df_all,
            left_on="PlayerID",
            right_on="NumeroMaglia_ID",
            how="left"
        ).drop(columns=["NumeroMaglia_ID"])
        
    else:
        # Per partita
        df_all = reduce(lambda left, right: pd.merge(left, right, on=["GameID", "NumeroMaglia_ID"], how="outer"), results)
        
        # Evito colonne duplicate
        cols_to_drop = [col for col in df_all.columns if col in df_players.columns and col not in ["GameID", "NumeroMaglia_ID"]]
        if cols_to_drop:
            df_players = df_players.drop(columns=cols_to_drop)
        
        # Merge finale su GameID e PlayerID
        df_players = df_players.merge(
            df_all,
            left_on=["GameID", "PlayerID"],
            right_on=["GameID", "NumeroMaglia_ID"],
            how="left"
        ).drop(columns=["NumeroMaglia_ID"])
        
        
    if sostituisciNoneConMediana:
        for col in [c for c in df_players.columns if c.endswith("_PerGiocatore")]:
            for ruolo, gruppo in df_players.groupby("RuoloCalc"):
                valori = gruppo[col].dropna()
                if not valori.empty:
                    mediana = valori.median()
                    df_players.loc[(df_players["RuoloCalc"] == ruolo) & (df_players[col].isna()), col] = mediana
    
    return df_players


df_players_3voti = calcola_totale_tocchi(df_rilevations, df_players, per_players=True)

df_players_3voti = calcola_scores_Generale(df_players, df_rilevations, eval_scores_Generale, per_players=True, sostituisciNoneConMediana=True)                  #I None li sostituisco con la mediana della variabile
df_players_3voti = calcola_scores_PerRuolo(df_players_3voti, df_rilevations, eval_scores_PerRuolo, per_players=True, sostituisciNoneConMediana=True)            #I None li sostituisco con la mediana di quel RuoloCalc della variabile
df_players_3voti = calcola_scores_PerGiocatore(df_players_3voti, df_rilevations, lista_variabili_Generale, per_players=True, sostituisciNoneConMediana=True)    #I None li sostituisco con la mediana di quel RuoloCalc della variabile
#Anche sostituendo un valor None con una mediana, il numero di tocchi non cambia. Quindi se voglio vedere/usare solo giocatori con X tocchi, funziona come prima, come se non avessi sostituito niente.

df_players_3voti

df_players_3voti_libero = df_players_3voti[df_players_3voti['RuoloCalc'] == 1]
df_players_3voti_schiacciatore = df_players_3voti[df_players_3voti['RuoloCalc'] == 2]
df_players_3voti_opposto = df_players_3voti[df_players_3voti['RuoloCalc'] == 3]
df_players_3voti_centrale = df_players_3voti[df_players_3voti['RuoloCalc'] == 4]
df_players_3voti_palleggiatore = df_players_3voti[df_players_3voti['RuoloCalc'] == 5]

df_players_each_game3voti = calcola_totale_tocchi(df_rilevations, df_players_each_game, per_players=False)

df_players_each_game3voti = calcola_scores_Generale(df_players_each_game3voti, df_rilevations, eval_scores_Generale, per_players=False, sostituisciNoneConMediana=True)
df_players_each_game3voti = calcola_scores_PerRuolo(df_players_each_game3voti, df_rilevations, eval_scores_PerRuolo, per_players=False, sostituisciNoneConMediana=True)
df_players_each_game3voti = calcola_scores_PerGiocatore(df_players_each_game3voti, df_rilevations, lista_variabili_Generale, per_players=False, sostituisciNoneConMediana=True)

df_players_each_game3voti

df_players_each_game3voti_libero = df_players_each_game3voti[df_players_each_game3voti['RuoloCalc'] == 1]
df_players_each_game3voti_schiacciatore = df_players_each_game3voti[df_players_each_game3voti['RuoloCalc'] == 2]
df_players_each_game3voti_opposto = df_players_each_game3voti[df_players_each_game3voti['RuoloCalc'] == 3]
df_players_each_game3voti_centrale = df_players_each_game3voti[df_players_each_game3voti['RuoloCalc'] == 4]
df_players_each_game3voti_palleggiatore = df_players_each_game3voti[df_players_each_game3voti['RuoloCalc'] == 5]








# Dizionario delle variabili che vuoi lavorare e dove si trovano in df_rilevations
dizionario_variabili_pca1 = {
    'Serve': df_rilevations['Foundamental'] == 'S',
    'Reception': df_rilevations['Foundamental'] == 'R',
    'Set': df_rilevations['Foundamental'] == 'E',
    'Attack': df_rilevations['Foundamental'] == 'A',
    'Block': df_rilevations['Foundamental'] == 'B',
    'Defense': df_rilevations['Foundamental'] == 'D',
    'FreeBall': df_rilevations['Foundamental'] == 'F'
}


colonne_filtro_tocchi1 = ['TotalServes', 'TotalReceptions', 'TotalSets', 'TotalAttacks', 'TotalBlocks', 'TotalDefenses', 'TotalFreeBalls']

eval_scores_pca1 = calcola_dizionario_voti(dizionario_variabili_pca1, df_rilevations)

df_players1 = df_players.copy()

#Funzione per disegnare il grafico con i boxplot usando ipywidgets (meglio di no, meglio usare la funzione che usa plotly)
'''

import ipywidgets as widgets
from IPython.display import display

def create_widget_selector_with_boxplot_toggle(df, lista_variabili_pca, player_id_col='PlayerID', ruolo_col='RuoloCalc'):
    df = df.copy()
    df['NomeCompleto'] = df['Nome'] + ' ' + df['Cognome']
    
    # Mappa colori ruolo
    ruolo_color_map = {
        0: 'gray',
        1: 'green',
        2: 'red',
        3: 'orange',
        4: 'deepskyblue',
        5: 'purple'
    }
    
    # Associo a ogni fondamentale la variabile che gli conta i tocchi
    mappa_totali = {
    'Reception': 'TotalReceptions',
    'Serve': 'TotalServes',
    'Set': 'TotalSets',
    'Attack': 'TotalAttacks',
    'Block': 'TotalBlocks',
    'Defense': 'TotalDefenses',
    'FreeBall': 'TotalFreeBalls'
    }

    # Widget: Slider per TotalTouch
    min_touch_slider = widgets.IntSlider(
        value=0,
        min=0,
        max=df['TotalTouch'].max(),
        step=10,   #di quanto aumentare i valori
        description='Minimo di tocchi totali:',
        style={'description_width': 'initial'},
        layout=widgets.Layout(width='400px'),  # <-- larghezza slider
        continuous_update=False
    )

    # Widget: Selezione giocatore
    player_dropdown = widgets.Dropdown(
        options=[('Nessuno', None)],
        value=None,
        description='Giocatore:',
        style={'description_width': 'initial'}
    )

    # Widget: Mostra boxplot
    boxplot_checkbox = widgets.Checkbox(
        value=True,
        description='Mostra boxplot',
        style={'description_width': 'initial'}
    )

    # Widget: Colora per ruolo
    role_checkbox = widgets.Checkbox(
        value=True,
        description='Colora per ruolo',
        style={'description_width': 'initial'}
    )

    output = widgets.Output()

    def update_plot(change=None):
        with output:
            output.clear_output(wait=True)

            selected_player = player_dropdown.value
            show_boxplot = boxplot_checkbox.value
            color_by_role = role_checkbox.value
            min_touch = min_touch_slider.value

            # Filtro per TotalTouch
            df_filtered = df[df['TotalTouch'] >= min_touch].copy()
            df_filtered['NomeCompleto'] = df_filtered['Nome'] + ' ' + df_filtered['Cognome']

            # Aggiorna dropdown dinamicamente
            players = sorted(df_filtered[player_id_col].unique())
            player_dropdown.options = [('Nessuno', None)] + [(str(p), p) for p in players]

            # Costruzione long-form
            df_plot = df_filtered[lista_variabili_pca + [player_id_col, 'NomeCompleto', 'RuoloCalc', 'TotalTouch', 'TotalServes', 'TotalReceptions', 'TotalSets', 'TotalAttacks', 'TotalBlocks', 'TotalDefenses', 'TotalFreeBalls']].reset_index()
            df_melted = pd.melt(
                df_plot,
                id_vars=['index', player_id_col, 'NomeCompleto', 'RuoloCalc', 'TotalTouch'],
                value_vars=lista_variabili_pca,
                var_name='Variable',
                value_name='Value'
            ).dropna(subset=['Value'])

            # Aggiungi colonna colore ruolo se serve
            df_melted['ColoreRuolo'] = df_melted[ruolo_col].map(ruolo_color_map).fillna('gray')
            
            df_melted['TotalByVariable'] = df_melted.apply(
                lambda row: df.loc[row['index'], mappa_totali.get(row['Variable'], 'TotalTouch')],
                axis=1
            )

            if color_by_role:
                fig = px.strip(
                    df_melted,
                    x='Variable',
                    y='Value',
                    color=ruolo_col,
                    color_discrete_map=ruolo_color_map,
                    custom_data=[player_id_col, 'NomeCompleto', 'Value', 'TotalTouch', 'TotalByVariable'],
                    hover_data=[]
                )
            else:
                df_melted['ColoreFisso'] = 'Tutti'
                fig = px.strip(
                    df_melted,
                    x='Variable',
                    y='Value',
                    color='ColoreFisso',
                    color_discrete_map={'Tutti': 'blue'},
                    custom_data=[player_id_col, 'NomeCompleto', 'Value', 'TotalTouch', 'TotalByVariable'],
                    hover_data=[]
                )

            fig.update_traces(
                hovertemplate=(
                    "<b>%{customdata[1]}</b><br>"              # NomeCompleto
                    "PlayerID: %{customdata[0]}<br>"           # PlayerID
                    "Valore: %{customdata[2]:.3f}<br>"         # Value
                    "Tocchi totali: %{customdata[3]}<br>" 
                    "Tocchi di questo fondamentale: %{customdata[4]}<extra></extra>"  # TotalByVariable
                ),
                marker=dict(size=6, opacity=0.2),
                jitter=0.6  # aumenta la dispersione dei punti
            )

            # Evidenzia giocatore selezionato
            if selected_player is not None:
                df_highlighted = df_melted[df_melted[player_id_col] == selected_player]
                if len(df_highlighted) > 0:
                    fig_highlight = px.strip(
                        df_highlighted,
                        x='Variable',
                        y='Value',
                        color='ColoreRuolo',
                        color_discrete_map=ruolo_color_map,
                        custom_data=[player_id_col, 'NomeCompleto', 'Value', 'TotalTouch', 'TotalByVariable'],
                        hover_data=[]
                    )
                    for trace in fig_highlight.data:
                        trace.update(
                            marker=dict(size=20, opacity=1.0, line=dict(width=3, color='black')),
                            jitter=0.01,
                            showlegend=False,
                            name=f'{trace.name}_highlighted',
                            hovertemplate=(
                                "<b>%{customdata[1]}</b><br>"              # NomeCompleto
                                "PlayerID: %{customdata[0]}<br>"           # PlayerID
                                "Valore: %{customdata[2]:.3f}<br>"         # Value
                                "TotalTouch (generale): %{customdata[3]}<br>" 
                                "Tocchi per questa variabile: %{customdata[4]}<extra></extra>"  # TotalByVariable
                            )
                        )
                        fig.add_trace(trace)
            else:
                fig.update_traces(marker=dict(size=8, opacity=0.2))

            # Boxplot grigi
            if show_boxplot:
                fig_box = px.box(df_melted, x='Variable', y='Value')
                for trace in fig_box.data:
                    trace.update(
                        width=0.4, opacity=0.4, showlegend=False,
                        boxpoints=False,
                        marker_color='gray', line_color='gray'
                    )
                    fig.add_trace(trace)

            fig.update_layout(
                title=f'Giocatore evidenziato: {selected_player or "Nessuno"}',
                height=600,
                showlegend=color_by_role,
                legend_title_text='RuoloCalc',
                legend=dict(
                    itemsizing='constant',
                    bgcolor='rgba(255,255,255,0.7)'
                )
            )

            fig.show()

    # Collega eventi
    player_dropdown.observe(update_plot, names='value')
    boxplot_checkbox.observe(update_plot, names='value')
    role_checkbox.observe(update_plot, names='value')
    min_touch_slider.observe(update_plot, names='value')

    # Mostra tutto
    container = widgets.VBox([
        min_touch_slider,
        player_dropdown,
        boxplot_checkbox,
        role_checkbox,
        output
    ])
    update_plot()
    display(container)

# Esempio di chiamata
create_widget_selector_with_boxplot_toggle(df_players1, lista_variabili_pca1, 'PlayerID', 'RuoloCalc')

'''


#Funzione per disegnare il grafico con i boxplot usando Dash e Plotly
def variabili_distribuzioni_boxplot(df, lista_variabili_pca, dizionario_variabili_to_tocchi):
    # df_players1 deve contenere: PlayerID, Nome, Cognome, Ruolo, TotalTouch, variabili di performance (es: 'AttackScore', 'ReceptionScore', ecc), e le TotalXxx per ogni fondamentale

    # Costruisci il dataframe melted con colonne custom
    def melt_dataframe(df, lista_variabili_pca):
        
        # Colonne necessarie
        touch_cols = [col for col in dizionario_variabili_to_tocchi.values() if col in df.columns]
        base_cols = ['PlayerID', 'NomeCompleto', 'RuoloCalc', 'TotalTouch']
        df_plot = df[lista_variabili_pca + base_cols + touch_cols].copy()

        # Melt
        df_melted = pd.melt(
            df_plot,
            id_vars=base_cols + touch_cols,
            value_vars=lista_variabili_pca,
            var_name='Variable',
            value_name='Value'
        ).dropna(subset=['Value'])

        # Colonna TotalByVariable
        def get_total_by_variable(row):
            total_col = dizionario_variabili_to_tocchi.get(row['Variable'])
            return row[total_col] if total_col and total_col in row else None

        df_melted['TotalByVariable'] = df_melted.apply(get_total_by_variable, axis=1)
        df_melted['TotalByVariable'] = df_melted['TotalByVariable'].fillna(0).astype(int)

        return df_melted


    # Mappa colori ruolo
    ruolo_color_map = {
        0: 'gray',
        1: 'green',
        2: 'red',
        3: 'orange',
        4: 'deepskyblue',
        5: 'purple'
    }


    # Slider per ogni variabile
    slider_components = []
    for var in lista_variabili_pca:
        col_tocchi = dizionario_variabili_to_tocchi.get(var)
        if col_tocchi and col_tocchi in df.columns:
            slider_components.append(
                html.Div([
                    html.Label(f"Minimo tocchi per {var}:"),
                    dcc.Slider(
                        id=f"min-touch-slider-{var}",
                        min=0,
                        max=50,
                        step=2,
                        value=0,
                        marks={i: str(i) for i in range(0, 50, 10)},
                        tooltip={"placement": "bottom"}
                    )
                ], style={"margin-bottom": "12px"})
            )

    app = dash.Dash(__name__)

    app.layout = html.Div([
        html.H3("Distribuzioni variabili giocatori"),

        dcc.Dropdown(   
            id='player-dropdown',
            options=[{'label': f"{r['NomeCompleto']}", 'value': r['PlayerID']} for _, r in df.iterrows()],
            value=None,
            placeholder='Seleziona un giocatore'
        ),

        dcc.Checklist(
            id='color-role-checklist',
            options=[{'label': 'Colora per ruolo', 'value': 'color'}],
            value=['color'],
            inline=True
        ),

        dcc.Checklist(
            id='boxplot-checklist',
            options=[{'label': 'Mostra boxplot', 'value': 'box'}],
            value=['box'],
            inline=True
        ),

        html.Label("Tocchi minimi totali:"),

        dcc.Slider(
            id='min-touch-slider',
            min=0,
            max=int(df['TotalTouch'].max()),
            step=20,
            value=10,
            marks={i: str(i) for i in range(0, int(df['TotalTouch'].max())+1, 500)},
            tooltip={"placement": "bottom"}
        ),

        dcc.Graph(id='distribution-graph'),

        # Qui i tuoi slider per i fondamentali, messi in riga orizzontale
        html.Div(
            slider_components,
            style={
                'display': 'flex',
                'flexDirection': 'row',
                'justifyContent': 'space-around',  # distribuisce gli slider con spazi uguali tra loro, e la metà dello spazio all'inizio e alla fine
                'overflowX': 'auto',
                'padding': '10px 0',
                'width': 'auto'
            }
        )
    ])

    # Callback dinamico per tutti gli slider
    from dash.dependencies import State

    #Gli input del callback vanno messi come parametri della funzione update_graph
    @app.callback(
        Output('distribution-graph', 'figure'),
        [Input('player-dropdown', 'value'),
         Input('color-role-checklist', 'value'),
         Input('boxplot-checklist', 'value'),
         Input('min-touch-slider', 'value')] +
        [Input(f"min-touch-slider-{var}", 'value') for var in lista_variabili_pca],
    )
    def update_graph(selected_player, color_by_role, show_boxplot, min_touch, *min_tocchi_vars):
        df_filtered = df[df['TotalTouch'] >= min_touch].copy()
        # Filtra per ogni variabile
        for i, var in enumerate(lista_variabili_pca):
            col_tocchi = dizionario_variabili_to_tocchi.get(var)
            if col_tocchi and col_tocchi in df_filtered.columns:
                df_filtered = df_filtered[df_filtered[col_tocchi] >= min_tocchi_vars[i]]
        df_melted = melt_dataframe(df_filtered, lista_variabili_pca)

        color_col = 'RuoloCalc' if 'color' in color_by_role else None
        color_map = ruolo_color_map if color_col else None

        fig = px.strip(
            df_melted,
            x='Variable',
            y='Value',
            color=color_col,
            color_discrete_map=color_map,
            custom_data=['PlayerID', 'NomeCompleto', 'Value', 'TotalTouch', 'TotalByVariable']  #Come grandezza metto il logaritmo del numero di tocchi di quella variabile. Forse devo fare +1
        )

        grandezza_punti = df_melted['TotalByVariable'].apply(lambda x: np.log1p(x)).to_list()
        print(grandezza_punti)

        fig.update_traces(
            hovertemplate=(
                "<b>%{customdata[1]}</b><br>"
                "PlayerID: %{customdata[0]}<br>"
                "Valore: %{customdata[2]:.3f}<br>"
                "TotalTouch: %{customdata[3]}<br>"
                "Tocchi di questa variabile: %{customdata[4]}<extra></extra>"
            ),
            marker=dict(
                    size=6,
                    opacity=0.3
            ),
            jitter=min(1, df_melted.shape[0] / 500)
        )

        if selected_player is not None:
            df_highlight = df_melted[df_melted['PlayerID'] == selected_player]
            fig_highlight = px.strip(
                df_highlight,
                x='Variable',
                y='Value',
                color_discrete_sequence=['black'],
                custom_data=['PlayerID', 'NomeCompleto', 'Value', 'TotalTouch', 'TotalByVariable']
            )
            for trace in fig_highlight.data:
                trace.update(
                    marker=dict(size=15, opacity=1.0, line=dict(width=3, color='black')),
                    jitter=0,
                    showlegend=False,
                    hovertemplate=(
                        "<b>%{customdata[1]}</b><br>"
                        "PlayerID: %{customdata[0]}<br>"
                        "Valore: %{customdata[2]:.3f}<br>"
                        "TotalTouch: %{customdata[3]}<br>"
                        "Tocchi di questa variabile: %{customdata[4]}<extra></extra>"
                    )
                )
                fig.add_trace(trace)

        if 'box' in show_boxplot:
            fig_box = px.box(df_melted, x='Variable', y='Value')
            for trace in fig_box.data:
                trace.update(width=0.4, opacity=0.4, showlegend=False, boxpoints=False,
                            marker_color='gray', line_color='gray')
                fig.add_trace(trace)

        fig.update_layout(
            title=f"Giocatore evidenziato: {selected_player if selected_player else 'Nessuno'}",
            height=650,
            legend_title_text='RuoloCalc',
            legend=dict(itemsizing='constant', bgcolor='rgba(255,255,255,0.7)')
        )

        return fig

    if __name__ == '__main__':
        app.run(debug=True, port=8061)

def variabili_distribuzioni_boxplot(df, lista_variabili_pca, dizionario_variabili_to_tocchi):
    # Colonne base
    base_cols = ['PlayerID', 'NomeCompleto', 'RuoloCalc', 'TotalTouch']
    touch_cols = [col for col in dizionario_variabili_to_tocchi.values() if col in df.columns]

    # Funzione per creare il dataframe melted
    def melt_dataframe(df, lista_variabili_pca):
        id_vars = list(dict.fromkeys(base_cols + touch_cols))
        
        # Aggiungi colonne per info partita se esistono
        if 'GameID' in df.columns:
            extra_cols = ['GameID', 'Date', 'HomeTeamNameShort', 'HomeTeamSetWon', 'VisitorTeamNameShort', 'VisitorTeamSetWon']
            for col in extra_cols:
                if col in df.columns and col not in id_vars:
                    id_vars.append(col)

        df_melted = pd.melt(
            df,
            id_vars=id_vars,
            value_vars=lista_variabili_pca,
            var_name='Variable',
            value_name='Value'
        ).dropna(subset=['Value'])

        def get_total_by_variable(row):
            variable_name = row['Variable']  # es. 'Serve_PerRuolo'
            total_col = dizionario_variabili_to_tocchi.get(variable_name)
            if total_col and total_col in row.index:
                return row[total_col]
            else:
                return 0

        df_melted['TotalByVariable'] = (
            df_melted.apply(get_total_by_variable, axis=1)
            .fillna(0)
            .astype(int)
        )

        # Crea le colonne MatchInfo qui dentro
        if 'GameID' in df_melted.columns:
            df_melted['MatchInfo'] = df_melted.apply(
                lambda row: f"{row.get('HomeTeamNameShort', 'N/A')} {row.get('HomeTeamSetWon', '0')} - {row.get('VisitorTeamSetWon', '0')} {row.get('VisitorTeamNameShort', 'N/A')}",
                axis=1
            )
            df_melted['MatchDate'] = df_melted.get('Date', '')
        else:
            df_melted['MatchInfo'] = ""
            df_melted['MatchDate'] = ""

        return df_melted

    # Mappa colori ruolo
    ruolo_color_map = {
        0: 'gray',
        1: 'green',
        2: 'red',
        3: 'darkorange',
        4: 'deepskyblue',
        5: 'purple'
    }
    
    ruolo_descrizione = {
        0: 'Ruolo Sconosciuto',
        1: 'Libero',
        2: 'Schiacciatore',
        3: 'Opposto',
        4: 'Centrale',
        5: 'Palleggiatore'
    }
    
    # Opzioni dropdown
    ruoli_unici = df[['RuoloCalc']].drop_duplicates().sort_values(by='RuoloCalc')

    # Creo app Dash
    app = dash.Dash(__name__)

    # Slider components per ogni variabile
    slider_components = []
    for var in lista_variabili_pca:
        col_tocchi = dizionario_variabili_to_tocchi.get(var)
        if col_tocchi and col_tocchi in df.columns:
            slider_components.append(
                html.Div([
                    html.Label(f"Minimo tocchi per {var}:"),
                    dcc.Slider(
                        id=f"min-touch-slider-{var}",
                        min=0,
                        max=50,
                        step=2,
                        value=0,
                        marks={i: str(i) for i in range(0, 50, 10)},
                        tooltip={"placement": "bottom"}
                    )
                ], style={"flex": "1 1 auto", "padding": "0 10px"})
            )

    app.layout = html.Div([
        html.H3("Distribuzioni variabili giocatori"),

        dcc.Dropdown(
            id='player-dropdown',
            options=[{'label': r['NomeCompleto'],
                      'value': r['PlayerID']} for _, r in df.iterrows()],
            value=None,
            placeholder='Seleziona un giocatore'
        ),
        
        # Store per il giocatore selezionato cliccandoci sopra
        dcc.Store(id='selected-player-store', storage_type='memory'),

        
        dcc.Dropdown(
            id='role-dropdown',
            options=[
                {
                    'label': html.Span(
                        f"{row['RuoloCalc']} - {ruolo_descrizione[row['RuoloCalc']]}",
                        style={
                            'color': ruolo_color_map.get(row['RuoloCalc'], 'black')
                        }
                    ),
                    'value': row['RuoloCalc']
                }
                for _, row in ruoli_unici.iterrows()
            ],
            multi=True,
            value=[0, 1, 2, 3, 4, 5],
            placeholder='Seleziona quali ruoli mostrare'
        ),
        
        dcc.Checklist(
            id='color-role-checklist',
            options=[{'label': 'Colora per ruolo', 'value': 'color'}],
            value=['color'],
            inline=True
        ),

        dcc.Checklist(
            id='boxplot-checklist',
            options=[{'label': 'Mostra boxplot', 'value': 'box'}],
            value=[],
            inline=True
        ),

        html.Label("Tocchi minimi totali:"),

        dcc.Slider(
            id='min-touch-slider',
            min=0,
            max=int(df['TotalTouch'].max()),
            step=20,
            value=10,
            marks={i: str(i) for i in range(0, int(df['TotalTouch'].max())+1, 500)},
            tooltip={"placement": "bottom"}
        ),

        dcc.Graph(id='distribution-graph'),

        html.Div(
            slider_components,
            style={
                'display': 'flex',
                'flexDirection': 'row',
                'justifyContent': 'space-around',
                'overflowX': 'auto',
                'padding': '10px 0',
                'width': '100%',
                'boxSizing': 'border-box',
            }
        )
    ])
    
    print("Lista variabili PCA:", lista_variabili_pca)
    print("Dizionario variabili to tocchi:", dizionario_variabili_to_tocchi)
    
    
    @app.callback(
        Output('selected-player-store', 'data'),
        Input('distribution-graph', 'clickData'),
        State('selected-player-store', 'data'),
        prevent_initial_call=True
    )
    def store_selected_player(clickData, current_selected):
        # Click nel vuoto → deseleziona (non funziona)
        if clickData is None:
            return None

        # Click su un punto → toggle selezione
        if 'points' in clickData and len(clickData['points']) > 0:
            player_id = clickData['points'][0]['customdata'][1]  # PlayerID
            if current_selected == player_id:
                return None
            else:
                return player_id

        return current_selected


    @app.callback(
        Output('distribution-graph', 'figure'),
        [Input('player-dropdown', 'value'),
         Input('role-dropdown', 'value'),
         Input('color-role-checklist', 'value'),
         Input('boxplot-checklist', 'value'),
         Input('min-touch-slider', 'value'),
         Input('selected-player-store', 'data') ] +
        [Input(f"min-touch-slider-{var}", 'value') for var in lista_variabili_pca],
    )
    def update_graph(selected_player, selected_roles, color_by_role, show_boxplot, min_touch, selected_player_click, *min_tocchi_vars):
        try:
            df_filtered = df.copy()
            
            if isinstance(selected_roles, str):
                selected_roles = [selected_roles]
            
            # Filtro per tocchi totali minimi
            df_filtered = df_filtered[df_filtered['TotalTouch'] >= min_touch].copy()
            
            # Filtro per tocchi minimi per ogni variabile
            for i, var in enumerate(lista_variabili_pca):
                col_tocchi = dizionario_variabili_to_tocchi.get(var)
                if col_tocchi and col_tocchi in df_filtered.columns:
                    df_filtered = df_filtered[df_filtered[col_tocchi] >= min_tocchi_vars[i]]
                    
            # Filtro per ruoli selezionati
            if selected_roles is None or len(selected_roles) == 0:
                return px.scatter(title="Nessun ruolo selezionato.")
            
            df_filtered = df_filtered[df_filtered['RuoloCalc'].isin(selected_roles)]
            
            if df_filtered.empty:
                return px.scatter(title="Nessun dato dopo l'applicazione dei filtri.")
                            
            # Crea il dataframe melted
            df_melted = melt_dataframe(df_filtered, lista_variabili_pca)
            
            if df_melted.empty:
                return px.scatter(title="Nessun dato nel dataframe melted.")

            color_col = 'RuoloCalc' if 'color' in color_by_role else None
            
            if color_col:
                color_map = ruolo_color_map
            else:
                color_map = None

            variabili = sorted(df_melted['Variable'].unique())
            cat_map = {v: i for i, v in enumerate(variabili)}

            fig = go.Figure()
            np.random.seed(42)
            
            # Mappa offset per ruolo. Per ogni ruolo indico di quanto si deve spostare rispetto al centro della variabile
            ruolo_offset_map = {
                0: -0.42, 
                1: -0.28, 
                2: -0.14,
                3: 0.0, 
                4: 0.14, 
                5: 0.28
            }        

            for var in variabili:
                df_var = df_melted[df_melted['Variable'] == var]
                if df_var.empty:
                    continue
                    
                x_base = cat_map[var]

                # Calcola dimensioni punti
                if df_var['TotalByVariable'].max() > 0:
                    max_log = np.sqrt(1 + df_var['TotalByVariable'].max())
                    sizes = 2 + 14 * np.sqrt(1 + df_var['TotalByVariable']) / max_log
                else:
                    sizes = [6] * len(df_var)

                if color_map:   #Se divido le osservazioni per ruolo
                    colors = df_var['RuoloCalc'].map(color_map).fillna('gray')
                    jitter = np.random.normal(0, (0.03 * (df_var.shape[0])**0.1 / len(lista_variabili_pca)), size=len(df_var))   #come DevSt metto df_filtered.shape[0] / 10000
                    offsets = df_var['RuoloCalc'].map(ruolo_offset_map).fillna(0).values
                    x_jittered = x_base + offsets + jitter
                else:
                    colors = 'blue'
                    jitter = np.random.normal(0, (0.1 * (df_var.shape[0])**0.1 / len(lista_variabili_pca)), size=len(df_var))
                    x_jittered = x_base + jitter
                    
                # Scatter plot principale
                if 'box' not in show_boxplot:  #Se mostro i boxplot, non mostro i punti. E quindi se non c'è il boxplot mostro i punti. Se invece vuoi mostrare entrambi togli questo if
                    fig.add_trace(go.Scatter(
                        x=x_jittered,
                        y=df_var['Value'],
                        mode='markers',
                        marker=dict(
                            size=sizes,
                            color=colors,
                            opacity=0.5,
                            line=dict(width=0.1, color='black')
                        ),
                        name=var,
                        legendgroup=var,
                        showlegend=False,
                        hovertemplate=(
                            "<b>%{customdata[0]}</b><br>"
                            "PlayerID: %{customdata[1]}<br>"
                            "Valore: %{y:.3f}<br>"
                            "TotalTouch: %{customdata[2]}<br>"
                            "Tocchi variabile: %{customdata[3]}<br>"
                            "%{customdata[4]}<br>"
                            "%{customdata[5]}<extra></extra>"
                        ),
                        customdata=np.stack([
                            df_var['NomeCompleto'],
                            df_var['PlayerID'],
                            df_var['TotalTouch'],
                            df_var['TotalByVariable'],
                            df_var['MatchInfo'],
                            df_var['MatchDate']
                        ], axis=-1)
                    ))
                
                
                # Usa click se presente, altrimenti dropdown
                selected_player = selected_player_click if selected_player_click else selected_player
                # Evidenzia giocatore selezionato
                if selected_player is not None:
                    df_highlight = df_var[df_var['PlayerID'] == selected_player]
                    if not df_highlight.empty:
                        fig.add_trace(go.Scatter(
                            x=[x_base],
                            y=df_highlight['Value'],
                            mode='markers',
                            marker=dict(
                                size=18,
                                symbol="diamond",
                                color='black',
                                opacity=1.0,
                                line=dict(width=3, color='black')
                            ),
                            name='Evidenziato',
                            showlegend=False,
                            hovertemplate=(
                                "<b>%{customdata[0]}</b><br>"
                                "PlayerID: %{customdata[1]}<br>"
                                "Valore: %{y:.3f}<br>"
                                "TotalTouch: %{customdata[2]}<br>"
                                "Tocchi variabile: %{customdata[3]}<extra></extra>"
                            ),
                            customdata=np.stack([
                                df_highlight['NomeCompleto'], 
                                df_highlight['PlayerID'], 
                                df_highlight['TotalTouch'], 
                                df_highlight['TotalByVariable']
                            ], axis=-1)
                        ))

                # Boxplot opzionale
                if 'box' in show_boxplot:  #Se mostro i boxplot
                    if 'color' in color_by_role:        #Se divido le osservazioni per ruolo, allora faccio un boxplot per ogni ruolo in ogni variabile
                        for ruolo_val, gruppo in df_var.groupby('RuoloCalc'):
                            fig.add_trace(go.Box(
                                x=[x_base + ruolo_offset_map.get(ruolo_val, 0)] * len(gruppo),
                                y=gruppo['Value'],
                                boxpoints='outliers',
                                marker_color=ruolo_color_map.get(ruolo_val, 'gray'),
                                line_color=ruolo_color_map.get(ruolo_val, 'gray'),
                                name=f"Box {var} - {ruolo_descrizione.get(ruolo_val, ruolo_val)}",
                                legendgroup=f"{var}_{ruolo_val}",
                                showlegend=False,
                                opacity=0.4
                            ))
                    else:       #Se non divido le osservazioni per ruolo, allora faccio un unico boxplot per ogni variabile
                        fig.add_trace(go.Box(
                            x=[x_base] * len(df_var),
                            y=df_var['Value'],
                            boxpoints='outliers',
                            marker_color='gray',
                            line_color='gray',
                            name=f"Box {var}",
                            legendgroup=var,
                            showlegend=False,
                            opacity=0.4
                        ))


            fig.update_layout(
                xaxis=dict(
                    tickmode='array',
                    tickvals=list(cat_map.values()),
                    ticktext=list(cat_map.keys()),
                    title='Variabile',
                    zeroline=False
                ),
                yaxis=dict(title='Valore'),
                height=650,
                title='Distribuzione variabili con dimensione punti proporzionale ai tocchi',
                hovermode='closest',
                margin=dict(l=40, r=40, t=80, b=40)
            )

            return fig
            
        except Exception as e:
            print(f"Errore nella callback: {str(e)}")
            import traceback
            traceback.print_exc()
            return px.scatter(title=f"Errore: {str(e)}")

    if __name__ == '__main__':
        app.run(debug=True, port=np.random.randint(7990, 8010))


#Funzione per disegnare la correlation heatmap con seaborn

def correlation_heatmap(df_players_pca):
    # Calcolo la matrice di correlazione
    corr_matrix = df_players_pca.corr()

    # Creo una matrice annot con i valori solo dove |corr| > 0.5
    annot_matrix = corr_matrix.copy()
    annot_matrix = annot_matrix.map(lambda x: f"{x:.2f}" if abs(x) > 0.3 else "")

    # Plot
    plt.figure(figsize=(8, 6))
    sns.heatmap(corr_matrix,
                annot=annot_matrix,
                fmt="",
                cmap='coolwarm',
                vmin=-1, vmax=1,
                square=True,
                linewidths=0.5,
                cbar_kws={'shrink': 0.8})

    plt.title('Matrice di correlazione')
    plt.tight_layout()
    plt.show()

#Funzione per disegnare il biplot e un grafico con la varianza spiegata
def biplot_e_varianza_spiegata(df_players_scaled, lista_variabili_pca, PCA_oggetto_fitTransformato, scores):

    # === BIPLOT ===
    # Assumendo df_players_scaled è il df su cui hai fatto PCA
    #df_players_opposto1_pca.index = df_players_opposto1_scaled.index  # se servisse riallineare
    df_plot = df_players_scaled.copy()

    # Aggiungi le componenti principali come colonne
    df_plot['PC1'] = scores[:, 0]
    df_plot['PC2'] = scores[:, 1]

    # Preparazione dati biplot
    pc1 = df_plot['PC1']
    pc2 = df_plot['PC2']
    customdata = df_plot[['NomeCompleto', 'PlayerID']].values

    # Scatter: punti dei giocatori
    scatter = go.Scatter(
        x=pc1,
        y=pc2,
        mode='markers',
        marker=dict(size=6, color='steelblue', opacity=0.6),
        name='Giocatori',
        customdata=customdata,
        hovertemplate=(
            "<b>%{customdata[0]}</b><br>"        # NomeCompleto
            "PlayerID: %{customdata[1]}<br>"
            "PC1: %{x:.2f}<br>"
            "PC2: %{y:.2f}<extra></extra>"
        )
    )

    # Vettori delle variabili originali (loadings)
    arrows = []
    for i, var in enumerate(lista_variabili_pca):
        x = PCA_oggetto_fitTransformato.components_[0, i]
        y = PCA_oggetto_fitTransformato.components_[1, i]
        arrows.append(go.Scatter(
            x=[0, x],
            y=[0, y],
            mode='lines+text',
            line=dict(color='darkorange', width=2),
            text=[None, var],
            textposition='top center',
            name=var,
            hoverinfo='skip'
        ))

    # Crea il biplot
    fig_biplot = go.Figure([scatter] + arrows)
    fig_biplot.update_layout(
        title=f'Biplot PCA (PC1 = {PCA_oggetto_fitTransformato.explained_variance_ratio_[0]*100:.1f}%, '
            f'PC2 = {PCA_oggetto_fitTransformato.explained_variance_ratio_[1]*100:.1f}%)',
        xaxis_title='PC1',
        yaxis_title='PC2',
        xaxis=dict(zeroline=True, zerolinecolor='gray', showgrid=True, gridcolor='lightgray', gridwidth=0.1),
        yaxis=dict(zeroline=True, zerolinecolor='gray', showgrid=True, gridcolor='lightgray', gridwidth=0.1),
        width=1400,
        height=600,
        showlegend=False,
        template='simple_white'
    )

    fig_biplot.show()



    # ----- GRAFICO VARIANZA SPIEGATA -----
    var_exp = PCA_oggetto_fitTransformato.explained_variance_ratio_
    cum_var_exp = np.cumsum(var_exp)
    x = np.arange(1, len(var_exp) + 1)

    fig, ax2 = plt.subplots(figsize=(14, 5))

    # Varianza per CP
    ax2.plot(x, var_exp,
            label='Varianza per CP',
            color='steelblue',
            marker='o',
            linestyle='-',
            markersize=6)

    # Varianza cumulata + area
    ax2.plot(x, cum_var_exp,
            label='Varianza cumulata',
            color='darkorange',
            marker='o',
            linestyle='-',
            markersize=6)
    ax2.fill_between(x, cum_var_exp, alpha=0.3, color='darkorange')

    ax2.set_xticks(x)
    ax2.set_ylim(0, 1.05)
    ax2.set_xlabel('Componente principale')
    ax2.set_ylabel('Proporzione di varianza spiegata')
    ax2.set_title('Varianza spiegata per CP e cumulata')

    ax2.grid(True, linestyle='--', alpha=0.5)
    ax2.legend(loc='upper left')
    plt.tight_layout()
    plt.show()



#Funzione per fare la PCA non lineare (non la usiamo perchè non possiamo sapere veramente l'importanza delle CP, inoltre probabilmente le relazioni non sono lineari)
'''
from sklearn.decomposition import KernelPCA

def applica_kernel_pca(df_players_pca, df_original, kernel='rbf', gamma=None, n_components=2):
    """
    Applica Kernel PCA a df_players_pca (già standardizzato) e produce un biplot interattivo.
    
    Parametri:
    - df_players_pca: DataFrame con solo le colonne numeriche standardizzate per la PCA
    - df_original: DataFrame contenente le colonne 'NomeCompleto', 'PlayerID', 'RuoloCalc'
    - kernel: tipo di kernel da usare ('linear', 'rbf', 'poly', 'sigmoid', ecc.)
    - gamma: parametro per il kernel RBF/poly/sigmoid
    - n_components: numero di componenti da mantenere (default: 2)

    Ritorna:
    - df_kernel_scores: DataFrame con i componenti principali
    - fig: figura plotly del biplot
    """
    # Applica Kernel PCA
    kpca = KernelPCA(n_components=n_components, kernel=kernel, gamma=gamma, fit_inverse_transform=True)
    X_kpca = kpca.fit_transform(df_players_pca)
    
    # Stima varianza spiegata (non disponibile direttamente)
    # Usiamo la norma al quadrato di ogni componente per stimare l'importanza relativa
    explained_var = np.var(X_kpca, axis=0)
    explained_var_ratio = explained_var / np.sum(explained_var)
    
    # DataFrame con i componenti principali
    df_kernel_scores = pd.DataFrame(X_kpca, columns=[f'PC{i+1}' for i in range(n_components)], index=df_players_pca.index)
    df_kernel_scores['PlayerID'] = df_original['PlayerID'].values
    df_kernel_scores['NomeCompleto'] = df_original['NomeCompleto'].values
    if 'RuoloCalc' in df_original.columns:
        df_kernel_scores['RuoloCalc'] = df_original['RuoloCalc'].values
    
    # Biplot con Plotly
    fig = px.scatter(
        df_kernel_scores, 
        x='PC1', y='PC2', 
        color='RuoloCalc' if 'RuoloCalc' in df_kernel_scores.columns else None,
        hover_data=['NomeCompleto', 'PlayerID'],
        title=f'Kernel PCA (kernel={kernel})',
        labels={'PC1': f"PC1 ({explained_var_ratio[0]*100:.1f}% var)", 'PC2': f"PC2 ({explained_var_ratio[1]*100:.1f}% var)"}
    )
    
    fig.update_traces(marker=dict(size=8, line=dict(width=0.5, color='DarkSlateGrey')))
    fig.update_layout(width=800, height=600)

    return df_kernel_scores, explained_var_ratio, fig
'''


#Definisco una funzione che dati:
# df_players, il mio dataframe con i giocatori
# eval_scores, il dizionario con i punteggi di ogni giocatore per ogni variabile
# dizionario_variabili_pca, il dizionario con le variabili di interesse e la relativa maschera booleana per filtrare df_rilevations
# colonne_filtro_tocchi, le colonne che contengono il numero di tocchi per ogni variabile di interesse
#Mi mostra tutto ciò che mi interessa, da grafici con distribuzioni dei punti nelle variabili, fino ai risultati della PCA

def funzione_mostra_tutto(df_players, eval_scores, dizionario_variabili_pca, dizionario_variabili_to_tocchi, tocchi_minimi_in_ogni_variabile=0, tocchi_minimi_tot=10):
    
    lista_variabili_pca = list(dizionario_variabili_pca.keys())
    colonne_filtro_tocchi = list(dizionario_variabili_to_tocchi.values())
    
    #Prima di standardizzare i dati, filtro prendendo solo le osservazioni che hanno almeno 2 tocchi in ogni variabile di interesse, così tolgo quelli che fanno rumore. Se avessi più dati potrei aumentare i tocchi minimi anche a 5
    mask = (df_players[colonne_filtro_tocchi] >= tocchi_minimi_in_ogni_variabile).all(axis=1)
    mask = mask & (df_players['TotalTouch'] >= tocchi_minimi_tot)   #Prendo anche solo quelli con almeno tocchi_minimi_tot come TotalTouch
    df_players = df_players.loc[mask].copy()
    
    print("Dizionario dei voti eval_scores")
    display(eval_scores)

    print(f"df_players con i giocatori che hanno almeno {tocchi_minimi_in_ogni_variabile} tocchi in ogni variabile")
    display(df_players)

    print(f"describe di df_players con i giocatori che hanno almeno {tocchi_minimi_in_ogni_variabile} tocchi in ogni variabile")
    display(df_players.describe())

    #Mostro il grafico delle distribuzioni dei punti nelle variabili, con i boxplot
    variabili_distribuzioni_boxplot(df_players, lista_variabili_pca, dizionario_variabili_to_tocchi)

    # Scala le colonne
    df_players_scaled = df_players.copy()
    df_players_scaled[lista_variabili_pca] = scaler.fit_transform(df_players[lista_variabili_pca])   #standardizza le variabili di interesse
    print("df_players_scaled")
    display(df_players_scaled)
    
    
    #Creo un nuovo df in cui tengo solo le colonne che mi servono per fare la PCA, ovvero le variabili con i punteggi standardizzati
    df_players_pca = df_players_scaled[lista_variabili_pca].copy()   
    
    #Disegno la correlation heatmap
    correlation_heatmap(df_players_pca)

    ##Procediamo con la PCA
    PCA_oggetto = PCA()
    #Faccio direttamente fit e transform insieme
    #Con fit alleno il modello sui miei dati. Con transform applico il modello ai miei dati, ottenendo per ogni osservazione i punteggi di ognuna delle CP
    scores = PCA_oggetto.fit_transform(df_players_pca)    #È un array n_righe × n_componenti. Ogni riga rappresenta un giocatore, ogni colonna una componente principale.
    print("scores")
    print(scores)
    
    ##Controlliamo il loading vector, ovvero il vettore dei loading delle componenti principali
    #Lo visualizzo come dataframe
    loadings = pd.DataFrame(
        PCA_oggetto.components_.T,
        index=lista_variabili_pca,
        columns=[f'PC{i+1}' for i in range(len(lista_variabili_pca))]
    )
    print("loadings")
    display(loadings)
    
    # Guardiamo la varianza spiegata da ogni singola componente
    print("Varianza spiegata da ogni componente: ", PCA_oggetto.explained_variance_ratio_)
    
    #Mostro il biplot e il grafico con la varianza spiegata
    biplot_e_varianza_spiegata(df_players_scaled, lista_variabili_pca, PCA_oggetto, scores)
    
    
    #PCA non lineare
    #df_scores_kpca, var_ratio_kpca, fig_kpca = applica_kernel_pca(df_players_pca, df_players_scaled, kernel='rbf', gamma=None, n_components=2)
    #fig_kpca.show()
    



def funzione_mostra_tutto1(df_players, dizionario_variabili_to_tocchi, tocchi_minimi_in_ogni_variabile=0, tocchi_minimi_tot=10, metodologia_voti="Generale", rimuovi_outliers=False):
    
    #lista_variabili_Generale = [col for col in df_players.columns if col.endswith("_Generale")]
    #lista_variabili_PerRuolo = [col for col in df_players.columns if col.endswith("_PerRuolo")]
    #lista_variabili_PerGiocatore = [col for col in df_players.columns if col.endswith("_PerGiocatore")]
    
    lista_variabili_Generale = [f"{variabile}_Generale" for variabile in dizionario_variabili_to_tocchi.keys()]
    lista_variabili_PerRuolo = [f"{variabile}_PerRuolo" for variabile in dizionario_variabili_to_tocchi.keys()]
    lista_variabili_PerGiocatore = [f"{variabile}_PerGiocatore" for variabile in dizionario_variabili_to_tocchi.keys()]
    
    
    #Creo dizionario_variabili_to_tocchi_Generale partendo da dizionario_variabili_to_tocchi. Questo serve ad associare ad ogni variabile (che finisce in _Generale, _PerRuolo o _PerGiocatore) la colonna dei tocchi
    dizionario_variabili_to_tocchi_Generale = {}
    dizionario_variabili_to_tocchi_PerRuolo = {}
    dizionario_variabili_to_tocchi_PerGiocatore = {}

    for variabile, colonna_tocchi in dizionario_variabili_to_tocchi.items():
        # Aggiungo il suffisso "_Generale" alla variabile
        variabile_generale = f"{variabile}_Generale"
        variabile_per_ruolo = f"{variabile}_PerRuolo"
        variabile_per_giocatore = f"{variabile}_PerGiocatore"
        dizionario_variabili_to_tocchi_Generale[variabile_generale] = colonna_tocchi
        dizionario_variabili_to_tocchi_PerRuolo[variabile_per_ruolo] = colonna_tocchi
        dizionario_variabili_to_tocchi_PerGiocatore[variabile_per_giocatore] = colonna_tocchi

    
    if metodologia_voti == "Generale":
        lista_variabili_pca = lista_variabili_Generale.copy()
        dizionario_variabili_to_tocchi = dizionario_variabili_to_tocchi_Generale.copy()
    elif metodologia_voti == "PerRuolo":
        lista_variabili_pca = lista_variabili_PerRuolo.copy()
        dizionario_variabili_to_tocchi = dizionario_variabili_to_tocchi_PerRuolo.copy()
    elif metodologia_voti == "PerGiocatore":
        lista_variabili_pca = lista_variabili_PerGiocatore.copy()
        dizionario_variabili_to_tocchi = dizionario_variabili_to_tocchi_PerGiocatore.copy()
    else:
        print("Hai sbagliato a inserire la metodologia di voto")

    #Le colonne_filto_tocchi sono le colonne_pca a cui tolgo ciò che c'è dopo _ , poi aggiungo "Total" all'inizio e s alla fine
    colonne_filtro_tocchi = [col.split("_")[0] for col in lista_variabili_pca]
    colonne_filtro_tocchi = [f"Total{col}s" for col in colonne_filtro_tocchi]
    #print(colonne_filtro_tocchi)
        
    #Prima di standardizzare i dati, filtro prendendo solo le osservazioni che hanno almeno 2 tocchi in ogni variabile di interesse, così tolgo quelli che fanno rumore. Se avessi più dati potrei aumentare i tocchi minimi anche a 5
    mask = (df_players[colonne_filtro_tocchi] >= tocchi_minimi_in_ogni_variabile).all(axis=1)
    mask = mask & (df_players['TotalTouch'] >= tocchi_minimi_tot)   #Prendo anche solo quelli con almeno tocchi_minimi_tot come TotalTouch
    df_players = df_players.loc[mask].copy()

    print(f"df_players con i giocatori che hanno almeno {tocchi_minimi_in_ogni_variabile} tocchi in ogni variabile")
    display(df_players)

    print(f"describe di df_players con i giocatori che hanno almeno {tocchi_minimi_in_ogni_variabile} tocchi in oni variabile")
    display(df_players.describe())

    #Mostro il grafico delle distribuzioni dei punti nelle variabili, con i boxplot
    variabili_distribuzioni_boxplot(df_players, lista_variabili_pca, dizionario_variabili_to_tocchi)  #Quella giusta
    #variabili_distribuzioni_boxplot(df_players, lista_variabili_Generale, dizionario_variabili_to_tocchi_Generale)
    #variabili_distribuzioni_boxplot(df_players, lista_variabili_PerRuolo, dizionario_variabili_to_tocchi_PerRuolo)
    #variabili_distribuzioni_boxplot(df_players, lista_variabili_PerGiocatore, dizionario_variabili_to_tocchi_PerGiocatore)

    # Scala le colonne
    df_players_scaled = df_players.copy()
    df_players_scaled[lista_variabili_pca] = scaler.fit_transform(df_players[lista_variabili_pca])   #standardizza le variabili di interesse
    print("df_players_scaled")
    display(df_players_scaled)
    
    if rimuovi_outliers:
        mask = pd.Series(True, index=df_players_scaled.index)
        for col in lista_variabili_pca:
            Q1 = df_players_scaled[col].quantile(0.25)
            Q3 = df_players_scaled[col].quantile(0.75)
            IQR = Q3 - Q1
            limite_inf = Q1 - 1.5 * IQR
            limite_sup = Q3 + 1.5 * IQR
            mask &= (df_players_scaled[col] >= limite_inf) & (df_players_scaled[col] <= limite_sup)
        df_players_scaled = df_players_scaled[mask]
        df_players_scaled[lista_variabili_pca] = scaler.fit_transform(df_players_scaled[lista_variabili_pca])  #Ristandardizzo
    
    
    #Creo un nuovo df in cui tengo solo le colonne che mi servono per fare la PCA, ovvero le variabili con i punteggi standardizzati
    df_players_pca = df_players_scaled[lista_variabili_pca].copy()   
    
    #Disegno la correlation heatmap
    correlation_heatmap(df_players_pca)

    ##Procediamo con la PCA
    PCA_oggetto = PCA()
    #Faccio direttamente fit e transform insieme
    #Con fit alleno il modello sui miei dati. Con transform applico il modello ai miei dati, ottenendo per ogni osservazione i punteggi di ognuna delle CP
    scores = PCA_oggetto.fit_transform(df_players_pca)    #È un array n_righe × n_componenti. Ogni riga rappresenta un giocatore, ogni colonna una componente principale.
    print("scores")
    print(scores)
    
    ##Controlliamo il loading vector, ovvero il vettore dei loading delle componenti principali
    #Lo visualizzo come dataframe
    loadings = pd.DataFrame(
        PCA_oggetto.components_.T,
        index=lista_variabili_pca,
        columns=[f'PC{i+1}' for i in range(len(lista_variabili_pca))]
    )
    print("loadings")
    display(loadings)
    
    # Guardiamo la varianza spiegata da ogni singola componente
    print("Varianza spiegata da ogni componente: ", PCA_oggetto.explained_variance_ratio_)
    
    #Mostro il biplot e il grafico con la varianza spiegata
    biplot_e_varianza_spiegata(df_players_scaled, lista_variabili_pca, PCA_oggetto, scores)
    
    
    #PCA non lineare
    #df_scores_kpca, var_ratio_kpca, fig_kpca = applica_kernel_pca(df_players_pca, df_players_scaled, kernel='rbf', gamma=None, n_components=2)
    #fig_kpca.show()
    


dizionario_variabili_to_tocchi = {
    'Serve': 'TotalServes',
    'Reception': 'TotalReceptions',
    'Set': 'TotalSets',
    'Attack': 'TotalAttacks',
    'Block': 'TotalBlocks',
    'Defense': 'TotalDefenses',
    'FreeBall': 'TotalFreeBalls'
}


tocchi_minimi_tot = 10
tocchi_minimi_in_ogni_variabile = 0   #Se è 0 ci saranno valori nulli nelle variabili, quindi non si può fare la PCA


funzione_mostra_tutto1(df_players_3voti, dizionario_variabili_to_tocchi, tocchi_minimi_in_ogni_variabile, tocchi_minimi_tot, metodologia_voti="PerGiocatore", rimuovi_outliers=True)


df_players_3voti_libero = df_players_3voti[df_players_3voti['RuoloCalc'] == 1]
df_players_3voti_schiacciatore = df_players_3voti[df_players_3voti['RuoloCalc'] == 2]
df_players_3voti_opposto = df_players_3voti[df_players_3voti['RuoloCalc'] == 3]
df_players_3voti_centrale = df_players_3voti[df_players_3voti['RuoloCalc'] == 4]
df_players_3voti_palleggiatore = df_players_3voti[df_players_3voti['RuoloCalc'] == 5]

dizionario_variabili_to_tocchi = {
    'Reception': 'TotalReceptions',
    'Set': 'TotalSets',
    'Defense': 'TotalDefenses',
    'FreeBall': 'TotalFreeBalls'
}

tocchi_minimi_tot = 10
tocchi_minimi_in_ogni_variabile = 0   #Se è 0 ci saranno valori nulli nelle variabili, quindi non si può fare la PCA


funzione_mostra_tutto1(df_players_3voti_libero, dizionario_variabili_to_tocchi, tocchi_minimi_in_ogni_variabile, tocchi_minimi_tot, metodologia_voti="PerRuolo", rimuovi_outliers=True)

dizionario_variabili_to_tocchi = {
    'Serve': 'TotalServes',
    'Reception': 'TotalReceptions',
    'Set': 'TotalSets',
    'Attack': 'TotalAttacks',
    'Block': 'TotalBlocks',
    'Defense': 'TotalDefenses',
    'FreeBall': 'TotalFreeBalls'
}

tocchi_minimi_tot = 10
tocchi_minimi_in_ogni_variabile = 0   #Se è 0 ci saranno valori nulli nelle variabili, quindi non si può fare la PCA. A meno che non li ho sostituiti con la mediana


funzione_mostra_tutto1(df_players_3voti_schiacciatore, dizionario_variabili_to_tocchi, tocchi_minimi_in_ogni_variabile, tocchi_minimi_tot, metodologia_voti="PerGiocatore", rimuovi_outliers=True)


dizionario_variabili_to_tocchi = {
    'Serve': 'TotalServes',
    'Attack': 'TotalAttacks',
    'Block': 'TotalBlocks',
    'Defense': 'TotalDefenses',
    'FreeBall': 'TotalFreeBalls'
}

tocchi_minimi_tot = 100
tocchi_minimi_in_ogni_variabile = 0   #Se è 0 ci saranno valori nulli nelle variabili, quindi non si può fare la PCA. A meno che non li ho sostituiti con la mediana


funzione_mostra_tutto1(df_players_3voti_centrale, dizionario_variabili_to_tocchi, tocchi_minimi_in_ogni_variabile, tocchi_minimi_tot, metodologia_voti="PerGiocatore")


















df_players_each_game3voti.columns



df_players_each_game3voti.columns

dizionario_variabili_to_tocchi = {
    'Serve': 'TotalServes',
    'Reception': 'TotalReceptions',
    'Set': 'TotalSets',
    'Attack': 'TotalAttacks',
    'Block': 'TotalBlocks',
    'Defense': 'TotalDefenses',
    'FreeBall': 'TotalFreeBalls'
}

tocchi_minimi_tot = 10
tocchi_minimi_in_ogni_variabile = 0   #Se è 0 ci saranno valori nulli nelle variabili, quindi non si può fare la PCA


funzione_mostra_tutto1(df_players_each_game3voti, dizionario_variabili_to_tocchi, tocchi_minimi_in_ogni_variabile, tocchi_minimi_tot, metodologia_voti="PerRuolo")

dizionario_variabili_to_tocchi = {
    'Reception': 'TotalReceptions',
    'Set': 'TotalSets',
    'Defense': 'TotalDefenses',
    'FreeBall': 'TotalFreeBalls'
}

tocchi_minimi_tot = 5
tocchi_minimi_in_ogni_variabile = 1   #Se è 0 ci saranno valori nulli nelle variabili, quindi non si può fare la PCA


funzione_mostra_tutto1(df_players_each_game3voti_libero, dizionario_variabili_to_tocchi, tocchi_minimi_in_ogni_variabile, tocchi_minimi_tot, metodologia_voti="PerGiocatore", rimuovi_outliers=True)

dizionario_variabili_to_tocchi = {
    'Serve': 'TotalServes',
    'Reception': 'TotalReceptions',
    'Set': 'TotalSets',
    'Attack': 'TotalAttacks',
    'Block': 'TotalBlocks',
    'Defense': 'TotalDefenses'
    #'FreeBall': 'TotalFreeBalls'
}

tocchi_minimi_tot = 30
tocchi_minimi_in_ogni_variabile = 0   #Se è 0 ci saranno valori nulli nelle variabili, quindi non si può fare la PCA


funzione_mostra_tutto1(df_players_each_game3voti_schiacciatore, dizionario_variabili_to_tocchi, tocchi_minimi_in_ogni_variabile, tocchi_minimi_tot, metodologia_voti="PerGiocatore", rimuovi_outliers=True)













from great_tables import GT
import gt_extras as gte

# Seleziona subset utile (primi 10 giocatori, solo colonne tecniche)
df_show = df_players1[[
    "Nome", "Cognome", "RuoloDescr",
    "Serve", "Reception", "Set", "Attack", "Block", "Defense", "FreeBall"
]].copy().head(10)

# Aggiungiamo una colonna "NomeCompleto" da usare come stub
df_show["NomeCompleto"] = df_show["Nome"] + " " + df_show["Cognome"]

# Costruzione della tabella GT
(
    GT(df_show, rowname_col="NomeCompleto")
    .tab_stubhead(label="Giocatore")
    .cols_hide(["Nome", "Cognome"])  # Nasconde colonne non necessarie
    .cols_align("center")
    .tab_header(title="Statistiche Tecniche Giocatori", subtitle="Esempio con gt_extras")

    # Applicazioni grafiche
    .pipe(gte.gt_plt_bar, columns=["Serve", "Reception", "Set", "Attack", "Block", "Defense", "FreeBall"])
    .pipe(gte.gt_theme_538)
)


import pandas as pd
import numpy as np
from IPython.display import display, HTML

def create_gt_style_table(df, numeric_cols=None, max_rows=20, sort_by=None, ascending=False):
    """
    Crea una tabella HTML con barre orizzontali integrate nelle celle
    
    Parameters:
    - df: DataFrame da visualizzare
    - numeric_cols: lista delle colonne numeriche da convertire in barre
    - max_rows: numero massimo di righe da mostrare
    - sort_by: colonna per l'ordinamento (None = nessun ordinamento)
    - ascending: True per crescente, False per decrescente
    """
    
    # Prepara i dati
    df_work = df.copy()
    
    # Applica l'ordinamento se richiesto
    if sort_by and sort_by in df_work.columns:
        df_work = df_work.sort_values(sort_by, ascending=ascending, na_position='last')
    
    df_display = df_work.head(max_rows).copy()
    
    # Auto-detect colonne numeriche se non specificate
    if numeric_cols is None:
        numeric_cols = df_display.select_dtypes(include=[np.number]).columns.tolist()
        # Rimuovi colonne con troppi NaN
        numeric_cols = [col for col in numeric_cols if df_display[col].notna().sum() > len(df_display) * 0.3]
    
    # Seleziona colonne di identificazione
    id_cols = []
    for col in ['Nome', 'Cognome', 'RuoloDescr', 'Nazionalità']:
        if col in df_display.columns:
            id_cols.append(col)
    
    # Combina colonne
    display_cols = id_cols + numeric_cols
    df_subset = df_display[display_cols].copy()
    
    # Normalizza i valori numerici per le barre (sempre sui dati originali completi)
    normalized_df = df_subset.copy()
    for col in numeric_cols:
        if col in df_subset.columns:
            # Usa TUTTI i dati originali per calcolare min/max
            col_data = df[col].dropna()
            if len(col_data) > 0:
                min_val, max_val = col_data.min(), col_data.max()
                if max_val != min_val:
                    normalized_df[col] = (df_subset[col] - min_val) / (max_val - min_val)
                else:
                    normalized_df[col] = 0.5
    
    def get_color_gradient(val):
        """Calcola il colore nella palette continua rosso-verde"""
        if pd.isna(val):
            return 'rgba(240, 240, 240, 0.8)'
        
        val = max(0, min(1, val))
        
        if val <= 0.5:
            # Da rosso a giallo (0 -> 0.5)
            ratio = val * 2
            r = int(220 + (255 - 220) * ratio)
            g = int(53 + (193 - 53) * ratio)
            b = int(69 + (7 - 69) * ratio)
        else:
            # Da giallo a verde (0.5 -> 1)
            ratio = (val - 0.5) * 2
            r = int(255 + (40 - 255) * ratio)
            g = int(193 + (167 - 193) * ratio)
            b = int(7 + (69 - 7) * ratio)
        
        return f'rgb({r}, {g}, {b})'
    
    def create_bar_cell(value, normalized_value):
        if pd.isna(value):
            return '<span style="color: #999; font-style: italic;">N/A</span>'
        
        color = get_color_gradient(normalized_value)
        bar_width = max(5, normalized_value * 80)  # Minimo 5%, massimo 80%
        
        return f'''
        <div style="position: relative; width: 100%; height: 25px; display: flex; align-items: center;">
            <div style="
                position: absolute;
                left: 0;
                top: 2px;
                height: 21px;
                width: {bar_width}%;
                background-color: {color};
                border-radius: 3px;
                opacity: 0.7;
            "></div>
            <span style="
                position: relative;
                z-index: 10;
                margin-left: 5px;
                font-weight: 600;
                font-size: 12px;
                color: #333;
            ">{value:.1f}</span>
        </div>
        '''
    
    # Crea le celle HTML
    html_data = []
    for idx, row in df_subset.iterrows():
        html_row = []
        for col in display_cols:
            if col in numeric_cols:
                normalized_val = normalized_df.loc[idx, col] if not pd.isna(normalized_df.loc[idx, col]) else 0
                cell_html = create_bar_cell(row[col], normalized_val)
            else:
                cell_html = str(row[col]) if pd.notna(row[col]) else 'N/A'
            html_row.append(cell_html)
        html_data.append(html_row)
    
    # Crea la tabella HTML
    table_id = f"gt-table-{hash(str(sort_by) + str(ascending)) % 10000}"
    html_table = f'<table id="{table_id}" class="gt-table">'
    
    # Header con indicatore di ordinamento
    html_table += '<thead><tr>'
    for col in display_cols:
        header_text = col
        if col == sort_by:
            arrow = "⬆️" if ascending else "⬇️"
            header_text = f"{col} {arrow}"
        html_table += f'<th>{header_text}</th>'
    html_table += '</tr></thead>'
    
    # Body
    html_table += '<tbody>'
    for row in html_data:
        html_table += '<tr>'
        for cell in row:
            html_table += f'<td>{cell}</td>'
        html_table += '</tr>'
    html_table += '</tbody></table>'
    
    # Aggiungi info sull'ordinamento
    sort_info = ""
    if sort_by:
        direction = "crescente" if ascending else "decrescente"
        sort_info = f'<p style="text-align: center; margin: 10px 0; font-style: italic; color: #666;">Ordinato per <strong>{sort_by}</strong> ({direction})</p>'
    
    # CSS styling
    css = '''
    <style>
    .gt-table {
        border-collapse: collapse;
        margin: 20px auto;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        font-size: 14px;
        width: 100%;
        max-width: 1400px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        overflow: hidden;
    }
    
    .gt-table thead {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .gt-table th {
        padding: 15px 12px;
        text-align: left;
        font-weight: 600;
        letter-spacing: 0.5px;
        border: none;
    }
    
    .gt-table td {
        padding: 12px;
        border-bottom: 1px solid #e1e5e9;
        vertical-align: middle;
    }
    
    .gt-table tbody tr:nth-child(even) {
        background-color: #f8f9fa;
    }
    
    .gt-table tbody tr:hover {
        background-color: #e3f2fd;
        transition: background-color 0.2s ease;
    }
    
    .gt-table tbody tr:last-child td {
        border-bottom: none;
    }
    </style>
    '''
    
    return HTML(css + sort_info + html_table)

class SortableTable:
    """Classe per gestire una tabella ordinabile"""
    
    def __init__(self, df, numeric_cols=None, max_rows=20):
        self.df = df
        self.numeric_cols = numeric_cols
        self.max_rows = max_rows
        
        # Auto-detect colonne se non specificate
        if self.numeric_cols is None:
            self.numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
            self.numeric_cols = [col for col in self.numeric_cols if df[col].notna().sum() > len(df) * 0.3]
        
        # Colonne disponibili per l'ordinamento
        id_cols = []
        for col in ['Nome', 'Cognome', 'RuoloDescr', 'Nazionalità']:
            if col in df.columns:
                id_cols.append(col)
        
        self.sortable_cols = id_cols + self.numeric_cols
        
        print(f"📊 Tabella creata con {len(df)} righe")
        print(f"🔢 Colonne numeriche: {self.numeric_cols}")
        print(f"📋 Colonne ordinabili: {self.sortable_cols}")
    
    def show(self, sort_by=None, ascending=False):
        """Mostra la tabella ordinata"""
        return create_gt_style_table(
            self.df, 
            numeric_cols=self.numeric_cols, 
            max_rows=self.max_rows,
            sort_by=sort_by,
            ascending=ascending
        )
    
    def by_column(self, column, ascending=False):
        """Ordina per una specifica colonna"""
        if column not in self.sortable_cols:
            print(f"❌ Colonna '{column}' non disponibile!")
            print(f"✅ Colonne disponibili: {self.sortable_cols}")
            return None
        
        direction = "crescente" if ascending else "decrescente"
        print(f"🔄 Ordinamento per '{column}' ({direction})")
        return self.show(sort_by=column, ascending=ascending)
    
    def top_by(self, column):
        """Mostra i valori più alti di una colonna"""
        return self.by_column(column, ascending=False)
    
    def bottom_by(self, column):
        """Mostra i valori più bassi di una colonna"""
        return self.by_column(column, ascending=True)

def create_sortable_table(df, cols=None, rows=20):
    """
    Crea una tabella ordinabile facilmente utilizzabile
    """
    if cols is None:
        # Auto-selezione intelligente
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        good_cols = []
        
        for col in numeric_cols:
            coverage = df[col].notna().sum() / len(df)
            if coverage > 0.4:  # Almeno 40% di dati
                good_cols.append(col)
        
        cols = good_cols[:6]  # Massimo 6 per leggibilità
    
    return SortableTable(df, numeric_cols=cols, max_rows=rows)

# Esempi di utilizzo
print("✅ Funzioni create con successo!")
print("\n🚀 Uso raccomandato:")
print("# Crea la tabella ordinabile")
print("table = create_sortable_table(df_players1, rows=20)")
print("")
print("# Ordina per colonna specifica")
print("display(table.by_column('TotalTouch'))  # Decrescente")
print("display(table.by_column('TotalTouch', ascending=True))  # Crescente")
print("")
print("# Scorciatoie utili")
print("display(table.top_by('Attack'))    # I migliori attaccanti")
print("display(table.bottom_by('Serve'))  # I peggiori al servizio")
print("")
print("📊 Uso personalizzato:")
print("stats = ['TotalTouch', 'Serve', 'Attack', 'Block']")
print("table = create_sortable_table(df_players1, cols=stats, rows=15)")
print("display(table.top_by('Attack'))")













# Trova i NumeroMaglia_ID che non sono in PlayerID
ids_non_presenti = set(df_rilevations["NumeroMaglia_ID"].unique()) - set(df_players["PlayerID"].unique())
ids_non_presenti_int = [int(x) for x in ids_non_presenti if pd.notnull(x)]
ids_non_presenti_int

# Filtra df_players_each_game per questi PlayerID
df_players_each_game_ids_non_presenti = df_players_each_game[df_players_each_game["PlayerID"].isin(ids_non_presenti_int)]

df_players_each_game_ids_non_presenti







