@echo off
REM Script di installazione per Volleyball Scraper (Windows)
REM Questo script installa tutte le dipendenze necessarie per eseguire Volleyball Scraper

echo === Volleyball Scraper - Script di installazione per Windows ===
echo Installazione delle dipendenze necessarie...

REM Verifica se pip è installato
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python/pip non trovato. Assicurati di avere Python installato e aggiunto al PATH.
    echo Puoi scaricare Python da https://www.python.org/downloads/
    pause
    exit /b 1
)

REM Installa le dipendenze Python
echo Installazione delle librerie Python...
pip install playwright requests beautifulsoup4

REM Installa i browser per Playwright
echo Installazione dei browser per Playwright...
python -m playwright install chromium

REM Crea le directory necessarie
echo Creazione delle directory necessarie...
if not exist "%USERPROFILE%\Documents\VolleyballScraper\data" mkdir "%USERPROFILE%\Documents\VolleyballScraper\data"
if not exist "%USERPROFILE%\Documents\VolleyballScraper\data\results" mkdir "%USERPROFILE%\Documents\VolleyballScraper\data\results"

echo Installazione completata!
echo.
echo Per avviare l'applicazione con interfaccia grafica:
echo python volleyball_scraper_gui_windows.py
echo.
echo Per utilizzare la versione a riga di comando:
echo python volleyball_scraper_windows.py Nome Cognome
echo.
echo Esempio:
echo python volleyball_scraper_windows.py Wilfredo Leon
echo.
pause
