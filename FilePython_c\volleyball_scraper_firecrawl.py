#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Volleyball Scraper - Programma per estrarre dati dei giocatori da volleybox.net
Versione che utilizza FireCrawl per aggirare Cloudflare
"""

import sys
import re
import unicodedata
import time
import json
import random
import os
from bs4 import BeautifulSoup
from pathlib import Path
from firecrawl import FirecrawlApp

import pandas as pd
from sqlalchemy import create_engine

# Directory per salvare i dati
DATA_DIR = Path(os.path.expanduser("~")) / "Documents" / "ModenaVolley" / "VolleyballScraper" / "data"
DATA_DIR.mkdir(parents=True, exist_ok=True)



#Metti nel db (o file csv) anche l'url che hai scelto e usato dal file txt
# Salva nel db (o file csv) anche l'immagine del giocatore trovato come url






# API key per FireCrawl
FIRECRAWL_API_KEY_list = ['fc-ea29b7d09070479391deb6f6a76af1fb',
                          'fc-6b1767fcf6284e88912efd7212c2c817',
                          'fc-d348e7fa8e0d473ab29f928f79acae06',
                          'fc-c313302652d04cb0ac9f670a3f755853',
                          'fc-065e987a5d5a45418fed2a090eaab954',
                          'fc-a3d2dc30b80246c1815115c455435611',
                          'fc-11275d4d1cae4e429e40d46c3bbab2ac',
                          'fc-6ae90377fefc49a2a59e6046e5f14208',
                          'fc-38b6a8dc483741dfb73dfcc4d65ea170',
                          'fc-b8576cd10ca04d738982ecc5c2b1ee6b',
                          'fc-ee8045c466054e2fa8488731e9a7ed08',
                          'fc-5ced641220c54e488e5131bcab5d2ff4',
                          'fc-6b1e41cfd57d434d84073b1392283964',
                          'fc-0089ce8214d5406fb043d2a1dfa6c728',
                          'fc-05f9a3175fb14f4595cf6db25ed68bc2',
                          'fc-870ce0c3f45b4e1cba3401185f7c1bcf',
                          'fc-66662cb21d4242a39d3729442dd5c843',
                          'fc-7d3847e6129240cea2a6bd5ea7801ded',
                          'fc-a38b5ff2bc0f4168821bd4fce94bb3e1']   #Se dice crediti insufficenti, usa la prossima chiave API. Nuove chiavi le ottieni creando un altro account. Un nuovo account lo crei da siti come https://temp-mail.io/it/message/1c218ff1-fd22-4259-84ce-02becc93a1cf



def normalize_name(name):
    """
    Normalizza il nome per la ricerca.
    Rimuove accenti, converte in minuscolo.
    Per i nomi (non cognomi) prende solo la prima parola.

    Args:
        name (str): Nome da normalizzare
        is_surname (bool): True se è un cognome, False se è un nome
    """
    if name is None:
        print("Il nome è NULL")
        raise ValueError
        
    # Rimuovi accenti e caratteri speciali
    name = unicodedata.normalize('NFKD', str(name)).encode('ASCII', 'ignore').decode('utf-8')  #rimuovo gli accenti
    name = name.replace("'", "")   #rimuovo l'apostrofo
    
    name = name.lower().strip()    # converti in minuscolo e rimuovi spazi iniziali/finali
    
    # prendo solo la prima parola
    primo_nome = name.split(" ")[0]

    # Sostituisco gli spazi con trattini
    nome_full = name.replace(" ", "-")
    
    return primo_nome, nome_full

def extract_player_data_from_html(html_content):
    """
    Estrae i dati del giocatore da una pagina HTML usando BeautifulSoup.

    Args:
        html_content (str): Contenuto HTML della pagina del giocatore

    Returns:
        dict: Dizionario con i dati del giocatore
    """
    player_info = {
        "nazionalità": None,
        "posizione": None,
        "data_di_nascita": None,
        "altezza": None,
        "peso": None,
        "schiacciata": None,
        "muro": None,
        "mano_dominante": None,
        "errore": None
    }

    try:
        # Analizza l'HTML con BeautifulSoup
        soup = BeautifulSoup(html_content, 'html.parser')

        # Cerca il div con classe "new_box pRelative" che contiene i dati del giocatore
        player_data_div = soup.find('div', class_='new_box pRelative')

        if player_data_div:
            print("Div 'new_box pRelative' trovato con BeautifulSoup")

            # Cerca tutte le coppie dt/dd all'interno del div
            dts = player_data_div.find_all('dt', class_='info-header')
            dds = player_data_div.find_all('dd', class_='info-data')

            if dts and dds:
                print(f"Trovati {len(dts)} elementi dt e {len(dds)} elementi dd")

                for i in range(min(len(dts), len(dds))):
                    label = dts[i].get_text().strip().lower()
                    value = dds[i].get_text().strip()

                    print(f"Coppia {i}: '{label}' = '{value}'")

                    if "nazionalità" in label:
                        player_info["nazionalità"] = value
                    elif "posizione" in label:
                        player_info["posizione"] = value
                    elif "data di nascita" in label:
                        player_info["data_di_nascita"] = value
                    elif "altezza" in label:
                        player_info["altezza"] = value
                    elif "peso" in label:
                        player_info["peso"] = value
                    elif "schiacciata" in label:
                        player_info["schiacciata"] = value
                    elif "muro" in label:
                        player_info["muro"] = value
                    elif "mano dominante" in label:
                        player_info["mano_dominante"] = value
            else:
                print("Nessun elemento dt/dd trovato nel div")
        else:
            print("Div 'new_box pRelative' non trovato con BeautifulSoup")

            # Prova a cercare in modo più generico
            print("Tentativo di ricerca più generico...")

            # Cerca qualsiasi div che potrebbe contenere informazioni sul giocatore
            info_divs = soup.find_all('div', class_=lambda c: c and ('info' in c.lower() or 'player' in c.lower() or 'data' in c.lower()))

            if info_divs:
                print(f"Trovati {len(info_divs)} div potenzialmente rilevanti")

                for div in info_divs:
                    # Cerca coppie di etichette e valori
                    labels = div.find_all(['dt', 'th', 'strong', 'label'])
                    values = div.find_all(['dd', 'td', 'span'])

                    if labels and values and len(labels) == len(values):
                        print(f"Trovate {len(labels)} coppie etichetta-valore")

                        for i in range(len(labels)):
                            label = labels[i].get_text().strip().lower()
                            value = values[i].get_text().strip()

                            print(f"Coppia {i}: '{label}' = '{value}'")

                            if "nazionalità" in label or "nationality" in label:
                                player_info["nazionalità"] = value
                            elif "posizione" in label or "position" in label:
                                player_info["posizione"] = value
                            elif "data di nascita" in label or "birth" in label:
                                player_info["data_di_nascita"] = value
                            elif "altezza" in label or "height" in label:
                                player_info["altezza"] = value
                            elif "peso" in label or "weight" in label:
                                player_info["peso"] = value
                            elif "schiacciata" in label or "spike" in label:
                                player_info["schiacciata"] = value
                            elif "muro" in label or "block" in label:
                                player_info["muro"] = value
                            elif "mano dominante" in label or "hand" in label:
                                player_info["mano_dominante"] = value

        # Verifica se abbiamo trovato almeno un dato
        found_data = any([
            player_info["nazionalità"],
            player_info["posizione"],
            player_info["data_di_nascita"],
            player_info["altezza"],
            player_info["peso"],
            player_info["schiacciata"],
            player_info["muro"],
            player_info["mano_dominante"]
        ])

        if not found_data:
            print("ATTENZIONE: Nessun dato trovato per il giocatore!")

            # Salva l'HTML per debug
            with open("debug_page.html", "w", encoding="utf-8") as f:
                f.write(html_content)
            print("HTML della pagina salvato in debug_page.html per analisi")
        else:
            print("Dati estratti con successo!")

    except Exception as e:
        player_info["errore"] = f"Errore nell'estrazione dati: {str(e)}"
        print(f"Eccezione durante l'estrazione: {str(e)}")

        # Salva l'HTML per debug in caso di errore
        try:
            with open("error_page.html", "w", encoding="utf-8") as f:
                f.write(html_content)
            print("HTML della pagina con errore salvato in error_page.html")
        except:
            pass

    return player_info

def get_player_info_with_firecrawl(url):
    """
    Estrae le informazioni di un giocatore da un URL di volleybox.net usando FireCrawl.

    Args:
        url (str): URL del giocatore

    Returns:
        dict: Dizionario contenente le informazioni del giocatore
    """
    # Inizializza il dizionario per i risultati
    player_info = {
        "url": url,
        "nazionalità": None,
        "posizione": None,
        "data_di_nascita": None,
        "altezza": None,
        "peso": None,
        "schiacciata": None,
        "muro": None,
        "mano_dominante": None,
        "errore": None
    }

    # Funzione per aggiornare lo stato
    def update_status(message):
        print(message)

    try:
        # Modifica l'URL per andare alla pagina principale del giocatore invece che alla pagina dei club
        # L'URL originale è del tipo https://volleybox.net/it/nome-cognome-pXXXXX/clubs
        # Vogliamo https://volleybox.net/it/nome-cognome-pXXXXX
        player_url = url.replace("/clubs", "")
        update_status(f"URL modificato: {player_url}")

        # Inizializza FireCrawl
        app = FirecrawlApp(api_key=FIRECRAWL_API_KEY_list[0])

        # Esegui il crawling della pagina del giocatore
        update_status(f"Scaricando {player_url} con FireCrawl...")

        # Avvia il crawling
        crawl_result = app.crawl_url(player_url, params={
            'limit': 1,  # Solo la pagina specificata
            'scrapeOptions': {
                'formats': ['markdown']  # Richiediamo il markdown invece dell'HTML
            }
        })
        
        debug_dir = Path("C:/Users/<USER>/Documents/ModenaVolley/Codice/FirecrawlResultDebugTxt")
        debug_dir.mkdir(parents=True, exist_ok=True)  # Crea la directory se non esiste

        # Salva il risultato completo per debug
        debug_file = debug_dir / f"firecrawl_result_{player_url.split('/')[-1]}.txt"
        with open(debug_file, "w", encoding="utf-8") as f:
            f.write(str(crawl_result))
        update_status(f"Risultato completo salvato in {debug_file} per debug")

        # Estrai le informazioni dai metadati
        update_status("Estraendo le informazioni dai metadati...")

        # Verifica se abbiamo i dati
        if hasattr(crawl_result, 'data') and crawl_result.data:
            # Cerca nei documenti
            for doc in crawl_result.data:
                # Verifica se abbiamo i metadati
                if hasattr(doc, 'metadata') and doc.metadata:
                    metadata = doc.metadata

                    # Estrai le informazioni dai metadati
                    #update_status("Metadati trovati, estraendo le informazioni...")
                    
                    # Gestisci il caso in cui description potrebbe essere una lista
                    # Estrai la nazionalità dal titolo o dalla descrizione
                    description = metadata.get('description', '')
                    if isinstance(description, list):
                        description = ' '.join(description)  # Unisci gli elementi della lista

                    if 'pallavolista da ' in description:
                        # Prendi tutto fino alla prossima frase o punto
                        country_part = description.split('pallavolista da ')[1].split('.')[0].split(' che ')[0].strip()
                        player_info["nazionalità"] = country_part
                        #update_status(f"Nazionalità trovata: {country_part}")

                    # Estrai la posizione
                    if 'palleggiatore' in description.lower():
                        player_info["posizione"] = "Palleggiatore"
                        #update_status("Posizione trovata: Palleggiatore")
                    elif 'schiacciatore' in description.lower():
                        player_info["posizione"] = "Schiacciatore"
                        #update_status("Posizione trovata: Schiacciatore")
                    elif 'centrale' in description.lower():
                        player_info["posizione"] = "Centrale"
                        #update_status("Posizione trovata: Centrale")
                    elif 'libero' in description.lower():
                        player_info["posizione"] = "Libero"
                        #update_status("Posizione trovata: Libero")
                    elif 'opposto' in description.lower():
                        player_info["posizione"] = "Opposto"
                        #update_status("Posizione trovata: Opposto")

                    # Estrai la data di nascita
                    if 'nato il ' in description:
                        dob_part = description.split('nato il ')[1].split(')')[0]
                        player_info["data_di_nascita"] = dob_part
                        #update_status(f"Data di nascita trovata: {dob_part}")

                # Verifica se abbiamo il markdown
                if hasattr(doc, 'markdown') and doc.markdown:
                    markdown_content = doc.markdown
                    
                    markdown_dir = Path("C:/Users/<USER>/Documents/ModenaVolley/Codice/FirecrawlMarkdown")
                    markdown_dir.mkdir(parents=True, exist_ok=True)  # Crea la directory se non esiste

                    # Salva il markdown per debug
                    markdown_file = markdown_dir / f"firecrawl_markdown_{player_url.split('/')[-1]}.md"
                    with open(markdown_file, "w", encoding="utf-8") as f:
                        f.write(markdown_content)
                    #update_status(f"Markdown salvato in {markdown_file} per debug")

                    # Estrai le informazioni dal markdown
                    #update_status("Estraendo le informazioni dal markdown...")

                    # Cerca le informazioni nel markdown
                    if "Nazionalità" in markdown_content and player_info["nazionalità"] is None:
                        lines = markdown_content.split('\n')
                        for i, line in enumerate(lines):
                            if "Nazionalità" in line and i+1 < len(lines):
                                # La nazionalità è nella riga successiva o nella stessa riga
                                if "Portogallo" in line:
                                    player_info["nazionalità"] = "Portogallo"
                                elif "Italia" in line:
                                    player_info["nazionalità"] = "Italia"
                                elif i+1 < len(lines) and "Portogallo" in lines[i+1]:
                                    player_info["nazionalità"] = "Portogallo"
                                elif i+1 < len(lines) and "Italia" in lines[i+1]:
                                    player_info["nazionalità"] = "Italia"
                                #update_status(f"Nazionalità trovata nel markdown: {player_info['nazionalità']}")

                    if "Posizione" in markdown_content and player_info["posizione"] is None:
                        lines = markdown_content.split('\n')
                        for i, line in enumerate(lines):
                            if "Posizione" in line and i+1 < len(lines):
                                # La posizione è nella riga successiva o nella stessa riga
                                if "Palleggiatore" in line:
                                    player_info["posizione"] = "Palleggiatore"
                                elif "Schiacciatore" in line:
                                    player_info["posizione"] = "Schiacciatore"
                                elif "Centrale" in line:
                                    player_info["posizione"] = "Centrale"
                                elif "Libero" in line:
                                    player_info["posizione"] = "Libero"
                                elif "Opposto" in line:
                                    player_info["posizione"] = "Opposto"
                                elif i+1 < len(lines):
                                    next_line = lines[i+1]
                                    if "Palleggiatore" in next_line:
                                        player_info["posizione"] = "Palleggiatore"
                                    elif "Schiacciatore" in next_line:
                                        player_info["posizione"] = "Schiacciatore"
                                    elif "Centrale" in next_line:
                                        player_info["posizione"] = "Centrale"
                                    elif "Libero" in next_line:
                                        player_info["posizione"] = "Libero"
                                    elif "Opposto" in next_line:
                                        player_info["posizione"] = "Opposto"
                                #update_status(f"Posizione trovata nel markdown: {player_info['posizione']}")

                    if "Data di nascita" in markdown_content and player_info["data_di_nascita"] is None:
                        lines = markdown_content.split('\n')
                        for i, line in enumerate(lines):
                            if "Data di nascita" in line:
                                # La data è nella stessa riga
                                if "-" in line:
                                    parts = line.split("Data di nascita")
                                    if len(parts) > 1:
                                        date_part = parts[1].strip()
                                        player_info["data_di_nascita"] = date_part
                                        #update_status(f"Data di nascita trovata nel markdown: {date_part}")

                    if "Altezza" in markdown_content and player_info["altezza"] is None:
                        lines = markdown_content.split('\n')
                        for i, line in enumerate(lines):
                            if "Altezza" in line:
                                # L'altezza è nella stessa riga
                                if "cm" in line:
                                    parts = line.split("Altezza")
                                    if len(parts) > 1:
                                        height_part = parts[1].strip()
                                        player_info["altezza"] = height_part
                                        #update_status(f"Altezza trovata nel markdown: {height_part}")

                    if "Peso" in markdown_content and player_info["peso"] is None:
                        lines = markdown_content.split('\n')
                        for i, line in enumerate(lines):
                            if "Peso" in line:
                                # Il peso è nella stessa riga
                                if "kg" in line:
                                    parts = line.split("Peso")
                                    if len(parts) > 1:
                                        weight_part = parts[1].strip()
                                        player_info["peso"] = weight_part
                                        #update_status(f"Peso trovato nel markdown: {weight_part}")

                    if "Schiacciata" in markdown_content and player_info["schiacciata"] is None:
                        lines = markdown_content.split('\n')
                        for i, line in enumerate(lines):
                            if "Schiacciata" in line:
                                # La schiacciata è nella stessa riga
                                if "cm" in line:
                                    parts = line.split("Schiacciata")
                                    if len(parts) > 1:
                                        spike_part = parts[1].strip()
                                        player_info["schiacciata"] = spike_part
                                        #update_status(f"Schiacciata trovata nel markdown: {spike_part}")

                    if "Muro" in markdown_content and player_info["muro"] is None:
                        lines = markdown_content.split('\n')
                        for i, line in enumerate(lines):
                            if "Muro" in line:
                                # Il muro è nella stessa riga
                                if "cm" in line:
                                    parts = line.split("Muro")
                                    if len(parts) > 1:
                                        block_part = parts[1].strip()
                                        player_info["muro"] = block_part
                                        #update_status(f"Muro trovato nel markdown: {block_part}")

        # Verifica se abbiamo trovato almeno un dato
        found_data = any([
            player_info["nazionalità"],
            player_info["posizione"],
            player_info["data_di_nascita"],
            player_info["altezza"],
            player_info["peso"],
            player_info["schiacciata"],
            player_info["muro"],
            player_info["mano_dominante"]
        ])

        if not found_data:
            player_info["errore"] = "Nessun dato trovato nei risultati di FireCrawl"
            update_status("Nessun dato trovato nei risultati di FireCrawl")

    except Exception as e:
        player_info["errore"] = f"Errore durante lo scraping con FireCrawl: {str(e)}"
        update_status(f"Errore durante lo scraping con FireCrawl: {str(e)}")

        # Stampa il traceback per debug
        import traceback
        traceback.print_exc()

    update_status("Estrazione completata")
    return player_info

def get_player_info_from_url(url):
    """
    Estrae le informazioni di un giocatore da un URL di volleybox.net.
    Questa funzione è un wrapper per get_player_info_with_firecrawl.

    Args:
        url (str): URL del giocatore

    Returns:
        dict: Dizionario contenente le informazioni del giocatore
    """
    return get_player_info_with_firecrawl(url)

def find_player_url(nome, cognome, url_file_path="url_giocatori.txt"):
    """
    Cerca l'URL di un giocatore nel file url_giocatori.txt basandosi sul nome e cognome normalizzati.

    Args:
        nome (str): Nome del giocatore
        cognome (str): Cognome del giocatore
        url_file_path (str): Percorso del file contenente gli URL

    Returns:
        str or None: URL del giocatore se trovato, altrimenti None
    """
    # Normalizza nome e cognome
    nome_norm_singolo, nome_norm_full = normalize_name(nome)  # normalizzo il nome prendendo sia solo il primo, che anche i secondi nomi (se ci sono)
    cognome_norm_singolo, cognome_norm_full = normalize_name(cognome) 
    
    # Crea tutte le possibili combinazioni di nome-cognome
    combinations = [
        f"{nome_norm_singolo}-{cognome_norm_full}",
        f"{nome_norm_full}-{cognome_norm_full}",
        f"{nome_norm_singolo}-{cognome_norm_singolo}",
        f"{nome_norm_full}-{cognome_norm_singolo}"
    ]

    try:
        with open(url_file_path, 'r', encoding='utf-8') as file:
            for line in file:
                url = line.strip()
                # Estrai la parte del nome dall'URL
                url_parts = url.split('/')
                if len(url_parts) >= 5:
                    player_part = url_parts[4]  # La parte con nome-cognome
                    player_part_lower = player_part.lower()
                    
                    # Verifica tutte le combinazioni
                    for combo in combinations:
                        if combo in player_part_lower:
                            print(f"Match trovato: {player_part}")
                            return url
                    
                    
                    # Cerca anche corrispondenze parziali o con ordine invertito
                    '''
                    if f"{nome_norm}-{cognome_norm}" in player_part_lower in player_part_lower:
                        print(f"Match con pattern trovato: {player_part}")
                        return url
                    '''
                    
    except Exception as e:
        print(f"Errore nella lettura del file URL: {str(e)}")

    return None

def process_players_dataframe(limit=None):
    """
    Legge il dataframe dalla tabella players_latest filtrando solo le righe che hanno
    tutti i valori nulli nelle colonne di interesse (Nazionalità, DataNascita, Altezza, ecc.),
    cerca gli URL dei giocatori, estrae le informazioni e aggiorna il dataframe.

    Args:
        limit (int, optional): Numero massimo di giocatori da elaborare. Se None, elabora tutti.

    Returns:
        pandas.DataFrame: Il dataframe aggiornato con i dati dei giocatori che avevano valori nulli
    """
    # Crea un engine per poter usare pandas
    engine = create_engine('postgresql+psycopg://postgres:AcquaLevissima1@localhost:5432/db_modena')

    # Leggi solo le righe dalla tabella players_latest che hanno tutti i valori nulli nelle colonne di interesse
    df_players_latest = pd.read_sql_query("""
    SELECT * FROM players_latest
    WHERE "Nazionalità" IS NULL
    AND "DataNascita" IS NULL
    AND "Altezza" IS NULL
    AND "Peso" IS NULL
    AND "Schiacciata" IS NULL
    AND "Muro" IS NULL
    AND "ManoDominante" IS NULL
    """, engine)

    # Applica il limite se specificato
    if limit is not None and limit > 0 and limit < len(df_players_latest):
        print(f"Limitando l'elaborazione a {limit} giocatori dei {len(df_players_latest)} trovati")
        df_players_latest = df_players_latest.head(limit)

    print(f"Lette {len(df_players_latest)} righe dalla tabella players_latest con tutti i valori nulli nelle colonne di interesse")
    
    # Salva il dataframe con le righe aggiornate come CSV
    csv_path = DATA_DIR / f"players_updated_2.csv"


    # Itera su ogni riga del dataframe
    for index, row in df_players_latest.iterrows():
        nome = row["Nome"]
        cognome = row["Cognome"]

        print(f"\nElaborazione di {nome} {cognome}...")

        # Cerca l'URL del giocatore
        player_url = find_player_url(nome, cognome)

        if player_url:
            print(f"URL trovato per {nome} {cognome}: {player_url}")

            # Aggiungi un ritardo casuale tra le richieste per evitare di essere bloccati
            if index > 0:  # Non ritardare la prima richiesta
                delay = random.uniform(3, 4)  # Ritardo casuale tra 4 e 5 secondi (FireCrawl ha già rate limiting)
                print(f"Attendo {delay:.1f} secondi prima della prossima richiesta...")
                time.sleep(delay)

            # Ottieni i dati del giocatore
            player_info = get_player_info_from_url(player_url)

            # Aggiorna le colonne del dataframe con i dati ottenuti
            if not player_info["errore"]:
                print(f"DATI ESTRATTI PER {nome} {cognome}:")

                # Stampa i dati trovati
                print("=" * 40)
                print(f"Nazionalità: {player_info['nazionalità']}")
                print(f"Posizione: {player_info['posizione']}")
                print(f"Data di nascita: {player_info['data_di_nascita']}")
                print(f"Altezza: {player_info['altezza']}")
                print(f"Peso: {player_info['peso']}")
                print(f"Schiacciata: {player_info['schiacciata']}")
                print(f"Muro: {player_info['muro']}")
                print(f"Mano dominante: {player_info['mano_dominante']}")
                print("=" * 40)

                # Aggiorna le colonne del dataframe
                df_players_latest.at[index, "Nazionalità"] = player_info["nazionalità"]
                df_players_latest.at[index, "DataNascita"] = player_info["data_di_nascita"]
                df_players_latest.at[index, "Altezza"] = player_info["altezza"]
                df_players_latest.at[index, "Peso"] = player_info["peso"]
                df_players_latest.at[index, "Schiacciata"] = player_info["schiacciata"]
                df_players_latest.at[index, "Muro"] = player_info["muro"]
                df_players_latest.at[index, "ManoDominante"] = player_info["mano_dominante"]
                
                df_players_latest.to_csv(csv_path, index=False)
                #print(f"\nDataframe salvato come CSV in: {csv_path}")
            
            else:
                print(f"Errore per {nome} {cognome}: {player_info['errore']}")
                if "Payment Required" in player_info['errore']:
                    print("Errore di pagamento rilevato. Passo alla prossima chiave API")
                    FIRECRAWL_API_KEY_list.pop(0)  #rimuovo il primo elemento della lista, la chiave usata finora, così d'ora in avanti uso quella dopo, che adesso è il primo elemento della lista
                    
        else:
            print(f"Nessun URL trovato per {nome} {cognome}")

    # Genera un timestamp per il nome del file
    #import datetime
    #timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    
    '''
    # Stampa un riepilogo dei dati raccolti
    print("\n" + "=" * 60)
    print("RIEPILOGO DEI DATI RACCOLTI (RIGHE PRECEDENTEMENTE NULLE):")
    print("=" * 60)
    for index, row in df_players_latest.iterrows():
        print(f"{row['Nome']} {row['Cognome']}:")
        print(f"  Nazionalità: {row['Nazionalità']}")
        print(f"  Data di nascita: {row['DataNascita']}")
        print(f"  Altezza: {row['Altezza']}")
        print(f"  Peso: {row['Peso']}")
        print(f"  Schiacciata: {row['Schiacciata']}")
        print(f"  Muro: {row['Muro']}")
        print(f"  Mano dominante: {row['ManoDominante']}")
        print("-" * 40)
    '''

    print("\nNOTA: I dati sono stati salvati SOLO nel file CSV e NON sono stati aggiornati nel database.")

    return df_players_latest

def main():
    """
    Funzione principale per l'esecuzione da riga di comando.
    """
    import argparse

    # Crea un parser per gli argomenti da riga di comando
    parser = argparse.ArgumentParser(description="Volleyball Scraper - Estrai informazioni sui giocatori da volleybox.net usando FireCrawl")
    parser.add_argument("--limit", type=int, default=None, help="Limita il numero di giocatori da elaborare")
    args = parser.parse_args()

    print("Volleyball Scraper - Estrai informazioni sui giocatori da volleybox.net usando FireCrawl")
    print("-"*80)

    if args.limit:
        print(f"Elaborazione limitata a {args.limit} giocatori.")

    try:
        # Elabora il dataframe e aggiorna con i dati dei giocatori
        process_players_dataframe(limit=args.limit)
        print("\nOperazione completata con successo!")
    except KeyboardInterrupt:
        print("\nOperazione interrotta dall'utente.")
    except Exception as e:
        print(f"\nErrore durante l'esecuzione: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()




#Fai un altro file dove periodicamente (ogni 1-2 anni aggiorni solo le colonne Altezza, Peso, Schiacciata, Muro)




