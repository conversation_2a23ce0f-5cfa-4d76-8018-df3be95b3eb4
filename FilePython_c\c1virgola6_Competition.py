import numpy as np 
import pandas as pd  #Realizzato con la versione di Pandas 2.2.3
import polars as pl
import os
import psycopg
from sqlalchemy import create_engine, text


conn = psycopg.connect(
    dbname="db_modena",           # database creato in pgAdmin4
    user="postgres",              # Il tuo nome utente PostgreSQL
    password="AcquaLevissima1",   # La password che hai scelto per 'postgres'
    host="localhost",             # 'localhost' se è sul tuo PC
    port=5432                     # La porta predefinita è 5432
)

cur = conn.cursor()


cur.execute("""
SELECT DISTINCT "Campionato" FROM games;
""")

results = cur.fetchall()
print(results)


# Esegui la query per aggiornare il nome della competizione
cur.execute("""
UPDATE games 
SET "Campionato" = 'Amichevole' 
WHERE "Campionato" = 'Friendly Match';
""")
cur.execute("""
UPDATE games 
SET "Campionato" = 'Superlega' 
WHERE "Campionato" = 'Regular Season SuperLega Credem Banca - Andata';
""")
cur.execute("""
UPDATE games 
SET "Campionato" = 'Superlega' 
WHERE "Campionato" LIKE 'Superlega 20___20__' OR "Campionato" LIKE 'Superlega 20___2_';   --sostituisco le stringhe tipo 2023_2024 o 2023-24
""")
cur.execute("""
UPDATE games 
SET "Campionato" = 'Coppa Italia' 
WHERE "Campionato" LIKE 'Coppa Italia 20___20__' OR "Campionato" LIKE 'Coppa Italia 20_____'; 
""")
cur.execute("""
UPDATE games 
SET "Competition" = 'Coppa Italia' 
WHERE "Campionato" LIKE 'Coppa Italia 20___20__' OR "Campionato" LIKE 'Coppa Italia 20_____'; 
""")
cur.execute("""
UPDATE games 
SET "Campionato" = 'Supercoppa Italiana' 
WHERE "Campionato" LIKE 'Supercoppa Italiana 20__'; 
""")
cur.execute("""
UPDATE games 
SET "Competition" = 'Supercoppa Italiana' 
WHERE "Campionato" LIKE 'Supercoppa Italiana 20__'; 
""")
cur.execute("""
UPDATE games 
SET "Campionato" = 'CEV Champions League' 
WHERE "Campionato" LIKE 'CEV Champions League Volley 20__' OR "Campionato" LIKE 'Champions League 20__-__';
""")
cur.execute("""
UPDATE games 
SET "Campionato" = 'World Club Championship' 
WHERE "Campionato" LIKE 'World Club Championship 20__-__';
""")

# Commit della transazione
conn.commit()


cur.execute("""
SELECT DISTINCT "Campionato" FROM games;
""")
results = cur.fetchall()
print("Valori unici di Campionato", results)

cur.execute("""
SELECT DISTINCT "Competition" FROM games;
""")
results = cur.fetchall()
print("Valori unici di Competition", results)

cur.execute("""
SELECT DISTINCT "Phase" FROM games;
""")
results = cur.fetchall()
print("Valori unici di Phase", results)

print("Finito!")






#Visualizzo i valori unici di Phase. Dovrebbero essere solo Andata Ritorno Playoff Playoff 5 (che è Playoff 5 posto)

# Nei playoff metto e nelle coppe, in Giornata metto 
# un valore da 0 a 10 per le partite di qualificazione/preliminari (se c'è). 
# un valo da 11 a 20 per le partite di girone (se c'è). 
# un valore da 21 a 30 per i 64esimi di finale (se ci sono). 
# un valore da 31 a 40 per i 32esimi di finale (se ci sono). 
# un valore da 41 a 50 per i 16esimi di finale (se ci sono). 
# un valore da 51 a 60 per gli ottavi di finale (se ci sono). 
# un valore da 61 a 70 per i quarti di finale (se ci sono).    (es le partite da gara1 a gara5 hanno Giornata 61, 62, 63, 64, 65)
# un valore da 71 a 80 per le semifinali.
# un valore da 71 a 80 per le finali.




