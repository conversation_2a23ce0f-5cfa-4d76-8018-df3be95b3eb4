#In questo file, partiremo da rilevations_view, in particolare da HomePlayerX, e PosizPalleggCasa, per creare le colonne probHomePlayerX, ovvero i giocatori in campo mettendo il libero quando credo ci sia.
#Creeremo la view rilevations_libero_view

#Ad ogni azione trovo chi è il LiberoCandidato

#Se la squadra di casa è in P6 (PosizPalleggCasa = 6), C1 è in posto 5, quindi sostituisco HomePlayer5 con il LiberoCandidato
#Se la squadra di casa è in P5 (PosizPalleggCasa = 5), C2 è in posto 1, quindi sostituisco HomePlayer1 con il LiberoCandidato, ma SOLO se stanno battendo gli ospiti, ovvero se "whichTeamServes" = TRUE
#Se la squadra di casa è in P4 (PosizPalleggCasa = 4), C2 è in posto 6, quindi sostituisco HomePlayer6 con il LiberoCandidato
#Se la squadra di casa è in P3 (PosizPalleggCasa = 3), C2 è in posto 5, quindi sostituisco HomePlayer5 con il LiberoCandidato
#Se la squadra di casa è in P2 (PosizPalleggCasa = 2), C1 è in posto 1, quindi sostituisco HomePlayer1 con il LiberoCandidato, ma SOLO se stanno battendo gli ospiti, ovvero se "whichTeamServes" = TRUE
#Se la squadra di casa è in P1 (PosizPalleggCasa = 1), C1 è in posto 6, quindi sostituisco HomePlayer6 con il LiberoCandidato

#Se la squadra di ospite è in P6 (PosizPalleggOspite = 6), C1 è in posto 5, quindi sostituisco VisitorPlayer5 con il LiberoCandidato
#Se la squadra di ospite è in P5 (PosizPalleggOspite = 5), C2 è in posto 1, quindi sostituisco VisitorPlayer1 con il LiberoCandidato, ma SOLO se sta battendo la squadra di casa, ovvero se "whichTeamServes" = FALSE
#Se la squadra di ospite è in P4 (PosizPalleggOspite = 4), C2 è in posto 6, quindi sostituisco VisitorPlayer6 con il LiberoCandidato
#Se la squadra di ospite è in P3 (PosizPalleggOspite = 3), C2 è in posto 5, quindi sostituisco VisitorPlayer5 con il LiberoCandidato
#Se la squadra di ospite è in P2 (PosizPalleggOspite = 2), C1 è in posto 1, quindi sostituisco VisitorPlayer1 con il LiberoCandidato, ma SOLO se sta battendo la squadra di casa, ovvero se "whichTeamServes" = FALSE
#Se la squadra di ospite è in P1 (PosizPalleggOspite = 1), C1 è in posto 6, quindi sostituisco VisitorPlayer6 con il LiberoCandidato

#Come decido chi è LiberoCandidato?
#Innanzitutto devo capire chi sono i miei due liberi disponibili.
#Creo le colonne HomeLiberoDisponibile1_ID e HomeLiberoDisponibile2_ID, che contengono il PlayerID del libero disponibile della squadra di casa, e VisitorLiberoDisponibile1_ID e VisitorLiberoDisponibile2_ID, che contengono il PlayerID del libero disponibile della squadra di ospite. (se non c'è un secondo libero metto NULL)

#Creo le colonne HomeLiberoCandidato_ID e VisitorLiberoCandidato_ID in questo modo:
#HomeLiberoCandidato_ID è uguale a HomeLiberoDisponibile1_ID se NumeroMaglia_ID = HomeLiberoDisponibile1_ID, altrimenti è uguale a HomeLiberoDisponibile2_ID se NumeroMaglia_ID = HomeLiberoDisponibile2_ID, altrimenti è uguale a NULL
#Dopodichè in HomeLiberoCandidato_ID riempio ogni NULL con il primo valore NULL che trovo sotto di sé (ovvero il primo valore non NULL che ha un RilevationNumber più alto). Infine, siccome sono rimasti i NULL nelle prime azioni, riempio anche quelli.





import numpy as np 
import pandas as pd  #Realizzato con la versione di Pandas 2.2.3
import polars as pl
import os
import psycopg
from sqlalchemy import create_engine, text

conn = psycopg.connect(
    dbname="db_modena",           # database creato in pgAdmin4
    user="postgres",              # Il tuo nome utente PostgreSQL
    password="AcquaLevissima1",   # La password che hai scelto per 'postgres'
    host="localhost",             # 'localhost' se è sul tuo PC
    port=5432                     # La porta predefinita è 5432
)

# Crea un cursore per eseguire le query
cur = conn.cursor()





cur.execute("DROP MATERIALIZED VIEW IF EXISTS rilevations_libero_view CASCADE")
conn.commit()

'''
cur.execute("""
    CREATE OR REPLACE VIEW rilevations_libero_view AS
    WITH home_liberi AS (
        SELECT 
            "GameID", 
            "TeamID_auto",
            MIN("PlayerID") AS "LiberoMin_ID",
            MAX("PlayerID") AS "LiberoMax_ID"
        FROM players_each_game_view
        WHERE "RuoloCalc" = 1
        GROUP BY "GameID", "TeamID_auto"
    ),
    visitor_liberi AS (
        SELECT 
            "GameID", 
            "TeamID_auto",
            MIN("PlayerID") AS "LiberoMin_ID",
            MAX("PlayerID") AS "LiberoMax_ID"
        FROM players_each_game_view
        WHERE "RuoloCalc" = 1
        GROUP BY "GameID", "TeamID_auto"
    )
    SELECT
        r."GameID",
        r."SetNumber",
        r."ActionNumber",
        r."RilevationNumber",
        r."TouchNumber",
        r."RilevationTime",
        r."PunteggioCasa",
        r."PunteggioOspiti",
        r."EventiParticolari",
        r."whichTeam",
        r."whichTeamID",
        r."NumeroMaglia",
        r."NumeroMaglia_ID",
        r."Foundamental",
        r."Type",
        r."Eval",
        r."SetterCall",
        r."AttkCombination",
        r."TargAttk",
        r."StartZone",
        r."EndZoneEsecZone",
        r."EndSubzoneEsecSubzone",
        r."SkillType",
        r."PlayersInfo",
        r."Special",
        r."CustomChar",
        r."SideoutBreakpoint",
        r."Colonna3sez3SCOUT",
        r."Colonna4sez3SCOUT",
        r."Colonna5sez3SCOUT",
        r."Colonna6sez3SCOUT",
        r."Colonna7sez3SCOUT",
        r."IDHomeTeam_auto",
        r."IDVisitorTeam_auto",
        r."whichTeamServes",
        r."PalleggCasa",
        r."PosizPalleggCasa",
        r."PalleggOspite",
        r."PosizPalleggOspite",
        r."Pcasa1linea",
        r."Pospite1linea",
        
        -- Creo le colonne HomeLiberoDisponibile1 e HomeLiberoDisponibile2, che contengono il numero di maglia del libero disponibile della squadra di casa
        hl."LiberoMin_ID" AS "HomeLiberoDisponibile1_ID",
        hl."LiberoMax_ID" AS "HomeLiberoDisponibile2_ID",
        
        -- Creo le colonne VisitorLiberoDisponibile1 e VisitorLiberoDisponibile2, che contengono il numero di maglia del libero disponibile della squadra di ospite
        vl."LiberoMin_ID" AS "VisitorLiberoDisponibile1_ID",
        vl."LiberoMax_ID" AS "VisitorLiberoDisponibile2_ID"
        
    FROM rilevations_view r
    LEFT JOIN home_liberi hl ON hl."GameID" = r."GameID" AND hl."TeamID_auto" = r."IDHomeTeam_auto"
    LEFT JOIN visitor_liberi vl ON vl."GameID" = r."GameID" AND vl."TeamID_auto" = r."IDVisitorTeam_auto"
""")

'''



cur.execute("""
CREATE MATERIALIZED VIEW rilevations_libero_view AS
WITH home_liberi AS (
    SELECT 
        "GameID", 
        "TeamID_auto",
        MIN("PlayerID") AS "LiberoMin_ID",
        MAX("PlayerID") AS "LiberoMax_ID"
    FROM players_each_game_view
    WHERE "RuoloCalc" = 1
    GROUP BY "GameID", "TeamID_auto"
),


visitor_liberi AS (
    SELECT 
        "GameID", 
        "TeamID_auto",
        MIN("PlayerID") AS "LiberoMin_ID",
        MAX("PlayerID") AS "LiberoMax_ID"
    FROM players_each_game_view
    WHERE "RuoloCalc" = 1
    GROUP BY "GameID", "TeamID_auto"
),


--Creo la colonna correctCustomChar, che nelle alzate (E) prende solo i primi due caratteri, di cui il primo deve essere tra (C, D, F, R), e il secondo tra (+, !, -). Nelle battute (S) prende solo i primi due caratteri, se il primo è una lettera tra R, T, C e il secondo è un numero. Negli attacchi (A) prendo solo le prime due cifre (la prima deve essere da 0 a 4, la seconda da 0 a 6).
correct_custom_char AS (  --Rendiamo i codici custom compatibili con quelli che abbiamo noi, che capiamo e che possiamo usare
  SELECT
    "GameID",
    "RilevationNumber",
    CASE
      WHEN "Foundamental" = 'E' AND
           LENGTH("CustomChar") >= 2 AND
           SUBSTRING("CustomChar" FROM 1 FOR 1) IN ('C', 'D', 'F', 'R') AND
           SUBSTRING("CustomChar" FROM 2 FOR 1) IN ('+', '!', '-')
        THEN LEFT("CustomChar", 2)

      WHEN "Foundamental" = 'S' AND
           LENGTH("CustomChar") >= 2 AND
           SUBSTRING("CustomChar" FROM 1 FOR 1) IN ('R', 'T', 'C') AND
           SUBSTRING("CustomChar" FROM 2 FOR 1) ~ '^[0-9]$'
        THEN LEFT("CustomChar", 2)

      WHEN "Foundamental" = 'A' AND
           LENGTH("CustomChar") >= 2 AND
           SUBSTRING("CustomChar" FROM 1 FOR 1) ~ '^[0-4]$' AND
           SUBSTRING("CustomChar" FROM 2 FOR 1) ~ '^[0-6]$'
        THEN LEFT("CustomChar", 2)

      ELSE NULL
    END AS "correctCustomChar"
  FROM rilevations
),




base AS (
    SELECT
        r."GameID",
        r."SetNumber",
        r."ActionNumber",
        r."RilevationNumber",
        r."TouchNumber",
        r."RilevationTime",
        r."PunteggioCasa",
        r."PunteggioOspiti",
        r."EventiParticolari",
        r."whichTeam",
        r."whichTeamID",
        r."whichTeamIDreal",
        r."NumeroMaglia",
        r."NumeroMaglia_ID",
        r."Foundamental",
        r."Type",
        r."Eval",
        r."SetterCall",
        r."AttkCombination",
        r."TargAttk",
        r."StartZone",
        r."EndZoneEsecZone",
        r."EndSubzoneEsecSubzone",
        r."SkillType",
        r."PlayersInfo",
        r."Special",
        r."CustomChar",
        cc."correctCustomChar",
        r."SideoutBreakpoint",
        r."Colonna3sez3SCOUT",
        r."Colonna4sez3SCOUT",
        r."Colonna5sez3SCOUT",
        r."Colonna6sez3SCOUT",
        r."Colonna7sez3SCOUT",
        r."IDHomeTeam_auto",
        r."IDVisitorTeam_auto",
        r."whichTeamServes",
        r."PalleggCasa",
        r."PosizPalleggCasa",
        r."PalleggOspite",
        r."PosizPalleggOspite",
        r."HomePlayer1_ID",
        r."HomePlayer2_ID",
        r."HomePlayer3_ID",
        r."HomePlayer4_ID",
        r."HomePlayer5_ID",
        r."HomePlayer6_ID",
        r."VisitorPlayer1_ID",
        r."VisitorPlayer2_ID",
        r."VisitorPlayer3_ID",
        r."VisitorPlayer4_ID",
        r."VisitorPlayer5_ID",
        r."VisitorPlayer6_ID",
        r."Pcasa1linea",
        r."Pospite1linea",
        
        hl."LiberoMin_ID" AS "HomeLiberoDisponibile1_ID",
        hl."LiberoMax_ID" AS "HomeLiberoDisponibile2_ID",
        vl."LiberoMin_ID" AS "VisitorLiberoDisponibile1_ID",
        vl."LiberoMax_ID" AS "VisitorLiberoDisponibile2_ID",
        
        rp1."is_poss_start",      --Aggiungo is_poss_start da rilevations_possessi1_view
        rp1."AbsNumeroPossesso",  --Aggiungo AbsNumeroPossesso da rilevations_possessi1_view
        rp2."NumPossessoHome",    --Aggiungo NumPossessoHome e NumPossessoVisitor da rilevations_possessi2_view
        rp2."NumPossessoVisitor",
        
        peg."RuoloCalc",  --Aggiungo RuoloCalc per ogni tocco, da players_each_game (il ruolo di quel giocatore in quella partita)
        
        -- Creo la colonna EndZoneEsecZone6aree, che prende il numero di EndZoneEsecZone e lo mette nelle 6 zone classiche
        CASE 
            WHEN r."EndZoneEsecZone" IN (7,5) THEN 5  --Se il tocco avviene in zona 7 o 5, metto 5
            WHEN r."EndZoneEsecZone" IN (8,6) THEN 6  --Se il tocco avviene in zona 8 o 6, metto 6
            WHEN r."EndZoneEsecZone" IN (9,1) THEN 1  --Se il tocco avviene in zona 9 o 1, metto 1
            ELSE r."EndZoneEsecZone"
        END AS "EndZoneEsecZone6aree",
        
        -- Creo la colonna EndZoneEsecZone3aree, che prende il numero di EndZoneEsecZone e lo mette in 3 aree verticali: 5, 6, 1
        CASE 
            WHEN r."EndZoneEsecZone" IN (4,7,5) THEN 5  --Se il tocco avviene in una delle 3 zone di sinistra, metto 5
            WHEN r."EndZoneEsecZone" IN (3,8,6) THEN 6  --Se il tocco avviene in una delle 3 zone di centro, metto 6
            WHEN r."EndZoneEsecZone" IN (2,9,1) THEN 1  --Se il tocco avviene in una delle 3 zone di destra, metto 1
            ELSE NULL
        END AS "EndZoneEsecZone3aree",
        
        -- Creo la colonna StartZoneCompact, che compatta le StartZone in battuta, rice, attacco, difesa.
        CASE 
            WHEN r."Foundamental" IN ('S', 'R') THEN
                CASE 
                    WHEN r."StartZone" IN (5,7) THEN 5      --Se la battuta parte da zona 5 o 7, metto 5
                    WHEN r."StartZone" IN (6) THEN 6        --Se la battuta parte da zona 6, metto 6
                    WHEN r."StartZone" IN (1,9) THEN 1      --Se la battuta parte da zona 1 o 9, metto 1
                    ELSE r."StartZone"
                END
            WHEN r."Foundamental" IN ('A', 'D') THEN
                CASE 
                    WHEN r."StartZone" IN (4,7,5) THEN 4      --Se l'attacco parte da zona 4 o 7, metto 4
                    WHEN r."StartZone" IN (6,8) THEN 6      --Se l'attacco parte da zona 6 o 8, metto 6
                    WHEN r."StartZone" IN (1,9) THEN 1      --Se l'attacco parte da zona 1 o 9, metto 1
                    WHEN r."StartZone" IN (2) THEN 2        --Se l'attacco parte da zona 2, metto 2
                    WHEN r."StartZone" IN (3) THEN 3        --Se l'attacco parte da zona 3, metto 3
                    ELSE r."StartZone"
                END
            ELSE r."StartZone"
        END AS "StartZoneCompact",
        
        
        --Creo la colonna correctCustomCharAggregate, che aggrega valori di correctCustomChar. Nelle alzate (E) prende solo il secondo carattere (+, !, -), nelle battute prende solo il primo carattere (R, T, C). Negli attacchi prende solo il primo carattere (0, 1, 2, 3, 4).
        CASE
            WHEN r."Foundamental" = 'E' AND LENGTH(cc."correctCustomChar") = 2 THEN
                CASE SUBSTRING(cc."correctCustomChar" FROM 2 FOR 1)
                    WHEN '+' THEN '+'
                    WHEN '!' THEN '!'
                    WHEN '-' THEN '-'
                    ELSE NULL
                END
            WHEN r."Foundamental" = 'S' AND LENGTH(cc."correctCustomChar") = 2 THEN
                CASE SUBSTRING(cc."correctCustomChar" FROM 1 FOR 1)
                    WHEN 'R' THEN 'R'
                    WHEN 'T' THEN 'T'
                    WHEN 'C' THEN 'C'
                    ELSE NULL
                END
            WHEN r."Foundamental" = 'A' AND LENGTH(cc."correctCustomChar") = 2 THEN
                CASE SUBSTRING(cc."correctCustomChar" FROM 1 FOR 1)
                    WHEN '0' THEN '0'
                    WHEN '1' THEN '1'
                    WHEN '2' THEN '2'
                    WHEN '3' THEN '3'
                    WHEN '4' THEN '4'
                    ELSE NULL
                END
            ELSE NULL
        END AS "correctCustomCharAggregate",
        
        --Creo la colonna correctSkillType che corregge/aggiunge valori di SkillType alla difesa, siccome a volte dopo un muro manca
        CASE 
            WHEN r."Foundamental" = 'D' AND prec."Foundamental" = 'B' THEN   --Se la difesa è dopo un muro (prec è una tabella che contiene il tocco precedente per ogni riga di rilevations_view)
                CASE 
                    WHEN r."whichTeam" = prec."whichTeam" THEN 'B'       --Se i tocchi di difesa e muro sono della stessa squadra, allora è una difesa dopo un attacco avversario in cui il nostro muro l'ha toccata, quindi metto B
                    ELSE 'C'                                                 --Altrimenti è una difesa di copertura di un nostro attacco, quindi metto C
                END
            WHEN r."Foundamental" = 'D' AND prec."Foundamental" = 'A' AND r."whichTeam" != prec."whichTeam" THEN 'S'       --Se la difesa è dopo un attacco avversario, allora era una schiacciata, quindi nella difesa metto 'S'
            ELSE r."SkillType"
        END AS "correctSkillType",
        
        --Creo la colonna TypeAggregate, che raggruppa i tipi di attacco in H o non H
        CASE
            WHEN r."Type" IN ('T', 'Q', 'M', 'U', 'N','O') THEN 'nonH'
            WHEN r."Type" = 'H' THEN 'H'
            ELSE r."Type"
        END AS "TypeAggregate",


        CASE 
            WHEN r."NumeroMaglia_ID" = hl."LiberoMin_ID" THEN hl."LiberoMin_ID"
            WHEN r."NumeroMaglia_ID" = hl."LiberoMax_ID" THEN hl."LiberoMax_ID"
            ELSE NULL
        END AS "HomeLiberoCandidatoRaw",

        CASE 
            WHEN r."NumeroMaglia_ID" = vl."LiberoMin_ID" THEN vl."LiberoMin_ID"
            WHEN r."NumeroMaglia_ID" = vl."LiberoMax_ID" THEN vl."LiberoMax_ID"
            ELSE NULL
        END AS "VisitorLiberoCandidatoRaw"

        

    FROM rilevations_view r
    LEFT JOIN home_liberi hl 
        ON hl."GameID" = r."GameID" AND hl."TeamID_auto" = r."IDHomeTeam_auto"
    LEFT JOIN visitor_liberi vl 
        ON vl."GameID" = r."GameID" AND vl."TeamID_auto" = r."IDVisitorTeam_auto"
    LEFT JOIN rilevations_possessi1_view rp1
        ON rp1."GameID" = r."GameID" 
        AND rp1."RilevationNumber" = r."RilevationNumber"
    LEFT JOIN rilevations_possessi2_view rp2
        ON rp2."GameID" = r."GameID" 
        AND rp2."RilevationNumber" = r."RilevationNumber"
    LEFT JOIN correct_custom_char cc
        ON cc."GameID" = r."GameID" 
        AND cc."RilevationNumber" = r."RilevationNumber"
    LEFT JOIN players_each_game peg
        ON peg."GameID" = r."GameID" 
        AND peg."PlayerID" = r."NumeroMaglia_ID"
    LEFT JOIN rilevations prec
        ON r."GameID" = prec."GameID"
        AND r."RilevationNumber" = prec."RilevationNumber" + 1
    
),


home_libero_candidato AS (
    SELECT *,
        COALESCE(
            FIRST_VALUE("HomeLiberoCandidatoRaw") OVER (
                PARTITION BY "GameID", "IDHomeTeam_auto", home_grp_down
                ORDER BY "RilevationNumber" DESC
            ),
            FIRST_VALUE("HomeLiberoCandidatoRaw") OVER (
                PARTITION BY "GameID", "IDHomeTeam_auto", home_grp_up
                ORDER BY "RilevationNumber" ASC
            )
        ) AS "HomeLiberoCandidato_ID"
    FROM (
        SELECT *,
            -- Propagazione verso il basso
            SUM(CASE WHEN "HomeLiberoCandidatoRaw" IS NOT NULL THEN 1 ELSE 0 END)
                OVER (PARTITION BY "GameID", "IDHomeTeam_auto" ORDER BY "RilevationNumber" DESC)
            AS home_grp_down,

            -- Propagazione verso l'alto
            SUM(CASE WHEN "HomeLiberoCandidatoRaw" IS NOT NULL THEN 1 ELSE 0 END)
                OVER (PARTITION BY "GameID", "IDHomeTeam_auto" ORDER BY "RilevationNumber" ASC)
            AS home_grp_up
        FROM base
    ) sub
),


visitor_libero_candidato AS (
    SELECT *,
        COALESCE(
            FIRST_VALUE("VisitorLiberoCandidatoRaw") OVER (
                PARTITION BY "GameID", "IDVisitorTeam_auto", visitor_grp_down
                ORDER BY "RilevationNumber" DESC
            ),
            FIRST_VALUE("VisitorLiberoCandidatoRaw") OVER (
                PARTITION BY "GameID", "IDVisitorTeam_auto", visitor_grp_up
                ORDER BY "RilevationNumber" ASC
            )
        ) AS "VisitorLiberoCandidato_ID"
    FROM (
        SELECT *,
            -- Propagazione verso il basso (propaghiamo il valore VisitorLiberoCandidatoRaw verso il basso per riempire tutti i NULL in basso, finchè non troviamo un valore non NULL)
            SUM(CASE WHEN "VisitorLiberoCandidatoRaw" IS NOT NULL THEN 1 ELSE 0 END)
                OVER (PARTITION BY "GameID", "IDVisitorTeam_auto" ORDER BY "RilevationNumber" DESC)
            AS visitor_grp_down,

            -- Propagazione verso l'alto (propaghiamo il valore VisitorLiberoCandidatoRaw verso l'alto per riempire tutti i NULL in alto, finchè non troviamo un valore non NULL). Lo facciamo siccome erano rimasti i NULL delle prime azioni
            SUM(CASE WHEN "VisitorLiberoCandidatoRaw" IS NOT NULL THEN 1 ELSE 0 END)
                OVER (PARTITION BY "GameID", "IDVisitorTeam_auto" ORDER BY "RilevationNumber" ASC)
            AS visitor_grp_up
        FROM home_libero_candidato
    ) sub
),


--Creo le colonne che contengono informazioni sui tocchi del possesso precedente, ovvero le colonne Prev
colonne_prev AS (
    SELECT
        "GameID",
        "SetNumber",
        "ActionNumber",
        "AbsNumeroPossesso",
        "whichTeamID",
        MAX(CASE WHEN "Foundamental" = 'S' THEN "correctCustomCharAggregate" END) AS "PrevServeCorrectCustomCharAggregate",	--PrevServeCorrectCustomCharAggregate è il correctCustomCharAggregate della battuta fatta nel possesso precedente, quindi dalla squadra avversaria
   	    MAX(CASE WHEN "Foundamental" = 'R' THEN "Eval" END) AS "PrevReceptionEval",											--PrevReceptionEval è l'eval della ricezione fatta nel possesso precedente, quindi dalla squadra avversaria
        MAX(CASE WHEN "Foundamental" = 'E' THEN "Eval" END) AS "PrevSetEval",												--PrevSetEval è il Eval dell'alzata fatta nel possesso precedente, quindi dalla squadra avversaria
        MAX(CASE WHEN "Foundamental" = 'E' THEN "correctCustomChar" END) AS "PrevSetCorrectCustomChar",						--PrevSetcorrectCustomChar è il correctCustomChar dell'alzata fatta nel possesso precedente, quindi dalla squadra avversaria
        MAX(CASE WHEN "Foundamental" = 'E' THEN "correctCustomCharAggregate" END) AS "PrevSetCorrectCustomCharAggregate",	--PrevSetCorrectCustomCharAggregate è il correctCustomCharAggregate dell'alzata fatta nel possesso precedente, quindi dalla squadra avversaria
        MAX(CASE WHEN "Foundamental" = 'E' THEN "EndZoneEsecZone3aree" END) AS "PrevSetEndZoneEsecZone3aree",				--PrevSetEndZoneEsecZone3aree è il EndZoneEsecZone3aree dell'alzata fatta nel possesso precedente, quindi dalla squadra avversaria   
        MAX(CASE WHEN "Foundamental" = 'A' THEN "Type" END) AS "PrevAttkType",												--PrevAttkType è il Type dell'attacco fatto nel possesso precedente, quindi dalla squadra avversaria
        MAX(CASE WHEN "Foundamental" = 'A' THEN "TypeAggregate" END) AS "PrevAttkTypeAggregate",							--PrevAttkTypeAggregate è il TypeAggregate dell'attacco fatto nel possesso precedente, quindi dalla squadra avversaria
        MAX(CASE WHEN "Foundamental" = 'A' THEN "Eval" END) AS "PrevAttkEval",												--PrevAttkEval è il Eval dell'attacco fatto nel possesso precedente, quindi dalla squadra avversaria
        MAX(CASE WHEN "Foundamental" = 'A' THEN "SkillType" END) AS "PrevAttkSkillType",									--PrevAttkSkillType è il SkillType dell'attacco fatto nel possesso precedente, quindi dalla squadra avversaria
        MAX(CASE WHEN "Foundamental" = 'D' THEN "Eval" END) AS "PrevDefenseEval",											--Prev è l'eval della difesa fatta nel possesso precedente, quindi dalla squadra avversaria
        MAX(CASE WHEN "Foundamental" = 'F' THEN "Eval" END) AS "PrevFreeBallEval"											--Prev è l'eval della free ball fatta nel possesso precedente, quindi dalla squadra avversaria
    FROM (
        SELECT DISTINCT ON ("GameID", "SetNumber", "ActionNumber", "AbsNumeroPossesso", "whichTeamID", "Foundamental")
            "GameID",
            "SetNumber",
            "ActionNumber",
            "AbsNumeroPossesso",
            "whichTeamID",
            "Foundamental",
            "Type",
            "TypeAggregate",
            "Eval",
            "EndZoneEsecZone3aree",
            "SkillType",
            "correctCustomChar",
            "correctCustomCharAggregate"
        FROM base
        WHERE "Foundamental" IN ('S', 'R', 'E', 'A', 'B', 'D', 'F')
        ORDER BY "GameID", "SetNumber", "ActionNumber", "AbsNumeroPossesso", "whichTeamID", "Foundamental", "RilevationNumber"
    ) sub
    GROUP BY "GameID", "SetNumber", "ActionNumber", "AbsNumeroPossesso", "whichTeamID"
),


--Creo le colonne che contengono informazioni sui tocchi di questo possesso, ovvero le colonne This
colonne_this AS (
    SELECT
        "GameID",
        "SetNumber",
        "ActionNumber",
        "AbsNumeroPossesso",
        "whichTeamID",
   	    MAX(CASE WHEN "Foundamental" = 'R' THEN "Eval" END) AS "ThisReceptionEval",											--ThisReceptionEval è l'eval della ricezione fatta nello stesso possesso, quindi dalla stessa squadra
        MAX(CASE WHEN "Foundamental" = 'R' THEN "Type" END) AS "ThisReceptionType",											--ThisReceptionType è il Type della ricezione fatta nello stesso possesso, quindi dalla stessa squadra
        MAX(CASE WHEN "Foundamental" = 'E' THEN "Type" END) AS "ThisSetType",												--ThisSetType è il Type dell'alzata fatta nello stesso possesso, quindi dalla stessa squadra
        MAX(CASE WHEN "Foundamental" = 'E' THEN "TypeAggregate" END) AS "ThisSetTypeAggregate",										--ThisSetTypeAggregate è il TypeAggregate dell'alzata fatta nello stesso possesso, quindi dalla stessa squadra
        MAX(CASE WHEN "Foundamental" = 'E' THEN "Eval" END) AS "ThisSetEval",												--ThisSetEval è il Eval dell'alzata fatta nello stesso possesso, quindi dalla stessa squadra
        MAX(CASE WHEN "Foundamental" = 'E' THEN "correctCustomChar" END) AS "ThisSetCorrectCustomChar",						--ThisSetcorrectCustomChar è il correctCustomChar dell'alzata fatta nello stesso possesso, quindi dalla stessa squadra
        MAX(CASE WHEN "Foundamental" = 'E' THEN "correctCustomCharAggregate" END) AS "ThisSetCorrectCustomCharAggregate",	--ThisSetCorrectCustomCharAggregate è il correctCustomCharAggregate dell'alzata fatta nello stesso possesso, quindi dalla stessa squadra
        MAX(CASE WHEN "Foundamental" = 'E' THEN "EndZoneEsecZone3aree" END) AS "ThisSetEndZoneEsecZone3aree",				--ThisSetEndZoneEsecZone3aree è il EndZoneEsecZone3aree dell'alzata fatta nello stesso possesso, quindi dalla stessa squadra 	    
        MAX(CASE WHEN "Foundamental" = 'A' THEN "Type" END) AS "ThisAttkType",												--ThisAttkType è il Type dell'attacco fatto nello stesso possesso, quindi dalla stessa squadra
        MAX(CASE WHEN "Foundamental" = 'A' THEN "Eval" END) AS "ThisAttkEval",												--ThisAttkEval è il Eval dell'attacco fatto nello stesso possesso, quindi dalla stessa squadra
        MAX(CASE WHEN "Foundamental" = 'D' THEN "Eval" END) AS "ThisDefenseEval",											--ThisDefenseEval è l'eval della difesa fatta nello stesso possesso, quindi dalla stessa squadra
        MAX(CASE WHEN "Foundamental" = 'F' THEN "Eval" END) AS "ThisFreeBallEval"											--ThisFreeBallEval è l'eval della free ball fatta nello stesso possesso, quindi dalla stessa squadra
    FROM (
        SELECT DISTINCT ON ("GameID", "SetNumber", "ActionNumber", "AbsNumeroPossesso", "whichTeamID", "Foundamental")
            "GameID",
            "SetNumber",
            "ActionNumber",
            "AbsNumeroPossesso",
            "whichTeamID",
            "Foundamental",
            "Type",
            "TypeAggregate",
            "Eval",
            "EndZoneEsecZone3aree",
            "SkillType",
            "correctCustomChar",
            "correctCustomCharAggregate"
        FROM base
        WHERE "Foundamental" IN ('S', 'R', 'E', 'A', 'B', 'D', 'F')
        ORDER BY "GameID", "SetNumber", "ActionNumber", "AbsNumeroPossesso", "whichTeamID", "Foundamental", "RilevationNumber"
    ) sub
    GROUP BY "GameID", "SetNumber", "ActionNumber", "AbsNumeroPossesso", "whichTeamID"
),


--Creo le colonne che contengono informazioni sui tocchi del prossimo possesso, ovvero le colonne Next
colonne_next AS (
    SELECT
        "GameID",
        "SetNumber",
        "ActionNumber",
        "AbsNumeroPossesso",
        "whichTeamID",
        MAX(CASE WHEN "Foundamental" = 'A' THEN "Type" END) AS "NextAttkType",												--NextAttkType è il Type dell'attacco fatto nel prossimo possesso, quindi dalla squadra avversaria
        MAX(CASE WHEN "Foundamental" = 'A' THEN "Eval" END) AS "NextAttkEval"												--NextAttkEval è il Eval dell'attacco fatto nel prossimo possesso, quindi dalla squadra avversaria
    FROM (
        SELECT DISTINCT ON ("GameID", "SetNumber", "ActionNumber", "AbsNumeroPossesso", "whichTeamID", "Foundamental")
            "GameID",
            "SetNumber",
            "ActionNumber",
            "AbsNumeroPossesso",
            "whichTeamID",
            "Foundamental",
            "Type",
            "TypeAggregate",
            "Eval",
            "EndZoneEsecZone3aree",
            "SkillType",
            "correctCustomChar",
            "correctCustomCharAggregate"
        FROM base
        WHERE "Foundamental" IN ('S', 'R', 'E', 'A', 'B', 'D', 'F')
        ORDER BY "GameID", "SetNumber", "ActionNumber", "AbsNumeroPossesso", "whichTeamID", "Foundamental", "RilevationNumber"
    ) sub
    GROUP BY "GameID", "SetNumber", "ActionNumber", "AbsNumeroPossesso", "whichTeamID"
),


--Creo le colonne che contengono informazioni sui tocchi tra due possessi, ovvero le colonne NextNext
colonne_next_next AS (
    SELECT
        "GameID",
        "SetNumber",
        "ActionNumber",
        "AbsNumeroPossesso",
        "whichTeamID",
        MAX(CASE WHEN "Foundamental" = 'A' THEN "Type" END) AS "NextNextAttkType",												--NextNextAttkType è il Type dell'attacco fatto nel prossimo possesso, quindi dalla squadra avversaria
        MAX(CASE WHEN "Foundamental" = 'A' THEN "Eval" END) AS "NextNextAttkEval"												--NextNextAttkEval è il Eval dell'attacco fatto nel prossimo possesso, quindi dalla squadra avversaria
    FROM (
        SELECT DISTINCT ON ("GameID", "SetNumber", "ActionNumber", "AbsNumeroPossesso", "whichTeamID", "Foundamental")
            "GameID",
            "SetNumber",
            "ActionNumber",
            "AbsNumeroPossesso",
            "whichTeamID",
            "Foundamental",
            "Type",
            "TypeAggregate",
            "Eval",
            "EndZoneEsecZone3aree",
            "SkillType",
            "correctCustomChar",
            "correctCustomCharAggregate"
        FROM base
        WHERE "Foundamental" IN ('S', 'R', 'E', 'A', 'B', 'D', 'F')
        ORDER BY "GameID", "SetNumber", "ActionNumber", "AbsNumeroPossesso", "whichTeamID", "Foundamental", "RilevationNumber"
    ) sub
    GROUP BY "GameID", "SetNumber", "ActionNumber", "AbsNumeroPossesso", "whichTeamID"
),



--Creo la colonna EndedInPoint, che per ogni tocco indica se l'azione è finita con un punto per la propria squadra (True), oppure per l'altra (False)
--Per ogni tocco, prendo tutte le righe che hanno stesso GameID, SetNumber, ActionNumber e che hanno Foundamental = 'p'. In teoria ne trovo solo una. Se questa ha lo stesso whichTeam del tocco, allora EndedInPoint = True, altrimenti EndedInPoint = False
ended_in_point AS (
  SELECT 
    v."GameID",
    v."RilevationNumber",
    CASE 
      WHEN p."whichTeam" IS NULL THEN NULL
      WHEN v."whichTeam" = p."whichTeam" THEN TRUE
      ELSE FALSE
    END AS "EndedInPoint"
  FROM visitor_libero_candidato v
  LEFT JOIN visitor_libero_candidato p 
    ON v."GameID" = p."GameID"
   AND v."SetNumber" = p."SetNumber"
   AND v."ActionNumber" = p."ActionNumber"
   AND p."Foundamental" = 'p'
)




SELECT 
    cte1.*,
    
    -- Colonne per la squadra di casa
    cte1."HomePlayer2_ID" AS "probHomePlayer2_ID",  --I giocatori in prima linea non cambiano
    cte1."HomePlayer3_ID" AS "probHomePlayer3_ID",
    cte1."HomePlayer4_ID" AS "probHomePlayer4_ID",
    CASE 
        WHEN cte1."PosizPalleggCasa" IN (6, 3) THEN cte1."HomeLiberoCandidato_ID"   --Se siamo in P6 o in P3, probHomePlayer5_ID è HomeLiberoCandidato_ID, invece che HomePlayer5_ID
        ELSE cte1."HomePlayer5_ID"
    END AS "probHomePlayer5_ID",
    CASE 
        WHEN cte1."PosizPalleggCasa" IN (5, 2) AND cte1."whichTeamServes" = TRUE THEN cte1."HomeLiberoCandidato_ID"   --Se siamo in P5 o P2 e sta battendo la squadra ospite, probHomePlayer1_ID è HomeLiberoCandidato_ID, invece che HomePlayer1_ID
        ELSE cte1."HomePlayer1_ID"
    END AS "probHomePlayer1_ID",
    CASE 
        WHEN cte1."PosizPalleggCasa" IN (4, 1) THEN cte1."HomeLiberoCandidato_ID"
        ELSE cte1."HomePlayer6_ID"
    END AS "probHomePlayer6_ID",

    -- Colonne per la squadra ospite
    cte1."VisitorPlayer2_ID" AS "probVisitorPlayer2_ID",
    cte1."VisitorPlayer3_ID" AS "probVisitorPlayer3_ID",
    cte1."VisitorPlayer4_ID" AS "probVisitorPlayer4_ID",
    CASE 
        WHEN cte1."PosizPalleggOspite" IN (6, 3) THEN cte1."VisitorLiberoCandidato_ID"
        ELSE cte1."VisitorPlayer5_ID"
    END AS "probVisitorPlayer5_ID",
    CASE 
        WHEN cte1."PosizPalleggOspite" IN (5, 2) AND cte1."whichTeamServes" = FALSE THEN cte1."VisitorLiberoCandidato_ID"
        ELSE cte1."VisitorPlayer1_ID"
    END AS "probVisitorPlayer1_ID",
    CASE 
        WHEN cte1."PosizPalleggOspite" IN (4, 1) THEN cte1."VisitorLiberoCandidato_ID"
        ELSE cte1."VisitorPlayer6_ID"
    END AS "probVisitorPlayer6_ID",
    
    cp."PrevServeCorrectCustomCharAggregate",
    cp."PrevReceptionEval",
    cp."PrevSetEval",
    cp."PrevSetCorrectCustomChar",
    cp."PrevSetCorrectCustomCharAggregate",
    cp."PrevSetEndZoneEsecZone3aree",
    cp."PrevAttkType",
    cp."PrevAttkTypeAggregate",
    cp."PrevAttkEval",
    cp."PrevAttkSkillType",
    cp."PrevDefenseEval",
    cp."PrevFreeBallEval",
    ct."ThisReceptionType",
    ct."ThisReceptionEval",
    ct."ThisSetType",
    ct."ThisSetEval",
    ct."ThisSetCorrectCustomChar",
    ct."ThisSetCorrectCustomCharAggregate",
    ct."ThisAttkType",
    ct."ThisSetTypeAggregate",
    ct."ThisAttkEval",
    ct."ThisDefenseEval",
    ct."ThisFreeBallEval",
    cn."NextAttkType",
    cn."NextAttkEval",
    cnn."NextNextAttkType",
    cnn."NextNextAttkEval",
    eip."EndedInPoint",
    
    
    --Creo ThisAppoggio
    CASE 
        WHEN ct."ThisSetCorrectCustomCharAggregate" = '+' THEN '+'
        WHEN ct."ThisSetCorrectCustomCharAggregate" = '!' THEN '!'
        WHEN ct."ThisSetCorrectCustomCharAggregate" = '-' THEN '-'
        WHEN ct."ThisReceptionEval" IN ('#', '+') THEN '+'
        WHEN ct."ThisReceptionEval" = '!' THEN '!'
        WHEN ct."ThisReceptionEval" = '-' THEN '-'
        
        WHEN ct."ThisFreeBallEval" IN ('#', '+') THEN '+'
        WHEN ct."ThisFreeBallEval" = '!' THEN '!'
        WHEN ct."ThisFreeBallEval" = '-' THEN '-' 
        
        WHEN ct."ThisDefenseEval" = '-' THEN '-' 
        
        ELSE NULL
    END AS "ThisAppoggio",
    
    
    --Creo PrevAppoggio
    CASE 
        WHEN cp."PrevSetCorrectCustomCharAggregate" = '+' THEN '+'
        WHEN cp."PrevSetCorrectCustomCharAggregate" = '!' THEN '!'
        WHEN cp."PrevSetCorrectCustomCharAggregate" = '-' THEN '-'
        WHEN cp."PrevReceptionEval" IN ('#', '+') THEN '+'
        WHEN cp."PrevReceptionEval" = '!' THEN '!'
        WHEN cp."PrevReceptionEval" = '-' THEN '-'
        
        WHEN cp."PrevFreeBallEval" IN ('#', '+') THEN '+'
        WHEN cp."PrevFreeBallEval" = '!' THEN '!'
        WHEN cp."PrevFreeBallEval" = '-' THEN '-' 
        
        WHEN cp."PrevDefenseEval" = '-' THEN '-' 
        
        ELSE NULL
    END AS "PrevAppoggio"


FROM visitor_libero_candidato cte1 -- oppure il nome della CTE finale precedente

LEFT JOIN colonne_prev cp
    ON cp."GameID" = cte1."GameID"
    AND cp."SetNumber" = cte1."SetNumber"
    AND cp."ActionNumber" = cte1."ActionNumber"
    AND cp."AbsNumeroPossesso" = cte1."AbsNumeroPossesso" - 1
    AND cp."whichTeamID" != cte1."whichTeamID"

LEFT JOIN colonne_this ct
    ON ct."GameID" = cte1."GameID"
    AND ct."SetNumber" = cte1."SetNumber"
    AND ct."ActionNumber" = cte1."ActionNumber"
    AND ct."AbsNumeroPossesso" = cte1."AbsNumeroPossesso"
    AND ct."whichTeamID" = cte1."whichTeamID"
    
LEFT JOIN colonne_next cn
    ON cn."GameID" = cte1."GameID"
    AND cn."SetNumber" = cte1."SetNumber"
    AND cn."ActionNumber" = cte1."ActionNumber"
    AND cn."AbsNumeroPossesso" = cte1."AbsNumeroPossesso" + 1
    AND cn."whichTeamID" != cte1."whichTeamID"
    
LEFT JOIN colonne_next_next cnn
    ON cnn."GameID" = cte1."GameID"
    AND cnn."SetNumber" = cte1."SetNumber"
    AND cnn."ActionNumber" = cte1."ActionNumber"
    AND cnn."AbsNumeroPossesso" = cte1."AbsNumeroPossesso" + 2
    AND cnn."whichTeamID" = cte1."whichTeamID"

LEFT JOIN ended_in_point eip ON 
    eip."GameID"=cte1."GameID" AND eip."RilevationNumber"=cte1."RilevationNumber"




""")


conn.commit()
print("✅ VIEW rilevations_libero_view creata con successo")





#Creo rilevations_libero_battute_view, che è come rilevations_libero_view, ma contiene solo le azioni con le battute
cur.execute("DROP VIEW IF EXISTS rilevations_libero_battute_view")
conn.commit()

cur.execute("""
CREATE OR REPLACE VIEW rilevations_libero_battute_view AS
SELECT * FROM rilevations_libero_view
WHERE "Foundamental" = 'S'
""")
conn.commit()

print("✅ VIEW rilevations_libero_battute_view creata con successo")










