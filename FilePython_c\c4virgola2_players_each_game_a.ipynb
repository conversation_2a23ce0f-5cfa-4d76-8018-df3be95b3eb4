{"cells": [{"cell_type": "code", "execution_count": 1, "id": "b57359d0", "metadata": {}, "outputs": [], "source": ["import numpy as np \n", "import pandas as pd  #Realizzato con la versione di Pandas 2.2.3\n", "import os\n", "import duckdb\n", "import psycopg\n", "from sqlalchemy import create_engine, text\n", "\n", "import plotly.graph_objects as go\n", "from dash import Dash, dcc, html, Input, Output"]}, {"cell_type": "code", "execution_count": 2, "id": "dd5c60d1", "metadata": {}, "outputs": [{"data": {"text/plain": ["<duckdb.duckdb.DuckDBPyConnection at 0x25573765130>"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# Connessione a DuckDB\n", "con = duckdb.connect('db_modena_1.duckdb')\n", "\n", "# Installazione dell'estensione postgres se non è già installata\n", "con.execute(\"INSTALL postgres\")\n", "con.execute(\"LOAD postgres\")"]}, {"cell_type": "code", "execution_count": 3, "id": "8b5fb8c0", "metadata": {}, "outputs": [{"data": {"text/plain": ["<duckdb.duckdb.DuckDBPyConnection at 0x2189c949ab0>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Estrazione della view players_each_game\n", "con.execute(\"\"\"\n", "    CREATE OR REPLACE TABLE players_each_game_duckdb AS \n", "    SELECT * FROM postgres_scan('dbname=db_modena user=postgres password=AcquaLevissima1 host=localhost port=5432', 'public', 'players_each_game');\n", "\"\"\")"]}, {"cell_type": "code", "execution_count": 4, "id": "a7ed9fc5", "metadata": {}, "outputs": [{"data": {"text/plain": ["<duckdb.duckdb.DuckDBPyConnection at 0x2189c949ab0>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Estrazione della view games_view\n", "con.execute(\"\"\"\n", "    CREATE OR REPLACE TABLE games_view_duckdb AS \n", "    SELECT * FROM postgres_scan('dbname=db_modena user=postgres password=AcquaLevissima1 host=localhost port=5432', 'public', 'games_view');\n", "\"\"\")"]}, {"cell_type": "code", "execution_count": 5, "id": "4cd3caeb", "metadata": {}, "outputs": [{"data": {"text/plain": ["<duckdb.duckdb.DuckDBPyConnection at 0x2189c949ab0>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Estrazione della view teams\n", "con.execute(\"\"\"\n", "    CREATE OR REPLACE TABLE teams_duckdb AS \n", "    SELECT * FROM postgres_scan('dbname=db_modena user=postgres password=AcquaLevissima1 host=localhost port=5432', 'public', 'teams');\n", "\"\"\")"]}, {"cell_type": "code", "execution_count": 6, "id": "109eab47", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8292c54067de4484b9a0cbd08549590b", "version_major": 2, "version_minor": 0}, "text/plain": ["FloatProgress(value=0.0, layout=Layout(width='auto'), style=ProgressStyle(bar_color='black'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<duckdb.duckdb.DuckDBPyConnection at 0x2189c949ab0>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Estrazione della view rilevations_libero_view\n", "con.execute(\"\"\"\n", "    CREATE OR REPLACE TABLE rilevations_libero_view_duckdb AS \n", "    SELECT * FROM postgres_scan('dbname=db_modena user=postgres password=AcquaLevissima1 host=localhost port=5432', 'public', 'rilevations_libero_view');\n", "\"\"\")"]}, {"cell_type": "code", "execution_count": 7, "id": "b9f82e67", "metadata": {}, "outputs": [{"data": {"text/plain": ["<duckdb.duckdb.DuckDBPyConnection at 0x2189c949ab0>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["con.execute(\"DROP VIEW IF EXISTS rilevations_libero_battute_view_duckdb\")\n", "con.execute(\"\"\"\n", "    CREATE OR REPLACE VIEW rilevations_libero_battute_view_duckdb AS\n", "    SELECT * FROM postgres_scan('dbname=db_modena user=postgres password=AcquaLevissima1 host=localhost port=5432', 'public', 'rilevations_libero_battute_view')\n", "    WHERE \"Foundamental\" = 'S';\n", "\"\"\")"]}, {"cell_type": "markdown", "id": "65e0d6f7", "metadata": {}, "source": ["<PERSON><PERSON><PERSON><PERSON> la tabella in maniera diversa, più semplice da leggere e più efficiente"]}, {"cell_type": "code", "execution_count": 8, "id": "c37f46ca", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "322d1ca07bda4b26af38e795800eff30", "version_major": 2, "version_minor": 0}, "text/plain": ["FloatProgress(value=0.0, layout=Layout(width='auto'), style=ProgressStyle(bar_color='black'))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<duckdb.duckdb.DuckDBPyConnection at 0x2189c949ab0>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["con.execute(\"DROP TABLE IF EXISTS players_each_game_a\")\n", "\n", "\n", "con.execute(\"\"\"\n", "CREATE TABLE players_each_game_a AS\n", "WITH base AS (\n", "  SELECT * FROM players_each_game_duckdb\n", "),\n", "\n", "num_azioni_giocate AS (\n", "    SELECT \"GameID\", UNNEST([\n", "        \"probHomePlayer1_ID\", \"probHomePlayer2_ID\", \"probHomePlayer3_ID\",\n", "        \"probHomePlayer4_ID\", \"probHomePlayer5_ID\", \"probHomePlayer6_ID\",\n", "        \"probVisitorPlayer1_ID\", \"probVisitorPlayer2_ID\", \"probVisitorPlayer3_ID\",\n", "        \"probVisitorPlayer4_ID\", \"probVisitorPlayer5_ID\", \"probVisitorPlayer6_ID\"\n", "    ]) AS \"PlayerID\"\n", "    FROM rilevations_libero_battute_view_duckdb\n", "),\n", "conteggi AS (\n", "    SELECT \"GameID\", \"PlayerID\", COUNT(*) AS \"NumAzioniGiocate\"\n", "    FROM num_azioni_giocate\n", "    WHERE \"PlayerID\" IS NOT NULL\n", "    GROUP BY ALL\n", "),\n", "\n", "num_azioni_giocate_1_linea AS (\n", "    SELECT \"GameID\", UNNEST([\n", "        \"probHomePlayer2_ID\", \"probHomePlayer3_ID\", \"probHomePlayer4_ID\",\n", "        \"probVisitorPlayer2_ID\", \"probVisitorPlayer3_ID\", \"probVisitorPlayer4_ID\"\n", "    ]) AS \"PlayerID\"\n", "    FROM rilevations_libero_battute_view_duckdb\n", "),\n", "conteggi_1_linea AS (\n", "    SELECT \"GameID\", \"PlayerID\", COUNT(*) AS \"NumAzioniGiocate1Linea\"\n", "    FROM num_azioni_giocate_1_linea\n", "    WHERE \"PlayerID\" IS NOT NULL\n", "    GROUP BY ALL\n", "),\n", "\n", "num_azioni_giocate_2_linea AS (\n", "    SELECT \"GameID\", UNNEST([\n", "        \"probHomePlayer1_ID\", \"probHomePlayer5_ID\", \"probHomePlayer6_ID\",\n", "        \"probVisitorPlayer1_ID\", \"probVisitorPlayer5_ID\", \"probVisitorPlayer6_ID\"\n", "    ]) AS \"PlayerID\"\n", "    FROM rilevations_libero_battute_view_duckdb\n", "),\n", "conteggi_2_linea AS (\n", "    SELECT \"GameID\", \"PlayerID\", COUNT(*) AS \"NumAzioniGiocate2Linea\"\n", "    FROM num_azioni_giocate_2_linea\n", "    WHERE \"PlayerID\" IS NOT NULL\n", "    GROUP BY ALL\n", "),\n", "\n", "\n", "total_touch AS (\n", "    SELECT \"GameID\", \"NumeroMaglia_ID\" AS \"PlayerID\", COUNT(*) AS \"TotalTouch\"\n", "    FROM rilevations_libero_view_duckdb\n", "    WHERE \"NumeroMaglia_ID\" IS NOT NULL\n", "    GROUP BY \"GameID\", \"NumeroMaglia_ID\"\n", "),\n", "\n", "--Attenzione perchè forse devo contare anche i punti in alzata che hanno TargAttk = 'S'\n", "punti_fatti AS (\n", "    SELECT \"GameID\", \"NumeroMaglia_ID\" AS \"PlayerID\", COUNT(*) AS \"PuntiFatti\"\n", "    FROM rilevations_libero_view_duckdb\n", "    WHERE \"Foundamental\" IN ('S', 'A', 'B')\n", "      AND \"Eval\" = '#'\n", "      AND \"NumeroMaglia_ID\" IS NOT NULL\n", "    GROUP BY \"GameID\", \"NumeroMaglia_ID\"\n", "),\n", "\n", "set_giocati AS (\n", "    SELECT \"GameID\", \"SetNumber\", \"NumeroMaglia_ID\" AS \"PlayerID\"\n", "    FROM rilevations_libero_view_duckdb\n", "    WHERE \"NumeroMaglia_ID\" IS NOT NULL\n", "    GROUP BY \"GameID\", \"SetNumber\", \"NumeroMaglia_ID\"\n", "),\n", "\n", "num_set_giocati AS (\n", "    SELECT \"GameID\", \"PlayerID\", COUNT(*) AS \"NumSetGiocati\"\n", "    FROM set_giocati\n", "    GROUP BY \"GameID\", \"PlayerID\"\n", "),\n", "\n", "punti_per_set AS (\n", "    SELECT\n", "        pf.\"GameID\",\n", "        pf.\"PlayerID\",\n", "        pf.\"PuntiFatti\",\n", "        ns.\"NumSetGiocati\",\n", "        CASE\n", "            WHEN ns.\"NumSetGiocati\" > 0 THEN pf.\"PuntiFatti\"::FLOAT / ns.\"NumSetGiocati\"\n", "            ELSE NULL\n", "        END AS \"PuntiPerSet\"\n", "    FROM punti_fatti pf\n", "    LEFT JOIN num_set_giocati ns ON pf.\"GameID\" = ns.\"GameID\" AND pf.\"PlayerID\" = ns.\"PlayerID\"\n", "),\n", "\n", "punti_per_azione AS (\n", "    SELECT\n", "        pf.\"GameID\",\n", "        pf.\"PlayerID\",\n", "        pf.\"PuntiFatti\",\n", "        c.\"NumAzioniGiocate\",\n", "        CASE\n", "            WHEN c.\"NumAzioniGiocate\" > 0 THEN pf.\"PuntiFatti\"::FLOAT / c.\"NumAzioniGiocate\"\n", "            ELSE NULL\n", "        END AS \"PuntiPerAzione\"\n", "    FROM punti_fatti pf\n", "    LEFT JOIN conteggi c\n", "        ON pf.\"GameID\" = c.\"GameID\" AND pf.\"PlayerID\" = c.\"PlayerID\"\n", "),\n", "\n", "punti_per_100azioni AS (\n", "    SELECT\n", "        ppa.\"GameID\",\n", "        ppa.\"PlayerID\",\n", "        ppa.\"PuntiPerAzione\" * 100 AS \"PuntiPer100Azioni\"\n", "    FROM punti_per_azione ppa\n", "),\n", "\n", "punti_fatti1linea AS (\n", "    SELECT\n", "        r.\"GameID\",\n", "        r.\"NumeroMaglia_ID\" AS \"PlayerID\",\n", "        COUNT(*) AS \"PuntiFatti1Linea\"\n", "    FROM\n", "        rilevations_libero_view_duckdb r\n", "    WHERE\n", "        r.\"Foundamental\" IN ('S', 'A', 'B')\n", "        AND r.\"<PERSON><PERSON>\" = '#'\n", "        AND r.\"NumeroMaglia_ID\" IS NOT NULL\n", "        AND (\n", "            r.\"probHomePlayer2_ID\" = r.\"NumeroMaglia_ID\" OR\n", "            r.\"probHomePlayer3_ID\" = r.\"NumeroMaglia_ID\" OR\n", "            r.\"probHomePlayer4_ID\" = r.\"NumeroMaglia_ID\" OR\n", "            r.\"probVisitorPlayer2_ID\" = r.\"NumeroMaglia_ID\" OR\n", "            r.\"probVisitorPlayer3_ID\" = r.\"NumeroMaglia_ID\" OR\n", "            r.\"probVisitorPlayer4_ID\" = r.\"NumeroMaglia_ID\"\n", "        )\n", "    GROUP BY\n", "        r.\"GameID\", r.\"NumeroMaglia_ID\"\n", "),\n", "\n", "punti_fatti2linea AS (\n", "    SELECT\n", "        r.\"GameID\",\n", "        r.\"NumeroMaglia_ID\" AS \"PlayerID\",\n", "        COUNT(*) AS \"PuntiFatti2Linea\"\n", "    FROM\n", "        rilevations_libero_view_duckdb r\n", "    WHERE\n", "        r.\"Foundamental\" IN ('S', 'A', 'B')\n", "        AND r.\"<PERSON><PERSON>\" = '#'\n", "        AND r.\"NumeroMaglia_ID\" IS NOT NULL\n", "        AND (\n", "            r.\"probHomePlayer1_ID\" = r.\"NumeroMaglia_ID\" OR\n", "            r.\"probHomePlayer5_ID\" = r.\"NumeroMaglia_ID\" OR\n", "            r.\"probHomePlayer6_ID\" = r.\"NumeroMaglia_ID\" OR\n", "            r.\"probVisitorPlayer1_ID\" = r.\"NumeroMaglia_ID\" OR\n", "            r.\"probVisitorPlayer5_ID\" = r.\"NumeroMaglia_ID\" OR\n", "            r.\"probVisitorPlayer6_ID\" = r.\"NumeroMaglia_ID\"\n", "        )\n", "    GROUP BY\n", "        r.\"GameID\", r.\"NumeroMaglia_ID\"\n", "),\n", "\n", "punti_per_azione1Linea AS (\n", "    SELECT\n", "        punti_fatti1linea.\"GameID\",\n", "        punti_fatti1linea.\"PlayerID\",\n", "        punti_fatti1linea.\"PuntiFatti1Linea\",\n", "        c1.\"NumAzioniGiocate1Linea\",\n", "        CASE\n", "            WHEN c1.\"NumAzioniGiocate1Linea\" > 0 THEN punti_fatti1linea.\"PuntiFatti1Linea\"::FLOAT / c1.\"NumAzioniGiocate1Linea\"\n", "            ELSE NULL\n", "        END AS \"PuntiPerAzione1Linea\"\n", "    FROM punti_fatti1linea\n", "    LEFT JOIN conteggi_1_linea AS c1\n", "        ON punti_fatti1linea.\"GameID\" = c1.\"GameID\" AND punti_fatti1linea.\"PlayerID\" = c1.\"PlayerID\"\n", "),\n", "\n", "punti_per_azione2Linea AS (\n", "    SELECT\n", "        punti_fatti2linea.\"GameID\",\n", "        punti_fatti2linea.\"PlayerID\",\n", "        punti_fatti2linea.\"PuntiFatti2Linea\",\n", "        c2.\"NumAzioniGiocate2Linea\",\n", "        CASE\n", "            WHEN c2.\"NumAzioniGiocate2Linea\" > 0 THEN punti_fatti2linea.\"PuntiFatti2Linea\"::FLOAT / c2.\"NumAzioniGiocate2Linea\"\n", "            ELSE NULL\n", "        END AS \"PuntiPerAzione2Linea\"\n", "    FROM punti_fatti2linea\n", "    LEFT JOIN conteggi_2_linea AS c2\n", "        ON punti_fatti2linea.\"GameID\" = c2.\"GameID\" AND punti_fatti2linea.\"PlayerID\" = c2.\"PlayerID\"\n", "),\n", "\n", "\n", "--AverageEval\n", "\n", "\n", "total_error AS (\n", "    SELECT \"GameID\", \"NumeroMaglia_ID\" AS \"PlayerID\", COUNT(*) AS \"TotalError\"\n", "    FROM rilevations_libero_view_duckdb\n", "    WHERE \"Eval\" = '='\n", "      AND \"NumeroMaglia_ID\" IS NOT NULL\n", "    GROUP BY \"GameID\", \"NumeroMaglia_ID\"\n", "),\n", "\n", "total_error_percentage AS (\n", "    SELECT\n", "        te.\"GameID\",\n", "        te.\"PlayerID\",\n", "        te.\"TotalError\",\n", "        tt.\"TotalTouch\",\n", "        CASE\n", "            WHEN tt.\"TotalTouch\" > 0 THEN te.\"TotalError\"::FLOAT / tt.\"TotalTouch\"\n", "            ELSE NULL\n", "        END AS \"TotalErrorPercentage\"\n", "    FROM total_error te\n", "    LEFT JOIN total_touch tt\n", "        ON te.\"GameID\" = tt.\"GameID\" AND te.\"PlayerID\" = tt.\"PlayerID\"\n", "),\n", "\n", "total_negative AS (\n", "    SELECT \"GameID\", \"NumeroMaglia_ID\" AS \"PlayerID\", COUNT(*) AS \"TotalNegative\"\n", "    FROM rilevations_libero_view_duckdb\n", "    WHERE \"Eval\" IN ('-', '=')\n", "      AND \"NumeroMaglia_ID\" IS NOT NULL\n", "    GROUP BY \"GameID\", \"NumeroMaglia_ID\"\n", "),\n", "\n", "total_negative_percentage AS (\n", "    SELECT\n", "        tn.\"GameID\",\n", "        tn.\"PlayerID\",\n", "        tn.\"TotalNegative\",\n", "        tt.\"TotalTouch\",\n", "        CASE\n", "            WHEN tt.\"TotalTouch\" > 0 THEN tn.\"TotalNegative\"::FLOAT / tt.\"TotalTouch\"\n", "            ELSE NULL\n", "        END AS \"TotalNegativePercentage\"\n", "    FROM total_negative tn\n", "    LEFT JOIN total_touch tt\n", "        ON tn.\"GameID\" = tt.\"GameID\" AND tn.\"PlayerID\" = tt.\"PlayerID\"\n", "),\n", "\n", "total_perfect AS (\n", "    SELECT \"GameID\", \"NumeroMaglia_ID\" AS \"PlayerID\", COUNT(*) AS \"TotalPerfect\"\n", "    FROM rilevations_libero_view_duckdb\n", "    WHERE \"Eval\" IN ('#')\n", "      AND \"NumeroMaglia_ID\" IS NOT NULL\n", "    GROUP BY \"GameID\", \"NumeroMaglia_ID\"\n", "),\n", "\n", "total_perfect_percentage AS (\n", "    SELECT\n", "        tp.\"GameID\",\n", "        tp.\"PlayerID\",\n", "        tp.\"TotalPerfect\",\n", "        tt.\"TotalTouch\",\n", "        CASE\n", "            WHEN tt.\"TotalTouch\" > 0 THEN tp.\"TotalPerfect\"::FLOAT / tt.\"TotalTouch\"\n", "            ELSE NULL\n", "        END AS \"TotalPerfectPercentage\"\n", "    FROM total_perfect tp\n", "    LEFT JOIN total_touch tt\n", "        ON tp.\"GameID\" = tt.\"GameID\" AND tp.\"PlayerID\" = tt.\"PlayerID\"\n", "),\n", "\n", "total_positive AS (\n", "    SELECT \"GameID\", \"NumeroMaglia_ID\" AS \"PlayerID\", COUNT(*) AS \"TotalPositive\"\n", "    FROM rilevations_libero_view_duckdb\n", "    WHERE \"Eval\" IN ('#', '+')\n", "      AND \"NumeroMaglia_ID\" IS NOT NULL\n", "    GROUP BY \"GameID\", \"NumeroMaglia_ID\"\n", "),\n", "\n", "total_positive_percentage AS (\n", "    SELECT\n", "        tp.\"GameID\",\n", "        tp.\"PlayerID\",\n", "        tp.\"TotalPositive\",\n", "        tt.\"TotalTouch\",\n", "        CASE\n", "            WHEN tt.\"TotalTouch\" > 0 THEN tp.\"TotalPositive\"::FLOAT / tt.\"TotalTouch\"\n", "            ELSE NULL\n", "        END AS \"TotalPositivePercentage\"\n", "    FROM total_positive tp\n", "    LEFT JOIN total_touch tt\n", "        ON tp.\"GameID\" = tt.\"GameID\" AND tp.\"PlayerID\" = tt.\"PlayerID\"\n", "),\n", "\n", "attacks AS (\n", "    SELECT \"GameID\", \"NumeroMaglia_ID\" AS \"PlayerID\", COUNT(*) AS \"Attacks\"\n", "    FROM rilevations_libero_view_duckdb\n", "    WHERE \"Foundamental\" IN ('A')\n", "      AND \"NumeroMaglia_ID\" IS NOT NULL\n", "    GROUP BY \"GameID\", \"NumeroMaglia_ID\"\n", "),\n", "\n", "team_attacks AS (\n", "  SELECT\n", "    b.\"<PERSON>ID\",\n", "    b.\"TeamID_auto\",\n", "    COUNT(DISTINCT r.\"RilevationNumber\") AS \"TeamAttacks\"  --conto quanti tocchi distinti ci sono stati\n", "  FROM base AS b\n", "  JOIN rilevations_libero_view_duckdb AS r\n", "    ON r.\"GameID\" = b.\"GameID\"\n", "    AND r.\"whichTeamID\" = b.\"TeamID_auto\"\n", "    AND r.\"Foundamental\" = 'A'\n", "  GROUP BY b.\"GameID\", b.\"TeamID_auto\"\n", "),\n", "\n", "team_attacks_while_on_field AS (\n", "  -- unpivot dei giocatori in campo in cui si verifica un attacco di squadra\n", "  SELECT \n", "    r.\"GameID\",\n", "    slot.\"PlayerID\",\n", "    COUNT(*) AS \"TeamAttacksWhileOnField\"\n", "  FROM rilevations_libero_view_duckdb r\n", "  -- consideriamo solo gli attacchi\n", "  JOIN LATERAL (\n", "    VALUES\n", "      (r.\"probHomePlayer1_ID\"), (r.\"probHomePlayer2_ID\"), (r.\"probHomePlayer3_ID\"),\n", "      (r.\"probHomePlayer4_ID\"), (r.\"probHomePlayer5_ID\"), (r.\"probHomePlayer6_ID\"),\n", "      (r.\"probVisitorPlayer1_ID\"), (r.\"probVisitorPlayer2_ID\"), (r.\"probVisitorPlayer3_ID\"),\n", "      (r.\"probVisitorPlayer4_ID\"), (r.\"probVisitorPlayer5_ID\"), (r.\"probVisitorPlayer6_ID\")\n", "  ) AS slot(\"PlayerID\")\n", "    ON slot.\"PlayerID\" IS NOT NULL\n", "  -- solo quando è un attacco e la squadra che attacca è quella del player\n", "  WHERE r.\"Foundamental\" = 'A'\n", "    AND r.\"whichTeamID\" = (\n", "      SELECT b.\"TeamID_auto\"\n", "      FROM base b\n", "      WHERE b.\"GameID\" = r.\"GameID\"\n", "        AND b.\"PlayerID\" = slot.\"PlayerID\"\n", "    )\n", "  GROUP BY r.\"GameID\", slot.\"PlayerID\"\n", "),\n", "\n", "role_team_attacks AS (\n", "  SELECT \n", "    b.\"<PERSON>ID\",\n", "    b.\"PlayerID\",\n", "    COUNT(*) AS \"RoleTeamAttacks\"\n", "  FROM base b\n", "\n", "  -- <PERSON><PERSON><PERSON> di attacco fatte dalla squadra del player\n", "  JOIN rilevations_libero_view_duckdb r\n", "    ON r.\"GameID\" = b.\"GameID\"\n", "   AND r.\"whichTeamID\" = b.\"TeamID_auto\"\n", "   AND r.\"Foundamental\" = 'A'\n", "\n", "  -- JOIN per controllare se chi ha fatto l'attacco ha lo stesso ruolo\n", "  JOIN base compagno\n", "    ON compagno.\"GameID\" = r.\"GameID\"\n", "   AND compagno.\"PlayerID\" = r.\"NumeroMaglia_ID\"\n", "   AND compagno.\"TeamID_auto\" = b.\"TeamID_auto\"\n", "   AND compagno.\"RuoloCalc\" = b.\"RuoloCalc\"\n", "\n", "  GROUP BY b.\"GameID\", b.\"PlayerID\"\n", "),\n", "\n", "--<PERSON><PERSON> che contiene le azioni in cui ogni giocatore era in campo\n", "actions_on_field AS (\n", "    -- 1) recupera le azioni in cui ogni giocatore era in campo\n", "    SELECT DISTINCT\n", "      r.\"GameID\",\n", "      r.\"ActionNumber\",\n", "      slot.\"PlayerID\"\n", "    FROM rilevations_libero_view_duckdb r\n", "    JOIN LATERAL (\n", "      VALUES\n", "        (r.\"probHomePlayer1_ID\"), (r.\"probHomePlayer2_ID\"),\n", "        (r.\"probHomePlayer3_ID\"), (r.\"probHomePlayer4_ID\"),\n", "        (r.\"probHomePlayer5_ID\"), (r.\"probHomePlayer6_ID\"),\n", "        (r.\"probVisitorPlayer1_ID\"), (r.\"probVisitorPlayer2_ID\"),\n", "        (r.\"probVisitorPlayer3_ID\"), (r.\"probVisitorPlayer4_ID\"),\n", "        (r.\"probVisitorPlayer5_ID\"), (r.\"probVisitorPlayer6_ID\")\n", "    ) AS slot(\"PlayerID\")\n", "      ON slot.\"PlayerID\" IS NOT NULL\n", "),\n", "\n", "--<PERSON><PERSON> che contiene le azioni di attacco di ogni compagno con stesso ruolo\n", "role_attacks AS (\n", "    -- 2) recupera le azioni di attacco di ogni compagno con stesso ruolo\n", "    SELECT DISTINCT\n", "      r.\"GameID\",\n", "      r.\"ActionNumber\",\n", "      comp.\"PlayerID\" AS \"PlayerID_base\"\n", "    FROM base b\n", "    JOIN rilevations_libero_view_duckdb r\n", "      ON r.\"GameID\" = b.\"GameID\"\n", "     AND r.\"whichTeamID\" = b.\"TeamID_auto\"\n", "     AND r.\"Foundamental\" = 'A'\n", "    -- unpivot per trovare il compagno che ha eseguito l'attacco\n", "    JOIN LATERAL (\n", "      VALUES\n", "        (r.\"probHomePlayer1_ID\"), (r.\"probHomePlayer2_ID\"),\n", "        (r.\"probHomePlayer3_ID\"), (r.\"probHomePlayer4_ID\"),\n", "        (r.\"probHomePlayer5_ID\"), (r.\"probHomePlayer6_ID\"),\n", "        (r.\"probVisitorPlayer1_ID\"), (r.\"probVisitorPlayer2_ID\"),\n", "        (r.\"probVisitorPlayer3_ID\"), (r.\"probVisitorPlayer4_ID\"),\n", "        (r.\"probVisitorPlayer5_ID\"), (r.\"probVisitorPlayer6_ID\")\n", "    ) AS slot(\"PlayerID\")\n", "      ON slot.\"PlayerID\" IS NOT NULL\n", "    -- filtro sul ruolo: il compagno deve avere lo stesso ruolo di b\n", "    JOIN base comp\n", "      ON comp.\"GameID\"   = r.\"GameID\"\n", "     AND comp.\"PlayerID\" = slot.\"PlayerID\"\n", "     AND comp.\"RuoloCalc\" = b.\"RuoloCalc\"\n", "     AND comp.\"TeamID_auto\" = b.\"TeamID_auto\"\n", "),\n", "\n", "\n", "role_team_attacks_while_on_field AS (\n", "    -- 3) conta quante di queste azioni sono comuni ad entrambi i set\n", "    SELECT\n", "      f.\"PlayerID\"       AS \"PlayerID_base\",\n", "      f.\"GameID\",\n", "      COUNT(*) AS \"RoleTeamAttacksWhileOnField\"\n", "    FROM actions_on_field f\n", "    JOIN role_attacks a\n", "      ON a.\"GameID\" = f.\"GameID\"\n", "     AND a.\"ActionNumber\" = f.\"ActionNumber\"\n", "     AND a.\"PlayerID_base\" = f.\"PlayerID\"\n", "    GROUP BY\n", "      f.\"PlayerID\", f.\"GameID\"\n", "),\n", "\n", "\n", "attk_participation AS (\n", "    SELECT\n", "        attacks.\"GameID\",\n", "        attacks.\"PlayerID\",\n", "        attacks.\"Attacks\",\n", "        tawof.\"TeamAttacksWhileOnField\",\n", "        CASE\n", "            WHEN tawof.\"TeamAttacksWhileOnField\" > 0 THEN \"Attacks\"::FLOAT / tawof.\"TeamAttacksWhileOnField\"\n", "            ELSE 0\n", "        END AS \"AttkParticipation\"\n", "    FROM attacks\n", "    LEFT JOIN team_attacks_while_on_field AS tawof\n", "        ON attacks.\"GameID\" = tawof.\"GameID\" AND attacks.\"PlayerID\" = tawof.\"PlayerID\"\n", "),\n", "\n", "game_attack_participation AS (\n", "    SELECT\n", "        attacks.\"GameID\",\n", "        attacks.\"PlayerID\",\n", "        attacks.\"Attacks\",\n", "        CASE\n", "            WHEN ta.\"TeamAttacks\" > 0 THEN \"Attacks\"::FLOAT / ta.\"TeamAttacks\"\n", "            ELSE 0\n", "        END AS \"GameAttkParticipation\"\n", "    FROM attacks\n", "    LEFT JOIN team_attacks AS ta\n", "        ON attacks.\"GameID\" = ta.\"GameID\" AND attacks.\"PlayerID\" = ta.\"PlayerID\"\n", "),\n", "\n", "role_attack_participation AS (\n", "    SELECT\n", "        attacks.\"GameID\",\n", "        attacks.\"PlayerID\",\n", "        attacks.\"Attacks\",\n", "        CASE\n", "            WHEN rtawf.\"RoleTeamAttacksWhileOnField\" > 0 THEN \"Attacks\"::FLOAT / rtawf.\"RoleTeamAttacksWhileOnField\"\n", "            ELSE 0\n", "        END AS \"RoleAttkParticipation\"\n", "    FROM attacks\n", "    LEFT JOIN role_team_attacks_while_on_field AS rtawf\n", "        ON rta.\"GameID\" = rtawf.\"GameID\" AND rta.\"PlayerID\" = rtawf.\"PlayerID_base\"\n", "),\n", "\n", "role_game_attack_participation AS (\n", "    SELECT\n", "        attacks.\"GameID\",\n", "        attacks.\"PlayerID\",\n", "        attacks.\"Attacks\",\n", "        CASE\n", "            WHEN rta.\"RoleTeamAttacks\" > 0 THEN \"Attacks\"::FLOAT / rta.\"RoleTeamAttacks\"\n", "            ELSE 0\n", "        END AS \"RoleGameAttkParticipation\"\n", "    FROM attacks\n", "    LEFT JOIN role_team_attacks AS rta\n", "        ON attacks.\"GameID\" = rta.\"GameID\" AND attacks.\"PlayerID\" = rta.\"PlayerID\"\n", ")\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "--<PERSON><PERSON><PERSON> tutte le colonne che ho calcolato finora\n", "\n", "SELECT \n", "    base.*, \n", "    c.\"NumAzioniGiocate\",\n", "    c1.\"NumAzioniGiocate1Linea\",\n", "    c2.\"NumAzioniGiocate2Linea\",\n", "    tt.\"TotalTouch\",\n", "    pf.\"PuntiFatti\",\n", "    ns.\"NumSetGiocati\",\n", "    pps.\"PuntiPerSet\",\n", "    ppa.\"PuntiPerAzione\",\n", "    ppa100.\"PuntiPer100Azioni\",\n", "    pf1.\"PuntiFatti1Linea\",\n", "    pf2.\"PuntiFatti2Linea\",\n", "    ppa1.\"PuntiPerAzione1Linea\",\n", "    ppa2.\"PuntiPerAzione2Linea\",\n", "    te.\"TotalError\",\n", "    tep.\"TotalErrorPercentage\",\n", "    tn.\"TotalNegative\",\n", "    tnp.\"TotalNegativePercentage\",\n", "    tp.\"TotalPerfect\",\n", "    tpp.\"TotalPerfectPercentage\",\n", "    tpos.\"TotalPositive\",\n", "    tposp.\"TotalPositivePercentage\",\n", "    attks.\"Attacks\",\n", "    rta.\"RoleTeamAttacks\",\n", "    rtawf.\"RoleTeamAttacksWhileOnField\",\n", "    ta.\"TeamAttacks\",\n", "    tac.\"TeamAttacksWhileOnField\",\n", "    ap.\"AttkParticipation\"\n", "    \n", "    \n", "FROM \n", "    base\n", "LEFT JOIN conteggi AS c\n", "    ON base.\"GameID\" = c.\"GameID\" AND base.\"PlayerID\" = c.\"PlayerID\"\n", "LEFT JOIN conteggi_1_linea AS c1\n", "    ON base.\"GameID\" = c1.\"GameID\" AND base.\"PlayerID\" = c1.\"PlayerID\"\n", "LEFT JOIN conteggi_2_linea AS c2\n", "    ON base.\"GameID\" = c2.\"GameID\" AND base.\"PlayerID\" = c2.\"PlayerID\"\n", "LEFT JOIN total_touch AS tt\n", "    ON base.\"GameID\" = tt.\"GameID\" AND base.\"PlayerID\" = tt.\"PlayerID\"\n", "LEFT JOIN punti_fatti AS pf\n", "    ON base.\"GameID\" = pf.\"GameID\" AND base.\"PlayerID\" = pf.\"PlayerID\"\n", "LEFT JOIN num_set_giocati AS ns\n", "    ON base.\"GameID\" = ns.\"GameID\" AND base.\"PlayerID\" = ns.\"PlayerID\"\n", "LEFT JOIN punti_per_set AS pps\n", "    ON base.\"GameID\" = pps.\"GameID\" AND base.\"PlayerID\" = pps.\"PlayerID\"\n", "LEFT JOIN punti_per_azione AS ppa\n", "    ON base.\"GameID\" = ppa.\"GameID\" AND base.\"PlayerID\" = ppa.\"PlayerID\"\n", "LEFT JOIN punti_per_100azioni AS ppa100\n", "    ON base.\"GameID\" = ppa100.\"GameID\" AND base.\"PlayerID\" = ppa100.\"PlayerID\"\n", "LEFT JOIN punti_fatti1linea AS pf1\n", "    ON base.\"GameID\" = pf1.\"GameID\" AND base.\"PlayerID\" = pf1.\"PlayerID\"\n", "LEFT JOIN punti_fatti2linea AS pf2\n", "    ON base.\"GameID\" = pf2.\"GameID\" AND base.\"PlayerID\" = pf2.\"PlayerID\"\n", "LEFT JOIN punti_per_azione1Linea AS ppa1\n", "    ON base.\"GameID\" = ppa1.\"GameID\" AND base.\"PlayerID\" = ppa1.\"PlayerID\"\n", "LEFT JOIN punti_per_azione2Linea AS ppa2\n", "    ON base.\"GameID\" = ppa2.\"GameID\" AND base.\"PlayerID\" = ppa2.\"PlayerID\"\n", "LEFT JOIN total_error AS te\n", "    ON base.\"GameID\" = te.\"GameID\" AND base.\"PlayerID\" = te.\"PlayerID\"\n", "LEFT JOIN total_error_percentage AS tep\n", "    ON base.\"GameID\" = tep.\"GameID\" AND base.\"PlayerID\" = tep.\"PlayerID\"\n", "LEFT JOIN total_negative AS tn\n", "    ON base.\"GameID\" = tn.\"GameID\" AND base.\"PlayerID\" = tn.\"PlayerID\"\n", "LEFT JOIN total_negative_percentage AS tnp\n", "    ON base.\"GameID\" = tnp.\"GameID\" AND base.\"PlayerID\" = tnp.\"PlayerID\"\n", "LEFT JOIN total_perfect AS tp\n", "    ON base.\"GameID\" = tp.\"GameID\" AND base.\"PlayerID\" = tp.\"PlayerID\"\n", "LEFT JOIN total_perfect_percentage AS tpp\n", "    ON base.\"GameID\" = tpp.\"GameID\" AND base.\"PlayerID\" = tpp.\"PlayerID\"\n", "LEFT JOIN total_positive AS tpos\n", "    ON base.\"GameID\" = tpos.\"GameID\" AND base.\"PlayerID\" = tpos.\"PlayerID\"\n", "LEFT JOIN total_positive_percentage AS tposp\n", "    ON base.\"GameID\" = tposp.\"GameID\" AND base.\"PlayerID\" = tposp.\"PlayerID\"\n", "LEFT JOIN attacks AS attks\n", "    ON base.\"GameID\" = attks.\"GameID\" AND base.\"PlayerID\" = attks.\"PlayerID\"\n", "LEFT JOIN team_attacks_while_on_field AS tac\n", "    ON base.\"GameID\" = tac.\"GameID\" AND base.\"PlayerID\" = tac.\"PlayerID\"\n", "LEFT JOIN team_attacks AS ta\n", "    ON base.\"GameID\" = ta.\"GameID\" AND base.\"TeamID_auto\" = ta.\"TeamID_auto\"\n", "LEFT JOIN role_team_attacks rta\n", "    ON base.\"GameID\" = rta.\"GameID\"\n", "    AND base.\"PlayerID\" = rta.\"PlayerID\"\n", "LEFT JOIN role_team_attacks_while_on_field rtawf\n", "    ON base.\"GameID\"   = rtawf.\"GameID\" AND base.\"PlayerID\" = rtawf.\"PlayerID_base\"\n", "LEFT JOIN attk_participation AS ap\n", "    ON base.\"GameID\" = ap.\"GameID\" AND base.\"PlayerID\" = ap.\"PlayerID\";\n", "\n", "\n", "\n", "\n", "\"\"\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6826927c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv (3.13.2)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}