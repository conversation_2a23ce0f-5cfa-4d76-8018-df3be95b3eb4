import numpy as np 
import pandas as pd  #Realizzato con la versione di Pandas 2.2.3
import polars as pl
import os
import psycopg
from sqlalchemy import create_engine, text

conn = psycopg.connect(
    dbname="db_modena",           # database creato in pgAdmin4
    user="postgres",              # Il tuo nome utente PostgreSQL
    password="AcquaLevissima1",   # La password che hai scelto per 'postgres'
    host="localhost",             # 'localhost' se è sul tuo PC
    port=5432                     # La porta predefinita è 5432
)

# Crea un cursore per eseguire le query
cur = conn.cursor()

#creo un engine per poter usare pandas
engine = create_engine('postgresql+psycopg://postgres:AcquaLevissima1@localhost:5432/db_modena')

#Le funzioni falle con
# try 
#   with conn 
#       istruzioni 
# except rollback



def crea_tabella_players_latest():
    with engine.connect() as conn:
        try:
            with conn.begin():  
                # Elimina la tabella se esiste
                conn.execute(text("DROP TABLE IF EXISTS players_latest"))

                # 1. First create the table
                conn.execute(text("""
                    CREATE TABLE players_latest AS
                    WITH ruolo_frequente AS (
                        SELECT "PlayerID", 
                            MODE() WITHIN GROUP (ORDER BY "RuoloCalc") AS "Ruolo"
                        FROM players_each_game
                        GROUP BY "PlayerID"
                    ),
                    ultimo_numero_maglia AS (
                        SELECT DISTINCT ON ("PlayerID") 
                            "PlayerID", 
                            "NumeroMaglia"
                        FROM players_each_game
                        ORDER BY "PlayerID", "GameID" DESC
                    ),
                    nome_cognome_preferiti AS (
                        SELECT "PlayerID",
                            MODE() WITHIN GROUP (ORDER BY "Nome") AS "Nome",
                            MODE() WITHIN GROUP (ORDER BY "Cognome") AS "Cognome"
                        FROM players_each_game
                        GROUP BY "PlayerID"
                    )
                    SELECT 
                        p."PlayerID",
                        ncp."Nome",
                        ncp."Cognome",
                        rf."Ruolo",
                        CASE rf."Ruolo"
                            WHEN 0 THEN 'RuoloSconosciuto'
                            WHEN 1 THEN 'Libero'
                            WHEN 2 THEN 'Schiacciatore'
                            WHEN 3 THEN 'Opposto'
                            WHEN 4 THEN 'Centrale'
                            WHEN 5 THEN 'Palleggiatore'
                            ELSE 'NonDefinito'
                        END AS "RuoloDescr",
                        unm."NumeroMaglia"
                    FROM (
                        SELECT DISTINCT "PlayerID"
                        FROM players_each_game
                    ) p
                    LEFT JOIN ruolo_frequente rf ON p."PlayerID" = rf."PlayerID"
                    LEFT JOIN ultimo_numero_maglia unm ON p."PlayerID" = unm."PlayerID"
                    LEFT JOIN nome_cognome_preferiti ncp ON p."PlayerID" = ncp."PlayerID"
                """))

                # 2. Then add the PRIMARY KEY constraint
                conn.execute(text("""
                    ALTER TABLE players_latest 
                    ADD PRIMARY KEY ("PlayerID")
                """))

                print("✅ Tabella Players_latest creata con successo con RuoloDescr")

        except Exception as e:
            print(f"❌ Errore nella creazione di Players_latest. Faccio rollback: {e}")
            #conn.rollback()




def aggiungi_colonne_a_players_latest():
    with engine.connect() as conn:
        try:
            with conn.begin():
                # Check if columns exist before adding them
                colonne_da_aggiungere = [
                    ("Nazionalità", "varchar"),
                    ("DataNascita", "varchar"),
                    ("Altezza", "varchar"),
                    ("Peso", "varchar"),
                    ("Schiacciata", "varchar"),
                    ("Muro", "varchar"),
                    ("ManoDominante", "varchar"),
                    ("URL_giocatore", "varchar"),
                    ("URL_immagine", "varchar")
                ]

                for colonna, tipo in colonne_da_aggiungere:
                    conn.execute(text(f"""
                        DO $$
                        BEGIN
                            ALTER TABLE players_latest ADD COLUMN IF NOT EXISTS "{colonna}" {tipo};
                        END $$;
                    """))
                print("✅ Colonne aggiunte a Players_latest con successo (se necessario)")
        except Exception as e:
            print(f"❌ Errore nell'aggiunta delle colonne a Players_latest. Faccio rollback: {e}")




def aggiorna_players_latest():
    with engine.connect() as conn:
        try:
            with conn.begin():
                # 1. Crea una tabella temporanea con i dati aggiornati da players_each_game
                conn.execute(text("""
                    CREATE TEMP TABLE players_temp AS
                    WITH ruolo_frequente AS (
                        SELECT "PlayerID", 
                            MODE() WITHIN GROUP (ORDER BY "RuoloCalc") AS "Ruolo"
                        FROM players_each_game
                        GROUP BY "PlayerID"
                    ),
                    ultimo_numero_maglia AS (
                        SELECT DISTINCT ON ("PlayerID") 
                            "PlayerID", 
                            "NumeroMaglia"
                        FROM players_each_game
                        ORDER BY "PlayerID", "GameID" DESC
                    ),
                    nome_cognome_preferiti AS (
                        SELECT "PlayerID",
                            MODE() WITHIN GROUP (ORDER BY "Nome") AS "Nome",
                            MODE() WITHIN GROUP (ORDER BY "Cognome") AS "Cognome"
                        FROM players_each_game
                        GROUP BY "PlayerID"
                    )
                    SELECT 
                        p."PlayerID",
                        ncp."Nome",
                        ncp."Cognome",
                        rf."Ruolo",
                        CASE rf."Ruolo"
                            WHEN 0 THEN 'RuoloSconosciuto'
                            WHEN 1 THEN 'Libero'
                            WHEN 2 THEN 'Schiacciatore'
                            WHEN 3 THEN 'Opposto'
                            WHEN 4 THEN 'Centrale'
                            WHEN 5 THEN 'Palleggiatore'
                            ELSE 'RuoloNonDefinito'
                        END AS "RuoloDescr",
                        unm."NumeroMaglia"
                    FROM (
                        SELECT DISTINCT "PlayerID"
                        FROM players_each_game
                    ) p
                    LEFT JOIN ruolo_frequente rf ON p."PlayerID" = rf."PlayerID"
                    LEFT JOIN ultimo_numero_maglia unm ON p."PlayerID" = unm."PlayerID"
                    LEFT JOIN nome_cognome_preferiti ncp ON p."PlayerID" = ncp."PlayerID";
                """))

                # 2. Aggiorna i player esistenti
                conn.execute(text("""
                    UPDATE players_latest AS pl
                    SET 
                        "Nome" = pt."Nome",
                        "Cognome" = pt."Cognome",
                        "Ruolo" = pt."Ruolo",
                        "RuoloDescr" = pt."RuoloDescr",
                        "NumeroMaglia" = pt."NumeroMaglia"
                    FROM players_temp pt
                    WHERE pl."PlayerID" = pt."PlayerID";
                """))

                # 3. Inserisci i nuovi player (non già presenti)
                conn.execute(text("""
                    INSERT INTO players_latest (
                        "PlayerID", "Nome", "Cognome", "Ruolo", "RuoloDescr", "NumeroMaglia"
                    )
                    SELECT pt."PlayerID", pt."Nome", pt."Cognome", pt."Ruolo", pt."RuoloDescr", pt."NumeroMaglia"
                    FROM players_temp pt
                    LEFT JOIN players_latest pl ON pl."PlayerID" = pt."PlayerID"
                    WHERE pl."PlayerID" IS NULL;
                """))
                
                # 4. Elimina i PlayerID non più presenti nei dati aggiornati
                conn.execute(text("""
                    DELETE FROM players_latest
                    WHERE "PlayerID" NOT IN (SELECT "PlayerID" FROM players_temp);
                """))

                print("✅ players_latest aggiornato con successo.")

        except Exception as e:
            print(f"❌ Errore nell'aggiornamento di players_latest. Faccio rollback: {e}")



#crea_tabella_players_latest()
aggiungi_colonne_a_players_latest()
#aggiorna_players_latest()





'''
def crea_tabella_players_latest_SQL_standard():
    
    with engine.connect() as conn:  # Connessione per eseguire il SQL
        try:
            with conn.begin():  # Inizia una transazione
    
                cur.execute("DROP TABLE IF EXISTS Players_latest")
                conn.commit()
                
                cur.execute("""
                CREATE TABLE Players_latest AS
                WITH RuoloFrequente AS (
                    -- Determina il ruolo più frequente per ogni giocatore
                    SELECT 
                        PlayerID, 
                        Ruolo,
                        COUNT(*) AS ruolo_count,  --Il count lo fa su tutte le righe che hanno lo stesso PlayerID e Ruolo (ovvero quelle per cui ho fatto il GROUP BY). COnta quante volte un giocatore (PlayerID) ha avuto un certo ruolo. Ovvero quando raggruppa le righe uguali per PlayerID e Ruolo, conta quante erano queste righe uguali
                        

                        ROW_NUMBER() OVER (
                            PARTITION BY PlayerID          --I singoli giocatori li distinguo per PlayerID
                            ORDER BY COUNT(*) DESC, Ruolo  -- Ordina per frequenza (numero di righe con stesso PlayerID e Ruolo) e poi alfabeticamente (il ruolo più basso, per gestire le parità)  (Se un giocatore ha lo stesso numero di ruolo n volte, metto rank 1 al ruolo più basso)
                        ) AS ruolo_rank                    --la colonna che contiene la classifica (1,2,3...) la chiamo ruolo_rank

                        
                        --RANK() OVER (
                        --    PARTITION BY PlayerID   --I singoli giocatori li distinguo per PlayerID
                        --    ORDER BY COUNT(*) DESC  --Li ordino in base a COUNT, ovvero quante volte quel giocatore ha avuto quel ruolo. Li ordino assegnando 1 al ruolo più frequente, 2 al secondo più frequente...
                        --) AS ruolo_rank             --la colonna che contiene la classifica (1,2,3...) la chiamo ruolo_rank
                    FROM PlayersEachGameB
                    WHERE PlayerID IS NOT NULL
                    GROUP BY PlayerID, Ruolo
                ), 
                UltimaMaglia AS (
                    -- Ottiene il numero di maglia dell'ultima partita di ogni giocatore
                    SELECT   --Per ogni giocatore (PlayerID) prendiamo il numero maglia che ha il GameID più alto
                        PlayerID, 
                        NumeroMaglia
                    FROM PlayersEachGameB p1
                    WHERE GameID = (  --Il GameID più alto viene trovato ogni volta per ogni riga di PlayersEachGameB
                        SELECT MAX(GameID) 
                        FROM PlayersEachGameB p2
                        WHERE p1.PlayerID = p2.PlayerID  
                    )
                --Quindi prima seleziono tutti i PlayerID e loro numeri maglia (con molti duplicati)
                --poi prendo solo quelli WHERE il GameID è il più alto
                
                )
                SELECT 
                    p.PlayerID, 
                    p.Nome, 
                    p.Cognome, 
                    u.NumeroMaglia,  -- Numero maglia dall'ultima partita
                    r.Ruolo  -- Ruolo più frequente
                FROM (SELECT DISTINCT PlayerID, Nome, Cognome FROM PlayersEachGameB) p
                LEFT JOIN RuoloFrequente r ON p.PlayerID = r.PlayerID AND r.ruolo_rank = 1
                LEFT JOIN UltimaMaglia u ON p.PlayerID = u.PlayerID;
                """)
                
                conn.commit()
                print("Tabella Players_latest creata con successo")
                
        except Exception as e:
            print(f"Errore nella chiamata di crea_tabella_players_latest. Faccio rollback: {e}")
            conn.rollback()
'''         
#Può capitare che in questa tabella compaia lo stesso giocatore con ruoli diversi. Se succede, significa che quel giocatore ha fatto lo stesso numero di partite con quei ruoli, quindi




