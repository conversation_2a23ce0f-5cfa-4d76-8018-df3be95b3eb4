import numpy as np 
import pandas as pd  #Realizzato con la versione di Pandas 2.2.3
import polars as pl
import os
import psycopg
from sqlalchemy import create_engine, text


conn = psycopg.connect(
    dbname="db_modena",           # database creato in pgAdmin4
    user="postgres",              # Il tuo nome utente PostgreSQL
    password="AcquaLevissima1",   # La password che hai scelto per 'postgres'
    host="localhost",             # 'localhost' se è sul tuo PC
    port=5432                     # La porta predefinita è 5432
)

cur = conn.cursor()


'''
# Verifica i giocatori in players_each_game per GameID 565
cur.execute("""
SELECT p."GameID", p."TeamID", t."TeamName", p."NumeroMaglia", p."Nome", p."Cognome"
FROM players_each_game p
JOIN teams t ON p."TeamID" = t."TeamID"
WHERE p."GameID" = 565
AND p."NumeroMaglia" IN (10, 12, 22, 9, 17, 8, 18, 7, 5, 1)
ORDER BY p."TeamID", p."NumeroMaglia";
""")

results = cur.fetchall()
print("\nGiocatori trovati in players_each_game per GameID 565:")
print("GameID | TeamID | TeamName | Maglia | Nome | Cognome")
print("-" * 70)
for row in results:
    print(f"{row[0]} | {row[1]} | {row[2]} | {row[3]} | {row[4]} | {row[5]}")
'''






'''
# Analisi più dettagliata dei giocatori mancanti
cur.execute("""
WITH missing_players AS (
    SELECT DISTINCT 
        r."GameID",
        CASE 
            WHEN r."whichTeam" = false THEN g."IDHomeTeam"
            ELSE g."IDVisitorTeam"
        END as team_id,
        CASE 
            WHEN r."whichTeam" = false THEN t_home."TeamName"
            ELSE t_visitor."TeamName"
        END as team_name,
        UNNEST(ARRAY[
            r."HomePlayer1", r."HomePlayer2", r."HomePlayer3",
            r."HomePlayer4", r."HomePlayer5", r."HomePlayer6"
        ]) as "NumeroMaglia"
    FROM rilevations r
    JOIN games g ON r."GameID" = g."GameID"
    JOIN teams t_home ON g."IDHomeTeam" = t_home."TeamID"
    JOIN teams t_visitor ON g."IDVisitorTeam" = t_visitor."TeamID"
    WHERE "NumeroMaglia" IS NOT NULL
)
SELECT 
    team_name,
    COUNT(*) as missing_count,
    array_agg(DISTINCT "GameID") as game_ids,
    array_agg(DISTINCT "NumeroMaglia") as missing_numbers
FROM missing_players mp
LEFT JOIN players_each_game peg ON 
    peg."GameID" = mp."GameID" AND
    peg."TeamID" = mp.team_id AND
    peg."NumeroMaglia" = mp."NumeroMaglia"
WHERE peg."PlayerID" IS NULL
GROUP BY team_name
ORDER BY missing_count DESC;
""")

team_analysis = cur.fetchall()
print("\nAnalisi per squadra dei giocatori mancanti:")
print("Squadra | Num. Mancanti | Partite coinvolte | Numeri maglia mancanti")
print("-" * 100)
for row in team_analysis:
    print(f"{row[0]} | {row[1]} | {row[2]} | {row[3]}")
'''




