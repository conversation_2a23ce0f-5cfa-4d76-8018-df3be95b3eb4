import numpy as np 
import pandas as pd  #Realizzato con la versione di Pandas 2.2.3
import polars as pl
import os
import psycopg
from sqlalchemy import create_engine, text

conn = psycopg.connect(
    dbname="db_modena",           # database creato in pgAdmin4
    user="postgres",              # Il tuo nome utente PostgreSQL
    password="AcquaLevissima1",   # La password che hai scelto per 'postgres'
    host="localhost",             # 'localhost' se è sul tuo PC
    port=5432                     # La porta predefinita è 5432
)

# Crea un cursore per eseguire le query
cur = conn.cursor()

engine = create_engine('postgresql+psycopg://postgres:AcquaLevissima1@localhost:5432/db_modena')


#Creo la tabella setter_calls
cur.execute("DROP TABLE IF EXISTS setter_calls")  #La cancello se esiste
conn.commit()

cur.execute("""
CREATE TABLE IF NOT EXISTS setter_calls (
    "CallID" varchar,
    "Col2" varchar,
    "Description" varchar,
    PRIMARY KEY ("CallID")
);
""")
conn.commit()

#Leggo il file csv come dataframe
df_setter_calls = pd.read_csv('data/Tabelle_in_csv/SetterCalls.csv')
df_setter_calls = df_setter_calls.iloc[:, :3]  #prendo solo le prime 3 colonne

# Rinomina le colonne per chiarezza
df_setter_calls.columns = ['CallID', 'Col2', 'Description']

# Inserisci i dati nella tabella
df_setter_calls.to_sql('setter_calls', engine, if_exists='replace', index=False)
print("Tabella setter_calls creata con successo!")




#Creo la tabella AttkCombinations
cur.execute("DROP TABLE IF EXISTS attk_combinations")  #La cancello se esiste
conn.commit()

cur.execute("""
CREATE TABLE attk_combinations (
    "CombinationID" varchar,
    "ZonaPartenzaAttacco" integer,
    "Col3" varchar,
    "BallType" varchar,
    "Color" integer,
    "Descrizione" varchar,
    "Col6" varchar,
    "Col9" varchar,
    "Col10" varchar,
    PRIMARY KEY ("CombinationID")
);
""")
conn.commit()




#Leggo il file csv come dataframe
df_attk_combinations = pd.read_csv('data/Tabelle_in_csv/Combinations.csv')
df_attk_combinations = df_attk_combinations.iloc[:, [0,1,2,3,4,5,8,9]]  #prendo solo queste colonne

# Rinomina le colonne per chiarezza
df_attk_combinations.columns = ['CombinationID', 'ZonaPartenzaAttacco', 'Col3', 'BallType', 'Descrizione', 'Col6', 'Col9', 'Col10']

# Inserisci i dati nella tabella
df_attk_combinations.to_sql('attk_combinations', engine, if_exists='replace', index=False)
print("Tabella attk_combinations creata con successo!")




#Creo la tabella Roles
cur.execute("DROP TABLE IF EXISTS roles")  #La cancello se esiste
conn.commit()

cur.execute("""
CREATE TABLE roles (
    "ID_Ruolo" integer,
    "Ruolo" varchar,
    PRIMARY KEY ("ID_Ruolo")
);
""")
conn.commit()

#Leggo il file csv come dataframe
df_roles = pd.read_csv('data/Tabelle_in_csv/RuoliDataVolley.csv')

#Inserisci i dati nella tabella
df_roles.to_sql('roles', engine, if_exists='replace', index=False)
print("Tabella roles creata con successo!")







