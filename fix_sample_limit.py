# CORREZIONE PER RIMUOVERE IL LIMITE DI 2000 RECORD
# Trova questa riga nel tuo codice:

# VECCHIO CODICE (da sostituire):
# df1_sample = df1.head(2000)
# debug_messages.append(f"🔍 Analizzando {len(df1_sample)} record di {source_fond}")

# NUOVO CODICE (sostituisci con questo):
df1_sample = df1  # Usa tutti i dati invece di limitare a 2000
debug_messages.append(f"🔍 Analizzando {len(df1_sample)} record di {source_fond} (dataset completo)")

# OPZIONALE: Se il dataset è molto grande e vuoi un compromesso tra velocità e completezza:
# df1_sample = df1.head(10000)  # Usa 10000 record invece di 2000
# debug_messages.append(f"🔍 Analizzando {len(df1_sample)} record di {source_fond} (campione di {len(df1_sample)})")

# ALTERNATIVA: Per dataset molto grandi, puoi usare un campione casuale:
# df1_sample = df1.sample(n=min(5000, len(df1)), random_state=42)  # Campione casuale di max 5000 record
# debug_messages.append(f"🔍 Analizzando {len(df1_sample)} record di {source_fond} (campione casuale)")

print("🔧 Correzione applicata!")
print("Ora l'analisi userà tutti i dati disponibili invece di limitarsi ai primi 2000 record.")
print("Questo dovrebbe mostrare il numero corretto di osservazioni.")
print("⚠️ Nota: Con dataset molto grandi, l'elaborazione potrebbe essere più lenta.")
