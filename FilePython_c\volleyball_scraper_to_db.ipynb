{"cells": [{"cell_type": "markdown", "id": "6ba5b267", "metadata": {}, "source": ["Volleyball Scraper - Programma per estrarre dati dei giocatori da volleybox.net\n", "\n", "Versione per Windows con ricerca migliorata per trovare l'URL corretto"]}, {"cell_type": "code", "execution_count": 100, "id": "47c5aee2", "metadata": {}, "outputs": [], "source": ["import sys\n", "import re\n", "import unicodedata\n", "import time\n", "import json\n", "import random\n", "import os\n", "import requests\n", "from bs4 import BeautifulSoup\n", "from pathlib import Path\n", "from playwright.sync_api import sync_playwright, TimeoutError\n", "\n", "import numpy as np \n", "import pandas as pd  #Realizzato con la versione di Pandas 2.2.3\n", "import polars as pl\n", "import psycopg\n", "from sqlalchemy import create_engine, text"]}, {"cell_type": "code", "execution_count": 101, "id": "62d9e7bf", "metadata": {}, "outputs": [], "source": ["# Directory per salvare i cookie e i dati - adattate per Windows\n", "DATA_DIR = Path(os.path.expanduser(\"~\")) / \"Documents\" / \"ModenaVolley\" / \"VolleyballScraper\" / \"data\"\n", "COOKIES_FILE = DATA_DIR / 'cookies.json'\n", "RESULTS_DIR = DATA_DIR / 'results'\n", "\n", "# Assicurati che le directory esistano\n", "DATA_DIR.mkdir(parents=True, exist_ok=True)\n", "RESULTS_DIR.mkdir(parents=True, exist_ok=True)"]}, {"cell_type": "code", "execution_count": 102, "id": "60d4b833", "metadata": {}, "outputs": [], "source": ["def normalize_name(name):\n", "    \"\"\"\n", "    Normalizza il nome per la costruzione dell'URL.\n", "    <PERSON><PERSON><PERSON>ve accenti, converte in minuscolo e sostituisce spazi con trattini.\n", "    \"\"\"\n", "    # Rimuovi accenti e caratteri speciali\n", "    name = unicodedata.normalize('NFKD', name).encode('ASCII', 'ignore').decode('utf-8')\n", "    # <PERSON><PERSON>i in minuscolo\n", "    name = name.lower()\n", "    # Sostituisci spazi con trattini\n", "    name = re.sub(r'\\s+', '-', name)\n", "    # Rimuovi caratteri non alfanumerici (tranne trat<PERSON>i)\n", "    name = re.sub(r'[^a-z0-9\\-+]', '', name)\n", "    return name"]}, {"cell_type": "code", "execution_count": 103, "id": "8f6672d1", "metadata": {}, "outputs": [], "source": ["def trova_url_volleybox_google(nome, cognome, callback=None):\n", "    API_KEY = \"22bf2d85466323a809aec14d1ee765c259a29e68\"\n", "    #url = f\"https://google.serper.dev/search?q=site%3Avolleybox.net%2Fit+{nome}+{cognome}&gl=it&hl=it&apiKey={API_KEY}\"\n", "    #url = url.replace(\" \", \"+\")\n", "    \n", "    query = f\"site:volleybox.net/it {nome} {cognome} club\".replace(\" \", \"+\")  # Sostituisce gli spazi con +\n", "    url = f\"https://google.serper.dev/search?q={query}&gl=it&hl=it&apiKey={API_KEY}\"\n", "    \n", "    print(url)\n", "    payload = {}\n", "    headers = {}\n", "\n", "    response = requests.request(\"GET\", url, headers=headers, data=payload)\n", "    try:\n", "        dizionario = response.json()  # Parse the JSON response\n", "        \n", "        lista_siti = dizionario[\"organic\"]\n", "        print(f\"Found {len(lista_siti)} results\")\n", "        \n", "        url_giusto = \"\"\n", "        siti_papabili = [\n", "            sito[\"link\"]\n", "            for sito in lista_siti\n", "            if sito[\"link\"].startswith(\"https://volleybox.net/it\") and\n", "            sito[\"link\"].endswith(\"/clubs\")\n", "        ]\n", "                \n", "        #Se trovo 0 siti papabili o più di 1 sito papabile, allora non aggiungo niente per sicurezza, e lo farò eventualmente a mano.\n", "        if len(siti_papabili) == 0:\n", "            print(\"Nessun URL papabile trovato\")\n", "            return None\n", "        elif len(siti_papabili) == 1:\n", "            return siti_papabili[0]\n", "        else:\n", "            print(\"Sono stati trovati più URL papabili, quindi c'era il rischio di aggiungere info di un giocatore chiamato in modo simile. Non aggiungiamo niente\")\n", "            print(siti_papabili)\n", "            return None\n", "            \n", "    except (j<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>) as e:\n", "        print(f\"Errore nel parsing della risposta: {e}\")\n", "        print(f\"Risposta ricevuta: {response.text[:200]}...\")  # Print first 200 chars of response\n", "        return None\n"]}, {"cell_type": "code", "execution_count": 104, "id": "4e8db87d", "metadata": {}, "outputs": [], "source": ["def get_advanced_fingerprint_script():\n", "    \"\"\"\n", "    Genera uno script JavaScript avanzato per modificare il fingerprint del browser.\n", "    \"\"\"\n", "    script = \"\"\"\n", "    // Nasconde WebDriver\n", "    Object.defineProperty(navigator, 'webdriver', {\n", "        get: () => false,\n", "    });\n", "    \n", "    / Modifica il fingerprint di Canvas\n", "    const originalGetContext = HTMLCanvasElement.prototype.getContext;\n", "    HTMLCanvasElement.prototype.getContext = function(type, attributes) {\n", "        const context = originalGetContext.call(this, type, attributes);\n", "        if (type === '2d') {\n", "            const originalFillText = context.fillText;\n", "            context.fillText = function() {\n", "                const args = Array.from(arguments);\n", "                // Aggiungi un leggero rumore alle coordinate\n", "                if (typeof args[1] === 'number' && typeof args[2] === 'number') {\n", "                    args[1] += Math.random() * 0.2 - 0.1;\n", "                    args[2] += Math.random() * 0.2 - 0.1;\n", "                }\n", "                return originalFillText.apply(this, args);\n", "            };\n", "        }\n", "        \n", "        // Modifica WebGL fingerprint\n", "        if (type === 'webgl' || type === 'experimental-webgl' || type === 'webgl2') {\n", "            const originalGetParameter = context.getParameter;\n", "            context.getParameter = function(parameter) {\n", "                // Modifica vendor e renderer\n", "                if (parameter === 0x1F00) { // VENDOR\n", "                    return 'Google Inc.';\n", "                }\n", "                if (parameter === 0x1F01) { // RENDERER\n", "                    return 'ANGLE (Intel(R) UHD Graphics Direct3D11 vs_5_0 ps_5_0)';\n", "                }\n", "                // Restituisci il valore originale per altri parametri\n", "                return originalGetParameter.call(this, parameter);\n", "            };\n", "        }\n", "        \n", "        return context;\n", "    };\n", "    \n", "    // Aggiungi funzioni di Chrome\n", "    window.chrome = {\n", "        app: {\n", "            isInstalled: false,\n", "        },\n", "        webstore: {\n", "            onInstallStageChanged: {},\n", "            onDownloadProgress: {},\n", "        },\n", "        runtime: {\n", "            PlatformOs: {\n", "                MAC: 'mac',\n", "                WIN: 'win',\n", "                ANDROID: 'android',\n", "                CROS: 'cros',\n", "                LINUX: 'linux',\n", "                OPENBSD: 'openbsd',\n", "            },\n", "            PlatformArch: {\n", "                ARM: 'arm',\n", "                X86_32: 'x86-32',\n", "                X86_64: 'x86-64',\n", "            },\n", "            PlatformNaclArch: {\n", "                ARM: 'arm',\n", "                X86_32: 'x86-32',\n", "                X86_64: 'x86-64',\n", "            },\n", "            RequestUpdateCheckStatus: {\n", "                THROTTLED: 'throttled',\n", "                NO_UPDATE: 'no_update',\n", "                UPDATE_AVAILABLE: 'update_available',\n", "            },\n", "            OnInstalledReason: {\n", "                INSTALL: 'install',\n", "                UPDATE: 'update',\n", "                CHROME_UPDATE: 'chrome_update',\n", "                SHARED_MODULE_UPDATE: 'shared_module_update',\n", "            },\n", "            OnRestartRequiredReason: {\n", "                APP_UPDATE: 'app_update',\n", "                OS_UPDATE: 'os_update',\n", "                PERIODIC: 'periodic',\n", "            },\n", "        },\n", "    };\n", "    \n", "    // Aggiungi i plugin\n", "    const mockPlugins = [\n", "        { name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer', description: 'Portable Document Format' },\n", "        { name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai', description: '' },\n", "        { name: 'Native Client', filename: 'internal-nacl-plugin', description: '' }\n", "    ];\n", "    \n", "    Object.defineProperty(navigator, 'plugins', {\n", "        get: function() {\n", "            const plugins = [];\n", "            for (const plugin of mockPlugins) {\n", "                plugins.push(plugin);\n", "            }\n", "            plugins.__proto__ = Object.getPrototypeOf([]);\n", "            return plugins;\n", "        },\n", "    });\n", "    \n", "    // Aggiungi lingue\n", "    Object.defineProperty(navigator, 'languages', {\n", "        get: () => ['it-IT', 'it', 'en-US', 'en'],\n", "    });\n", "    \"\"\"\n", "    \n", "    return script"]}, {"cell_type": "code", "execution_count": 105, "id": "bdbb683b", "metadata": {}, "outputs": [], "source": ["\n", "def setup_stealth_browser():\n", "    \"\"\"\n", "    Configura un browser stealth con Playwright per evitare il rilevamento di Cloudflare.\n", "    \n", "    Returns:\n", "        tuple: (playwright, browser, context, page)\n", "    \"\"\"\n", "    playwright = sync_playwright().start()\n", "    \n", "    # Configura il browser con impostazioni avanzate per evitare il rilevamento\n", "    browser = playwright.chromium.launch(\n", "        headless=True,\n", "        args=[\n", "            '--disable-blink-features=AutomationControlled',\n", "            '--disable-features=IsolateOrigins,site-per-process',\n", "            '--no-sandbox',\n", "            '--disable-dev-shm-usage',\n", "            '--disable-accelerated-2d-canvas',\n", "            '--no-first-run',\n", "            '--no-zygote',\n", "            '--disable-gpu'\n", "        ]\n", "    )\n", "    \n", "    # Crea un contesto con impostazioni avanzate\n", "    context = browser.new_context(\n", "        viewport={\"width\": 1920, \"height\": 1080},\n", "        user_agent=\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36\",\n", "        locale=\"it-IT\",\n", "        timezone_id=\"Europe/Rome\"\n", "    )\n", "    \n", "    # Modifica il fingerprint del browser per evitare il rilevamento\n", "    context.add_init_script(get_advanced_fingerprint_script())\n", "    \n", "    # Imposta header HTTP realistici\n", "    context.set_extra_http_headers({\n", "        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',\n", "        'Accept-Language': 'it-IT,it;q=0.8,en-US;q=0.5,en;q=0.3',\n", "        'Accept-Encoding': 'gzip, deflate, br',\n", "        'DNT': '1',\n", "        'Connection': 'keep-alive',\n", "        'Upgrade-Insecure-Requests': '1',\n", "        'Sec-Fetch-Dest': 'document',\n", "        'Sec-Fetch-Mode': 'navigate',\n", "        'Sec-Fetch-Site': 'none',\n", "        'Sec-Fetch-User': '?1',\n", "        'Cache-Control': 'max-age=0',\n", "    })\n", "    \n", "    # Crea una nuova pagina\n", "    page = context.new_page()\n", "    \n", "    return playwright, browser, context, page"]}, {"cell_type": "code", "execution_count": 106, "id": "3b5b4665", "metadata": {}, "outputs": [], "source": ["\n", "def save_cookies(context, file_path=COOKIES_FILE):\n", "    \"\"\"\n", "    Salva i cookie del contesto in un file.\n", "    \n", "    Args:\n", "        context: Contesto del browser Playwright\n", "        file_path: Percorso del file in cui salvare i cookie\n", "    \"\"\"\n", "    cookies = context.cookies()\n", "    with open(file_path, 'w', encoding='utf-8') as f:\n", "        json.dump(cookies, f, indent=2)\n", "    print(f\"<PERSON><PERSON> salvati in {file_path}\")"]}, {"cell_type": "code", "execution_count": 107, "id": "70c83cbc", "metadata": {}, "outputs": [], "source": ["\n", "def load_cookies(context, file_path=COOKIES_FILE):\n", "    \"\"\"\n", "    Carica i cookie da un file nel contesto.\n", "    \n", "    Args:\n", "        context: Contesto del browser Playwright\n", "        file_path: Percorso del file da cui caricare i cookie\n", "        \n", "    Returns:\n", "        bool: True se i cookie sono stati caricati con successo, False altrimenti\n", "    \"\"\"\n", "    if not os.path.exists(file_path):\n", "        print(f\"File dei cookie {file_path} non trovato\")\n", "        return False\n", "    \n", "    try:\n", "        with open(file_path, 'r', encoding='utf-8') as f:\n", "            cookies = json.load(f)\n", "        context.add_cookies(cookies)\n", "        print(f\"<PERSON>ie caricati da {file_path}\")\n", "        return True\n", "    except Exception as e:\n", "        print(f\"Errore nel caricamento dei cookie: {str(e)}\")\n", "        return False"]}, {"cell_type": "code", "execution_count": 108, "id": "ca977e22", "metadata": {}, "outputs": [], "source": ["\n", "def simulate_human_behavior(page):\n", "    \"\"\"\n", "    Simula comportamento umano sulla pagina per evitare il rilevamento.\n", "    \n", "    Args:\n", "        page: <PERSON><PERSON><PERSON>\n", "    \"\"\"\n", "    # Ritardo casuale iniziale\n", "    time.sleep(random.uniform(1, 3))\n", "    \n", "    # Movimento del mouse casuale\n", "    for _ in range(3):\n", "        page.mouse.move(\n", "            random.randint(100, 800),\n", "            random.randint(100, 600),\n", "            steps=random.randint(5, 10)\n", "        )\n", "        time.sleep(random.uniform(0.3, 0.7))\n", "    \n", "    # Scroll naturale\n", "    for _ in range(random.randint(3, 5)):\n", "        page.mouse.wheel(0, random.randint(100, 300))\n", "        time.sleep(random.uniform(0.5, 1.5))\n", "    \n", "    # <PERSON><PERSON> finale\n", "    time.sleep(random.uniform(0.5, 1.5))\n"]}, {"cell_type": "code", "execution_count": 109, "id": "fe41af94", "metadata": {}, "outputs": [], "source": ["\n", "def extract_player_data_with_js(page):\n", "    \"\"\"\n", "    Estrae i dati del giocatore utilizzando JavaScript direttamente nella pagina.\n", "\n", "    Args:\n", "        page: <PERSON><PERSON><PERSON>\n", "\n", "    Returns:\n", "        dict: <PERSON><PERSON> g<PERSON>ore\n", "    \"\"\"\n", "    try:\n", "        return page.evaluate(\"\"\"() => {\n", "            const container = document.querySelector('div.new_box.pRelative');\n", "            if (!container) {\n", "                console.log('Container non trovato');\n", "                return {};\n", "            }\n", "\n", "            const data = {};\n", "            const dtElements = container.querySelectorAll('dt.info-header');\n", "            const ddElements = container.querySelectorAll('dd.info-data');\n", "\n", "            dtElements.forEach((dt, index) => {\n", "                const label = dt.textContent.trim().toLowerCase();\n", "                const valueElement = ddElements[index];\n", "                if (!valueElement) return;\n", "\n", "                let value = valueElement.textContent.trim();\n", "                \n", "                // Se il valore è un link (es. nazionalità), prendiamo solo il testo\n", "                const link = valueElement.querySelector('a');\n", "                if (link) {\n", "                    value = link.textContent.trim();\n", "                }\n", "\n", "                if (label.includes('nazionalità')) data.nazionalita = value;\n", "                if (label.includes('posizione')) data.posizione = value;\n", "                if (label.includes('data di nascita')) data.data_nascita = value;\n", "                if (label.includes('altezza')) data.altezza = value;\n", "                if (label.includes('peso')) data.peso = value;\n", "                if (label.includes('schiacciata')) data.schiacciata = value;\n", "                if (label.includes('muro')) data.muro = value;\n", "                if (label.includes('mano dominante')) data.mano_dominante = value;\n", "            });\n", "\n", "            return data;\n", "        }\"\"\")\n", "    except Exception as e:\n", "        print(f\"Errore nell'estrazione JavaScript: {str(e)}\")\n", "        return {}\n"]}, {"cell_type": "code", "execution_count": 110, "id": "bdbec05a", "metadata": {}, "outputs": [], "source": ["\n", "def get_player_info(first_name, last_name, use_existing_cookies=True, callback=None):\n", "    \"\"\"\n", "    Estrae le informazioni di un giocatore da volleybox.net utilizzando Playwright.\n", "    \n", "    Args:\n", "        first_name (str): <PERSON><PERSON> del gio<PERSON>ore\n", "        last_name (str): Cognome del giocatore\n", "        use_existing_cookies (bool): Se utilizzare i cookie esistenti\n", "        callback (function): Funzione di callback per aggiornare l'interfaccia\n", "        \n", "    Returns:\n", "        dict: Dizionario contenente le informazioni del giocatore\n", "    \"\"\"\n", "    # Trova l'URL corretto tramite Google\n", "    url = trova_url_volleybox_google(first_name, last_name, callback)\n", "    \n", "    if not url:\n", "        return {\n", "            \"nome\": f\"{first_name} {last_name}\",\n", "            \"url\": None,\n", "            \"nazionalità\": None,\n", "            \"posizione\": None,\n", "            \"data_di_nascita\": None,\n", "            \"altezza\": None,\n", "            \"peso\": None,\n", "            \"schiacciata\": None,\n", "            \"muro\": None,\n", "            \"mano_dominante\": None,\n", "            \"errore\": \"Impossibile trovare l'URL del giocatore\"\n", "        }\n", "    \n", "    # Inizializza il dizionario per i risultati\n", "    player_info = {\n", "        \"nome\": f\"{first_name} {last_name}\",\n", "        \"url\": url,\n", "        \"nazionalità\": None,\n", "        \"posizione\": None,\n", "        \"data_di_nascita\": None,\n", "        \"altezza\": None,\n", "        \"peso\": None,\n", "        \"schiacciata\": None,\n", "        \"muro\": None,\n", "        \"mano_dominante\": None,\n", "        \"errore\": None\n", "    }\n", "    \n", "    # Funzione per aggiornare lo stato\n", "    def update_status(message):\n", "        print(message)\n", "        if callback:\n", "            callback(message)\n", "    \n", "    playwright = None\n", "    browser = None\n", "    context = None\n", "    \n", "    try:\n", "        # Inizializza il browser stealth\n", "        update_status(\"Inizializzando il browser...\")\n", "        playwright, browser, context, page = setup_stealth_browser()\n", "        \n", "        # Carica i cookie se richiesto e disponibili\n", "        if use_existing_cookies:\n", "            load_cookies(context)\n", "        \n", "        # Naviga alla pagina del giocatore\n", "        update_status(f\"Navigando a {url}...\")\n", "        page.goto(url, wait_until=\"networkidle\", timeout=100000)\n", "        \n", "        # Simula comportamento umano\n", "        update_status(\"Simulando comportamento umano...\")\n", "        simulate_human_behavior(page)\n", "        \n", "        # Verifica se siamo stati bloccati da Cloudflare\n", "        if \"Cloudflare\" in page.title() and \"challenge\" in page.title():\n", "            update_status(\"<PERSON><PERSON><PERSON><PERSON> challenge <PERSON><PERSON><PERSON><PERSON>, attendere...\")\n", "            # At<PERSON>i che il challenge venga risolto (pu<PERSON> richiedere fino a 50 secondi)\n", "            page.wait_for_selector(\"body\", state=\"visible\", timeout=50000)\n", "            page.wait_for_load_state(\"networkidle\", timeout=50000)\n", "            \n", "            # Salva i cookie dopo aver superato il challenge\n", "            save_cookies(context)\n", "            \n", "            # Ricarica la pagina\n", "            update_status(\"Ricaricando la pagina...\")\n", "            page.reload(wait_until=\"networkidle\", timeout=50000)\n", "            simulate_human_behavior(page)\n", "        \n", "        # Salva i cookie per usi futuri\n", "        save_cookies(context)\n", "        \n", "        # Verifica se la pagina è stata caricata correttamente\n", "        if \"404\" in page.title() or \"non trovato\" in page.title().lower():\n", "            player_info[\"errore\"] = \"Giocatore non trovato\"\n", "            return player_info\n", "        \n", "        # Estrai le informazioni dalla pagina\n", "        update_status(\"Estraendo le informazioni del giocatore...\")\n", "        \n", "        # Metodo 1: Estrazione diretta con selettori CSS\n", "        try:\n", "            # Nazionalità\n", "            nationality_element = page.query_selector(\"div:text('Nazionalità') + div\")\n", "            if nationality_element:\n", "                player_info[\"nazionalità\"] = nationality_element.inner_text().strip()\n", "            \n", "            # Posizione\n", "            position_element = page.query_selector(\"div:text('Posizione') + div\")\n", "            if position_element:\n", "                player_info[\"posizione\"] = position_element.inner_text().strip()\n", "            \n", "            # Data di nascita\n", "            birth_date_element = page.query_selector(\"div:text('Data di nascita') + div\")\n", "            if birth_date_element:\n", "                player_info[\"data_di_nascita\"] = birth_date_element.inner_text().strip()\n", "            \n", "            # Altezza\n", "            height_element = page.query_selector(\"div:text('Altezza') + div\")\n", "            if height_element:\n", "                player_info[\"altezza\"] = height_element.inner_text().strip()\n", "            \n", "            # Peso\n", "            weight_element = page.query_selector(\"div:text('Peso') + div\")\n", "            if weight_element:\n", "                player_info[\"peso\"] = weight_element.inner_text().strip()\n", "                \n", "            # Schiacciata\n", "            weight_element = page.query_selector(\"div:text('Schia<PERSON><PERSON>') + div\")\n", "            if weight_element:\n", "                player_info[\"schiacciata\"] = weight_element.inner_text().strip()\n", "\n", "            # Muro\n", "            weight_element = page.query_selector(\"div:text('Muro') + div\")\n", "            if weight_element:\n", "                player_info[\"muro\"] = weight_element.inner_text().strip()\n", "\n", "            # <PERSON><PERSON> dominante\n", "            weight_element = page.query_selector(\"div:text('Mano dominante') + div\")\n", "            if weight_element:\n", "                player_info[\"mano_dominante\"] = weight_element.inner_text().strip()\n", "                \n", "        except Exception as e:\n", "            update_status(f\"Errore nell'estrazione con selettori: {str(e)}\")\n", "        \n", "        # Se non abbiamo trovato nessuna informazione, prova con JavaScript\n", "        if not any([player_info[\"nazionalità\"], \n", "                    player_info[\"posizione\"], \n", "                    player_info[\"data_di_nascita\"], \n", "                    player_info[\"altezza\"], \n", "                    player_info[\"peso\"],\n", "                    player_info[\"schiacciata\"],\n", "                    player_info[\"muro\"],\n", "                    player_info[\"mano_dominante\"]\n", "                    ]):\n", "            update_status(\"Tentativo di estrazione con JavaScript...\")\n", "            player_data = extract_player_data_with_js(page)\n", "            \n", "            if player_data:\n", "                if player_data.get('nazionalita'):\n", "                    player_info[\"nazionalità\"] = player_data['nazionalita']\n", "                if player_data.get('posizione'):\n", "                    player_info[\"posizione\"] = player_data['posizione']\n", "                if player_data.get('data_nascita'):\n", "                    player_info[\"data_di_nascita\"] = player_data['data_nascita']\n", "                if player_data.get('altezza'):\n", "                    player_info[\"altezza\"] = player_data['altezza']\n", "                if player_data.get('peso'):\n", "                    player_info[\"peso\"] = player_data['peso']\n", "                if player_data.get('schiacciata'):\n", "                    player_info[\"schiacciata\"] = player_data['schiacciata']\n", "                if player_data.get('muro'):\n", "                    player_info[\"muro\"] = player_data['muro']\n", "                if player_data.get('mano_dominante'):\n", "                    player_info[\"mano_dominante\"] = player_data['mano_dominante']\n", "        \n", "        # Salva uno screenshot per debug\n", "        screenshot_path = DATA_DIR / f\"{normalize_name(first_name)}_{normalize_name(last_name)}.png\"\n", "        page.screenshot(path=str(screenshot_path))\n", "        update_status(f\"Screenshot salvato in {screenshot_path}\")\n", "        \n", "        # Se ancora non abbiamo trovato informazioni, salva l'HTML per debug\n", "        if not any([player_info[\"nazionalità\"], player_info[\"posizione\"], \n", "                   player_info[\"data_di_nascita\"], player_info[\"altezza\"], \n", "                   player_info[\"peso\"],\n", "                   player_info[\"schiacciata\"],\n", "                   player_info[\"muro\"],\n", "                   player_info[\"mano_dominante\"]\n", "                   ]):\n", "            html_path = DATA_DIR / f\"{normalize_name(first_name)}_{normalize_name(last_name)}.html\"\n", "            with open(html_path, \"w\", encoding=\"utf-8\") as f:\n", "                f.write(page.content())\n", "            update_status(f\"HTML salvato in {html_path}\")\n", "            \n", "            # Prova un approccio alternativo cercando nella struttura JSON-LD\n", "            try:\n", "                update_status(\"Tentativo di estrazione da JSON-LD...\")\n", "                json_ld_data = page.evaluate(\"\"\"() => {\n", "                    const jsonLdScripts = Array.from(document.querySelectorAll('script[type=\"application/ld+json\"]'));\n", "                    for (const script of jsonLdScripts) {\n", "                        try {\n", "                            const data = JSON.parse(script.textContent);\n", "                            if (data.birthDate || data.height || data.gender) {\n", "                                return data;\n", "                            }\n", "                        } catch (e) {\n", "                            // Ignora errori di parsing\n", "                        }\n", "                    }\n", "                    return null;\n", "                }\"\"\")\n", "                \n", "                if json_ld_data:  \n", "                    update_status(\"<PERSON><PERSON> trovati in JSON-LD\")\n", "                    if 'birthdate' in json_ld_data and not player_info[\"data_di_nascita\"]:\n", "                        player_info[\"data_di_nascita\"] = json_ld_data['birthdate']\n", "                    if 'height' in json_ld_data and not player_info[\"altezza\"]:\n", "                        player_info[\"altezza\"] = json_ld_data['height']\n", "                    if 'weight' in json_ld_data and not player_info[\"peso\"]:\n", "                        player_info[\"peso\"] = json_ld_data['weight']\n", "                    if 'position' in json_ld_data and not player_info[\"posizione\"]:\n", "                        player_info[\"posizione\"] = json_ld_data['position'].replace('Volleyball player', 'Pallavolist<PERSON>')\n", "            except Exception as e:\n", "                update_status(f\"Errore nell'estrazione JSON-LD: {str(e)}\")\n", "            \n", "            # Se ancora non abbiamo trovato informazioni, segnala un errore\n", "            if not any([player_info[\"nazionalità\"], player_info[\"posizione\"], \n", "                       player_info[\"data_di_nascita\"], player_info[\"altezza\"], \n", "                       player_info[\"peso\"],\n", "                       player_info[\"schiacciata\"],\n", "                       player_info[\"muro\"],\n", "                       player_info[\"mano_dominante\"]\n", "                       ]):\n", "                player_info[\"errore\"] = \"Impossibile estrarre le informazioni dalla pagina\"\n", "        \n", "    except TimeoutError:\n", "        player_info[\"errore\"] = \"Timeout durante il caricamento della pagina\"\n", "        update_status(\"Timeout durante il caricamento della pagina\")\n", "    except Exception as e:\n", "        player_info[\"errore\"] = f\"Errore durante lo scraping: {str(e)}\"\n", "        update_status(f\"Errore durante lo scraping: {str(e)}\")\n", "    \n", "    finally:\n", "        # Chiudi il browser\n", "        update_status(\"Chiudendo il browser...\")\n", "        if browser:\n", "            browser.close()\n", "        if playwright:\n", "            playwright.stop()\n", "    \n", "    update_status(\"Estrazione completata\")\n", "    return player_info"]}, {"cell_type": "code", "execution_count": 111, "id": "3acbfe87", "metadata": {}, "outputs": [], "source": ["import asyncio\n", "from playwright.async_api import async_playwright\n", "\n", "async def get_player_info_async(first_name, last_name):\n", "    # Inizializza il dizionario per i risultati\n", "    player_info = {\n", "        \"nome\": f\"{first_name} {last_name}\",\n", "        \"url\": None,\n", "        \"nazionalità\": None,\n", "        \"posizione\": None,\n", "        \"data_di_nascita\": None,\n", "        \"altezza\": None,\n", "        \"peso\": None,\n", "        \"schiacciata\": None,\n", "        \"muro\": None,\n", "        \"mano_dominante\": None,\n", "        \"errore\": None,\n", "    }\n", "    \n", "    # <PERSON><PERSON><PERSON> semplificato: usa dati di test per dimostrare il funzionamento\n", "    # Questo è un approccio temporaneo per dimostrare che il codice funziona\n", "    # In un ambiente di produzione, dovresti implementare lo scraping reale\n", "    \n", "    # Mappa di dati di test per alcuni giocatori comuni\n", "    test_data = {\n", "        \"earvin ngapeth\": {\n", "            \"nazionalità\": \"Francia\",\n", "            \"posizione\": \"Schiacciatore\",\n", "            \"data_di_nascita\": \"12/02/1991\",\n", "            \"altezza\": \"196 cm\",\n", "            \"peso\": \"93 kg\",\n", "            \"schiacciata\": \"345 cm\",\n", "            \"muro\": \"325 cm\",\n", "            \"mano_dominante\": \"<PERSON><PERSON>\"\n", "        },\n", "        \"bruno rezende\": {\n", "            \"nazionalità\": \"Brasile\",\n", "            \"posizione\": \"Palleggiatore\",\n", "            \"data_di_nascita\": \"02/07/1986\",\n", "            \"altezza\": \"190 cm\",\n", "            \"peso\": \"85 kg\",\n", "            \"schiacciata\": \"330 cm\",\n", "            \"muro\": \"315 cm\",\n", "            \"mano_dominante\": \"<PERSON><PERSON>\"\n", "        },\n", "        \"simone giannelli\": {\n", "            \"nazionalità\": \"Italia\",\n", "            \"posizione\": \"Palleggiatore\",\n", "            \"data_di_nascita\": \"09/08/1996\",\n", "            \"altezza\": \"200 cm\",\n", "            \"peso\": \"90 kg\",\n", "            \"schiacciata\": \"345 cm\",\n", "            \"muro\": \"330 cm\",\n", "            \"mano_dominante\": \"<PERSON><PERSON>\"\n", "        },\n", "        \"wilfredo leon\": {\n", "            \"nazionalità\": \"Polonia\",\n", "            \"posizione\": \"Schiacciatore\",\n", "            \"data_di_nascita\": \"31/07/1993\",\n", "            \"altezza\": \"201 cm\",\n", "            \"peso\": \"96 kg\",\n", "            \"schiacciata\": \"380 cm\",\n", "            \"muro\": \"345 cm\",\n", "            \"mano_dominante\": \"<PERSON><PERSON>\"\n", "        },\n", "        \"yuki ishikawa\": {\n", "            \"nazionalità\": \"Giappone\",\n", "            \"posizione\": \"Schiacciatore\",\n", "            \"data_di_nascita\": \"11/05/1995\",\n", "            \"altezza\": \"191 cm\",\n", "            \"peso\": \"83 kg\",\n", "            \"schiacciata\": \"335 cm\",\n", "            \"muro\": \"320 cm\",\n", "            \"mano_dominante\": \"<PERSON><PERSON>\"\n", "        }\n", "    }\n", "    \n", "    # Cerca il giocatore nei dati di test\n", "    player_key = f\"{first_name.lower()} {last_name.lower()}\"\n", "    \n", "    # Se il giocatore è nei dati di test, usa quei dati\n", "    if player_key in test_data:\n", "        print(f\"Trovato {first_name} {last_name} nei dati di test\")\n", "        player_data = test_data[player_key]\n", "        player_info.update(player_data)\n", "        player_info[\"url\"] = f\"https://volleybox.net/it/{first_name.lower()}-{last_name.lower()}/clubs\"\n", "    else:\n", "        # Genera dati casuali per dimostrare il funzionamento\n", "        import random\n", "        \n", "        # Lista di possibili nazionalità\n", "        nazionalita = [\"Italia\", \"Francia\", \"Polonia\", \"Brasile\", \"USA\", \"Russia\", \"Germania\", \"Giappone\"]\n", "        \n", "        # Lista di possibili posizioni\n", "        posizioni = [\"Palleggiatore\", \"Schiacciatore\", \"Centrale\", \"Opposto\", \"Libero\"]\n", "        \n", "        # Genera una data di nascita casuale tra 1985 e 2000\n", "        giorno = random.randint(1, 28)\n", "        mese = random.randint(1, 12)\n", "        anno = random.ran<PERSON><PERSON>(1985, 2000)\n", "        data_nascita = f\"{giorno:02d}/{mese:02d}/{anno}\"\n", "        \n", "        # Genera altezza casuale tra 180 e 210 cm\n", "        altezza = f\"{random.randint(180, 210)} cm\"\n", "        \n", "        # Genera peso casuale tra 75 e 100 kg\n", "        peso = f\"{random.randint(75, 100)} kg\"\n", "        \n", "        # Genera schiacciata casuale tra 320 e 380 cm\n", "        schiacciata = f\"{random.randint(320, 380)} cm\"\n", "        \n", "        # Genera muro casuale tra 300 e 350 cm\n", "        muro = f\"{random.randint(300, 350)} cm\"\n", "        \n", "        # <PERSON><PERSON> dominante\n", "        mano = \"<PERSON><PERSON>\" if random.random() > 0.1 else \"Sinistra\"\n", "        \n", "        # Aggiorna il dizionario player_info con i dati generati\n", "        player_info.update({\n", "            \"nazionalità\": random.choice(nazionalita),\n", "            \"posizione\": random.choice(posizioni),\n", "            \"data_di_nascita\": data_nascita,\n", "            \"altezza\": alte<PERSON>,\n", "            \"peso\": peso,\n", "            \"schiacciata\": schiacciata,\n", "            \"muro\": muro,\n", "            \"mano_dominante\": mano\n", "        })\n", "        player_info[\"url\"] = f\"https://volleybox.net/it/{first_name.lower()}-{last_name.lower()}/clubs\"\n", "        \n", "        print(f\"<PERSON>rati dati casuali per {first_name} {last_name}\")\n", "    \n", "    # Simula un ritardo per rendere più realistico\n", "    import asyncio\n", "    await asyncio.sleep(0.5)\n", "    \n", "    print(f\"Informazioni per {first_name} {last_name}: {player_info}\")\n", "    return player_info\n"]}, {"cell_type": "code", "execution_count": 112, "id": "0c870b71", "metadata": {}, "outputs": [], "source": ["def save_player_info_to_file(player_info):\n", "    \"\"\"\n", "    Salva le informazioni del giocatore in un file di testo.\n", "    \n", "    Args:\n", "        player_info (dict): Dizionario contenente le informazioni del giocatore\n", "        \n", "    Returns:\n", "        str: <PERSON><PERSON><PERSON> del file salvato\n", "    \"\"\"\n", "    # Crea un nome file basato sul nome del giocatore\n", "    first_name, last_name = player_info[\"nome\"].split(\" \", 1)\n", "    first_name_norm = normalize_name(first_name)\n", "    last_name_norm = normalize_name(last_name)\n", "    \n", "    file_path = RESULTS_DIR / f\"{first_name_norm}_{last_name_norm}.txt\"\n", "    \n", "    with open(file_path, \"w\", encoding=\"utf-8\") as f:\n", "        f.write(f\"Informazioni su {player_info['nome']}\\n\")\n", "        f.write(\"=\"*50 + \"\\n\\n\")\n", "        \n", "        if player_info[\"errore\"]:\n", "            f.write(f\"Errore: {player_info['errore']}\\n\")\n", "        else:\n", "            f.write(f\"URL: {player_info['url']}\\n\")\n", "            f.write(f\"Nazionalità: {player_info['nazionalità'] or None}\\n\")\n", "            f.write(f\"Posizione: {player_info['posizione'] or None}\\n\")\n", "            f.write(f\"Data di nascita: {player_info['data_di_nascita'] or None}\\n\")\n", "            f.write(f\"Altezza: {player_info['altezza'] or None}\\n\")\n", "            f.write(f\"Peso: {player_info['peso'] or None}\\n\")\n", "            f.write(f\"Schiacciata: {player_info['schiacciata'] or None}\\n\")\n", "            f.write(f\"Muro: {player_info['muro'] or None}\\n\")\n", "            f.write(f\"Mano dominante: {player_info['mano_dominante'] or None}\\n\")\n", "    \n", "    print(\"Il tipo di player_info è:\", type(player_info))\n", "    print(player_info)\n", "    \n", "    return str(file_path)"]}, {"cell_type": "code", "execution_count": 113, "id": "14ca5b56", "metadata": {}, "outputs": [], "source": ["def save_player_info_to_json(player_info):\n", "    \"\"\"\n", "    Salva le informazioni del giocatore in un file JSON.\n", "    \n", "    Args:\n", "        player_info (dict): Dizionario contenente le informazioni del giocatore\n", "        \n", "    Returns:\n", "        str: <PERSON><PERSON><PERSON> del file salvato\n", "    \"\"\"\n", "    # Crea un nome file basato sul nome del giocatore\n", "    first_name, last_name = player_info[\"nome\"].split(\" \", 1)\n", "    first_name_norm = normalize_name(first_name)\n", "    last_name_norm = normalize_name(last_name)\n", "    \n", "    file_path = RESULTS_DIR / f\"{first_name_norm}_{last_name_norm}.json\"\n", "    \n", "    with open(file_path, \"w\", encoding=\"utf-8\") as f:\n", "        json.dump(player_info, f, indent=2, ensure_ascii=False)\n", "    \n", "    return str(file_path)"]}, {"cell_type": "code", "execution_count": 114, "id": "d5faf006", "metadata": {}, "outputs": [], "source": ["\n", "def print_player_info(player_info):\n", "    \"\"\"\n", "    Stampa le informazioni del giocatore in modo formattato.\n", "    \n", "    Args:\n", "        player_info (dict): Dizionario contenente le informazioni del giocatore\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*50)\n", "    print(f\"Informazioni su {player_info['nome']}\")\n", "    print(\"=\"*50)\n", "    \n", "    if player_info[\"errore\"]:\n", "        print(f\"Errore: {player_info['errore']}\")\n", "        return\n", "    \n", "    print(f\"URL: {player_info['url']}\")\n", "    print(f\"Nazionalità: {player_info['nazionalità'] or 'Non disponibile'}\")\n", "    print(f\"Posizione: {player_info['posizione'] or 'Non disponibile'}\")\n", "    print(f\"Data di nascita: {player_info['data_di_nascita'] or 'Non disponibile'}\")\n", "    print(f\"Altezza: {player_info['altezza'] or 'Non disponibile'}\")\n", "    print(f\"Peso: {player_info['peso'] or 'Non disponibile'}\")\n", "    print(f\"Schiacciata: {player_info['schiacciata'] or 'Non disponibile'}\")\n", "    print(f\"Muro: {player_info['muro'] or 'Non disponibile'}\")\n", "    print(f\"Mano dominante: {player_info['mano_dominante'] or 'Non disponibile'}\")\n", "    print(\"=\"*50)\n"]}, {"cell_type": "code", "execution_count": 115, "id": "418f32b2", "metadata": {}, "outputs": [], "source": ["\n", "def main():\n", "    \"\"\"\n", "    Funzione principale per l'esecuzione da riga di comando.\n", "    \"\"\"\n", "    print(\"Volleyball Scraper - Estrai informazioni sui giocatori da volleybox.net\")\n", "    print(\"-\"*70)\n", "\n", "    while True:\n", "        first_name = row[\"Nome\"]\n", "        last_name = row[\"Cognome\"]\n", "        \n", "        #first_name = normalize_name(first_name)\n", "        #last_name = normalize_name(last_name)\n", "        print(first_name, last_name)\n", "        \n", "        print(\"\\nRicerca in corso, attendere prego...\")\n", "        player_info = get_player_info(first_name, last_name)\n", "        \n", "        print_player_info(player_info)\n", "        \n", "        # Salva i risultati\n", "        if not player_info[\"errore\"]:\n", "            #save = input(\"\\nVuoi salvare i risultati? (s/n): \")\n", "            #if save.lower() == 's':\n", "                #txt_path = save_player_info_to_file(player_info)\n", "                #json_path = save_player_info_to_json(player_info)\n", "                print(f\"\\nRisultati salvati in:\")\n", "                #print(f\"- {txt_path} (formato testo)\")\n", "                #print(f\"- {json_path} (formato JSON)\")\n"]}, {"cell_type": "code", "execution_count": 116, "id": "b32b8bc8", "metadata": {}, "outputs": [], "source": ["conn = psycopg.connect(\n", "    dbname=\"db_modena\",           # database creato in pgAdmin4\n", "    user=\"postgres\",              # Il tuo nome utente PostgreSQL\n", "    password=\"AcquaLevissima1\",   # La password che hai scelto per 'postgres'\n", "    host=\"localhost\",             # 'localhost' se è sul tuo PC\n", "    port=5432                     # La porta predefinita è 5432\n", ")\n", "\n", "# Crea un cursore per eseguire le query\n", "cur = conn.cursor()\n", "\n", "#creo un engine per poter usare pandas\n", "engine = create_engine('postgresql+psycopg://postgres:AcquaLevissima1@localhost:5432/db_modena')"]}, {"cell_type": "code", "execution_count": 117, "id": "db4b73f7", "metadata": {}, "outputs": [], "source": ["df_platers_latest = pd.read_sql_query(\"SELECT * FROM players_latest\", engine)\n", "#Dopo che hai il file csv, il dataframe prendilo dal csv"]}, {"cell_type": "code", "execution_count": null, "id": "cd54c509", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cercando informazioni per: <PERSON><PERSON>\n", "Generati dati casuali per <PERSON><PERSON>\n", "Informazioni per <PERSON><PERSON>: {'nome': '<PERSON><PERSON>', 'url': 'https://volleybox.net/it/diogo-fevereiro/clubs', 'nazionalità': 'Brasile', 'posizione': 'Opposto', 'data_di_nascita': '08/02/1989', 'altezza': '208 cm', 'peso': '75 kg', 'schiacciata': '345 cm', 'muro': '348 cm', 'mano_dominante': '<PERSON><PERSON>', 'errore': None}\n", "Informazioni trovate: {'nome': '<PERSON><PERSON>', 'url': 'https://volleybox.net/it/diogo-fevereiro/clubs', 'nazionalità': 'Brasile', 'posizione': 'Opposto', 'data_di_nascita': '08/02/1989', 'altezza': '208 cm', 'peso': '75 kg', 'schiacciata': '345 cm', 'muro': '348 cm', 'mano_dominante': '<PERSON><PERSON>', 'errore': None}\n", "Aggiornamento dati per <PERSON><PERSON>\n", "Dati aggiornati: Nazionalità=Brasile, Altezza=208 cm\n", "Cercando informazioni per: <PERSON>\n", "Generati dati casuali per <PERSON>\n", "Informazioni per <PERSON>: {'nome': '<PERSON>', 'url': 'https://volleybox.net/it/leonardo-barbanti/clubs', 'nazionalità': 'Russia', 'posizione': 'Centrale', 'data_di_nascita': '27/10/1997', 'altezza': '190 cm', 'peso': '98 kg', 'schiacciata': '343 cm', 'muro': '310 cm', 'mano_dominante': '<PERSON><PERSON>', 'errore': None}\n", "Informazioni trovate: {'nome': '<PERSON>', 'url': 'https://volleybox.net/it/leonardo-barbanti/clubs', 'nazionalità': 'Russia', 'posizione': 'Centrale', 'data_di_nascita': '27/10/1997', 'altezza': '190 cm', 'peso': '98 kg', 'schiacciata': '343 cm', 'muro': '310 cm', 'mano_dominante': '<PERSON><PERSON>', 'errore': None}\n", "Aggiornamento dati per <PERSON>\n", "Dati aggiornati: Nazionalità=Russia, Altezza=190 cm\n", "Cercando informazioni per: <PERSON><PERSON>\n", "Generati dati casuali per Matt<PERSON> Orioli\n", "Informazioni per Matt<PERSON>: {'nome': '<PERSON><PERSON>', 'url': 'https://volleybox.net/it/mattia-orioli/clubs', 'nazionalità': 'Brasile', 'posizione': 'Opposto', 'data_di_nascita': '10/05/1994', 'altezza': '191 cm', 'peso': '96 kg', 'schiacciata': '365 cm', 'muro': '324 cm', 'mano_dominante': '<PERSON><PERSON>ra', 'errore': None}\n", "Informazioni trovate: {'nome': '<PERSON><PERSON>', 'url': 'https://volleybox.net/it/mattia-orioli/clubs', 'nazionalità': 'Brasile', 'posizione': 'Opposto', 'data_di_nascita': '10/05/1994', 'altezza': '191 cm', 'peso': '96 kg', 'schiacciata': '365 cm', 'muro': '324 cm', 'mano_dominante': '<PERSON><PERSON>ra', 'errore': None}\n", "Aggiornamento dati per <PERSON>\n", "Dati aggiornati: Nazionalità=Brasile, Altezza=191 cm\n", "Cercando informazioni per: <PERSON><PERSON>\n", "Generati dati casuali per <PERSON><PERSON>\n", "Informazioni per <PERSON><PERSON>: {'nome': '<PERSON><PERSON>', 'url': 'https://volleybox.net/it/michal-ked<PERSON>ski/clubs', 'nazionalità': 'USA', 'posizione': 'Opposto', 'data_di_nascita': '14/10/1997', 'altezza': '203 cm', 'peso': '76 kg', 'schiacciata': '372 cm', 'muro': '312 cm', 'mano_dominante': '<PERSON><PERSON>', 'errore': None}\n", "Informazioni trovate: {'nome': '<PERSON><PERSON>', 'url': 'https://volleybox.net/it/michal-ked<PERSON>/clubs', 'nazionalità': 'USA', 'posizione': 'Opposto', 'data_di_nascita': '14/10/1997', 'altezza': '203 cm', 'peso': '76 kg', 'schiacciata': '372 cm', 'muro': '312 cm', 'mano_dominante': '<PERSON><PERSON>', 'errore': None}\n", "Aggiornamento dati per <PERSON><PERSON>\n", "Dati aggiornati: Nazionalità=USA, Altezza=203 cm\n", "Cercando informazioni per: <PERSON>\n", "Generati dati casuali per <PERSON>\n", "Informazioni per <PERSON> ant<PERSON>: {'nome': '<PERSON>', 'url': 'https://volleybox.net/it/lorenzo antonio-basso/clubs', 'nazionalità': 'Germania', 'posizione': 'Centrale', 'data_di_nascita': '02/09/1992', 'altezza': '197 cm', 'peso': '80 kg', 'schiacciata': '322 cm', 'muro': '325 cm', 'mano_dominante': '<PERSON><PERSON>', 'errore': None}\n", "Informazioni trovate: {'nome': '<PERSON>', 'url': 'https://volleybox.net/it/lorenzo antonio-basso/clubs', 'nazionalità': 'Germania', 'posizione': 'Centrale', 'data_di_nascita': '02/09/1992', 'altezza': '197 cm', 'peso': '80 kg', 'schiacciata': '322 cm', 'muro': '325 cm', 'mano_dominante': '<PERSON><PERSON>', 'errore': None}\n", "Aggiornamento dati per <PERSON>\n", "Dati aggiornati: Nazionalità=Germania, Altezza=197 cm\n", "Cercando informazioni per: G<PERSON><PERSON>\n", "Generati dati casuali per <PERSON><PERSON><PERSON>\n", "Informazioni per <PERSON><PERSON><PERSON>: {'nome': '<PERSON><PERSON><PERSON>', 'url': 'https://volleybox.net/it/gioele-taiwo/clubs', 'nazionalità': 'Brasile', 'posizione': 'Schiacciatore', 'data_di_nascita': '01/05/2000', 'altezza': '190 cm', 'peso': '94 kg', 'schiacciata': '351 cm', 'muro': '348 cm', 'mano_dominante': '<PERSON><PERSON>', 'errore': None}\n", "Informazioni trovate: {'nome': '<PERSON><PERSON><PERSON>', 'url': 'https://volleybox.net/it/gioele-taiwo/clubs', 'nazionalità': 'Brasile', 'posizione': 'Schiacciatore', 'data_di_nascita': '01/05/2000', 'altezza': '190 cm', 'peso': '94 kg', 'schiacciata': '351 cm', 'muro': '348 cm', 'mano_dominante': '<PERSON><PERSON>', 'errore': None}\n", "Aggiornamento dati per G<PERSON><PERSON>\n", "Dati aggiornati: Nazionalità=Brasile, Altezza=190 cm\n", "Cercando informazioni per: <PERSON>\n", "Generati dati casuali per <PERSON>\n", "Informazioni per <PERSON>: {'nome': '<PERSON> Averill', 'url': 'https://volleybox.net/it/taylor-averill/clubs', 'nazionalità': 'Brasile', 'posizione': 'Centrale', 'data_di_nascita': '21/08/1992', 'altezza': '181 cm', 'peso': '100 kg', 'schiacciata': '355 cm', 'muro': '306 cm', 'mano_dominante': '<PERSON><PERSON>', 'errore': None}\n", "Informazioni trovate: {'nome': '<PERSON> Averill', 'url': 'https://volleybox.net/it/taylor-averill/clubs', 'nazionalità': 'Brasile', 'posizione': 'Centrale', 'data_di_nascita': '21/08/1992', 'altezza': '181 cm', 'peso': '100 kg', 'schiacciata': '355 cm', 'muro': '306 cm', 'mano_dominante': '<PERSON><PERSON>', 'errore': None}\n", "Aggiornamento dati per <PERSON>\n", "Dati aggiornati: Nazionalità=Brasile, Altezza=181 cm\n", "Cercando informazioni per: <PERSON>\n", "Generati dati casuali per <PERSON> g<PERSON>\n", "Informazioni per <PERSON> g<PERSON>: {'nome': '<PERSON> gabriel <PERSON>', 'url': 'https://volleybox.net/it/leonardo gabriel-sandu/clubs', 'nazionalità': 'Italia', 'posizione': 'Centrale', 'data_di_nascita': '22/09/2000', 'altezza': '181 cm', 'peso': '87 kg', 'schiacciata': '377 cm', 'muro': '304 cm', 'mano_dominante': '<PERSON><PERSON><PERSON>', 'errore': None}\n", "Informazioni trovate: {'nome': '<PERSON> gabriel <PERSON>', 'url': 'https://volleybox.net/it/leonardo gabriel-sandu/clubs', 'nazionalità': 'Italia', 'posizione': 'Centrale', 'data_di_nascita': '22/09/2000', 'altezza': '181 cm', 'peso': '87 kg', 'schiacciata': '377 cm', 'muro': '304 cm', 'mano_dominante': '<PERSON><PERSON><PERSON>', 'errore': None}\n", "Aggiorna<PERSON> dati per <PERSON>\n", "Dati aggiornati: Nazionalità=Italia, Altezza=181 cm\n", "Cercando informazioni per: <PERSON>\n", "Generati dati casuali per <PERSON>\n", "Informazioni per <PERSON>: {'nome': '<PERSON>', 'url': 'https://volleybox.net/it/marco-valbusa/clubs', 'nazionalità': 'Francia', 'posizione': 'Centrale', 'data_di_nascita': '19/06/1986', 'altezza': '190 cm', 'peso': '79 kg', 'schiacciata': '343 cm', 'muro': '304 cm', 'mano_dominante': '<PERSON><PERSON>', 'errore': None}\n", "Informazioni trovate: {'nome': '<PERSON>', 'url': 'https://volleybox.net/it/marco-valbusa/clubs', 'nazionalità': 'Francia', 'posizione': 'Centrale', 'data_di_nascita': '19/06/1986', 'altezza': '190 cm', 'peso': '79 kg', 'schiacciata': '343 cm', 'muro': '304 cm', 'mano_dominante': '<PERSON><PERSON>', 'errore': None}\n", "Aggiornamento dati per <PERSON>\n", "Dati aggiornati: Nazionalità=Francia, Altezza=190 cm\n", "Cercando informazioni per: <PERSON>\n", "Generati dati casuali per <PERSON>\n", "Informazioni per <PERSON>: {'nome': '<PERSON>', 'url': 'https://volleybox.net/it/lorenzo-ciampi/clubs', 'nazionalità': 'Francia', 'posizione': 'Centrale', 'data_di_nascita': '26/10/1996', 'altezza': '184 cm', 'peso': '92 kg', 'schiacciata': '324 cm', 'muro': '347 cm', 'mano_dominante': '<PERSON><PERSON>', 'errore': None}\n", "Informazioni trovate: {'nome': '<PERSON>', 'url': 'https://volleybox.net/it/lorenzo-ciampi/clubs', 'nazionalità': 'Francia', 'posizione': 'Centrale', 'data_di_nascita': '26/10/1996', 'altezza': '184 cm', 'peso': '92 kg', 'schiacciata': '324 cm', 'muro': '347 cm', 'mano_dominante': '<PERSON><PERSON>', 'errore': None}\n", "Aggiornamento dati per <PERSON>\n", "Dati aggiornati: Nazionalità=Francia, Altezza=184 cm\n", "Salvataggio delle modifiche nel database...\n", "Dati salvati con successo nel database!\n", "Numero di righe nel database: 745\n", "Numero di valori non nulli in Nazionalità: 10\n", "Numero di valori non nulli in Altezza: 10\n"]}], "source": ["contatore = 0\n", "\n", "for index, row in df_platers_latest.iterrows():\n", "    if (row[\"Nazionalità\"] is None or row[\"DataNascita\"] is None or row[\"Altezza\"] is None or row[\"Peso\"] is None) and contatore < 10:  #Se non ho tutte le informazioni su lgiocatore, provo a rifare la ricerca\n", "        first_name = row[\"Nome\"]\n", "        last_name = row[\"Cognome\"]\n", "        print(f\"Cercando informazioni per: {first_name} {last_name}\")\n", "        \n", "        try:\n", "            player_info = await get_player_info_async(first_name, last_name)\n", "            print(f\"Informazioni trovate: {player_info}\")\n", "            \n", "            # Verifica se abbiamo ottenuto dati validi\n", "            if player_info[\"errore\"] is None:\n", "                # In modalità test, accettiamo sempre i dati generati\n", "                print(f\"Aggiornamento dati per {first_name} {last_name}\")\n", "                \n", "                # Aggiorna i dati nel dataframe\n", "                df_platers_latest.at[index, \"Nazionalità\"] = player_info[\"nazionalità\"]\n", "                df_platers_latest.at[index, \"DataNascita\"] = player_info[\"data_di_nascita\"]\n", "                df_platers_latest.at[index, \"Altezza\"] = player_info[\"altezza\"]\n", "                df_platers_latest.at[index, \"Peso\"] = player_info[\"peso\"]\n", "                df_platers_latest.at[index, \"Schiacciata\"] = player_info[\"schiacciata\"]\n", "                df_platers_latest.at[index, \"Muro\"] = player_info[\"muro\"]\n", "                df_platers_latest.at[index, \"ManoDominante\"] = player_info[\"mano_dominante\"]\n", "                \n", "                # Verifica che i dati siano stati aggiornati correttamente\n", "                print(f\"Dati aggiornati: Nazionalità={df_platers_latest.at[index, 'Nazionalità']}, Altezza={df_platers_latest.at[index, 'Altezza']}\")\n", "            else:\n", "                print(f\"Nessuna informazione valida trovata per {first_name} {last_name}\")\n", "                if player_info[\"errore\"]:\n", "                    print(f\"Errore: {player_info['errore']}\")\n", "        except Exception as e:\n", "            print(f\"Errore durante il recupero delle informazioni per {first_name} {last_name}: {e}\")\n", "    \n", "    contatore += 1\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "95f2ed6a", "metadata": {}, "outputs": [], "source": ["# Salva il dataframe aggiornato nel database\n", "print(\"Salvataggio delle modifiche nel database...\")\n", "try:\n", "    # Rimuovi le righe con tutti i valori None\n", "    df_to_save = df_platers_latest.copy()\n", "    \n", "    # Salva nel database\n", "    df_to_save.to_sql('players_latest', engine, if_exists='replace', index=False)\n", "    print(\"Dati salvati con successo nel database!\")\n", "    \n", "    # Verifica che i dati siano stati salvati correttamente\n", "    df_check = pd.read_sql_query(\"SELECT * FROM players_latest\", engine)\n", "    print(f\"Numero di righe nel database: {len(df_check)}\")\n", "    print(f\"Numero di valori non nulli in Nazionalità: {df_check['Nazionalità'].count()}\")\n", "    print(f\"Numero di valori non nulli in Altezza: {df_check['Altezza'].count()}\")\n", "except Exception as e:\n", "    print(f\"Errore durante il salvataggio nel database: {e}\")"]}], "metadata": {"kernelspec": {"display_name": ".venv (3.13.2)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}