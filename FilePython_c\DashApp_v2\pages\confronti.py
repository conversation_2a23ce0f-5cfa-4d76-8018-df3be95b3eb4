"""
Pagina Confronti dell'applicazione
"""
import dash
from dash import html, dcc, callback, Input, Output, State
import dash_bootstrap_components as dbc
import plotly.express as px
import plotly.graph_objects as go
import pandas as pd
import numpy as np

def layout(filtered_data=None, filters=None):
    """
    Layout della pagina Confronti
    
    Args:
        filtered_data (dict): Dati filtrati dai filtri globali
        filters (dict): Filtri attualmente attivi
    """
    
    return html.Div([
        dbc.Container([
            dbc.Row([
                dbc.Col([
                    html.H1("Confronti e Analisi Comparativa", className="text-center mb-4"),
                    html.Hr()
                ])
            ]),
            
            # Tipo di confronto
            dbc.Row([
                dbc.Col([
                    html.Label("Tipo di confronto:"),
                    dcc.RadioItems(
                        id='tipo-confronto',
                        options=[
                            {'label': 'Giocatori', 'value': 'players'},
                            {'label': 'Squadre', 'value': 'teams'},
                            {'label': 'Periodi temporali', 'value': 'periods'}
                        ],
                        value='players',
                        className="mb-3"
                    )
                ], width=12)
            ], className="mb-4"),
            
            # Selezione elementi da confrontare
            dbc.Row([
                dbc.Col([
                    html.Label("Elemento 1:"),
                    dcc.Dropdown(
                        id='elemento-1-dropdown',
                        placeholder="Seleziona il primo elemento...",
                        className="mb-3"
                    )
                ], width=6),
                dbc.Col([
                    html.Label("Elemento 2:"),
                    dcc.Dropdown(
                        id='elemento-2-dropdown',
                        placeholder="Seleziona il secondo elemento...",
                        className="mb-3"
                    )
                ], width=6)
            ], className="mb-4"),
            
            # Metriche da confrontare
            dbc.Row([
                dbc.Col([
                    html.Label("Metriche da confrontare:"),
                    dcc.Checklist(
                        id='metriche-checklist',
                        options=[
                            {'label': 'Battuta', 'value': 'Serve'},
                            {'label': 'Ricezione', 'value': 'Reception'},
                            {'label': 'Alzata', 'value': 'Set'},
                            {'label': 'Attacco', 'value': 'Attack'},
                            {'label': 'Muro', 'value': 'Block'},
                            {'label': 'Difesa', 'value': 'Defense'},
                            {'label': 'Free Ball', 'value': 'FreeBall'}
                        ],
                        value=['Attack', 'Serve', 'Reception'],
                        className="mb-3"
                    )
                ], width=12)
            ], className="mb-4"),
            
            # Grafici di confronto
            dbc.Row([
                dbc.Col([
                    dcc.Graph(id="confronti-radar-chart")
                ], width=6),
                dbc.Col([
                    dcc.Graph(id="confronti-bar-chart")
                ], width=6)
            ], className="mb-4"),
            
            # Grafico a barre orizzontali per confronto diretto
            dbc.Row([
                dbc.Col([
                    dcc.Graph(id="confronti-horizontal-bar")
                ], width=12)
            ], className="mb-4"),
            
            # Tabella comparativa
            dbc.Row([
                dbc.Col([
                    html.H4("Tabella Comparativa"),
                    html.Div(id="confronti-table")
                ], width=12)
            ], className="mb-4"),
            
            # Statistiche del confronto
            dbc.Row([
                dbc.Col([
                    html.H4("Riepilogo del Confronto"),
                    html.Div(id="confronti-summary")
                ], width=12)
            ])
        ])
    ])

# Callback per popolare i dropdown in base al tipo di confronto
@callback(
    Output("elemento-1-dropdown", "options"),
    Output("elemento-2-dropdown", "options"),
    Input("tipo-confronto", "value"),
    State("raw-data-store", "data")
)
def update_element_options(tipo_confronto, raw_data):
    if not raw_data:
        return [], []
    
    if tipo_confronto == "players":
        if 'df_players' in raw_data and raw_data['df_players']:
            players_df = pd.DataFrame(raw_data['df_players'])
            options = [{'label': f"{row['Nome']} {row['Cognome']}", 'value': row['PlayerID']} 
                      for _, row in players_df.iterrows()]
            return options, options
        return [], []
    
    elif tipo_confronto == "teams":
        if 'df_teams' in raw_data and raw_data['df_teams']:
            teams_df = pd.DataFrame(raw_data['df_teams'])
            options = [{'label': row['TeamNameShort'], 'value': row['TeamID']} 
                      for _, row in teams_df.iterrows()]
            return options, options
        return [], []
    
    elif tipo_confronto == "periods":
        if 'df_games' in raw_data and raw_data['df_games']:
            games_df = pd.DataFrame(raw_data['df_games'])
            periods = games_df['Annata'].unique()
            options = [{'label': f"Stagione {period}", 'value': period} for period in periods]
            return options, options
        return [], []
    
    return [], []

# Callback per aggiornare i grafici di confronto
@callback(
    Output("confronti-radar-chart", "figure"),
    Output("confronti-bar-chart", "figure"),
    Output("confronti-horizontal-bar", "figure"),
    Output("confronti-table", "children"),
    Output("confronti-summary", "children"),
    Input("elemento-1-dropdown", "value"),
    Input("elemento-2-dropdown", "value"),
    Input("metriche-checklist", "value"),
    Input("tipo-confronto", "value"),
    State("raw-data-store", "data")
)
def update_confronti_charts(elem1, elem2, metriche, tipo, raw_data):
    if not all([elem1, elem2, metriche, raw_data]):
        return {}, {}, {}, "Seleziona due elementi e le metriche per confrontarli", ""
    
    try:
        # Prepara i dati per il confronto
        comparison_data = prepare_comparison_data(elem1, elem2, metriche, tipo, raw_data)
        
        if not comparison_data:
            return {}, {}, {}, "Dati non disponibili per il confronto", ""
        
        # Crea i grafici
        radar_fig = create_radar_chart(comparison_data, metriche)
        bar_fig = create_bar_chart(comparison_data, metriche)
        horizontal_fig = create_horizontal_bar(comparison_data, metriche)
        
        # Crea la tabella
        table = create_comparison_table(comparison_data, metriche)
        
        # Crea il riepilogo
        summary = create_comparison_summary(comparison_data, metriche)
        
        return radar_fig, bar_fig, horizontal_fig, table, summary
        
    except Exception as e:
        return {}, {}, {}, f"Errore nel generare i confronti: {str(e)}", ""

def prepare_comparison_data(elem1, elem2, metriche, tipo, raw_data):
    """Prepara i dati per il confronto"""
    if tipo == "players":
        return prepare_player_comparison(elem1, elem2, metriche, raw_data)
    elif tipo == "teams":
        return prepare_team_comparison(elem1, elem2, metriche, raw_data)
    elif tipo == "periods":
        return prepare_period_comparison(elem1, elem2, metriche, raw_data)
    return None

def prepare_player_comparison(player1_id, player2_id, metriche, raw_data):
    """Prepara i dati per il confronto tra giocatori"""
    if 'df_rilevations' not in raw_data or not raw_data['df_rilevations']:
        return None
    
    rilevations_df = pd.DataFrame(raw_data['df_rilevations'])
    
    # Filtra per i giocatori selezionati
    player1_data = rilevations_df[rilevations_df['PlayerID'] == player1_id]
    player2_data = rilevations_df[rilevations_df['PlayerID'] == player2_id]
    
    comparison = {}
    for metrica in metriche:
        if metrica in rilevations_df.columns:
            # Calcola statistiche per ogni giocatore
            p1_stats = calculate_player_stats(player1_data, metrica)
            p2_stats = calculate_player_stats(player2_data, metrica)
            
            comparison[metrica] = {
                'player1': p1_stats,
                'player2': p2_stats
            }
    
    return comparison

def calculate_player_stats(player_data, metrica):
    """Calcola le statistiche per un giocatore su una metrica specifica"""
    if player_data.empty:
        return {'count': 0, 'efficiency': 0, 'positive_pct': 0}
    
    # Conta le azioni
    count = len(player_data)
    
    # Calcola efficienza (esempio per attacco)
    if metrica == 'Attack':
        positive = len(player_data[player_data['AttackThisAppoggio_Positivo'] == 1])
        efficiency = positive / count if count > 0 else 0
        positive_pct = (positive / count) * 100 if count > 0 else 0
    else:
        efficiency = 0
        positive_pct = 0
    
    return {
        'count': count,
        'efficiency': efficiency,
        'positive_pct': positive_pct
    }

def create_radar_chart(comparison_data, metriche):
    """Crea un grafico radar per il confronto"""
    if not comparison_data:
        return {}
    
    # Prepara i dati per il radar
    categories = metriche
    player1_values = [comparison_data.get(m, {}).get('player1', {}).get('efficiency', 0) for m in metriche]
    player2_values = [comparison_data.get(m, {}).get('player2', {}).get('efficiency', 0) for m in metriche]
    
    fig = go.Figure()
    
    fig.add_trace(go.Scatterpolar(
        r=player1_values,
        theta=categories,
        fill='toself',
        name='Elemento 1',
        line_color='blue'
    ))
    
    fig.add_trace(go.Scatterpolar(
        r=player2_values,
        theta=categories,
        fill='toself',
        name='Elemento 2',
        line_color='red'
    ))
    
    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 1]
            )),
        showlegend=True,
        title="Confronto Radar delle Metriche"
    )
    
    return fig

def create_bar_chart(comparison_data, metriche):
    """Crea un grafico a barre per il confronto"""
    if not comparison_data:
        return {}
    
    # Prepara i dati per il grafico a barre
    categories = metriche
    player1_values = [comparison_data.get(m, {}).get('player1', {}).get('count', 0) for m in metriche]
    player2_values = [comparison_data.get(m, {}).get('player2', {}).get('count', 0) for m in metriche]
    
    fig = go.Figure()
    
    fig.add_trace(go.Bar(
        x=categories,
        y=player1_values,
        name='Elemento 1',
        marker_color='blue'
    ))
    
    fig.add_trace(go.Bar(
        x=categories,
        y=player2_values,
        name='Elemento 2',
        marker_color='red'
    ))
    
    fig.update_layout(
        title="Confronto Numero di Azioni",
        xaxis_title="Metriche",
        yaxis_title="Numero di Azioni",
        barmode='group'
    )
    
    return fig

def create_horizontal_bar(comparison_data, metriche):
    """Crea un grafico a barre orizzontali per il confronto diretto"""
    if not comparison_data:
        return {}
    
    # Prepara i dati per il confronto diretto
    categories = metriche
    player1_values = [comparison_data.get(m, {}).get('player1', {}).get('efficiency', 0) for m in metriche]
    player2_values = [comparison_data.get(m, {}).get('player2', {}).get('efficiency', 0) for m in metriche]
    
    fig = go.Figure()
    
    fig.add_trace(go.Bar(
        y=categories,
        x=player1_values,
        name='Elemento 1',
        orientation='h',
        marker_color='blue'
    ))
    
    fig.add_trace(go.Bar(
        y=categories,
        x=player2_values,
        name='Elemento 2',
        orientation='h',
        marker_color='red'
    ))
    
    fig.update_layout(
        title="Confronto Efficienza",
        xaxis_title="Efficienza",
        yaxis_title="Metriche",
        barmode='group'
    )
    
    return fig

def create_comparison_table(comparison_data, metriche):
    """Crea una tabella comparativa"""
    if not comparison_data:
        return "Nessun dato disponibile per il confronto"
    
    # Crea le righe della tabella
    table_rows = []
    for metrica in metriche:
        if metrica in comparison_data:
            p1_data = comparison_data[metrica]['player1']
            p2_data = comparison_data[metrica]['player2']
            
            row = html.Tr([
                html.Td(metrica),
                html.Td(f"{p1_data.get('count', 0)}"),
                html.Td(f"{p1_data.get('efficiency', 0):.3f}"),
                html.Td(f"{p2_data.get('count', 0)}"),
                html.Td(f"{p2_data.get('efficiency', 0):.3f}")
            ])
            table_rows.append(row)
    
    table = dbc.Table([
        html.Thead([
            html.Tr([
                html.Th("Metrica"),
                html.Th("Elemento 1 - Count"),
                html.Th("Elemento 1 - Efficienza"),
                html.Th("Elemento 2 - Count"),
                html.Th("Elemento 2 - Efficienza")
            ])
        ]),
        html.Tbody(table_rows)
    ], bordered=True, hover=True, responsive=True)
    
    return table

def create_comparison_summary(comparison_data, metriche):
    """Crea un riepilogo del confronto"""
    if not comparison_data:
        return "Nessun dato disponibile per il riepilogo"
    
    # Calcola statistiche riassuntive
    total_p1 = sum(comparison_data.get(m, {}).get('player1', {}).get('count', 0) for m in metriche)
    total_p2 = sum(comparison_data.get(m, {}).get('player2', {}).get('count', 0) for m in metriche)
    
    avg_eff_p1 = np.mean([comparison_data.get(m, {}).get('player1', {}).get('efficiency', 0) for m in metriche])
    avg_eff_p2 = np.mean([comparison_data.get(m, {}).get('player2', {}).get('efficiency', 0) for m in metriche])
    
    summary = html.Div([
        html.H5("Riepilogo del Confronto"),
        html.P(f"Elemento 1 - Totale azioni: {total_p1}, Efficienza media: {avg_eff_p1:.3f}"),
        html.P(f"Elemento 2 - Totale azioni: {total_p2}, Efficienza media: {avg_eff_p2:.3f}"),
        html.P(f"Differenza efficienza: {abs(avg_eff_p1 - avg_eff_p2):.3f}"),
        html.P(f"Elemento più efficiente: {'Elemento 1' if avg_eff_p1 > avg_eff_p2 else 'Elemento 2'}")
    ])
    
    return summary





