import os
import sys
import importlib.util
import subprocess

def check_module(module_name):
    """Check if a module can be imported"""
    try:
        importlib.import_module(module_name)
        return True
    except ImportError:
        return False

def main():
    print("Testing VolleyVision setup...")
    
    # Check if required modules are installed
    required_modules = [
        'torch', 'cv2', 'PIL', 'numpy', 'matplotlib', 
        'tqdm', 'roboflow', 'scipy', 'pandas'
    ]
    
    missing_modules = []
    for module in required_modules:
        if not check_module(module):
            missing_modules.append(module)
    
    if missing_modules:
        print(f"Missing modules: {', '.join(missing_modules)}")
        print("Please run setup_volleyvision.bat to install all required dependencies.")
        return False
    
    # Check if model files exist
    model_files = [
        os.path.join('FilePython_c', 'VolleyVision', 'Stage I - Volleyball', 'models', 'yolov7-tiny.pt'),
        os.path.join('FilePython_c', 'VolleyVision', 'Stage I - Volleyball', 'Da<PERSON>iamRPN', 'dasiamrpn_model.onnx'),
        os.path.join('FilePython_c', 'VolleyVision', 'Stage I - Volleyball', 'DaSiamRPN', 'dasiamrpn_kernel_cls1.onnx'),
        os.path.join('FilePython_c', 'VolleyVision', 'Stage I - Volleyball', 'DaSiamRPN', 'dasiamrpn_kernel_r1.onnx')
    ]
    
    missing_files = []
    for file_path in model_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"Missing model files: {', '.join(missing_files)}")
        print("Please run setup_volleyvision.bat to download all required model files.")
        return False
    
    # Check if Output directory exists
    output_dir = os.path.join('FilePython_c', 'VolleyVision', 'Stage I - Volleyball', 'Output')
    if not os.path.exists(output_dir):
        print(f"Missing Output directory: {output_dir}")
        print("Please run setup_volleyvision.bat to create the Output directory.")
        return False
    
    print("All checks passed! VolleyVision is set up correctly.")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
