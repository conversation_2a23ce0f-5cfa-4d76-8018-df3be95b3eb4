# CODICE CORRETTO PER IL DIAGRAMMA SANKEY
# Copia e incolla questo nella tua cella del notebook

import pandas as pd
import plotly.graph_objects as go

# Parametri (modifica questi secondo le tue necessità)
fond1 = 'S'  # Primo fondamentale
char1 = 'Type'  # Prima caratteristica
fond2 = 'R'  # Secondo fondamentale  
char2 = 'Type'  # Seconda caratteristica

print(f"char1: {char1}")
print(f"char2: {char2}")
print(f"fond1: {fond1}")
print(f"fond2: {fond2}")

# Regole per collegare i fondamentali (assicurati che siano definite)
regole_abs = {
    'R': {
        'S': lambda prev_abs: 2
    },
    'D': {
        'R': lambda prev_abs: 3,
        'S': lambda prev_abs: 3,
        'E': lambda prev_abs: prev_abs + 1,
        'A': lambda prev_abs: prev_abs + 1
    },
    'F': {
        'R': lambda prev_abs: 3,
        'S': lambda prev_abs: 3,
        'E': lambda prev_abs: prev_abs + 1,
        'A': lambda prev_abs: prev_abs + 1
    },
    'E': {
        'R': lambda prev_abs: prev_abs,
        'D': lambda prev_abs: prev_abs,
        'F': lambda prev_abs: prev_abs,
        'S': lambda prev_abs: prev_abs + 1,
        'A': lambda prev_abs: prev_abs + 1,
        'B': lambda prev_abs: prev_abs + 1
    },
    'A': {
        'R': lambda prev_abs: prev_abs,
        'E': lambda prev_abs: prev_abs,
        'D': lambda prev_abs: prev_abs,
        'F': lambda prev_abs: prev_abs,
        'S': lambda prev_abs: prev_abs + 1,
        'B': lambda prev_abs: prev_abs + 1
    },
    'B': {
        'R': lambda prev_abs: prev_abs + 1,
        'E': lambda prev_abs: prev_abs + 1,
        'A': lambda prev_abs: prev_abs + 1,
        'D': lambda prev_abs: prev_abs + 1,
        'F': lambda prev_abs: prev_abs + 1,
        'S': lambda prev_abs: prev_abs + 2
    },
}

# Carica i dati (assicurati che 'df' sia definito)
# df dovrebbe essere il tuo DataFrame principale

# Verifica che df esista
if 'df' not in locals():
    print("❌ DataFrame 'df' non trovato! Assicurati di aver caricato i dati.")
    # Carica i dati se necessario
    try:
        df = con.execute("""
        SELECT 
            "GameID",
            "Foundamental",
            "AbsNumeroPossesso",
            "RilevationNumber",
            "whichTeamID",
            "NumeroMaglia_ID",
            "Type",
            "Eval",
            "SetterCall",
            "TargAttk",
            "StartZone",
            "AttkCombination",
            'End_' || "EndZoneEsecZone" AS "EndZoneEsecZone",
            "EndSubzoneEsecSubzone",
            'Skill_' || "SkillType" AS "SkillType",
            "PlayersInfo",
            "Special",
            "CustomChar"
        FROM rilevations_libero_view_duckdb
        WHERE "Foundamental" IN ('S', 'R', 'E', 'A', 'B', 'D', 'F')  
        """).df()
        print(f"✅ Dataframe caricato: {len(df)} righe")
    except Exception as e:
        print(f"❌ ERRORE nel caricare i dati: {e}")
        df = pd.DataFrame()

if df.empty:
    print("❌ Nessun dato disponibile")
else:
    print(f"✅ Dati disponibili: {len(df)} righe")
    
    # Verifica se esiste una regola
    if fond2 not in regole_abs or fond1 not in regole_abs[fond2]:
        print(f"❌ Nessuna regola definita da {fond1} a {fond2}")
    else:
        print(f"✅ Regola trovata: {fond1} → {fond2}")
        
        # Filtra i dataframe
        df1 = df[df['Foundamental'] == fond1].copy()
        df2 = df[df['Foundamental'] == fond2].copy()
        
        print(f"📊 DataFrame 1 ({fond1}): {len(df1)} righe")
        print(f"📊 DataFrame 2 ({fond2}): {len(df2)} righe")
        
        if len(df1) == 0 or len(df2) == 0:
            print("❌ Uno dei DataFrame è vuoto")
        else:
            # Verifica che le caratteristiche esistano
            if char1 not in df1.columns:
                print(f"❌ Caratteristica '{char1}' non trovata in {fond1}")
                print(f"Colonne disponibili: {df1.columns.tolist()}")
            elif char2 not in df2.columns:
                print(f"❌ Caratteristica '{char2}' non trovata in {fond2}")
                print(f"Colonne disponibili: {df2.columns.tolist()}")
            else:
                print(f"✅ Caratteristiche trovate: {char1} e {char2}")
                
                # Crea i collegamenti senza merge - approccio diretto
                join_pairs = []
                matches_found = 0
                
                print("🔍 Creazione collegamenti...")
                
                # Prendi un campione per velocità
                df1_sample = df1.head(1000)
                
                for idx, row in df1_sample.iterrows():
                    prev_abs = row['AbsNumeroPossesso']
                    game_id = row['GameID']
                    team_id = row['whichTeamID']
                    
                    try:
                        expected_abs = regole_abs[fond2][fond1](prev_abs)
                    except Exception as e:
                        continue
                    
                    # Trova i match
                    matches = df2[
                        (df2['GameID'] == game_id) &
                        (df2['AbsNumeroPossesso'] == expected_abs) &
                        (df2['whichTeamID'] == team_id)
                    ]
                    
                    if not matches.empty:
                        matches_found += len(matches)
                        match = matches.iloc[0]
                        val1 = row.get(char1)
                        val2 = match.get(char2)
                        
                        if pd.notna(val1) and pd.notna(val2) and str(val1) != '' and str(val2) != '':
                            join_pairs.append((str(val1), str(val2)))
                
                print(f"🔗 Trovati {matches_found} match, creati {len(join_pairs)} collegamenti")
                
                if not join_pairs:
                    print("❌ Nessun collegamento valido trovato")
                else:
                    # Crea il dataframe dei collegamenti
                    df_pairs = pd.DataFrame(join_pairs, columns=['source', 'target'])
                    df_links = df_pairs.groupby(['source', 'target']).size().reset_index(name='value')
                    
                    print(f"📊 Collegamenti unici: {len(df_links)}")
                    
                    # Crea le etichette
                    source_labels = [f"{fond1}: {label}" for label in df_links['source'].unique()]
                    target_labels = [f"{fond2}: {label}" for label in df_links['target'].unique()]
                    all_labels = source_labels + target_labels
                    
                    # Mappa i valori agli indici
                    source_map = {val: idx for idx, val in enumerate(df_links['source'].unique())}
                    target_map = {val: idx + len(source_labels) for idx, val in enumerate(df_links['target'].unique())}
                    
                    # Crea il diagramma Sankey
                    fig = go.Figure(data=[go.Sankey(
                        node=dict(
                            label=all_labels,
                            pad=15,
                            thickness=20,
                            line=dict(color="black", width=0.5),
                            color=["lightblue"] * len(source_labels) + ["lightcoral"] * len(target_labels)
                        ),
                        link=dict(
                            source=[source_map[s] for s in df_links['source']],
                            target=[target_map[t] for t in df_links['target']],
                            value=df_links['value'].tolist()
                        )
                    )])
                    
                    fig.update_layout(
                        title_text=f"{fond1} ({char1}) → {fond2} ({char2})",
                        font_size=12,
                        height=600
                    )
                    
                    # Mostra il diagramma
                    fig.show()
                    print("✅ Diagramma Sankey mostrato!")
