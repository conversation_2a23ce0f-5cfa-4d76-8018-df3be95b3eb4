{"cells": [{"cell_type": "markdown", "id": "0e730b08", "metadata": {}, "source": ["Proviamo a creare la web app in locale"]}, {"cell_type": "code", "execution_count": null, "id": "28659827", "metadata": {}, "outputs": [], "source": ["import dash\n", "from dash import dcc, html, Input, Output, callback\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime, timedelta\n", "\n", "# Inizializza l'app Dash\n", "app = dash.Dash(__name__, suppress_callback_exceptions=True)\n", "\n", "# Genera dati di esempio più semplici\n", "np.random.seed(42)\n", "dates = pd.date_range(start='2024-01-01', end='2024-12-31', freq='D')\n", "sales_data = pd.DataFrame({\n", "    'date': dates,\n", "    'vendite': np.random.randint(100, 1000, len(dates)),\n", "    'prodotto': np.random.choice(['Prodotto A', 'Prodotto B', 'Prodotto C'], len(dates)),\n", "    'regione': np.random.choice(['Nord', 'Centro', 'Sud'], len(dates))\n", "})\n", "\n", "# Layout della homepage\n", "homepage_layout = html.Div([\n", "    html.Div([\n", "        html.H1(\"🏠 Dashboard Aziendale\", style={'textAlign': 'center', 'color': '#2c3e50'}),\n", "        html.P(\"Benvenuto nel sistema di analytics aziendale\", style={'textAlign': 'center', 'fontSize': '18px'}),\n", "        html.Hr(),\n", "    ], style={'marginBottom': '30px'}),\n", "    \n", "    # Card di navigazione\n", "    html.Div([\n", "        html.Div([\n", "            html.H3(\"📊 Analytics\", style={'color': '#3498db'}),\n", "            html.P(\"Visualizza grafici e statistiche delle vendite\"),\n", "            html.A(html.<PERSON><PERSON>(\"Vai ad Analytics\", style={'backgroundColor': '#3498db', 'color': 'white', 'border': 'none', 'padding': '10px 20px', 'borderRadius': '5px', 'cursor': 'pointer'}), href=\"/analytics\")\n", "        ], style={'width': '30%', 'display': 'inline-block', 'padding': '20px', 'backgroundColor': '#ecf0f1', 'margin': '10px', 'borderRadius': '10px', 'textAlign': 'center'}),\n", "        \n", "        html.Div([\n", "            html.H3(\"📈 Reports\", style={'color': '#e74c3c'}),\n", "            html.P(\"Genera report dettagliati sui dati\"),\n", "            html.A(html.<PERSON><PERSON>(\"Vai ai Reports\", style={'backgroundColor': '#e74c3c', 'color': 'white', 'border': 'none', 'padding': '10px 20px', 'borderRadius': '5px', 'cursor': 'pointer'}), href=\"/reports\")\n", "        ], style={'width': '30%', 'display': 'inline-block', 'padding': '20px', 'backgroundColor': '#ecf0f1', 'margin': '10px', 'borderRadius': '10px', 'textAlign': 'center'}),\n", "        \n", "        html.Div([\n", "            html.H3(\"⚙️ Impostazioni\", style={'color': '#2ecc71'}),\n", "            html.P(\"Configura parametri e preferenze\"),\n", "            html.A(html.<PERSON><PERSON>(\"Vai alle Impostazioni\", style={'backgroundColor': '#2ecc71', 'color': 'white', 'border': 'none', 'padding': '10px 20px', 'borderRadius': '5px', 'cursor': 'pointer'}), href=\"/settings\")\n", "        ], style={'width': '30%', 'display': 'inline-block', 'padding': '20px', 'backgroundColor': '#ecf0f1', 'margin': '10px', 'borderRadius': '10px', 'textAlign': 'center'}),\n", "    ], style={'textAlign': 'center'}),\n", "    \n", "    # Statistiche rapide\n", "    html.Div([\n", "        html.H3(\"Statistiche Rapide\", style={'color': '#2c3e50', 'textAlign': 'center'}),\n", "        html.Div([\n", "            html.Div([\n", "                html.H2(f\"€{sales_data['vendite'].sum():,.0f}\", style={'color': '#e74c3c', 'margin': '0'}),\n", "                html.P(\"Vendite Totali\", style={'margin': '5px 0'})\n", "            ], style={'textAlign': 'center', 'padding': '20px', 'backgroundColor': '#fff', 'margin': '10px', 'borderRadius': '5px', 'boxShadow': '0 2px 4px rgba(0,0,0,0.1)', 'width': '22%', 'display': 'inline-block'}),\n", "            \n", "            html.Div([\n", "                html.H2(f\"€{sales_data['vendite'].mean():,.0f}\", style={'color': '#3498db', 'margin': '0'}),\n", "                html.P(\"Media Giornaliera\", style={'margin': '5px 0'})\n", "            ], style={'textAlign': 'center', 'padding': '20px', 'backgroundColor': '#fff', 'margin': '10px', 'borderRadius': '5px', 'boxShadow': '0 2px 4px rgba(0,0,0,0.1)', 'width': '22%', 'display': 'inline-block'}),\n", "            \n", "            html.Div([\n", "                html.H2(f\"{len(sales_data)}\", style={'color': '#2ecc71', 'margin': '0'}),\n", "                html.P(\"<PERSON><PERSON><PERSON>\", style={'margin': '5px 0'})\n", "            ], style={'textAlign': 'center', 'padding': '20px', 'backgroundColor': '#fff', 'margin': '10px', 'borderRadius': '5px', 'boxShadow': '0 2px 4px rgba(0,0,0,0.1)', 'width': '22%', 'display': 'inline-block'}),\n", "            \n", "            html.Div([\n", "                html.H2(f\"{sales_data['prodotto'].nunique()}\", style={'color': '#f39c12', 'margin': '0'}),\n", "                html.P(\"Prodotti Attivi\", style={'margin': '5px 0'})\n", "            ], style={'textAlign': 'center', 'padding': '20px', 'backgroundColor': '#fff', 'margin': '10px', 'borderRadius': '5px', 'boxShadow': '0 2px 4px rgba(0,0,0,0.1)', 'width': '22%', 'display': 'inline-block'}),\n", "        ])\n", "    ], style={'marginTop': '30px'})\n", "])\n", "\n", "# Layout della pagina Analytics\n", "analytics_layout = html.Div([\n", "    html.Div([\n", "        html.H1(\"📊 Analytics\", style={'textAlign': 'center', 'color': '#2c3e50'}),\n", "        html.A(\"← Torna alla Home\", href=\"/\", style={'textDecoration': 'none', 'color': '#3498db'}),\n", "        html.Hr(),\n", "    ], style={'marginBottom': '20px'}),\n", "    \n", "    # Controlli interattivi\n", "    html.Div([\n", "        html.Div([\n", "            html.Label(\"Seleziona Prodotto:\", style={'fontWeight': 'bold'}),\n", "            dcc.Dropdown(\n", "                id='prodotto-dropdown',\n", "                options=[{'label': prod, 'value': prod} for prod in sales_data['prodotto'].unique()],\n", "                value=sales_data['prodotto'].unique()[0],\n", "                style={'marginBottom': '10px'}\n", "            ),\n", "        ], style={'width': '48%', 'display': 'inline-block'}),\n", "        \n", "        html.Div([\n", "            html.Label(\"Seleziona Regione:\", style={'fontWeight': 'bold'}),\n", "            dcc.Dropdown(\n", "                id='regione-dropdown',\n", "                options=[{'label': reg, 'value': reg} for reg in sales_data['regione'].unique()],\n", "                value=sales_data['regione'].unique()[0],\n", "                style={'marginBottom': '10px'}\n", "            ),\n", "        ], style={'width': '48%', 'float': 'right', 'display': 'inline-block'}),\n", "    ], style={'marginBottom': '20px'}),\n", "    \n", "    # Grafici\n", "    html.Div([\n", "        html.Div([\n", "            dcc.Graph(id='line-chart')\n", "        ], style={'width': '50%', 'display': 'inline-block'}),\n", "        \n", "        html.Div([\n", "            dcc.Graph(id='bar-chart')\n", "        ], style={'width': '50%', 'display': 'inline-block'}),\n", "    ]),\n", "    \n", "    html.Div([\n", "        dcc.Graph(id='pie-chart')\n", "    ], style={'width': '100%', 'marginTop': '20px'})\n", "])\n", "\n", "# Layout della pagina Reports\n", "reports_layout = html.Div([\n", "    html.Div([\n", "        html.H1(\"📈 Reports\", style={'textAlign': 'center', 'color': '#2c3e50'}),\n", "        html.A(\"← Torna alla Home\", href=\"/\", style={'textDecoration': 'none', 'color': '#3498db'}),\n", "        html.Hr(),\n", "    ], style={'marginBottom': '20px'}),\n", "    \n", "    html.Div([\n", "        html.H3(\"Report Vendite per Prodotto\", style={'color': '#e74c3c'}),\n", "        html.Div(id='product-report'),\n", "        \n", "        html.Hr(style={'margin': '30px 0'}),\n", "        \n", "        html.H3(\"Report Vendite per Regione\", style={'color': '#e74c3c'}),\n", "        html.Div(id='region-report'),\n", "    ])\n", "])\n", "\n", "# Layout della pagina Settings\n", "settings_layout = html.Div([\n", "    html.Div([\n", "        html.H1(\"⚙️ Impostazioni\", style={'textAlign': 'center', 'color': '#2c3e50'}),\n", "        html.A(\"← Torna alla Home\", href=\"/\", style={'textDecoration': 'none', 'color': '#3498db'}),\n", "        html.Hr(),\n", "    ], style={'marginBottom': '20px'}),\n", "    \n", "    html.Div([\n", "        html.H3(\"Configurazione Dashboard\", style={'color': '#2ecc71'}),\n", "        \n", "        html.Div([\n", "            html.Label(\"Tema:\", style={'fontWeight': 'bold'}),\n", "            dcc.Dropdown(\n", "                id='theme-dropdown',\n", "                options=[\n", "                    {'label': 'Chiaro', 'value': 'light'},\n", "                    {'label': '<PERSON>uro', 'value': 'dark'}\n", "                ],\n", "                value='light',\n", "                style={'marginBottom': '20px'}\n", "            ),\n", "        ], style={'width': '48%', 'display': 'inline-block'}),\n", "        \n", "        html.Div([\n", "            html.Label(\"Aggiornamento automatico:\", style={'fontWeight': 'bold'}),\n", "            dcc.Dropdown(\n", "                id='refresh-dropdown',\n", "                options=[\n", "                    {'label': '30 secondi', 'value': 30},\n", "                    {'label': '1 minuto', 'value': 60},\n", "                    {'label': '5 minuti', 'value': 300}\n", "                ],\n", "                value=60,\n", "                style={'marginBottom': '20px'}\n", "            ),\n", "        ], style={'width': '48%', 'float': 'right', 'display': 'inline-block'}),\n", "        \n", "        html.Div([\n", "            html.Button(\"Salva Impostazioni\", id='save-settings', \n", "                       style={'backgroundColor': '#2ecc71', 'color': 'white', 'border': 'none', 'padding': '10px 20px', 'borderRadius': '5px', 'cursor': 'pointer'}),\n", "            html.Div(id='settings-message', style={'marginTop': '10px'})\n", "        ])\n", "    ])\n", "])\n", "\n", "# Layout principale con routing\n", "app.layout = html.Div([\n", "    dcc.Location(id='url', refresh=False),\n", "    html.Div(id='page-content')\n", "])\n", "\n", "# Callback per il routing\n", "@app.callback(Output('page-content', 'children'),\n", "              Input('url', 'pathname'))\n", "def display_page(pathname):\n", "    if pathname == '/analytics':\n", "        return analytics_layout\n", "    elif pathname == '/reports':\n", "        return reports_layout\n", "    elif pathname == '/settings':\n", "        return settings_layout\n", "    else:\n", "        return homepage_layout\n", "\n", "# Callback per il grafico a linee\n", "@app.callback(\n", "    Output('line-chart', 'figure'),\n", "    [Input('prodotto-dropdown', 'value')]\n", ")\n", "def update_line_chart(selected_product):\n", "    if selected_product is None:\n", "        return {}\n", "    \n", "    filtered_df = sales_data[sales_data['prodotto'] == selected_product]\n", "    \n", "    fig = px.line(filtered_df, x='date', y='vendite', \n", "                  title=f'Trend Vendite - {selected_product}',\n", "                  labels={'date': 'Data', 'vendite': 'Vendite (€)'})\n", "    \n", "    fig.update_layout(\n", "        plot_bgcolor='rgba(0,0,0,0)',\n", "        paper_bgcolor='rgba(0,0,0,0)',\n", "        title_font_size=16\n", "    )\n", "    \n", "    return fig\n", "\n", "# Callback per il grafico a barre\n", "@app.callback(\n", "    Output('bar-chart', 'figure'),\n", "    [Input('regione-dropdown', 'value')]\n", ")\n", "def update_bar_chart(selected_region):\n", "    if selected_region is None:\n", "        return {}\n", "    \n", "    filtered_df = sales_data[sales_data['regione'] == selected_region]\n", "    monthly_sales = filtered_df.groupby('prodotto')['vendite'].sum().reset_index()\n", "    \n", "    fig = px.bar(monthly_sales, x='prodotto', y='vendite',\n", "                 title=f'Vendite per <PERSON> - {selected_region}',\n", "                 labels={'prodotto': 'Prodotto', 'vendite': 'Vendite (€)'})\n", "    \n", "    fig.update_layout(\n", "        plot_bgcolor='rgba(0,0,0,0)',\n", "        paper_bgcolor='rgba(0,0,0,0)',\n", "        title_font_size=16\n", "    )\n", "    \n", "    return fig\n", "\n", "# Callback per il grafico a torta\n", "@app.callback(\n", "    Output('pie-chart', 'figure'),\n", "    [Input('prodotto-dropdown', 'value'),\n", "     Input('regione-dropdown', 'value')]\n", ")\n", "def update_pie_chart(selected_product, selected_region):\n", "    region_sales = sales_data.groupby('regione')['vendite'].sum().reset_index()\n", "    \n", "    fig = px.pie(region_sales, values='vendite', names='regione',\n", "                 title='Distribuzione Vendite per Regione')\n", "    \n", "    fig.update_layout(\n", "        plot_bgcolor='rgba(0,0,0,0)',\n", "        paper_bgcolor='rgba(0,0,0,0)',\n", "        title_font_size=16\n", "    )\n", "    \n", "    return fig\n", "\n", "# Callback per i report prodotti\n", "@app.callback(\n", "    Output('product-report', 'children'),\n", "    Input('url', 'pathname')\n", ")\n", "def update_product_report(pathname):\n", "    if pathname != '/reports':\n", "        return []\n", "    \n", "    product_summary = sales_data.groupby('prodotto')['vendite'].agg(['sum', 'mean', 'count']).reset_index()\n", "    product_summary.columns = ['Prodotto', 'Vendite Totali', 'Media Vendite', '<PERSON><PERSON>ni Vendite']\n", "    \n", "    return html.Table([\n", "        html.Thead([\n", "            html.Tr([html.Th(col) for col in product_summary.columns])\n", "        ]),\n", "        html.Tbody([\n", "            html.Tr([\n", "                html.Td(product_summary.iloc[i][col]) for col in product_summary.columns\n", "            ]) for i in range(len(product_summary))\n", "        ])\n", "    ], style={'width': '100%', 'textAlign': 'center'})\n", "\n", "# Callback per i report regioni\n", "@app.callback(\n", "    Output('region-report', 'children'),\n", "    Input('url', 'pathname')\n", ")\n", "def update_region_report(pathname):\n", "    if pathname != '/reports':\n", "        return []\n", "    \n", "    region_summary = sales_data.groupby('regione')['vendite'].agg(['sum', 'mean', 'count']).reset_index()\n", "    region_summary.columns = ['Regione', 'Vendite Totali', 'Media Vendite', '<PERSON><PERSON>ni Vendite']\n", "    \n", "    return html.Table([\n", "        html.Thead([\n", "            html.Tr([html.Th(col) for col in region_summary.columns])\n", "        ]),\n", "        html.Tbody([\n", "            html.Tr([\n", "                html.Td(region_summary.iloc[i][col]) for col in region_summary.columns\n", "            ]) for i in range(len(region_summary))\n", "        ])\n", "    ], style={'width': '100%', 'textAlign': 'center'})\n", "\n", "# Callback per salvare le impostazioni\n", "@app.callback(\n", "    Output('settings-message', 'children'),\n", "    Input('save-settings', 'n_clicks'),\n", "    prevent_initial_call=True\n", ")\n", "def save_settings(n_clicks):\n", "    if n_clicks:\n", "        return html.Div(\"✅ Impostazioni salvate!\", style={'color': '#2ecc71', 'fontWeight': 'bold'})\n", "    return \"\"\n", "\n", "# Esegui l'app\n", "if __name__ == '__main__':\n", "    print(\"🚀 Avvio della Dashboard Multipagina...\")\n", "    print(\"📊 Apri il browser e vai su: http://127.0.0.1:8050\")\n", "    print(\"🔗 Pagine disponibili:\")\n", "    print(\"   - Home: http://127.0.0.1:8050/\")\n", "    print(\"   - Analytics: http://127.0.0.1:8050/analytics\")\n", "    print(\"   - Reports: http://127.0.0.1:8050/reports\")\n", "    print(\"   - Settings: http://127.0.0.1:8050/settings\")\n", "    print(\"⏹️  Premi Ctrl+C per fermare il server\")\n", "    \n", "    app.run(debug=True, host='127.0.0.1', port=8050)"]}, {"cell_type": "markdown", "id": "f4313159", "metadata": {}, "source": ["Aggiung<PERSON>o il login"]}, {"cell_type": "code", "execution_count": 13, "id": "b4a19d29", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 Avvio della Dashboard con Autenticazione...\n", "📊 Apri il browser e vai su: http://127.0.0.1:8051\n", "🔐 Credenziali disponibili:\n", "   - admin / password123\n", "   - user / user123\n", "   - demo / demo\n", "⏹️  Premi Ctrl+C per fermare il server\n"]}, {"data": {"text/html": ["\n", "        <iframe\n", "            width=\"100%\"\n", "            height=\"650\"\n", "            src=\"http://127.0.0.1:8051/\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "            \n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.IFrame at 0x1cb89defd50>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import dash\n", "from dash import dcc, html, Input, Output, State, callback\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime, timedelta\n", "import hashlib\n", "\n", "# Inizializza l'app Dash\n", "app = dash.Dash(__name__, suppress_callback_exceptions=True)\n", "\n", "# Database utenti semplice (in produzione usare un DB vero)\n", "USERS_DB = {\n", "    'admin': hashlib.sha256('password123'.encode()).hexdigest(),\n", "    'user': hashlib.sha256('user123'.encode()).hexdigest(),\n", "    'demo': hashlib.sha256('demo'.encode()).hexdigest()\n", "}\n", "\n", "# Genera dati di esempio\n", "np.random.seed(42)\n", "dates = pd.date_range(start='2024-01-01', end='2024-12-31', freq='D')\n", "sales_data = pd.DataFrame({\n", "    'date': dates,\n", "    'vendite': np.random.randint(100, 1000, len(dates)),\n", "    'prodotto': np.random.choice(['Prodotto A', 'Prodotto B', 'Prodotto C'], len(dates)),\n", "    'regione': np.random.choice(['Nord', 'Centro', 'Sud'], len(dates))\n", "})\n", "\n", "# Layout della pagina di login\n", "login_layout = html.Div([\n", "    html.Div([\n", "        html.Div([\n", "            html.H1(\"🔐 Login Dashboard\", style={'textAlign': 'center', 'color': '#2c3e50', 'marginBottom': '30px'}),\n", "            \n", "            html.Div([\n", "                html.Label(\"Username:\", style={'fontWeight': 'bold', 'marginBottom': '5px', 'display': 'block'}),\n", "                dcc.Input(\n", "                    id='username-input',\n", "                    type='text',\n", "                    placeholder='Inserisci username',\n", "                    style={'width': '100%', 'padding': '10px', 'marginBottom': '15px', 'border': '1px solid #ddd', 'borderRadius': '5px'}\n", "                ),\n", "                \n", "                html.Label(\"Password:\", style={'fontWeight': 'bold', 'marginBottom': '5px', 'display': 'block'}),\n", "                dcc.Input(\n", "                    id='password-input',\n", "                    type='password',\n", "                    placeholder='Inserisci password',\n", "                    style={'width': '100%', 'padding': '10px', 'marginBottom': '20px', 'border': '1px solid #ddd', 'borderRadius': '5px'}\n", "                ),\n", "                \n", "                html.<PERSON><PERSON>(\n", "                    'Accedi',\n", "                    id='login-button',\n", "                    n_clicks=0,\n", "                    style={'width': '100%', 'padding': '12px', 'backgroundColor': '#3498db', 'color': 'white', 'border': 'none', 'borderRadius': '5px', 'cursor': 'pointer', 'fontSize': '16px'}\n", "                ),\n", "                \n", "                html.Div(id='login-message', style={'marginTop': '15px', 'textAlign': 'center'})\n", "            ])\n", "        ], style={'backgroundColor': 'white', 'padding': '40px', 'borderRadius': '10px', 'boxShadow': '0 4px 6px rgba(0, 0, 0, 0.1)', 'width': '400px'})\n", "    ], style={'display': 'flex', 'justifyContent': 'center', 'alignItems': 'center', 'height': '100vh', 'backgroundColor': '#ecf0f1'}),\n", "    \n", "    # Credenziali di esempio\n", "    html.Div([\n", "        html.H4(\"👤 Credenziali di Prova:\", style={'color': '#7f8c8d', 'marginBottom': '10px'}),\n", "        html.P(\"admin / password123\", style={'margin': '5px 0', 'color': '#7f8c8d'}),\n", "        html.P(\"user / user123\", style={'margin': '5px 0', 'color': '#7f8c8d'}),\n", "        html.P(\"demo / demo\", style={'margin': '5px 0', 'color': '#7f8c8d'})\n", "    ], style={'position': 'fixed', 'top': '20px', 'right': '20px', 'backgroundColor': 'white', 'padding': '15px', 'borderRadius': '5px', 'boxShadow': '0 2px 4px rgba(0, 0, 0, 0.1)'})\n", "], style={'fontFamily': 'Arial, sans-serif'})\n", "\n", "# Layout della homepage (dopo login)\n", "homepage_layout = html.Div([\n", "    # Header con logout\n", "    html.Div([\n", "        html.Div([\n", "            html.H1(\"🏠 Dashboard Aziendale\", style={'color': '#2c3e50', 'margin': '0'}),\n", "            html.Div([\n", "                html.Span(id='user-welcome', style={'marginRight': '20px', 'color': '#7f8c8d'}),\n", "                html.Button('Logout', id='logout-button', \n", "                           style={'backgroundColor': '#e74c3c', 'color': 'white', 'border': 'none', 'padding': '8px 15px', 'borderRadius': '5px', 'cursor': 'pointer'})\n", "            ], style={'display': 'flex', 'alignItems': 'center'})\n", "        ], style={'display': 'flex', 'justifyContent': 'space-between', 'alignItems': 'center', 'padding': '20px 0'})\n", "    ], style={'borderBottom': '1px solid #ecf0f1', 'marginBottom': '30px'}),\n", "    \n", "    html.Div([\n", "        html.P(\"Benvenuto nel sistema di analytics aziendale\", style={'textAlign': 'center', 'fontSize': '18px'}),\n", "    ], style={'marginBottom': '30px'}),\n", "    \n", "    # Card di navigazione\n", "    html.Div([\n", "        html.Div([\n", "            html.H3(\"📊 Analytics\", style={'color': '#3498db'}),\n", "            html.P(\"Visualizza grafici e statistiche delle vendite\"),\n", "            html.A(html.<PERSON><PERSON>(\"Vai ad Analytics\", style={'backgroundColor': '#3498db', 'color': 'white', 'border': 'none', 'padding': '10px 20px', 'borderRadius': '5px', 'cursor': 'pointer'}), href=\"/analytics\")\n", "        ], style={'width': '30%', 'display': 'inline-block', 'padding': '20px', 'backgroundColor': '#ecf0f1', 'margin': '10px', 'borderRadius': '10px', 'textAlign': 'center'}),\n", "        \n", "        html.Div([\n", "            html.H3(\"📈 Reports\", style={'color': '#e74c3c'}),\n", "            html.P(\"Genera report dettagliati sui dati\"),\n", "            html.A(html.<PERSON><PERSON>(\"Vai ai Reports\", style={'backgroundColor': '#e74c3c', 'color': 'white', 'border': 'none', 'padding': '10px 20px', 'borderRadius': '5px', 'cursor': 'pointer'}), href=\"/reports\")\n", "        ], style={'width': '30%', 'display': 'inline-block', 'padding': '20px', 'backgroundColor': '#ecf0f1', 'margin': '10px', 'borderRadius': '10px', 'textAlign': 'center'}),\n", "        \n", "        html.Div([\n", "            html.H3(\"⚙️ Impostazioni\", style={'color': '#2ecc71'}),\n", "            html.P(\"Configura parametri e preferenze\"),\n", "            html.A(html.<PERSON><PERSON>(\"Vai alle Impostazioni\", style={'backgroundColor': '#2ecc71', 'color': 'white', 'border': 'none', 'padding': '10px 20px', 'borderRadius': '5px', 'cursor': 'pointer'}), href=\"/settings\")\n", "        ], style={'width': '30%', 'display': 'inline-block', 'padding': '20px', 'backgroundColor': '#ecf0f1', 'margin': '10px', 'borderRadius': '10px', 'textAlign': 'center'}),\n", "    ], style={'textAlign': 'center'}),\n", "    \n", "    # Statistiche rapide\n", "    html.Div([\n", "        html.H3(\"Statistiche Rapide\", style={'color': '#2c3e50', 'textAlign': 'center'}),\n", "        html.Div([\n", "            html.Div([\n", "                html.H2(f\"€{sales_data['vendite'].sum():,.0f}\", style={'color': '#e74c3c', 'margin': '0'}),\n", "                html.P(\"Vendite Totali\", style={'margin': '5px 0'})\n", "            ], style={'textAlign': 'center', 'padding': '20px', 'backgroundColor': '#fff', 'margin': '10px', 'borderRadius': '5px', 'boxShadow': '0 2px 4px rgba(0,0,0,0.1)', 'width': '22%', 'display': 'inline-block'}),\n", "            \n", "            html.Div([\n", "                html.H2(f\"€{sales_data['vendite'].mean():,.0f}\", style={'color': '#3498db', 'margin': '0'}),\n", "                html.P(\"Media Giornaliera\", style={'margin': '5px 0'})\n", "            ], style={'textAlign': 'center', 'padding': '20px', 'backgroundColor': '#fff', 'margin': '10px', 'borderRadius': '5px', 'boxShadow': '0 2px 4px rgba(0,0,0,0.1)', 'width': '22%', 'display': 'inline-block'}),\n", "            \n", "            html.Div([\n", "                html.H2(f\"{len(sales_data)}\", style={'color': '#2ecc71', 'margin': '0'}),\n", "                html.P(\"<PERSON><PERSON><PERSON>\", style={'margin': '5px 0'})\n", "            ], style={'textAlign': 'center', 'padding': '20px', 'backgroundColor': '#fff', 'margin': '10px', 'borderRadius': '5px', 'boxShadow': '0 2px 4px rgba(0,0,0,0.1)', 'width': '22%', 'display': 'inline-block'}),\n", "            \n", "            html.Div([\n", "                html.H2(f\"{sales_data['prodotto'].nunique()}\", style={'color': '#f39c12', 'margin': '0'}),\n", "                html.P(\"Prodotti Attivi\", style={'margin': '5px 0'})\n", "            ], style={'textAlign': 'center', 'padding': '20px', 'backgroundColor': '#fff', 'margin': '10px', 'borderRadius': '5px', 'boxShadow': '0 2px 4px rgba(0,0,0,0.1)', 'width': '22%', 'display': 'inline-block'}),\n", "        ])\n", "    ], style={'marginTop': '30px'})\n", "])\n", "\n", "# Layout della pagina Analytics\n", "analytics_layout = html.Div([\n", "    html.Div([\n", "        html.Div([\n", "            html.H1(\"📊 Analytics\", style={'color': '#2c3e50', 'margin': '0'}),\n", "            html.Div([\n", "                html.A(\"← Torna alla Home\", href=\"/\", style={'textDecoration': 'none', 'color': '#3498db', 'marginRight': '20px'}),\n", "                html.Button('Logout', id='logout-button-analytics', \n", "                           style={'backgroundColor': '#e74c3c', 'color': 'white', 'border': 'none', 'padding': '8px 15px', 'borderRadius': '5px', 'cursor': 'pointer'})\n", "            ])\n", "        ], style={'display': 'flex', 'justifyContent': 'space-between', 'alignItems': 'center', 'padding': '20px 0'})\n", "    ], style={'borderBottom': '1px solid #ecf0f1', 'marginBottom': '20px'}),\n", "    \n", "    # Controlli interattivi\n", "    html.Div([\n", "        html.Div([\n", "            html.Label(\"Seleziona Prodotto:\", style={'fontWeight': 'bold'}),\n", "            dcc.Dropdown(\n", "                id='prodotto-dropdown',\n", "                options=[{'label': prod, 'value': prod} for prod in sales_data['prodotto'].unique()],\n", "                value=sales_data['prodotto'].unique()[0],\n", "                style={'marginBottom': '10px'}\n", "            ),\n", "        ], style={'width': '48%', 'display': 'inline-block'}),\n", "        \n", "        html.Div([\n", "            html.Label(\"Seleziona Regione:\", style={'fontWeight': 'bold'}),\n", "            dcc.Dropdown(\n", "                id='regione-dropdown',\n", "                options=[{'label': reg, 'value': reg} for reg in sales_data['regione'].unique()],\n", "                value=sales_data['regione'].unique()[0],\n", "                style={'marginBottom': '10px'}\n", "            ),\n", "        ], style={'width': '48%', 'float': 'right', 'display': 'inline-block'}),\n", "    ], style={'marginBottom': '20px'}),\n", "    \n", "    # Grafici\n", "    html.Div([\n", "        html.Div([\n", "            dcc.Graph(id='line-chart')\n", "        ], style={'width': '50%', 'display': 'inline-block'}),\n", "        \n", "        html.Div([\n", "            dcc.Graph(id='bar-chart')\n", "        ], style={'width': '50%', 'display': 'inline-block'}),\n", "    ]),\n", "    \n", "    html.Div([\n", "        dcc.Graph(id='pie-chart')\n", "    ], style={'width': '100%', 'marginTop': '20px'})\n", "])\n", "\n", "# Layout della pagina Reports\n", "reports_layout = html.Div([\n", "    html.Div([\n", "        html.Div([\n", "            html.H1(\"📈 Reports\", style={'color': '#2c3e50', 'margin': '0'}),\n", "            html.Div([\n", "                html.A(\"← Torna alla Home\", href=\"/\", style={'textDecoration': 'none', 'color': '#3498db', 'marginRight': '20px'}),\n", "                html.Button('Logout', id='logout-button-reports', \n", "                           style={'backgroundColor': '#e74c3c', 'color': 'white', 'border': 'none', 'padding': '8px 15px', 'borderRadius': '5px', 'cursor': 'pointer'})\n", "            ])\n", "        ], style={'display': 'flex', 'justifyContent': 'space-between', 'alignItems': 'center', 'padding': '20px 0'})\n", "    ], style={'borderBottom': '1px solid #ecf0f1', 'marginBottom': '20px'}),\n", "    \n", "    html.Div([\n", "        html.H3(\"Report Vendite per Prodotto\", style={'color': '#e74c3c'}),\n", "        html.Div(id='product-report'),\n", "        \n", "        html.Hr(style={'margin': '30px 0'}),\n", "        \n", "        html.H3(\"Report Vendite per Regione\", style={'color': '#e74c3c'}),\n", "        html.Div(id='region-report'),\n", "    ])\n", "])\n", "\n", "# Layout della pagina Settings\n", "settings_layout = html.Div([\n", "    html.Div([\n", "        html.Div([\n", "            html.H1(\"⚙️ Impostazioni\", style={'color': '#2c3e50', 'margin': '0'}),\n", "            html.Div([\n", "                html.A(\"← Torna alla Home\", href=\"/\", style={'textDecoration': 'none', 'color': '#3498db', 'marginRight': '20px'}),\n", "                html.Button('Logout', id='logout-button-settings', \n", "                           style={'backgroundColor': '#e74c3c', 'color': 'white', 'border': 'none', 'padding': '8px 15px', 'borderRadius': '5px', 'cursor': 'pointer'})\n", "            ])\n", "        ], style={'display': 'flex', 'justifyContent': 'space-between', 'alignItems': 'center', 'padding': '20px 0'})\n", "    ], style={'borderBottom': '1px solid #ecf0f1', 'marginBottom': '20px'}),\n", "    \n", "    html.Div([\n", "        html.H3(\"Configurazione Dashboard\", style={'color': '#2ecc71'}),\n", "        \n", "        html.Div([\n", "            html.Label(\"Tema:\", style={'fontWeight': 'bold'}),\n", "            dcc.Dropdown(\n", "                id='theme-dropdown',\n", "                options=[\n", "                    {'label': 'Chiaro', 'value': 'light'},\n", "                    {'label': '<PERSON>uro', 'value': 'dark'}\n", "                ],\n", "                value='light',\n", "                style={'marginBottom': '20px'}\n", "            ),\n", "        ], style={'width': '48%', 'display': 'inline-block'}),\n", "        \n", "        html.Div([\n", "            html.Label(\"Aggiornamento automatico:\", style={'fontWeight': 'bold'}),\n", "            dcc.Dropdown(\n", "                id='refresh-dropdown',\n", "                options=[\n", "                    {'label': '30 secondi', 'value': 30},\n", "                    {'label': '1 minuto', 'value': 60},\n", "                    {'label': '5 minuti', 'value': 300}\n", "                ],\n", "                value=60,\n", "                style={'marginBottom': '20px'}\n", "            ),\n", "        ], style={'width': '48%', 'float': 'right', 'display': 'inline-block'}),\n", "        \n", "        html.Div([\n", "            html.Button(\"Salva Impostazioni\", id='save-settings', \n", "                       style={'backgroundColor': '#2ecc71', 'color': 'white', 'border': 'none', 'padding': '10px 20px', 'borderRadius': '5px', 'cursor': 'pointer'}),\n", "            html.Div(id='settings-message', style={'marginTop': '10px'})\n", "        ])\n", "    ])\n", "])\n", "\n", "# Layout principale con routing\n", "app.layout = html.Div([\n", "    dcc.Location(id='url', refresh=False),\n", "    dcc.Store(id='session-store', storage_type='session'),\n", "    html.Div(id='page-content')\n", "])\n", "\n", "# Callback per il login\n", "@app.callback(\n", "    [Output('session-store', 'data'),\n", "     Output('login-message', 'children'),\n", "     Output('url', 'pathname')],\n", "    [Input('login-button', 'n_clicks')],\n", "    [State('username-input', 'value'),\n", "     State('password-input', 'value')]\n", ")\n", "def login_user(n_clicks, username, password):\n", "    if n_clicks > 0:\n", "        if username and password:\n", "            hashed_password = hashlib.sha256(password.encode()).hexdigest()\n", "            if username in USERS_DB and USERS_DB[username] == hashed_password:\n", "                return {'logged_in': True, 'username': username}, \"\", \"/\"\n", "            else:\n", "                return {'logged_in': False}, html.Div(\"❌ Credenziali non valide\", style={'color': '#e74c3c', 'fontWeight': 'bold'}), \"/login\"\n", "        else:\n", "            return {'logged_in': False}, html.Div(\"⚠️ Inserisci username e password\", style={'color': '#f39c12', 'fontWeight': 'bold'}), \"/login\"\n", "    return {'logged_in': False}, \"\", \"/login\"\n", "\n", "# Callback per il logout\n", "@app.callback(\n", "    [Output('session-store', 'data', allow_duplicate=True),\n", "     Output('url', 'pathname', allow_duplicate=True)],\n", "    [Input('logout-button', 'n_clicks'),\n", "     Input('logout-button-analytics', 'n_clicks'),\n", "     Input('logout-button-reports', 'n_clicks'),\n", "     Input('logout-button-settings', 'n_clicks')],\n", "    prevent_initial_call=True\n", ")\n", "def logout_user(n1, n2, n3, n4):\n", "    if any([n1, n2, n3, n4]):\n", "        return {'logged_in': False}, \"/login\"\n", "    return {'logged_in': False}, \"/login\"\n", "\n", "# Callback per il routing con autenticazione\n", "@app.callback(\n", "    [Output('page-content', 'children'),\n", "     Output('user-welcome', 'children')],\n", "    [Input('url', 'pathname'),\n", "     Input('session-store', 'data')]\n", ")\n", "def display_page(pathname, session_data):\n", "    if not session_data or not session_data.get('logged_in'):\n", "        return login_layout, \"\"\n", "    \n", "    username = session_data.get('username', 'Utente')\n", "    welcome_msg = f\"<PERSON><PERSON><PERSON>, {username}!\"\n", "    \n", "    if pathname == '/analytics':\n", "        return analytics_layout, welcome_msg\n", "    elif pathname == '/reports':\n", "        return reports_layout, welcome_msg\n", "    elif pathname == '/settings':\n", "        return settings_layout, welcome_msg\n", "    elif pathname == '/login':\n", "        return login_layout, \"\"\n", "    else:\n", "        return homepage_layout, welcome_msg\n", "\n", "# Callback per il grafico a linee\n", "@app.callback(\n", "    Output('line-chart', 'figure'),\n", "    [Input('prodotto-dropdown', 'value')]\n", ")\n", "def update_line_chart(selected_product):\n", "    if selected_product is None:\n", "        return {}\n", "    \n", "    filtered_df = sales_data[sales_data['prodotto'] == selected_product]\n", "    \n", "    fig = px.line(filtered_df, x='date', y='vendite', \n", "                  title=f'Trend Vendite - {selected_product}',\n", "                  labels={'date': 'Data', 'vendite': 'Vendite (€)'})\n", "    \n", "    fig.update_layout(\n", "        plot_bgcolor='rgba(0,0,0,0)',\n", "        paper_bgcolor='rgba(0,0,0,0)',\n", "        title_font_size=16\n", "    )\n", "    \n", "    return fig\n", "\n", "# Callback per il grafico a barre\n", "@app.callback(\n", "    Output('bar-chart', 'figure'),\n", "    [Input('regione-dropdown', 'value')]\n", ")\n", "def update_bar_chart(selected_region):\n", "    if selected_region is None:\n", "        return {}\n", "    \n", "    filtered_df = sales_data[sales_data['regione'] == selected_region]\n", "    monthly_sales = filtered_df.groupby('prodotto')['vendite'].sum().reset_index()\n", "    \n", "    fig = px.bar(monthly_sales, x='prodotto', y='vendite',\n", "                 title=f'Vendite per <PERSON> - {selected_region}',\n", "                 labels={'prodotto': 'Prodotto', 'vendite': 'Vendite (€)'})\n", "    \n", "    fig.update_layout(\n", "        plot_bgcolor='rgba(0,0,0,0)',\n", "        paper_bgcolor='rgba(0,0,0,0)',\n", "        title_font_size=16\n", "    )\n", "    \n", "    return fig\n", "\n", "# Callback per il grafico a torta\n", "@app.callback(\n", "    Output('pie-chart', 'figure'),\n", "    [Input('prodotto-dropdown', 'value'),\n", "     Input('regione-dropdown', 'value')]\n", ")\n", "def update_pie_chart(selected_product, selected_region):\n", "    region_sales = sales_data.groupby('regione')['vendite'].sum().reset_index()\n", "    \n", "    fig = px.pie(region_sales, values='vendite', names='regione',\n", "                 title='Distribuzione Vendite per Regione')\n", "    \n", "    fig.update_layout(\n", "        plot_bgcolor='rgba(0,0,0,0)',\n", "        paper_bgcolor='rgba(0,0,0,0)',\n", "        title_font_size=16\n", "    )\n", "    \n", "    return fig\n", "\n", "# Callback per i report prodotti\n", "@app.callback(\n", "    Output('product-report', 'children'),\n", "    Input('url', 'pathname')\n", ")\n", "def update_product_report(pathname):\n", "    if pathname != '/reports':\n", "        return []\n", "    \n", "    product_summary = sales_data.groupby('prodotto')['vendite'].agg(['sum', 'mean', 'count']).reset_index()\n", "    product_summary.columns = ['Prodotto', 'Vendite Totali', 'Media Vendite', '<PERSON><PERSON>ni Vendite']\n", "    \n", "    return html.Table([\n", "        html.Thead([\n", "            html.Tr([html.Th(col) for col in product_summary.columns])\n", "        ]),\n", "        html.Tbody([\n", "            html.Tr([\n", "                html.Td(product_summary.iloc[i][col]) for col in product_summary.columns\n", "            ]) for i in range(len(product_summary))\n", "        ])\n", "    ], style={'width': '100%', 'textAlign': 'center'})\n", "\n", "# Callback per i report regioni\n", "@app.callback(\n", "    Output('region-report', 'children'),\n", "    Input('url', 'pathname')\n", ")\n", "def update_region_report(pathname):\n", "    if pathname != '/reports':\n", "        return []\n", "    \n", "    region_summary = sales_data.groupby('regione')['vendite'].agg(['sum', 'mean', 'count']).reset_index()\n", "    region_summary.columns = ['Regione', 'Vendite Totali', 'Media Vendite', '<PERSON><PERSON>ni Vendite']\n", "    \n", "    return html.Table([\n", "        html.Thead([\n", "            html.Tr([html.Th(col) for col in region_summary.columns])\n", "        ]),\n", "        html.Tbody([\n", "            html.Tr([\n", "                html.Td(region_summary.iloc[i][col]) for col in region_summary.columns\n", "            ]) for i in range(len(region_summary))\n", "        ])\n", "    ], style={'width': '100%', 'textAlign': 'center'})\n", "\n", "# Callback per salvare le impostazioni\n", "@app.callback(\n", "    Output('settings-message', 'children'),\n", "    Input('save-settings', 'n_clicks'),\n", "    prevent_initial_call=True\n", ")\n", "def save_settings(n_clicks):\n", "    if n_clicks:\n", "        return html.Div(\"✅ Impostazioni salvate!\", style={'color': '#2ecc71', 'fontWeight': 'bold'})\n", "    return \"\"\n", "\n", "# Esegui l'app\n", "if __name__ == '__main__':\n", "    print(\"🚀 Avvio della Dashboard con Autenticazione...\")\n", "    print(\"📊 Apri il browser e vai su: http://127.0.0.1:8051\")\n", "    print(\"🔐 Credenziali disponibili:\")\n", "    print(\"   - admin / password123\")\n", "    print(\"   - user / user123\") \n", "    print(\"   - demo / demo\")\n", "    print(\"⏹️  Premi Ctrl+C per fermare il server\")\n", "    \n", "    app.run(debug=True, host='127.0.0.1', port=8051)\n", "\n", "#per chiudere il processo che sta usando la porta XXXX posso aprire il command prompt e fare:\n", "#netstat -ano | findstr :XXXX\n", "#Dopo che ho trovato il PID del processo faccio:\n", "#taskkill /PID XXXX /F\n"]}, {"cell_type": "code", "execution_count": null, "id": "1171adc4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}