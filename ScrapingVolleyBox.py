# Importa le librerie necessarie
import requests
from bs4 import BeautifulSoup
#C:\Users\<USER>\Documents\ModenaVolley\Codice\.venv\Scripts\pip install requests beautifulsoup4
import time
import random

def trova_url_volleybox_duckduckgo(nome, cognome):
    query = f"site:volleybox.net/it {nome} {cognome}"
    url_ricerca = f"https://html.duckduckgo.com/html/?q={query.replace(' ', '+')}"
    
    headers = {
        "User-Agent": random.choice([
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.109 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.54 Safari/537.36"
        ])
    }

    response = requests.get(url_ricerca, headers=headers)
    if response.status_code != 200:
        print("Errore nella richiesta a DuckDuckGo")
        return None

    soup = BeautifulSoup(response.text, "html.parser")
    
    # Trova il primo link effettivo ai risultati
    for result in soup.select(".result__url, .result__a"):
        url = result.text.strip()

        # Se l'URL non è completo, aggiungi "https://"
        if not url.startswith("http"):
            url = "https://" + url

        # Assicuriamoci che sia un link di volleybox.net
        if "volleybox.net/it" in url:
            return url

    print("Nessun URL trovato")
    return None



def estrai_info_giocatore(url_giocatore):
    headers = {
        "User-Agent": random.choice([
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.109 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.54 Safari/537.36"
        ])
    }
    
    response = requests.get(url_giocatore, headers=headers)
    print(f"Codice di stato: {response.status_code}")

    # Stampa i primi 500 caratteri della pagina per vedere se ci blocca
    print(response.text[:500])
    if response.status_code != 200:
        print("Errore nel caricamento della pagina")
        return None

    soup = BeautifulSoup(response.text, "html.parser")

    # Trova il box contenente le informazioni
    box_info = soup.find("div", class_="new_box pRelative")
    if not box_info:
        print("Informazioni non trovate")
        return None

    # Estrai il testo del box
    testo_box = box_info.get_text(separator="\n").strip()
    righe = testo_box.split("\n")

    # Inizializza le variabili
    nome = ruolo = altezza = peso = nazionalita = "N/A"

    for riga in righe:
        riga_lower = riga.lower()
        if "altezza" in riga_lower:
            altezza = riga.split(":")[-1].strip()
        elif "peso" in riga_lower:
            peso = riga.split(":")[-1].strip()
        elif "nazionalità" in riga_lower:
            nazionalita = riga.split(":")[-1].strip()
        elif "ruolo" in riga_lower:
            ruolo = riga.split(":")[-1].strip()

    return {
        "Nome": soup.find("h1").text.strip() if soup.find("h1") else "Nome non trovato",
        "Ruolo": ruolo,
        "Altezza": altezza,
        "Peso": peso,
        "Nazionalità": nazionalita
    }





# Esempio di utilizzo
nome = "Wilfredo"
cognome = "Leon"
url = trova_url_volleybox_duckduckgo(nome, cognome)
print("URL trovato:", url)

# Aspetta tra 3 e 6 secondi per ridurre il rischio di blocco
time.sleep(random.uniform(3, 6))

info = estrai_info_giocatore(url)
print(info)

