{"cells": [{"cell_type": "markdown", "metadata": {"id": "vpxmQ-xNB1VY"}, "source": ["<p align=\"center\">\n", "<img src=\"https://raw.githubusercontent.com/shukkkur/VolleyVision/main/assets/vv_logo.png\">\n", "</p>\n", "<h1 align=\"center\">Welcome to VolleyVision<br><code>Local</code> - Quick & Easy</h1>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Installazione delle dipendenze\n", "\n", "Installiamo le dipendenze necessarie una alla volta per evitare conflitti:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: pip in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (24.3.1)\n", "Collecting pip\n", "  Using cached pip-25.1.1-py3-none-any.whl.metadata (3.6 kB)\n", "Using cached pip-25.1.1-py3-none-any.whl (1.8 MB)\n", "Installing collected packages: pip\n", "  Attempting uninstall: pip\n", "    Found existing installation: pip 24.3.1\n", "    Uninstalling pip-24.3.1:\n", "      Successfully uninstalled pip-24.3.1\n", "Successfully installed pip-25.1.1\n"]}], "source": ["# Aggiorna pip\n", "!python -m pip install --upgrade pip"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: torch in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (2.7.0)\n", "Requirement already satisfied: filelock in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from torch) (3.18.0)\n", "Requirement already satisfied: typing-extensions>=4.10.0 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from torch) (4.13.2)\n", "Requirement already satisfied: sympy>=1.13.3 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from torch) (1.14.0)\n", "Requirement already satisfied: networkx in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from torch) (3.4.2)\n", "Requirement already satisfied: jinja2 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from torch) (3.1.6)\n", "Requirement already satisfied: fsspec in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from torch) (2025.5.0)\n", "Requirement already satisfied: setuptools in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from torch) (80.8.0)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from sympy>=1.13.3->torch) (1.3.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from jinja2->torch) (3.0.2)\n", "Requirement already satisfied: opencv-python in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (4.11.0.86)\n", "Requirement already satisfied: numpy>=1.21.2 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from opencv-python) (2.2.6)\n", "Requirement already satisfied: pillow in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (11.2.1)\n", "Requirement already satisfied: numpy in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (2.2.6)\n", "Requirement already satisfied: matplotlib in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (3.10.3)\n", "Requirement already satisfied: contourpy>=1.0.1 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from matplotlib) (1.3.2)\n", "Requirement already satisfied: cycler>=0.10 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from matplotlib) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from matplotlib) (4.58.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from matplotlib) (1.4.8)\n", "Requirement already satisfied: numpy>=1.23 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from matplotlib) (2.2.6)\n", "Requirement already satisfied: packaging>=20.0 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from matplotlib) (25.0)\n", "Requirement already satisfied: pillow>=8 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from matplotlib) (11.2.1)\n", "Requirement already satisfied: pyparsing>=2.3.1 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from matplotlib) (3.2.3)\n", "Requirement already satisfied: python-dateutil>=2.7 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from matplotlib) (2.9.0.post0)\n", "Requirement already satisfied: six>=1.5 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from python-dateutil>=2.7->matplotlib) (1.17.0)\n", "Requirement already satisfied: tqdm in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (4.67.1)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from tqdm) (0.4.6)\n", "Requirement already satisfied: roboflow in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (1.1.64)\n", "Requirement already satisfied: certifi in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from roboflow) (2025.4.26)\n", "Requirement already satisfied: idna==3.7 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from roboflow) (3.7)\n", "Requirement already satisfied: cycler in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from roboflow) (0.12.1)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from roboflow) (1.4.8)\n", "Requirement already satisfied: matplotlib in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from roboflow) (3.10.3)\n", "Requirement already satisfied: numpy>=1.18.5 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from roboflow) (2.2.6)\n", "Requirement already satisfied: opencv-python-headless==4.10.0.84 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from roboflow) (4.10.0.84)\n", "Requirement already satisfied: Pillow>=7.1.2 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from roboflow) (11.2.1)\n", "Requirement already satisfied: pillow-heif>=0.18.0 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from roboflow) (0.22.0)\n", "Requirement already satisfied: python-dateutil in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from roboflow) (2.9.0.post0)\n", "Requirement already satisfied: python-dotenv in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from roboflow) (1.1.0)\n", "Requirement already satisfied: requests in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from roboflow) (2.32.3)\n", "Requirement already satisfied: six in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from roboflow) (1.17.0)\n", "Requirement already satisfied: urllib3>=1.26.6 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from roboflow) (2.4.0)\n", "Requirement already satisfied: tqdm>=4.41.0 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from roboflow) (4.67.1)\n", "Requirement already satisfied: PyYAML>=5.3.1 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from roboflow) (6.0.2)\n", "Requirement already satisfied: requests-toolbelt in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from roboflow) (1.0.0)\n", "Requirement already satisfied: filetype in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from roboflow) (1.2.0)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from tqdm>=4.41.0->roboflow) (0.4.6)\n", "Requirement already satisfied: contourpy>=1.0.1 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from matplotlib->roboflow) (1.3.2)\n", "Requirement already satisfied: fonttools>=4.22.0 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from matplotlib->roboflow) (4.58.0)\n", "Requirement already satisfied: packaging>=20.0 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from matplotlib->roboflow) (25.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from matplotlib->roboflow) (3.2.3)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from requests->roboflow) (3.4.2)\n", "Requirement already satisfied: requests in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (2.32.3)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from requests) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from requests) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from requests) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from requests) (2025.4.26)\n", "Requirement already satisfied: pyyaml in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (6.0.2)\n", "Requirement already satisfied: scipy in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (1.15.3)\n", "Requirement already satisfied: numpy<2.5,>=1.23.5 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from scipy) (2.2.6)\n", "Requirement already satisfied: pandas in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (2.2.3)\n", "Requirement already satisfied: numpy>=1.26.0 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from pandas) (2.2.6)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from pandas) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from pandas) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from pandas) (2025.2)\n", "Requirement already satisfied: six>=1.5 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from python-dateutil>=2.8.2->pandas) (1.17.0)\n", "Requirement already satisfied: seaborn in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (0.13.2)\n", "Requirement already satisfied: numpy!=1.24.0,>=1.20 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from seaborn) (2.2.6)\n", "Requirement already satisfied: pandas>=1.2 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from seaborn) (2.2.3)\n", "Requirement already satisfied: matplotlib!=3.6.1,>=3.4 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from seaborn) (3.10.3)\n", "Requirement already satisfied: contourpy>=1.0.1 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from matplotlib!=3.6.1,>=3.4->seaborn) (1.3.2)\n", "Requirement already satisfied: cycler>=0.10 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from matplotlib!=3.6.1,>=3.4->seaborn) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from matplotlib!=3.6.1,>=3.4->seaborn) (4.58.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from matplotlib!=3.6.1,>=3.4->seaborn) (1.4.8)\n", "Requirement already satisfied: packaging>=20.0 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from matplotlib!=3.6.1,>=3.4->seaborn) (25.0)\n", "Requirement already satisfied: pillow>=8 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from matplotlib!=3.6.1,>=3.4->seaborn) (11.2.1)\n", "Requirement already satisfied: pyparsing>=2.3.1 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from matplotlib!=3.6.1,>=3.4->seaborn) (3.2.3)\n", "Requirement already satisfied: python-dateutil>=2.7 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from matplotlib!=3.6.1,>=3.4->seaborn) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from pandas>=1.2->seaborn) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from pandas>=1.2->seaborn) (2025.2)\n", "Requirement already satisfied: six>=1.5 in c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages (from python-dateutil>=2.7->matplotlib!=3.6.1,>=3.4->seaborn) (1.17.0)\n"]}], "source": ["# In<PERSON>la le dipendenze una alla volta\n", "!pip install torch\n", "!pip install opencv-python\n", "!pip install pillow\n", "!pip install numpy\n", "!pip install matplotlib\n", "!pip install tqdm\n", "!pip install roboflow\n", "!pip install requests\n", "!pip install pyyaml\n", "!pip install scipy\n", "!pip install pandas\n", "!pip install seaborn"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Python version: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]\n", "NumPy version: 2.2.6\n", "Matplotlib version: 3.10.3\n", "Requests version: 2.32.3\n", "tqdm version: unknown\n"]}], "source": ["# Importa le librerie necessarie\n", "import os\n", "import sys\n", "import numpy as np\n", "import matplotlib\n", "import matplotlib.pyplot as plt\n", "from IPython.display import HTML\n", "from base64 import b64encode\n", "import requests\n", "from tqdm import tqdm\n", "\n", "# Verifica che le librerie siano state importate correttamente\n", "print(f\"Python version: {sys.version}\")\n", "print(f\"NumPy version: {np.__version__ if hasattr(np, '__version__') else 'unknown'}\")\n", "print(f\"Matplotlib version: {matplotlib.__version__ if hasattr(matplotlib, '__version__') else 'unknown'}\")\n", "print(f\"Requests version: {requests.__version__ if hasattr(requests, '__version__') else 'unknown'}\")\n", "print(f\"tqdm version: {tqdm.__version__ if hasattr(tqdm, '__version__') else 'unknown'}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mRunning cells with 'VolleyVisionVenv (Python 3.13.2)' requires the ipykernel package.\n", "\u001b[1;31m<PERSON><PERSON>all 'ipykernel' into the Python environment. \n", "\u001b[1;31mCommand: 'c:/Users/<USER>/Documents/ModenaVolley/Codice/VolleyVisionVenv/Scripts/python.exe -m pip install ipykernel -U --force-reinstall'"]}], "source": ["# Importa OpenCV con gestione degli errori\n", "try:\n", "    import cv2\n", "    print(f\"OpenCV version: {cv2.__version__}\")\n", "except ImportError:\n", "    print(\"Errore nell'importazione di OpenCV. Prova a reinstallarlo con: pip install opencv-python\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mRunning cells with 'VolleyVisionVenv (Python 3.13.2)' requires the ipykernel package.\n", "\u001b[1;31m<PERSON><PERSON>all 'ipykernel' into the Python environment. \n", "\u001b[1;31mCommand: 'c:/Users/<USER>/Documents/ModenaVolley/Codice/VolleyVisionVenv/Scripts/python.exe -m pip install ipykernel -U --force-reinstall'"]}], "source": ["# Importa PyTorch con gestione degli errori\n", "try:\n", "    import torch\n", "    print(f\"PyTorch version: {torch.__version__}\")\n", "    print(f\"CUDA available: {torch.cuda.is_available()}\")\n", "except ImportError:\n", "    print(\"Errore nell'importazione di PyTorch. Prova a reinstallarlo con: pip install torch\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Navigazione alle cartelle corrette\n", "\n", "Ora navighiamo alla cartella VolleyVision e poi alla sottocartella Stage I - Volleyball:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Percorso corrente: c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\FilePython_c\\VolleyVision\\Stage I - Volleyball\n", "Errore: Il percorso c:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\FilePython_c\\VolleyVision\\Stage I - Volleyball\\VolleyVision\\Stage I - Volleyball non esiste.\n", "Verifica che la struttura delle cartelle sia corretta.\n"]}], "source": ["# O<PERSON><PERSON> il percorso corrente\n", "current_path = os.getcwd()\n", "print(f\"Percorso corrente: {current_path}\")\n", "\n", "# Verifica se siamo già nella cartella FilePython_c\n", "if \"FilePython_c\" in current_path:\n", "    # Naviga alla cartella VolleyVision\n", "    volleyball_path = os.path.join(current_path, \"VolleyVision\", \"Stage I - Volleyball\")\n", "else:\n", "    # Assumiamo che siamo nella cartella principale del progetto\n", "    volleyball_path = os.path.join(current_path, \"FilePython_c\", \"VolleyVision\", \"Stage I - Volleyball\")\n", "\n", "# Verifica se il percorso esiste\n", "if os.path.exists(volleyball_path):\n", "    # Cambia directory\n", "    os.ch<PERSON>(volleyball_path)\n", "    print(f\"Nuova directory: {os.getcwd()}\")\n", "else:\n", "    print(f\"Errore: Il percorso {volleyball_path} non esiste.\")\n", "    print(\"Verifica che la struttura delle cartelle sia corretta.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Preparazione dei file necessari\n", "\n", "Ora verifichiamo e creiamo le cartelle necessarie, e scarichiamo i file del modello se non esistono:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Funzione per scaricare file con barra di progresso\n", "def download_file(url, destination):\n", "    \"\"\"Download a file from a URL with a progress bar\"\"\"\n", "    try:\n", "        response = requests.get(url, stream=True)\n", "        response.raise_for_status()  # Raise an exception for HTTP errors\n", "        \n", "        total_size = int(response.headers.get('content-length', 0))\n", "        block_size = 1024  # 1 Kibibyte\n", "        \n", "        # Crea la directory se non esiste\n", "        os.makedirs(os.path.dirname(destination), exist_ok=True)\n", "        \n", "        print(f\"Downloading {url} to {destination}\")\n", "        \n", "        with open(destination, 'wb') as file, tqdm(\n", "                desc=os.path.basename(destination),\n", "                total=total_size,\n", "                unit='iB',\n", "                unit_scale=True,\n", "                unit_divisor=1024,\n", "            ) as bar:\n", "            for data in response.iter_content(block_size):\n", "                size = file.write(data)\n", "                bar.update(size)\n", "        \n", "        return True\n", "    except Exception as e:\n", "        print(f\"Errore durante il download: {e}\")\n", "        return False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Crea la cartella Output se non esiste\n", "os.makedirs(\"Output\", exist_ok=True)\n", "\n", "# Crea la cartella models se non esiste\n", "os.makedirs(\"models\", exist_ok=True)\n", "\n", "# Crea la cartella DaSiamRPN se non esiste\n", "os.makedirs(\"DaSiamRPN\", exist_ok=True)\n", "\n", "# Crea la cartella VideoOutput se non esiste\n", "os.makedirs(\"VideoOutput\", exist_ok=True)\n", "\n", "# Scarica i file del modello YOLOv7 se non esistono\n", "if not os.path.exists(\"models/yolov7-tiny.pt\"):\n", "    download_file(\n", "        \"https://github.com/WongKinYiu/yolov7/releases/download/v0.1/yolov7-tiny.pt\",\n", "        \"models/yolov7-tiny.pt\"\n", "    )\n", "\n", "# Scarica i file del modello DaSiamRPN se non esistono\n", "if not os.path.exists(\"DaSiamRPN/dasiamrpn_model.onnx\"):\n", "    download_file(\n", "        \"https://github.com/opencv/opencv_zoo/raw/master/models/object_tracking_dasiamrpn/dasiamrpn_model.onnx\",\n", "        \"DaSiamRPN/dasiamrpn_model.onnx\"\n", "    )\n", "\n", "if not os.path.exists(\"DaSiamRPN/dasiamrpn_kernel_cls1.onnx\"):\n", "    download_file(\n", "        \"https://github.com/opencv/opencv_zoo/raw/master/models/object_tracking_dasiamrpn/dasiamrpn_kernel_cls1.onnx\",\n", "        \"DaSiamRPN/dasiamrpn_kernel_cls1.onnx\"\n", "    )\n", "\n", "if not os.path.exists(\"DaSiamRPN/dasiamrpn_kernel_r1.onnx\"):\n", "    download_file(\n", "        \"https://github.com/opencv/opencv_zoo/raw/master/models/object_tracking_dasiamrpn/dasiamrpn_kernel_r1.onnx\",\n", "        \"DaSiamRPN/dasiamrpn_kernel_r1.onnx\"\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Scarica un video di esempio\n", "\n", "Scarichiamo un video di pallavolo di esempio per testare il sistema:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Scarica un video di esempio se non esiste\n", "if not os.path.exists(\"sample_volleyball.mp4\"):\n", "    download_file(\n", "        \"https://github.com/shukkkur/VolleyVision/raw/main/assets/back_view.mp4\",\n", "        \"sample_volleyball.mp4\"\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Verifica che il file my_utils.py contenga l'import di re\n", "\n", "Verifichiamo che il file my_utils.py contenga l'import di re, altrimenti lo aggiungiamo:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Verifica che il file my_utils.py contenga l'import di re\n", "try:\n", "    with open(\"my_utils.py\", \"r\") as f:\n", "        content = f.read()\n", "\n", "    if \"import re\" not in content:\n", "        # Aggiungi l'import di re dopo gli altri import\n", "        content = content.replace(\"from typing import Generator, List, Tuple\", \"from typing import Generator, List, Tuple\\nimport re\")\n", "        \n", "        # Scrivi il contenuto modificato nel file\n", "        with open(\"my_utils.py\", \"w\") as f:\n", "            f.write(content)\n", "        \n", "        print(\"Aggiunto l'import di re al file my_utils.py\")\n", "    else:\n", "        print(\"Il file my_utils.py contiene già l'import di re\")\n", "except Exception as e:\n", "    print(f\"Errore durante la modifica del file my_utils.py: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Funzione per visualizzare i video\n", "\n", "Definiamo una funzione per visualizzare i video nel notebook:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Funzione per visualizzare i video nel notebook\n", "def show_video(video_path, width=640):\n", "    try:\n", "        video_file = open(video_path, \"r+b\").read()\n", "        video_url = f\"data:video/mp4;base64,{b64encode(video_file).decode()}\"\n", "        return HTML(f\"\"\"\n", "        <video width={width} controls>\n", "            <source src=\"{video_url}\" type=\"video/mp4\">\n", "        </video>\"\"\")\n", "    except Exception as e:\n", "        print(f\"Errore durante la visualizzazione del video: {e}\")\n", "        return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Esegui il rilevamento e il tracciamento della palla da pallavolo\n", "\n", "Ora eseguiamo il rilevamento e il tracciamento della palla da pallavolo:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Esegui il rilevamento e il tracciamento della palla da pallavolo\n", "!python volley_track.py --input_video_path sample_volleyball.mp4 --model yolov7 --confidence 0.25 --marker circle --color yellow"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualizza il video di output\n", "\n", "Visualizziamo il video di output:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualizza il video di output\n", "output_path = \"VideoOutput/yolov7Track_sample_volleyball.mp4\"\n", "if os.path.exists(output_path):\n", "    show_video(output_path)\n", "else:\n", "    print(f\"Video di output non trovato: {output_path}\")\n", "    print(\"Controlla se l'elaborazione è stata completata con successo.\")\n", "    \n", "    # Elenca i file nella cartella VideoOutput\n", "    if os.path.exists(\"VideoOutput\"):\n", "        print(\"\\nFile nella cartella VideoOutput:\")\n", "        for file in os.listdir(\"VideoOutput\"):\n", "            print(f\" - {file}\")"]}], "metadata": {"kernelspec": {"display_name": "VolleyVisionVenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 4}