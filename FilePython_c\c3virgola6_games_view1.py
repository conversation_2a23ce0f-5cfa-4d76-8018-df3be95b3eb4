#Arricchisco games_view con nuove colonne.
#Aggiungo TotalPoints, Variabilità, 


import numpy as np 
import pandas as pd  #Realizzato con la versione di Pandas 2.2.3
import os
import psycopg
from sqlalchemy import create_engine, text


conn = psycopg.connect(
    dbname="db_modena",           # database creato in pgAdmin4
    user="postgres",              # Il tuo nome utente PostgreSQL
    password="AcquaLevissima1",   # La password che hai scelto per 'postgres'
    host="localhost",             # 'localhost' se è sul tuo PC
    port=5432                     # La porta predefinita è 5432
)

cur = conn.cursor() 


cur.execute("DROP VIEW IF EXISTS games_view1")
conn.commit()

cur.execute("""
CREATE OR REPLACE games_view1


""")







