{"cells": [{"cell_type": "markdown", "metadata": {"id": "vpxmQ-xNB1VY"}, "source": ["<p align=\"center\">\n", "<img src=\"https://raw.githubusercontent.com/shukkkur/VolleyVision/main/assets/vv_logo.png\">\n", "</p>\n", "<h1 align=\"center\">Welcome to VolleyVision<br><code>Local</code> - Quick & Easy</h1>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Verifica dell'ambiente\n", "\n", "Verifichiamo che l'ambiente virtuale VolleyVisionVenv sia configurato correttamente:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"ename": "ContextualVersionConflict", "evalue": "(idna 3.10 (c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages), Requirement.parse('idna==3.7'), {'roboflow'})", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mContextualVersionConflict\u001b[39m                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[3]\u001b[39m\u001b[32m, line 10\u001b[39m\n\u001b[32m      8\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m package \u001b[38;5;129;01min\u001b[39;00m required_packages:\n\u001b[32m      9\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m10\u001b[39m         \u001b[43mpkg_resources\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget_distribution\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpackage\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     11\u001b[39m     \u001b[38;5;28;01mexcept\u001b[39;00m pkg_resources.DistributionNotFound:\n\u001b[32m     12\u001b[39m         missing_packages.append(package)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\VolleyVisionVenv\\Lib\\site-packages\\pkg_resources\\__init__.py:523\u001b[39m, in \u001b[36mget_distribution\u001b[39m\u001b[34m(dist)\u001b[39m\n\u001b[32m    521\u001b[39m     dist = Requirement.parse(dist)\n\u001b[32m    522\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(dist, Requirement):\n\u001b[32m--> \u001b[39m\u001b[32m523\u001b[39m     dist = \u001b[43mget_provider\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdist\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    524\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(dist, Distribution):\n\u001b[32m    525\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33mExpected str, Requirement, or Distribution\u001b[39m\u001b[33m\"\u001b[39m, dist)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\VolleyVisionVenv\\Lib\\site-packages\\pkg_resources\\__init__.py:414\u001b[39m, in \u001b[36mget_provider\u001b[39m\u001b[34m(moduleOrReq)\u001b[39m\n\u001b[32m    412\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"Return an IResourceProvider for the named module or requirement\"\"\"\u001b[39;00m\n\u001b[32m    413\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(moduleOrReq, Requirement):\n\u001b[32m--> \u001b[39m\u001b[32m414\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m working_set.find(moduleOrReq) \u001b[38;5;129;01mor\u001b[39;00m \u001b[43mrequire\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mstr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mmoduleOrReq\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m[\u001b[32m0\u001b[39m]\n\u001b[32m    415\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    416\u001b[39m     module = sys.modules[moduleOrReq]\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\VolleyVisionVenv\\Lib\\site-packages\\pkg_resources\\__init__.py:1062\u001b[39m, in \u001b[36mWorkingSet.require\u001b[39m\u001b[34m(self, *requirements)\u001b[39m\n\u001b[32m   1053\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mrequire\u001b[39m(\u001b[38;5;28mself\u001b[39m, *requirements: _NestedStr) -> \u001b[38;5;28mlist\u001b[39m[Distribution]:\n\u001b[32m   1054\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Ensure that distributions matching `requirements` are activated\u001b[39;00m\n\u001b[32m   1055\u001b[39m \n\u001b[32m   1056\u001b[39m \u001b[33;03m    `requirements` must be a string or a (possibly-nested) sequence\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m   1060\u001b[39m \u001b[33;03m    included, even if they were already activated in this working set.\u001b[39;00m\n\u001b[32m   1061\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m1062\u001b[39m     needed = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mresolve\u001b[49m\u001b[43m(\u001b[49m\u001b[43mparse_requirements\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequirements\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1064\u001b[39m     \u001b[38;5;28;01mfor\u001b[39;00m dist \u001b[38;5;129;01min\u001b[39;00m needed:\n\u001b[32m   1065\u001b[39m         \u001b[38;5;28mself\u001b[39m.add(dist)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\VolleyVisionVenv\\Lib\\site-packages\\pkg_resources\\__init__.py:889\u001b[39m, in \u001b[36mWorkingSet.resolve\u001b[39m\u001b[34m(self, requirements, env, installer, replace_conflicting, extras)\u001b[39m\n\u001b[32m    886\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m req_extras.markers_pass(req, extras):\n\u001b[32m    887\u001b[39m     \u001b[38;5;28;01mcontinue\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m889\u001b[39m dist = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_resolve_dist\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    890\u001b[39m \u001b[43m    \u001b[49m\u001b[43mreq\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mbest\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mreplace_conflicting\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43menv\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minstaller\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrequired_by\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mto_activate\u001b[49m\n\u001b[32m    891\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    893\u001b[39m \u001b[38;5;66;03m# push the new requirements onto the stack\u001b[39;00m\n\u001b[32m    894\u001b[39m new_requirements = dist.requires(req.extras)[::-\u001b[32m1\u001b[39m]\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\Documents\\ModenaVolley\\Codice\\VolleyVisionVenv\\Lib\\site-packages\\pkg_resources\\__init__.py:935\u001b[39m, in \u001b[36mWorkingSet._resolve_dist\u001b[39m\u001b[34m(self, req, best, replace_conflicting, env, installer, required_by, to_activate)\u001b[39m\n\u001b[32m    932\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m dist \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m req:\n\u001b[32m    933\u001b[39m     \u001b[38;5;66;03m# Oops, the \"best\" so far conflicts with a dependency\u001b[39;00m\n\u001b[32m    934\u001b[39m     dependent_req = required_by[req]\n\u001b[32m--> \u001b[39m\u001b[32m935\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m VersionConflict(dist, req).with_context(dependent_req)\n\u001b[32m    936\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m dist\n", "\u001b[31mContextualVersionConflict\u001b[39m: (idna 3.10 (c:\\users\\<USER>\\documents\\modenavolley\\codice\\volleyvisionvenv\\lib\\site-packages), Requirement.parse('idna==3.7'), {'roboflow'})"]}], "source": ["# Verifica che le dipendenze necessarie siano installate\n", "import sys\n", "import pkg_resources\n", "\n", "required_packages = ['torch', 'opencv-python', 'pillow', 'numpy', 'matplotlib', 'tqdm', 'roboflow']\n", "missing_packages = []\n", "\n", "for package in required_packages:\n", "    try:\n", "        pkg_resources.get_distribution(package)\n", "    except pkg_resources.DistributionNotFound:\n", "        missing_packages.append(package)\n", "\n", "if missing_packages:\n", "    print(f\"Attenzione: I seguenti pacchetti non sono installati: {', '.join(missing_packages)}\")\n", "    print(\"Ese<PERSON><PERSON> lo script 'crea_venv_volleyvision.bat' per creare un ambiente virtuale con tutte le dipendenze necessarie.\")\n", "else:\n", "    print(\"Tutte le dipendenze necessarie sono installate. L'ambiente è configurato correttamente.\")\n", "    print(f\"Versione di Python: {sys.version}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Importa le librerie necessarie\n", "import os\n", "import cv2\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from IPython.display import HTML\n", "from base64 import b64encode\n", "import requests\n", "from tqdm import tqdm"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Navigazione alle cartelle corrette\n", "\n", "Ora navighiamo alla cartella VolleyVision e poi alla sottocartella Stage I - Volleyball:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# O<PERSON><PERSON> il percorso corrente\n", "current_path = os.getcwd()\n", "print(f\"Percorso corrente: {current_path}\")\n", "\n", "# Verifica se siamo già nella cartella FilePython_c\n", "if \"FilePython_c\" in current_path:\n", "    # Naviga alla cartella VolleyVision\n", "    volleyball_path = os.path.join(current_path, \"VolleyVision\", \"Stage I - Volleyball\")\n", "else:\n", "    # Assumiamo che siamo nella cartella principale del progetto\n", "    volleyball_path = os.path.join(current_path, \"FilePython_c\", \"VolleyVision\", \"Stage I - Volleyball\")\n", "\n", "# Cambia directory\n", "os.ch<PERSON>(volleyball_path)\n", "print(f\"Nuova directory: {os.getcwd()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Preparazione dei file necessari\n", "\n", "Ora verifichiamo e creiamo le cartelle necessarie, e scarichiamo i file del modello se non esistono:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Funzione per scaricare file con barra di progresso\n", "def download_file(url, destination):\n", "    \"\"\"Download a file from a URL with a progress bar\"\"\"\n", "    response = requests.get(url, stream=True)\n", "    total_size = int(response.headers.get('content-length', 0))\n", "    block_size = 1024  # 1 Kibibyte\n", "    \n", "    # Crea la directory se non esiste\n", "    os.makedirs(os.path.dirname(destination), exist_ok=True)\n", "    \n", "    print(f\"Downloading {url} to {destination}\")\n", "    \n", "    with open(destination, 'wb') as file, tqdm(\n", "            desc=os.path.basename(destination),\n", "            total=total_size,\n", "            unit='iB',\n", "            unit_scale=True,\n", "            unit_divisor=1024,\n", "        ) as bar:\n", "        for data in response.iter_content(block_size):\n", "            size = file.write(data)\n", "            bar.update(size)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Crea la cartella Output se non esiste\n", "os.makedirs(\"Output\", exist_ok=True)\n", "\n", "# Crea la cartella models se non esiste\n", "os.makedirs(\"models\", exist_ok=True)\n", "\n", "# Crea la cartella DaSiamRPN se non esiste\n", "os.makedirs(\"DaSiamRPN\", exist_ok=True)\n", "\n", "# Scarica i file del modello YOLOv7 se non esistono\n", "if not os.path.exists(\"models/yolov7-tiny.pt\"):\n", "    download_file(\n", "        \"https://github.com/WongKinYiu/yolov7/releases/download/v0.1/yolov7-tiny.pt\",\n", "        \"models/yolov7-tiny.pt\"\n", "    )\n", "\n", "# Scarica i file del modello DaSiamRPN se non esistono\n", "if not os.path.exists(\"DaSiamRPN/dasiamrpn_model.onnx\"):\n", "    download_file(\n", "        \"https://github.com/opencv/opencv_zoo/raw/master/models/object_tracking_dasiamrpn/dasiamrpn_model.onnx\",\n", "        \"DaSiamRPN/dasiamrpn_model.onnx\"\n", "    )\n", "\n", "if not os.path.exists(\"DaSiamRPN/dasiamrpn_kernel_cls1.onnx\"):\n", "    download_file(\n", "        \"https://github.com/opencv/opencv_zoo/raw/master/models/object_tracking_dasiamrpn/dasiamrpn_kernel_cls1.onnx\",\n", "        \"DaSiamRPN/dasiamrpn_kernel_cls1.onnx\"\n", "    )\n", "\n", "if not os.path.exists(\"DaSiamRPN/dasiamrpn_kernel_r1.onnx\"):\n", "    download_file(\n", "        \"https://github.com/opencv/opencv_zoo/raw/master/models/object_tracking_dasiamrpn/dasiamrpn_kernel_r1.onnx\",\n", "        \"DaSiamRPN/dasiamrpn_kernel_r1.onnx\"\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Scarica un video di esempio\n", "\n", "Scarichiamo un video di pallavolo di esempio per testare il sistema:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Scarica un video di esempio se non esiste\n", "if not os.path.exists(\"sample_volleyball.mp4\"):\n", "    download_file(\n", "        \"https://github.com/shukkkur/VolleyVision/raw/main/assets/back_view.mp4\",\n", "        \"sample_volleyball.mp4\"\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Verifica che il file my_utils.py contenga l'import di re\n", "\n", "Verifichiamo che il file my_utils.py contenga l'import di re, altrimenti lo aggiungiamo:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Verifica che il file my_utils.py contenga l'import di re\n", "with open(\"my_utils.py\", \"r\") as f:\n", "    content = f.read()\n", "\n", "if \"import re\" not in content:\n", "    # Aggiungi l'import di re dopo gli altri import\n", "    content = content.replace(\"from typing import Generator, List, Tuple\", \"from typing import Generator, List, Tuple\\nimport re\")\n", "    \n", "    # Scrivi il contenuto modificato nel file\n", "    with open(\"my_utils.py\", \"w\") as f:\n", "        f.write(content)\n", "    \n", "    print(\"Aggiunto l'import di re al file my_utils.py\")\n", "else:\n", "    print(\"Il file my_utils.py contiene già l'import di re\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Funzione per visualizzare i video\n", "\n", "Definiamo una funzione per visualizzare i video nel notebook:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Funzione per visualizzare i video nel notebook\n", "def show_video(video_path, width=640):\n", "    video_file = open(video_path, \"r+b\").read()\n", "    video_url = f\"data:video/mp4;base64,{b64encode(video_file).decode()}\"\n", "    return HTML(f\"\"\"\n", "    <video width={width} controls>\n", "        <source src=\"{video_url}\" type=\"video/mp4\">\n", "    </video>\"\"\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Esegui il rilevamento e il tracciamento della palla da pallavolo\n", "\n", "Ora eseguiamo il rilevamento e il tracciamento della palla da pallavolo:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Esegui il rilevamento e il tracciamento della palla da pallavolo\n", "!python volley_track.py --input_video_path sample_volleyball.mp4 --model yolov7 --confidence 0.25 --marker circle --color yellow"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualizza il video di output\n", "\n", "Visualizziamo il video di output:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualizza il video di output\n", "output_path = \"VideoOutput/yolov7Track_sample_volleyball.mp4\"\n", "if os.path.exists(output_path):\n", "    show_video(output_path)\n", "else:\n", "    print(f\"Video di output non trovato: {output_path}\")\n", "    print(\"Controlla se l'elaborazione è stata completata con successo.\")\n", "    \n", "    # Elenca i file nella cartella VideoOutput\n", "    if os.path.exists(\"VideoOutput\"):\n", "        print(\"\\nFile nella cartella VideoOutput:\")\n", "        for file in os.listdir(\"VideoOutput\"):\n", "            print(f\" - {file}\")"]}], "metadata": {"kernelspec": {"display_name": "VolleyVisionVenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 4}