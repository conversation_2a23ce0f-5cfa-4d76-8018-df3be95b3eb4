weights: best.pt
cfg: cfg\training\yolov7-tiny.yaml
data: Volleyball-Tracking-16/data.yaml
hyp: data/hyp.vv.yaml
epochs: 300
batch_size: 16
img_size:
- 640
- 640
rect: false
resume: false
nosave: false
notest: false
noautoanchor: false
evolve: false
bucket: ''
cache_images: false
image_weights: false
device: '0'
multi_scale: false
single_cls: true
adam: false
sync_bn: false
local_rank: -1
workers: 4
project: runs/train
entity: null
name: bs16_e300_w4_640_tiny2_original
exist_ok: false
quad: false
linear_lr: false
label_smoothing: 0.0
upload_dataset: false
bbox_interval: -1
save_period: -1
artifact_alias: latest
freeze:
- 0
v5_metric: false
world_size: 1
global_rank: -1
save_dir: runs\train\bs16_e300_w4_640_tiny2_original
total_batch_size: 16
