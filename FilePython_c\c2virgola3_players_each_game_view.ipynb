{"cells": [{"cell_type": "code", "execution_count": 1, "id": "e6ca071b", "metadata": {}, "outputs": [], "source": ["import numpy as np \n", "import pandas as pd  #Realizzato con la versione di Pandas 2.2.3\n", "import polars as pl\n", "import os\n", "import psycopg\n", "from sqlalchemy import create_engine, text"]}, {"cell_type": "code", "execution_count": 14, "id": "27a9f973", "metadata": {}, "outputs": [], "source": ["conn = psycopg.connect(\n", "    dbname=\"db_modena\",           # database creato in pgAdmin4\n", "    user=\"postgres\",              # Il tuo nome utente PostgreSQL\n", "    password=\"AcquaLevissima1\",   # La password che hai scelto per 'postgres'\n", "    host=\"localhost\",             # 'localhost' se è sul tuo PC\n", "    port=5432                     # La porta predefinita è 5432\n", ")\n", "\n", "# Crea un cursore per eseguire le query\n", "cur = conn.cursor()"]}, {"cell_type": "code", "execution_count": 15, "id": "02c3dbd5", "metadata": {}, "outputs": [], "source": ["conn.rollback()\n", "\n", "cur.execute(\"\"\"\n", "DROP VIEW IF EXISTS players_each_game_view\n", "\"\"\")\n", "\n", "cur.execute(\"\"\"\n", "CREATE VIEW players_each_game_view AS\n", "SELECT\n", "    peg.*,\n", "    t.\"TeamID\",\n", "    t.\"<PERSON><PERSON>ame\",\n", "    t.\"HomeAway\"\n", "\n", "FROM\n", "    players_each_game peg\n", "JOIN\n", "    teams_each_game t\n", "    ON peg.\"GameID\" = t.\"GameID\" AND peg.\"TeamID_auto\" = t.\"TeamID_auto\"\n", "\n", "\"\"\")\n", "conn.commit()\n"]}, {"cell_type": "code", "execution_count": 8, "id": "da3ab564", "metadata": {}, "outputs": [], "source": ["cur.close()\n", "conn.close()"]}], "metadata": {"kernelspec": {"display_name": ".venv (3.13.2)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}